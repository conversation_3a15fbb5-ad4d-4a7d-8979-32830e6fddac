<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="appointment_emergency_cashier_record_view_inherit" model="ir.ui.view">
            <field name="name">Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="inherit_id" ref="nuro_appointment_cashier.nuro_appointment_form_view_sheet_cashier"/>
            <field name="arch" type="xml">

<!--                <xpath expr="//button[@name='process_request_action_view_payment']" position="attributes">-->
<!--                    <attribute name="groups">-->
<!--                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user-->
<!--                    </attribute>-->
<!--                </xpath>-->

                <xpath expr="//button[@name='create_appointment_credit_payment']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//button[@name='create_appointment_cash_payment']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//separator[@name='payment_information']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_type']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='expense_record_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='employee_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='appointment_type_id']" position="before">
                    <field name="appointment_type_readonly" invisible="1"/>
                </xpath>

                <xpath expr="//field[@name='appointment_type_id']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', ('state', '!=', 'draft'), ('appointment_type_readonly',
                        '=', True)]}
                    </attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="cashier_panel_form_view_emergency_cashier_view" model="ir.ui.view">
            <field name="name">Cashier Panel</field>
            <field name="model">cashier.panel</field>
            <field name="inherit_id" ref="static_panel_request.cashier_panel_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='process_request_action_view']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//separator[@name='payment_information']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_type']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='expense_record_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='employee_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

<!--                <xpath expr="//group[@name='payment_info']" position="attributes">-->
<!--                    <attribute name="groups">-->
<!--                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user-->
<!--                    </attribute>-->
<!--                </xpath>-->

            </field>
        </record>

        <!--Inherit Form View to Modify it-->
        <record id="pharmacy_sale_order_form_view_inherit_emergency_cashier" model="ir.ui.view">
            <field name="name">Pharmacy Sale</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="nuro_pharmacy_cashier.pharmacy_sale_order_form_view_inherit"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='get_payment_pharmacy_format_view']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//separator[@name='payment_information']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_type']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='expense_record_id']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='employee_id']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="pharmacy_sale_order_form_view_emergency_cashier" model="ir.ui.view">
            <field name="name">Sale</field>
            <field name="model">sale.order</field>
            <field name="priority">200</field>
            <field name="inherit_id" ref="nuro_pharmacy.pharmacy_sale_order_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//page[@name='pharmacy_order_line']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,nuro_hms_groups.group_doctor_user,nuro_emergency.group_emergency_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='amount_untaxed']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='discount_amount']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='total_amount']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='paid_amount']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='credit_amount']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='balance_amount']" position="attributes">
                    <attribute name="groups">
                        nuro_pharmacy.group_pharmacy_user,nuro_pharmacy.group_pharmacy_return_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="blood_transfusion_form_view_emergency_cashier" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="inherit_id" ref="nuro_blood_transfusion.blood_transfusion_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='get_payment_bt_format_view']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//separator[@name='payment_information']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_type']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='expense_record_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='employee_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='bill_to_user_id']" position="attributes">
                    <attribute name="groups">
                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user
                    </attribute>
                </xpath>

<!--                <xpath expr="//group[@name='payment_info']" position="attributes">-->
<!--                    <attribute name="groups">-->
<!--                        nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user-->
<!--                    </attribute>-->
<!--                </xpath>-->

            </field>
        </record>

        <record id="nuro_emergency_appointment_cashier" model="ir.actions.act_window">
            <field name="name">Cashier's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="search_view_id" ref="nuro_appointment_cashier.nuro_appointment_search_view_cashier"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_tree_view_sheet_for_cashier')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_graph_view_sheet_for_cashier')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_pivot_view_sheet_for_cashier')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_form_view_sheet_cashier')})]"/>
            <field name="context">{'search_default_appointment_date_cashier': 1, 'default_appointment_type':
                'emergency', 'default_appointment_type': 'emergency', 'default_appointment_type_readonly': True}
            </field>
            <field name="domain">[('appointment_type', '=', 'emergency')]</field>
        </record>

        <record id="action_emergency_cashier_panel_view" model="ir.actions.act_window">
            <field name="name">Cashier</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">cashier.panel</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('emergency_admission_id', '!=', False)]</field>
            <field name="context">{'search_default_cashier_panel_date_cashier': 1, 'delete': False, 'create': False}
            </field>
            <field name="search_view_id" ref="static_panel_request.cashier_panel_search_view"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('static_panel_request.cashier_panel_tree_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('static_panel_request.cashier_panel_form_view')})]"/>
        </record>

        <record id="action_pharmacy_sale_order_action_emergency_prescription" model="ir.actions.act_window">
            <field name="name">Pharmacy</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
            <field name="search_view_id" ref="nuro_pharmacy.pharmacy_sale_inherit_search_view"/>
            <field name="domain">[('pharmacy_sale', '=', True), ('state', '=', 'draft'), ('emergency_admission_id', '!=', False)]</field>
            <field name="context">{'default_pharmacy_sale': True, 'search_default_today_pharmacy_sale': 1}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_pharmacy.pharmacy_sale_order_list_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_pharmacy.pharmacy_sale_order_form_view')})]"/>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="emergency_admission_form_view_form_cashier_view" model="ir.ui.view">
            <field name="name">Emergency Admission</field>
            <field name="model">emergency.admission</field>
            <field name="inherit_id" ref="nuro_emergency.emergency_admission_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='open_payment_process_invoice']" position="attributes">
                    <attribute name="groups">nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user</attribute>
                </xpath>

                <xpath expr="//button[@name='discharge_now']" position="attributes">
                    <attribute name="groups">nuro_emergency.group_emergency_user,emergency_cashier.nuro_emergency_cashier_group_user</attribute>
                </xpath>

                <xpath expr="//button[@name='create_admission_request']" position="attributes">
                    <attribute name="groups">nuro_emergency.group_emergency_user,nuro_hms_groups.group_doctor_user,emergency_cashier.nuro_emergency_cashier_group_user</attribute>
                </xpath>

                <xpath expr="//field[@name='discharge_with_credit']" position="attributes">
                    <attribute name="groups">nuro_cashier_closing.group_cashier_user,emergency_cashier.nuro_emergency_cashier_group_user</attribute>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="nuro_patient_form_credit_extend_form_view_inherit" model="ir.ui.view">
            <field name="name">Patient</field>
            <field name="model">nuro.patient</field>
            <field name="inherit_id" ref="nuro_patient_credit.nuro_patient_form_credit_extend"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='%(nuro_patient_credit.action_assign_credit_limit_patient)d']" position="attributes">
                    <attribute name="groups">nuro_cashier_closing.group_cashier_manager,emergency_cashier.nuro_emergency_cashier_group_user</attribute>
                </xpath>

            </field>
        </record>

    </data>
</odoo>