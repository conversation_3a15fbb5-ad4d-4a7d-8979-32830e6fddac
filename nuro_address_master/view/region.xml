<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="nuro_res_region_form_view" model="ir.ui.view">
            <field name="name">res.region.form</field>
            <field name="model">res.region</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="name" required="1"/>
                            </group>
                            <group>
                                <field name="state_id"/>
                                <field name="country_id" required="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="nuro_res_region_tree_view" model="ir.ui.view">
            <field name="name">res.region.form</field>
            <field name="model">res.region</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="state_id"/>
                    <field name="country_id"/>
                </tree>
            </field>
        </record>

        <record id="action_nuro_region_view" model="ir.actions.act_window">
            <field name="name">Region</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.region</field>
            <field name="view_mode">tree,form</field>
        </record>

    </data>
</odoo>