# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset
# 
# Translators:
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2018
# <PERSON>, 2018
# <PERSON><PERSON><PERSON>em<PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-09-20 10:14+0000\n"
"Last-Translator: trendspotter <<EMAIL>>, 2019\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:404
#, python-format
msgid " (copy)"
msgstr "(kopírovat)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:540
#, python-format
msgid " (grouped)"
msgstr "(seskupené)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_entry_count
msgid "# Asset Entries"
msgstr "počet položek aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_nbr
msgid "# of Depreciation Lines"
msgstr "počet odpisových řádků"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_nbr
msgid "# of Installment Lines"
msgstr "počet splátkových řádků"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:account_asset.account_asset_cron
#: model:ir.cron,name:account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr "Účetní aktiva: Vygenerovat položky aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid "Account Date"
msgstr "Datum účtu"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
msgid "Account Entry"
msgstr "Položka účtu"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "Účet použitý v položkách odpisů, aby se snížila hodnota majetku."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Účet používaný v periodických údajích za účelem zaznamenání části aktiva "
"jako náklad."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "Účet použitý k zaznamenání nákupu aktiva za jeho původní cenu."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:52
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "Účetní položky čekající na ruční ověření"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_active
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_active
msgid "Active"
msgstr "Aktivní"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "Další možnosti"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#, python-format
msgid "Amount"
msgstr "Částka"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Částka odpisových linií"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_installment_value
msgid "Amount of Installment Lines"
msgstr "Částka splátkových linií"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_analytic_id
msgid "Analytic Account"
msgstr "Analytický účet"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "Majetek"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_asset_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Účet majetku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#: model:ir.ui.view,arch_db:account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "Kategorie aktiv"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Doba trvání aktiv ke změně"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_end_date
msgid "Asset End Date"
msgstr "Datum ukončení aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_asset_method_time
msgid "Asset Method Time"
msgstr "Způsob odepisování"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_name
msgid "Asset Name"
msgstr "Název aktiva"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_start_date
msgid "Asset Start Date"
msgstr "Počáteční datum aktiva"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_name
#: model:ir.model.fields,field_description:account_asset.field_product_product_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "Typ aktiva"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model:ir.ui.view,arch_db:account_asset.res_config_settings_view_form
msgid "Asset Types"
msgstr "Typy aktiv"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "Kategorie aktiv"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:286
#, python-format
msgid "Asset created"
msgstr "Aktivum vytvořeno"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "Odpisová metodika majetku"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:318
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "Majetek prodán či zlikvidován. Účetní záznam čeká na potvrzení."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "Majetek"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_asset_report
#: model:ir.model,name:account_asset.model_asset_asset_report
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_graph
#: model:ir.ui.view,arch_db:account_asset.action_account_asset_report_pivot
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Analýza majetku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Aktiva a výnosy"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Majetek v uzavřeném stavu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Majetek ve stavu koncept a otevřeném stavu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "Majetek ve stavu koncept"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "Majetek ve stavu běžící"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_open_asset
msgid "Auto-confirm Assets"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Zrušit"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Category"
msgstr "Kategorie"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "Kategorie aktiva"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"Zaškrtněte toto, pokud chcete automaticky potvrdit majetky pro tutu "
"kategorii, když jsou vytvořeny pomocí dokladů."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr "Zaškrtněte, pokud chcete seskupit generované položky podle kategorií."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"Vyberte metodu, kterou chcete použít pro výpočet množství odpisových řádků.\n"
"* Lineární: Vypočteno na základě: Hrubá hodnota / počet odpisů\n"
"* Degresivní: Vypočítáno na základě: Zbytková hodnota * Degresivní faktor"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""
"Vyberte metodu, která se použije pro výpočet dat a počtu záznamů.\n"
"* Počet záznamů: Stanovte počet záznamů a čas mezi 2 odpisy.\n"
"* Datum ukončení: Vyberte čas mezi 2 odpisy a datem, kdy odpisy nejdou dále."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_depreciation_confirmation_wizard_date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"Vyberte období, pro které chcete automaticky zaúčtovat odpisové řádky "
"běžících majetků."

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
msgid "Close"
msgstr "Zavřít"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Uzavřeno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_company_id
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "Firma"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method
msgid "Computation Method"
msgstr "Metoda výpočtu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "Spočítat majetek"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "Vypočítat odpisy"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Potvrdit"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Asset Moves"
msgstr "Vytvořit pohyby majetku"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Revenue Moves"
msgstr "Vytvořené pohyby výnosů"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_uid
msgid "Created by"
msgstr "Vytvořil(a)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Kumulativní odpisy"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:597
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_currency_id
#, python-format
msgid "Currency"
msgstr "Měna"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Aktuální"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_amount
msgid "Current Depreciation"
msgstr "Běžný odpis"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_date
msgid "Date"
msgstr "Datum"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "Datum aktiv"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "Datum zakoupení majetku"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "Datum odpisu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "Účet odloženého výnosu"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_product_product_deferred_revenue_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template_deferred_revenue_category_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "Typ odloženého výnosu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "Odložené tržby"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Degressive"
msgstr "Klesající"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_progress_factor
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_progress_factor
msgid "Degressive Factor"
msgstr "Činitel klesání"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "Amortizace"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "Odpisová tabule"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_depreciation_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_depreciation_date
msgid "Depreciation Date"
msgstr "Datum odpisu"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "Odpisy: Účet aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "Odpisy: Účet nákladů"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_id
msgid "Depreciation Entry"
msgstr "Položka odpisu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "Odpisové informace"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_depreciation_line_ids
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "Řádky odpisů"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "Odpisová metoda"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "Měsíční odpis"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_name
msgid "Depreciation Name"
msgstr "Jméno odpisu"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:85
#, python-format
msgid "Depreciation board modified"
msgstr "Odpisy byly upraveny"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:600
#, python-format
msgid "Depreciation line posted."
msgstr "Odpisová řádka zveřejněna."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_display_name
msgid "Display Name"
msgstr "Zobrazovaný název"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:327
#, python-format
msgid "Disposal Move"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:330
#, python-format
msgid "Disposal Moves"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:579
#, python-format
msgid "Document closed."
msgstr "Dokument uzavřen."

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "Návrh"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_end
msgid "Ending Date"
msgstr "Datum ukončení"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_end
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_end
msgid "Ending date"
msgstr "Datum ukončení"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "Rozšířené filtry…"

#. module: account_asset
#: model:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""
"Z této zprávy můžete získat přehled o všech odpisech.\n"
"vyhledávací panel lze také použít k personalizaci hlášení o odpisech aktiv."

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Vytvořit položky"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_gross_value
msgid "Gross Amount"
msgstr "Hrubá částka"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value
msgid "Gross Value"
msgstr "Hrubá hodnota"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "Hrubá hodnota aktiva"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Seskupit podle…"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_group_entries
msgid "Group Journal Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_id
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first January / Start date of fiscal "
"year"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""
"Označuje, že první položka odpisů tohoto aktiva musí být provedena od data "
"nákupu namísto 1. ledna"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_invoice_id
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Faktura"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice_line
msgid "Invoice Line"
msgstr "Řádek faktury"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Toto je částka, kterou plánujete, že nebudete odpisovat."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "Položky"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_journal_id
msgid "Journal"
msgstr "Deník"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:438
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "Položky deníku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category___last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard___last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify___last_update
msgid "Last Modified on"
msgstr "Naposled upraveno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_uid
msgid "Last Updated by"
msgstr "Naposled upraveno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard_write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Linear"
msgstr "Lineární"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_check
msgid "Linked"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Upravit"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_modify
#: model:ir.model,name:account_asset.model_asset_modify
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Upravit majetek"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "Upravit odpisy"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Month"
msgstr "Měsíc"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line_asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "Měsíční opakované výnosy"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_remaining_value
msgid "Next Period Depreciation"
msgstr "Další doba odpisu"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_note
msgid "Note"
msgstr "Poznámka"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_number
msgid "Number of Depreciations"
msgstr "Počet odpisů"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "Počet vstupů"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_period
msgid "Number of Months in a Period"
msgstr "Počet měsíců za dobu"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:599
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_partner_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_partner_id
#, python-format
msgid "Partner"
msgstr "Kontakt"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_method_period
msgid "Period Length"
msgstr "Délka období"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "Periodicita"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr ""

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:49
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_move_posted_check
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_move_check
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "Vloženo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_posted_value
msgid "Posted Amount"
msgstr "Zaúčtovaná částka"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "Zaúčtované řádky odpisů"

#. module: account_asset
#: model:ir.model,name:account_asset.model_product_template
msgid "Product Template"
msgstr "Šablona výrobku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_prorata
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_prorata
msgid "Prorata Temporis"
msgstr "Prorata Temporis"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:370
#, python-format
msgid ""
"Prorata temporis can be applied only for time method \"number of "
"depreciations\"."
msgstr ""
"Prorata temporis může být použita pouze pro časovou metodu \"počet odpisů\"."

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "Nákup"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "Měsíc zakoupení"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Purchase: Asset"
msgstr "Nákup: Aktivum"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify_name
msgid "Reason"
msgstr "Důvod"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_code
msgid "Reference"
msgstr "Reference"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "Zbytek"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_value_residual
msgid "Residual Value"
msgstr "Zbytková hodnota"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "Spuštěno"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Sale: Revenue Recognition"
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "Prodej"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_salvage_value
msgid "Salvage Value"
msgstr "Záchranná hodnota"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "Hledat kategorii majetku"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "Prodej nebo Likvidace"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "Uložit jako koncept"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Zde zjistěte čas mezi dvěma odpisy v měsíci"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line_parent_state
msgid "State of Asset"
msgstr "Stav majetku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_state
msgid "Status"
msgstr "Stav"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_period
msgid "The amount of time between two depreciations, in months"
msgstr "Množství času mezi dvěma odpisy v měsíci"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_method_number
#: model:ir.model.fields,help:account_asset.field_account_asset_category_method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Počet odpisů nutný k odepsání vašeho majetku"

#. module: account_asset
#: code:addons/account_asset/models/account_invoice.py:62
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be null."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:482
#, python-format
msgid ""
"This depreciation is already linked to a journal entry! Please post or "
"delete it."
msgstr ""

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""
"Tento průvodce zaúčtuje splátky / odpisy za vybraný měsíc.<br/>\n"
"Tím se vytvoří i zápisy do deníku pro všechny související splátky v tomto období uznání aktiv / výnosů."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_method_time
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_method_time
msgid "Time Method"
msgstr "Časová metoda"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "Časová metoda založená na"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category_type
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Type"
msgstr "Typ"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:55
#, python-format
msgid "Unposted"
msgstr "Nezaúčtované"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_unposted_value
msgid "Unposted Amount"
msgstr "Nezaúčtovaná částka"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Dodavatel"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset_state
#: model:ir.model.fields,help:account_asset.field_account_asset_depreciation_line_parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Když se vytvoří nový majetek, jeho status je Návrh.\n"
"Po jeh potvrzení ze status mění na Probíhající a v účetnictví může být započato odepisování.\n"
"Po ukončení odpisování můžete majetek ručně uzavřít.  Pokud je zaevidovaný poslední řádek odpisů, majetek změní status na Uzavřený automaticky."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report_name
msgid "Year"
msgstr "Rok"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:111
#, python-format
msgid "You cannot delete a document is in %s state."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:114
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr "Nemůžete odstranit dokument, který obsahuje zveřejněné položky."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:608
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "Nelze smazat již odepsané řádky."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:610
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "Nelze smazat vložené řádky splátky."

#. module: account_asset
#: model:ir.model,name:account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr "asset.depreciation.confirmation.wizard"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "např. Počítače"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "např. notebook iBook"

#. module: account_asset
#: model:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "months"
msgstr "měsíce"
