# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from odoo.exceptions import UserError

from odoo import api, fields, models, _


class NuroBloodTransfusionCashPayment(models.TransientModel):
    _name = 'nuro.blood.transfusion.cash.payment'
    _description = 'Blood Transfusion Cash Payment'

    @api.model
    def default_get(self, vals):
        """
        Default Get for the Cash Payment Method for the Blood Transfusion
        :param vals:
        :return:
        """
        res = super(NuroBloodTransfusionCashPayment, self).default_get(vals)
        blood_transfusion_id = self.env['nuro.blood.transfusion'].browse(self.env.context.get('active_id'))
        if blood_transfusion_id:
            res.update({
                'blood_transfusion_id': blood_transfusion_id.id,
                'amount': blood_transfusion_id.subtotal
            })
        return res

    blood_transfusion_id = fields.Many2one('nuro.blood.transfusion', string='Blood Transfusion ID#')
    payment_method_id = fields.Many2one('account.journal', string='Payment Method',
                                        domain=[('type', '=', 'cash'), ('cashier_journal', '=', True)],
                                        default=lambda self:
                                        self.env['account.journal'].search([
                                            ('type', '=', 'cash'),
                                            ('cashier_journal', '=', True)
                                        ], limit=1).id, required=True, readonly=True)
    date = fields.Datetime(string='Date', default=fields.Datetime.now, readonly=True)
    amount = fields.Float("Amount To Pay", readonly=True)
    total_amount = fields.Float('Amount', related='blood_transfusion_id.subtotal', store=True)
    user_id = fields.Many2one('res.users', 'User', default=lambda self: self.env.user, store=True, readonly=True)
    discount_amount = fields.Float('Discount')
    approve_by = fields.Many2one('hr.employee', 'Approved By', domain=lambda self: [
        ('user_id', '!=', False),
        ('user_id.groups_id', 'in', self.env.ref('nuro_cashier_payment_setting.discount_request_approve').id)])

    @api.onchange('discount_amount')
    def onchange_discount_amount(self):
        """
        Onchange of discount on Blood Transfusion Cash wizard
        :return:
        """
        if 0.0 < self.discount_amount < self.blood_transfusion_id.subtotal:
            self.amount = self.blood_transfusion_id.subtotal - self.discount_amount
        elif self.discount_amount < 0.0:
            self.discount_amount = False
            self.amount = self.blood_transfusion_id.subtotal
            message = 'You can not Assign Discount in Negative!!!'
            return {'warning': {'title': 'Discount Amount Error', "message": message}}
        elif self.blood_transfusion_id.subtotal <= self.discount_amount:
            self.discount_amount = False
            self.amount = self.blood_transfusion_id.subtotal
            message = 'You can not Assign Discount More than or equal to Total Amount!!!'
            return {'warning': {'title': 'Discount Amount Error', "message": message}}
        else:
            self.amount = self.blood_transfusion_id.subtotal

    def action_blood_transfusion_cash_payment(self):
        """
        Method for the cash Payment Creation
        :return:
        """
        # if not self.blood_transfusion_id.donor_patient_id:
        #     raise UserError(_('Please Add Donor name Before Processing It.!!!'))
        current_user = self.env.user
        for rec in self:
            if rec.amount <= 0.0:
                raise UserError(_('No Amount for Cash Payment!'))
            if rec.blood_transfusion_id.state not in ('draft', 'request'):
                raise UserError(_('Payment already has been made!!'))
            if not current_user.account_id:
                raise UserError(_('Please define Account for Selected User!'))
            partner_id = rec.blood_transfusion_id.patient_id.partner_id.parent_id or \
                         rec.blood_transfusion_id.patient_id.partner_id
            if rec.blood_transfusion_id:
                inv_ids = rec.blood_transfusion_id.create_invoice(
                    amount=rec.amount,
                    discount_amount=rec.discount_amount,
                    partner_id=partner_id,
                    invoice_type='out_invoice'
                )
                inv_ids.cash_payment = True
                inv_ids.action_post()
                if rec.amount > 0.0:
                    payment_method_id = rec.payment_method_id.inbound_payment_method_ids.id or \
                                        rec.payment_method_id.outbound_payment_method_ids.id
                    payment_account = rec.payment_method_id.default_credit_account_id.id
                    rec.payment_method_id.sudo().write({'default_credit_account_id': self.env.user.account_id.id})
                    # noinspection PyProtectedMember
                    payments = rec.blood_transfusion_id.create_payment(
                        partner_id=partner_id,
                        amount=rec.amount,
                        journal_id=rec.payment_method_id,
                        payment_method_id=payment_method_id,
                        payment_type='inbound',
                        invoice_ids=inv_ids
                    )
                    payments.cash_payment = True
                    payments.post()
                    rec.payment_method_id.sudo().write({'default_credit_account_id': payment_account})
                    approve_by = False
                    if self.approve_by:
                        approve_by = self.approve_by.id
                    rec.blood_transfusion_id.write({
                        'payment_method': 'cash',
                        'state': 'send_to_lab',
                        'discount_amount': self.discount_amount,
                        'cashier_user': self.env.user.id,
                        'approve_by': approve_by,
                        'paid_amount': self.amount
                    })
                rec.blood_transfusion_id.request_blood_request()
            return rec.blood_transfusion_id.print_receipt()
