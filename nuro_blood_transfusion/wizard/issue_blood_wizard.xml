<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="issue_blood_wizard_form" model="ir.ui.view">
            <field name="name">Issue Blood</field>
            <field name="model">issue.blood.wizard</field>
            <field name="arch" type="xml">
                <form string="Issue Blood">
                    <sheet>
                        <group>
                            <group>
                                <field name="br_id" invisible="1"/>
                                <field name="blood_location_id" invisible="1"/>
                                <field name="blood_group_id" invisible="1"/>
                                <field name="donor_ids" widget="many2many_tags" invisible="1"/>
                            </group>
                            <group>
                                <field name="taken_by" required="1"/>
                                <field name="taken_by_user_id"
                                       options="{'no_create': True,'no_open': True}"
                                       attrs="{'invisible': [('taken_by', '!=', 'nurse')], 'required': [('taken_by', '=', 'nurse')]}"/>
                                <field name="taken_by_family_member"
                                       attrs="{'invisible': [('taken_by', '!=', 'family_member')], 'required': [('taken_by', '=', 'family_member')]}"/>
                                <field name="taken_date" invisible="1"/>
                            </group>
                        </group>
                        <field name="bd_line_ids" invisible="0"/>
                        <field name="blood_move_ids"
                               domain="[
           ('blood_group_id', '=', blood_group_id),
           ('available_qty', '=', 1),
           ('type', '=', 'IN'),
           ('blood_donation_id.result_details', '!=', False),
           ('id', 'in', bd_line_ids)
       ]"/>

                    </sheet>
                    <footer>
                        <button string="Issue" type="object" name="assign_blood_request" class="oe_highlight"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_issue_blood_wizard_form" model="ir.actions.act_window">
            <field name="name">Issue Blood</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">issue.blood.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="issue_blood_wizard_form"/>
        </record>

    </data>
</odoo>
