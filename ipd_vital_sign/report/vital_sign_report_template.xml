<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!--============== Vital Sign Report Template =========-->
        <template id="vital_sign_report_template">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <t t-foreach="docs" t-as="o">
                        <div class="page">
                            <style>
                                table, th, td {
                                border-collapse: collapse;
                                }
                                th, td {
                                padding: 5px;
                                text-align: left;
                                }
                                .wrap {
                                width: 810px;
                                margin-left: auto;
                                margin-right: auto;
                                }
                            </style>
                            <hr/>
                            <div class="oe_structure"/>
                            <p style="font-size:24px;text-align:center;font-weight:bold;">Patient Information</p>
                            <table style="width: 100%;">
                                <tbody>
                                    <tr>
                                        <td>
                                            <strong>Pt's Name:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.patient_id.name"/>
                                        </td>
                                        <td>
                                            <strong>PID:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.identification_code"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Age:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.age"/>
                                        </td>
                                        <td>
                                            <strong>Gender:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.gender"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Mobile:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.mobile"/>
                                        </td>
                                        <td>
                                            <strong>IPD:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.name"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Primary Doctor:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.doctor_id.name"/>
                                        </td>
                                        <td>
                                            <strong>Ward:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.ward_id.name"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Bed:</strong>
                                        </td>
                                        <td>
                                            <span t-field="o.bed_id.name"/>
                                        </td>
                                        <td>
                                            <strong>Diagnosis:</strong>
                                        </td>
                                        <td>
                                            <span t-esc="o.get_ipd_ventilator_diagnosis()"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <br/>
                            <h3 style="text-align: center;">Vital Signs</h3>
                            <table style="width: 100%;" border="1">
                                <thead>
                                    <tr>
                                        <th>
                                            Date &amp; Time
                                        </th>
                                        <t t-foreach="o.get_vital_sign_record()" t-as="line">
                                            <th>
                                                <span t-esc="line.name"/>
                                            </th>
                                        </t>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="o.get_record()" t-as="record">
                                        <td t-foreach="record.values()" t-as="line">
                                            <span t-esc="line"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="footer">
                                <div class="row" style="width: 100%;">
                                    <img t-if="o.company_id and o.company_id.footer_image"
                                         t-att-src="image_data_uri(o.company_id.footer_image)"
                                         style="height: 150px; width: 1000px; position: absolute;"
                                         alt="Footer Image"
                                    />
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>

        <!-- ============ Paper Format For Vital Sign ===============-->
        <record id="paperformat_vital_sign_report" model="report.paperformat">
            <field name="name">Vital Sign</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">35</field>
            <field name="margin_bottom">12</field>
            <field name="margin_left">5</field>
            <field name="margin_right">5</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">35</field>
            <field name="dpi">90</field>
        </record>

        <!-- ========= Action For Vital Sign ==========-->
        <record id="action_vital_sign_report" model="ir.actions.report">
            <field name="name">Vital sign Report</field>
            <field name="model">nuro.inpatient</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ipd_vital_sign.vital_sign_report_template</field>
            <field name="report_file">ipd_vital_sign.vital_sign_report_template</field>
            <field name="paperformat_id" ref="ipd_vital_sign.paperformat_vital_sign_report"/>
            <field name="binding_model_id" eval="False"/>
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>
