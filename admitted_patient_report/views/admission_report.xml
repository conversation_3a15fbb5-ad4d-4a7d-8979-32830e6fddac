<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_inpatient_detailed_report_tree" model="ir.ui.view">
            <field name="name">inpatient.detailed.report.tree</field>
            <field name="model">inpatient.detailed.report</field>
            <field name="arch" type="xml">
                <tree string="Inpatient Detailed Report" create="false" edit="false" delete="false">
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="identification_code"/>
                    <field name="gender"/>
                    <field name="mobile"/>
                    <field name="district_id"/>
                    <field name="admission_date"/>
                    <field name="diagnosis"/>
                    <field name="admission_duration"/>
                    <field name="imaging_count"/>
                    <field name="lab_count"/>
                    <field name="prescription_count"/>
                    <field name="op_count"/>
                    <field name="surgery_count"/>
                    <field name="blood_transfusion_count"/>
                    <field name="ward_service_count"/>
                    <button name="action_view_details" string="View Details" type="object" class="oe_highlight"/>
                </tree>
            </field>
        </record>

        <!-- Search View -->
        <record id="view_inpatient_detailed_report_search" model="ir.ui.view">
            <field name="name">inpatient.detailed.report.search</field>
            <field name="model">inpatient.detailed.report</field>
            <field name="arch" type="xml">
                <search string="Inpatient Detailed Report">
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="identification_code"/>
                    <field name="district_id"/>
                    <field name="diagnosis"/>
                    <separator/>
                    <filter string="Male" name="male" domain="[('gender', '=', 'male')]"/>
                    <filter string="Female" name="female" domain="[('gender', '=', 'female')]"/>
                    <separator/>
                    <filter string="This Year" name="this_year"
                            domain="[('admission_date', '&gt;=', context_today().strftime('%Y-01-01')),
                                    ('admission_date', '&lt;=', context_today().strftime('%Y-12-31'))]"/>
                    <filter string="Today" name="today"
                            domain="[('admission_date', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter string="Date Range" name="admission_date_range"
                            domain="[('admission_date', '&gt;=', context.get('default_admission_date_start')),
                                    ('admission_date', '&lt;=', context.get('default_admission_date_end'))]"
                            help="Filter by admission date range"/>
                    <group expand="0" string="Group By">
                        <filter string="Patient" name="group_patient" context="{'group_by': 'patient_id'}"/>
                        <filter string="District" name="group_district" context="{'group_by': 'district_id'}"/>
                        <filter string="Admission Month" name="group_admission_month" context="{'group_by': 'admission_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action -->
        <record id="action_inpatient_detailed_report" model="ir.actions.act_window">
            <field name="name">Inpatient Detailed Report</field>
            <field name="res_model">inpatient.detailed.report</field>
            <field name="view_mode">tree</field>
            <field name="context">{'search_default_this_year': 1}</field>
        </record>

<!--        &lt;!&ndash; Menu &ndash;&gt;-->
<!--        <menuitem id="menu_inpatient_detailed_report"-->
<!--                  name="Inpatient Detailed Report"-->
<!--                  parent="nuro_inpatient_report.menu_inpatient_report_main_menu"-->
<!--                  action="admitted_patient_report.action_inpatient_detailed_report"-->
<!--                  sequence="200"/>-->
    </data>
</odoo>