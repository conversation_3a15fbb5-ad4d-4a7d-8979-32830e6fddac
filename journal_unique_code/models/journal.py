# -*- coding: utf-8 -*-
# Copyright  Softprime consulting Pvt Ltd
from odoo import api, fields, models, _
from odoo.exceptions import UserError


class AccountJournal(models.Model):
    _inherit = 'account.journal'

    @api.constrains('code')
    def check_duplicate_short_code(self):
        if self.code:
            already_exist = self.search([('code', '=', self.code),
                                         ('id', '!=', self.id),
                                         ('company_id', '=', self.company_id.id)])
            if already_exist:
                raise UserError(_("Code should be Unique!"))
