<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="inherit_po_asset_form" model="ir.ui.view">
        <field name="name">Po Asset</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="nuro_purchse_asset.po_asset_req_receive_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']//tree//field[@name='asset_category_id']" position="after">
                <field name="is_add_show" invisible="1"/>
                <field name="asset_id" domain="[('category_id', '=', asset_category_id), ('state', 'in', ['draft', 'open'])]"
                       options="{'no_create': True}" attrs="{'readonly': [('is_add_show', '!=', True)]}"/>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='asset_product_id']" position="attributes">
                <attribute name="domain">['|',('is_add_asset', '=', True), ('is_asset', '=', True)]</attribute>
            </xpath>
        </field>
    </record>
</odoo>