<odoo>
    <data>
        <record model="ir.ui.view" id="top_material_request_tree_view">
            <field name="name">top.material.request.tree</field>
            <field name="model">top.material.request</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="date"/>
                    <field name="sale_id"/>
                    <field name="name"/>
                    <field name="analytic_account_id" invisible="1"/>
                    <field name="total_sale_price"/>
                    <field name="total_cost"/>
                    <field name="gross_profit"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="top_material_request_form_view">
            <field name="name">top.material.request.form</field>
            <field name="model">top.material.request</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="%(btm_sale_mini_project_material.action_top_req_approval)d" type="action"
                                string="Send For Approval" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button name="action_approve_top_req" type="object"
                                groups="btm_sale_mini_project_material.group_top_material_req_manager"
                                string="Confirm" attrs="{'invisible': [('state', '!=', 'waiting')]}"/>
                        <button name="action_cancel_top_req" type="object"
                                string="Cancel" attrs="{'invisible': [('state', 'not in', ['draft', 'waiting'])]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,waiting,confirm,cancel"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" type="object" name="action_open_picking" icon="fa-tasks"
                                    attrs="{'invisible': [('state', 'in', ['draft', 'waiting'])]}">
                                Internal Transfers
                            </button>
                        </div>

                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <h1>
                                    <field name="name" class="oe_inline" readonly="1"/>
                                </h1>
                            </div>
                        </div>

                        <group col="4" colspan="4">
                            <field name="partner_id" attrs="{'readonly': [('state', '!=', 'draft')]}" required="1"/>
                            <field name="date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="wh_id" options="{'no_create': True}"
                                   attrs="{'readonly': [('state', 'in', ['invoice', 'done'])]}"/>
                            <field name="sale_id" readonly="1"/>
                        </group>

                        <notebook>
                            <page string="Material Planing">
                                <button name="action_partial_create_picking" type="object" class="oe_highlight"
                                        groups="stock.group_stock_manager"
                                        string="Partial Internal Transfer"
                                        attrs="{'invisible': [('state', '!=', 'confirm')]}"/>

                                <button name="action_full_internal_transfer" type="object" class="oe_highlight"
                                        groups="stock.group_stock_manager"
                                        string="Full Internal Transfer"
                                        attrs="{'invisible': [('state', '!=', 'confirm')]}"/>

                                <field name="product_line" nolabel="1" attrs="{'readonly': [('state', '=', 'done')]}">
                                    <tree editable="bottom" decoration-info="qty==requested_qty">
                                        <field name="product_id" required="1"
                                               attrs="{'readonly': [('state', 'in', ['waiting', 'confirm'])]}"
                                               doamin="[('type', '=', 'product')]" options="{'no_create': True}"/>
                                        <field name="uom_id" options="{'no_create': True}"
                                               attrs="{'readonly': [('state', 'in', ['waiting', 'confirm'])]}"/>
                                        <field name="qty"
                                               attrs="{'readonly': [('state', 'in', ['waiting', 'confirm'])]}"/>
                                        <field name="req_qty"/>
                                        <field name="requested_qty" readonly="1" force_save="1"/>
                                        <field name="transfer_qty" readonly="1" force_save="1"/>
                                        <field name="cost"/>
                                        <field name="total_cost"/>
                                        <field name="state" invisible="1"/>
                                        <field name="line_done" invisible="1"/>
                                        <field name="wh_id" invisible="1"/>
                                    </tree>
                                </field>
                                <group name="mini_note_group" col="6">
                                    <group colspan="4">
                                        <field name="note" nolabel="1" placeholder="Terms and conditions..."/>
                                    </group>
                                    <group class="oe_subtotal_footer oe_right" colspan="2" name="mini_sale_total">
                                        <field name="total_sale_price" class="oe_subtotal_footer oe_right" force_save="1"/>
                                        <field name="total_cost" class="oe_subtotal_footer oe_right" readonly="1"
                                               force_save="1"/>
                                        <field name="gross_profit" class="oe_subtotal_footer oe_right" force_save="1"/>
                                    </group>
                                    <div class="oe_clear"/>
                                </group>
                            </page>
                            <page string="Other Information">
                                <group col="4" colspan="4">
                                    <field name="req_date" readonly="1"/>
                                    <field name="req_user_id" readonly="1"/>
                                    <field name="approve_user_id" readonly="1"/>
                                    <field name="create_uid" readonly="1"/>
                                    <field name="company_id" readonly="1"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record model="ir.actions.act_window" id="top_material_req_action">
            <field name="name">Sale Mini Project</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">top.material.request</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'create': False}</field>
        </record>

        <record model="ir.actions.act_window" id="top_material_req_action_waiting">
            <field name="name">Sale Mini Project</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">top.material.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'waiting')]</field>
            <field name="context">{'create': False}</field>
        </record>

        <!--        <menuitem id="main_menu_top_material_request"-->
        <!--                 sequence="12" web_icon="btm_sale_mini_project_material,static/description/top.png"-->
        <!--                 name="TOP Material" groups="btm_sale_mini_project_material.group_top_material_req_user"/>-->

        <menuitem id="menu_top_material_requests"
                  sequence="6"
                  action="top_material_req_action"
                  parent="sale.sale_menu_root"
                  groups="btm_sale_mini_project_material.group_top_material_req_user"
                  name="Mini Project Material"/>

        <!--        <menuitem id="menu_top_material_request_waiting"-->
        <!--                  sequence="1"-->
        <!--                  groups="btm_sale_mini_project_material.group_top_material_req_manager"-->
        <!--                  action="top_material_req_action_waiting"-->
        <!--                  parent="main_menu_top_material_request"-->
        <!--                  name="TOP Material To Approve"/>-->

    </data>
</odoo>