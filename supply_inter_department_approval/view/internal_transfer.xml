<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="internal_transfer_form_view_inter_department_request_approval" model="ir.ui.view">
            <field name="name">Internal Transfer</field>
            <field name="model">material.request</field>
            <field name="inherit_id" ref="supply_request_approval.form_inherit_supply_req_approval"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='assigned_to']" position="attributes">
                    <attribute name="required">0</attribute>
                    <attribute name="attrs">{'readonly': [('state','!=', 'draft')], 'required': [('is_internal_transfer', '!=', True)], 'invisible': [('is_internal_transfer', '=', True)]}</attribute>
                </xpath>

                <xpath expr="//button[@name='button_to_approve']" position="attributes">
                    <attribute name="attrs">{'invisible': ['|', ('state', '!=', 'draft'), ('is_internal_transfer', '=', True)]}</attribute>
                </xpath>

                <xpath expr="//button[@name='button_approved']" position="attributes">
                    <attribute name="attrs">{'invisible': ['|', ('state', '!=', 'to_approve'), ('is_internal_transfer', '=', True)]}</attribute>
                </xpath>

                <xpath expr="//button[@name='%(supply_request_approval.action_change_approver)d']" position="attributes">
                    <attribute name="attrs">{'invisible': ['|', ('state', 'not in', ('to_approve')), ('is_internal_transfer', '=', True)]}</attribute>
                </xpath>

            </field>
        </record>

    </data>
</odoo>