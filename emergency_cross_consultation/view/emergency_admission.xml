<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="cross-consultation_emergency_form_view" model="ir.ui.view">
            <field name="name">Speciality Consultation</field>
            <field name="model">speciality.consultation</field>
            <field name="inherit_id" ref="nuro_inpatient.nuro_speciality_consultation_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='speciality_id']" position="after">
                    <field name="emergency_admission_id" readonly="1" force_save="1"
                           options="{'no_open': True, 'no_create': True}"
                           attrs="{'invisible': [('emergency_admission_id', '=', False)]}"/>
                </xpath>

                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="get_emergency_view" type="object" class="oe_stat_button" icon="fa-book"
                            string="Emergency" attrs="{'invisible': [('emergency_admission_id', '=', False)]}"/>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="emergency_admission_cross_consultation_form_inherit" model="ir.ui.view">
            <field name="name">Emergency Admission</field>
            <field name="model">emergency.admission</field>
            <field name="inherit_id" ref="nuro_emergency.emergency_admission_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//header" position="inside">
                    <button name="consult_speciality_doctor_wizard" type="object" string="Cross Consultation"
                            class="oe_highlight" states="initiated" groups="nuro_emergency.group_emergency_user"/>
                </xpath>

            </field>
        </record>

        <record id="nuro_speciality_consultation_search_view_emergency_filter" model="ir.ui.view">
            <field name="name">Speciality Consultation</field>
            <field name="model">speciality.consultation</field>
            <field name="inherit_id" ref="nuro_inpatient.nuro_speciality_consultation_search_view"/>
            <field name="arch" type="xml">
                <xpath expr="//separator[@name='field_seperator']" position="after">
                    <field name="emergency_admission_id" string="Emergency"/>
                    <filter string="Emergency" name="emergency_consultation"
                            domain="[('emergency_admission_id','!=',False)]"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>