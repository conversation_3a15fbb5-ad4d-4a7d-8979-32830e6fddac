<?xml version="1.0" encoding='UTF-8' ?>
<odoo noupdate="1">
    <record model="ir.cron" id="contract_line_cron_for_renew">
        <field name="name">Renew Contract lines</field>
        <field name="model_id" ref="model_contract_line" />
        <field name="state">code</field>
        <field name="code">model.cron_renew_contract_line()</field>
        <field name="user_id" ref="base.user_root" />
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field eval="False" name="doall" />
    </record>
</odoo>
