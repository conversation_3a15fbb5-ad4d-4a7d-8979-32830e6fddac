# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * contract
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-21 18:22+0000\n"
"PO-Revision-Date: 2019-10-16 18:31+0000\n"
"Last-Translator: 黎伟杰 <<EMAIL>>\n"
"Language-Team: Chinese (China) (https://www.transifex.com/oca/teams/23907/"
"zh_CN/)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 3.8\n"

#. module: contract
#: model:mail.template,body_html:contract.email_contract_template
msgid ""
"\n"
"<div style=\"font-family: '<PERSON>ida Grande', Ubuntu, Aria<PERSON>, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; \">\n"
"    <p>Hello ${object.partner_id.name or ''},</p>\n"
"    <p>A new contract has been created: </p>\n"
"\n"
"    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
"       &nbsp;&nbsp;<strong>REFERENCES</strong><br />\n"
"       &nbsp;&nbsp;Contract: <strong>${object.name}</strong><br />\n"
"       % if object.date_start:\n"
"       &nbsp;&nbsp;Contract Date Start: ${object.date_start or ''}<br />\n"
"       % endif\n"
"\n"
"       % if object.user_id:\n"
"       % if object.user_id.email:\n"
"       &nbsp;&nbsp;Your Contact: <a href=\"mailto:${object.user_id.email or "
"''}?subject=Contract%20${object.name}\">${object.user_id.name}</a>\n"
"       % else:\n"
"       &nbsp;&nbsp;Your Contact: ${object.user_id.name}\n"
"       % endif\n"
"       % endif\n"
"    </p>\n"
"\n"
"    <br/>\n"
"    <p>If you have any questions, do not hesitate to contact us.</p>\n"
"    <p>Thank you for choosing ${object.company_id.name or 'us'}!</p>\n"
"    <br/>\n"
"    <br/>\n"
"    <div style=\"width: 375px; margin: 0px; padding: 0px; background-color: "
"#8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: 5px 5px; "
"background-repeat: repeat no-repeat;\">\n"
"        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; color: "
"#DDD;\">\n"
"            <strong style=\"text-transform:uppercase;\">${object.company_id."
"name}</strong></h3>\n"
"    </div>\n"
"    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-height: "
"16px; background-color: #F2F2F2;\">\n"
"        <span style=\"color: #222; margin-bottom: 5px; display: block; \">\n"
"            ${object.company_id.partner_id.sudo()."
"with_context(show_address=True, html_format=True).name_get()[0][1] | safe}\n"
"        </span>\n"
"        % if object.company_id.phone:\n"
"            <div style=\"margin-top: 0px; margin-right: 0px; margin-bottom: "
"0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; padding-bottom: "
"0px; padding-left: 0px; \">\n"
"                Phone: ${object.company_id.phone}\n"
"            </div>\n"
"        % endif\n"
"        % if object.company_id.website:\n"
"            <div>\n"
"                Web: <a href=\"${object.company_id.website}\">${object."
"company_id.website}</a>\n"
"            </div>\n"
"        %endif\n"
"        <p></p>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""
"\n"
"<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-"
"serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; \">\n"
"    <p>你好${object.partner_id.name or ''},</p>\n"
"    <p>已经创建了一份新合同： </p>\n"
"\n"
"    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
"       &nbsp;&nbsp;<strong>参考</strong><br />\n"
"       &nbsp;&nbsp;合同： <strong>${object.name}</strong><br />\n"
"       % if object.date_start:\n"
"       &nbsp;&nbsp;合同日期开始： ${object.date_start or ''}<br />\n"
"       % endif\n"
"\n"
"       % if object.user_id:\n"
"       % if object.user_id.email:\n"
"       &nbsp;&nbsp;您的联系人：<a href=\"mailto:${object.user_id.email or "
"''}?subject=Contract%20${object.name}\">${object.user_id.name}</a>\n"
"       % else:\n"
"       &nbsp;&nbsp;您的联系人：${object.user_id.name}\n"
"       % endif\n"
"       % endif\n"
"    </p>\n"
"\n"
"    <br/>\n"
"    <p>如果您有任何疑问，请随时与我们联系。</p>\n"
"    <p>感谢您的选择 ${object.company_id.name or 'us'}!</p>\n"
"    <br/>\n"
"    <br/>\n"
"    <div style=\"width: 375px; margin: 0px; padding: 0px; background-color: "
"#8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: 5px 5px; "
"background-repeat: repeat no-repeat;\">\n"
"        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; color: "
"#DDD;\">\n"
"            <strong style=\"text-transform:uppercase;\">${object.company_id."
"name}</strong></h3>\n"
"    </div>\n"
"    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-height: "
"16px; background-color: #F2F2F2;\">\n"
"        <span style=\"color: #222; margin-bottom: 5px; display: block; \">\n"
"            ${object.company_id.partner_id.sudo()."
"with_context(show_address=True, html_format=True).name_get()[0][1] | safe}\n"
"        </span>\n"
"        % if object.company_id.phone:\n"
"            <div style=\"margin-top: 0px; margin-right: 0px; margin-bottom: "
"0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; padding-bottom: "
"0px; padding-left: 0px; \">\n"
"                电话：${object.company_id.phone}\n"
"            </div>\n"
"        % endif\n"
"        % if object.company_id.website:\n"
"            <div>\n"
"                网址：<a href=\"${object.company_id.website}\">${object."
"company_id.website}</a>\n"
"            </div>\n"
"        %endif\n"
"        <p></p>\n"
"    </div>\n"
"</div>\n"
"        "

#. module: contract
#: model:mail.template,subject:contract.email_contract_template
msgid "${object.company_id.name} Contract (Ref ${object.name or 'n/a'})"
msgstr "${object.company_id.name} 合同(参考 ${object.name or 'n/a'})"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_manually_create_invoice_form_view
msgid ""
"<span attrs=\"{'invisible': [('contract_to_invoice_count', '&gt;', 1)]}\">\n"
"                                contract to invoice\n"
"                            </span>\n"
"                            <span attrs=\"{'invisible': "
"[('contract_to_invoice_count', '&lt;', 1)]}\">\n"
"                                contracts to invoice\n"
"                            </span>"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
#, fuzzy
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong>合计</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid ""
"<strong>#END#</strong>\n"
"                                    : End date\n"
"                                    of\n"
"                                    the\n"
"                                    invoiced period"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "<strong>#END#</strong>: End date of the invoiced period"
msgstr "<strong>#结束#</strong>: 发票日期的结束日期"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid ""
"<strong>#START#</strong>\n"
"                                    : Start\n"
"                                    date\n"
"                                    of the\n"
"                                    invoiced period"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "<strong>#START#</strong>: Start date of the invoiced period"
msgstr "<strong>#开始#</strong>: 发票日期的开始日期"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Contract: </strong>"
msgstr "<strong>合同：</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Date Start</strong>"
msgstr "<strong>开始日期</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Description</strong>"
msgstr "<strong>说明</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
#, fuzzy
msgid "<strong>Notes: </strong>"
msgstr "<strong>合计</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Partner:</strong>"
msgstr "<strong>业务伙伴：</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Price</strong>"
msgstr "<strong>价格</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Quantity</strong>"
msgstr "<strong>数量</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Recurring Items</strong>"
msgstr "<strong>重复项目</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Responsible: </strong>"
msgstr "<strong>负责人：</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Total</strong>"
msgstr "<strong>合计</strong>"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>单价</strong>"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "A canceled contract line can't be set to auto-renew"
msgstr "已取消的合同行无法设置为自动续订"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "A contract line with a successor can't be set to auto-renew"
msgstr "与接替的合同行不能设置为自动更新"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "A contract line with a successor must have a end date"
msgstr "与接替的合同行必须有结束日期"

#. module: contract
#: model:ir.model,name:contract.model_contract_abstract_contract
msgid "Abstract Recurring Contract"
msgstr "抽象定期合同"

#. module: contract
#: model:ir.model,name:contract.model_contract_abstract_contract_line
msgid "Abstract Recurring Contract Line"
msgstr "抽象定期合同行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_needaction
msgid "Action Needed"
msgstr "需要采取行动"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__active
#: model:ir.model.fields,field_description:contract.field_contract_line__active
msgid "Active"
msgstr "有效"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_ids
msgid "Activities"
msgstr "活动"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "Add a line"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "Add a note"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "Add a section"
msgstr ""

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "An auto-renew line must have a end date"
msgstr "自动续订行必须有结束日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__analytic_tag_ids
#, fuzzy
msgid "Analytic Tags"
msgstr "分析账户"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__analytic_account_id
msgid "Analytic account"
msgstr "分析账户"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Archived"
msgstr "已归档"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
msgid "Are you sure you want to cancel this line"
msgstr "您确定要取消此行吗"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#, fuzzy
msgid "Are you sure you want to re-activate this contract?"
msgstr "您确定要取消此行吗"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_terminate_form_view
#, fuzzy
msgid "Are you sure you want to terminate this contract?"
msgstr "您确定要取消此行吗"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Associated Partner"
msgstr "相关业务伙伴"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__is_auto_renew
#: model:ir.model.fields,field_description:contract.field_contract_line__is_auto_renew
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__is_auto_renew
#: model:ir.model.fields,field_description:contract.field_contract_template_line__is_auto_renew
msgid "Auto Renew"
msgstr "自动更新"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__automatic_price
#: model:ir.model.fields,field_description:contract.field_contract_line__automatic_price
#: model:ir.model.fields,field_description:contract.field_contract_template_line__automatic_price
msgid "Auto-price?"
msgstr "自动价格？"

#. module: contract
#: model:ir.model,name:contract.model_contract_recurrency_basic_mixin
msgid "Basic recurrency mixin for abstract contract models"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_terminate_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_plan_successor_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_plan_successor_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_uncancel_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_manually_create_invoice_form_view
msgid "Cancel"
msgstr "取消"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#, fuzzy
msgid "Cancel Contract Termination"
msgstr "合同模板行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__is_cancel_allowed
msgid "Cancel allowed?"
msgstr "取消允许？"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Cancel not allowed for this line"
msgstr "取消此行不允许"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__is_canceled
#: model:ir.model.fields,field_description:contract.field_contract_line__is_canceled
#: model:ir.model.fields,field_description:contract.field_contract_template_line__is_canceled
#: model:ir.model.fields.selection,name:contract.selection__contract_line__state__canceled
msgid "Canceled"
msgstr "已取消"

#. module: contract
#: model_terms:ir.actions.act_window,help:contract.contract_template_action
msgid "Click to create a new contract template."
msgstr "单击以创建新的合同模板。"

#. module: contract
#: model_terms:ir.actions.act_window,help:contract.action_customer_contract
#: model_terms:ir.actions.act_window,help:contract.action_supplier_contract
msgid "Click to create a new contract."
msgstr "单击以创建新合同。"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_line__state__closed
msgid "Closed"
msgstr "已关闭"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__commercial_partner_id
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Commercial Entity"
msgstr "商业实体"

#. module: contract
#: model:ir.model,name:contract.model_res_company
#, fuzzy
msgid "Companies"
msgstr "公司"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__company_id
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__company_id
#: model:ir.model.fields,field_description:contract.field_contract_contract__company_id
#: model:ir.model.fields,field_description:contract.field_contract_line__company_id
#: model:ir.model.fields,field_description:contract.field_contract_tag__company_id
#: model:ir.model.fields,field_description:contract.field_contract_template__company_id
#: model:ir.model.fields,field_description:contract.field_contract_template_line__company_id
msgid "Company"
msgstr "公司"

#. module: contract
#: code:addons/contract/models/contract.py:0
#, python-format
msgid "Compose Email"
msgstr "编写Email"

#. module: contract
#: model:ir.model,name:contract.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: contract
#: model:ir.model,name:contract.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: contract
#: model:ir.actions.report,name:contract.report_contract
#: model:ir.model,name:contract.model_contract_contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__contract_id
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__contract_id
#: model:ir.model.fields,field_description:contract.field_contract_line__contract_id
#: model:ir.model.fields,field_description:contract.field_contract_template_line__contract_id
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.res_config_settings_form_view
#: model:mail.template,report_name:contract.email_contract_template
msgid "Contract"
msgstr "合同"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_terminate_form_view
#, fuzzy
msgid "Contract Contract Terminate"
msgstr "合同模板"

#. module: contract
#: model:ir.model,name:contract.model_contract_line
#: model:ir.model.fields,field_description:contract.field_account_move_line__contract_line_id
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__contract_line_id
msgid "Contract Line"
msgstr "合同行"

#. module: contract
#: model:ir.model,name:contract.model_contract_line_wizard
msgid "Contract Line Wizard"
msgstr "合同行向导"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_line__predecessor_contract_line_id
msgid "Contract Line origin of this one."
msgstr "合同行起源于此。"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_manually_create_invoice_form_view
msgid "Contract Manually Create Invoice"
msgstr ""

#. module: contract
#: model:ir.model,name:contract.model_contract_manually_create_invoice
#, fuzzy
msgid "Contract Manually Create Invoice Wizard"
msgstr "合同行向导"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "Contract Name"
msgstr "合同名称"

#. module: contract
#: model:ir.model,name:contract.model_contract_tag
#: model:ir.ui.menu,name:contract.contract_tag_menu
#, fuzzy
msgid "Contract Tag"
msgstr "合同类型"

#. module: contract
#: model:ir.actions.act_window,name:contract.contract_tag_act_window
#, fuzzy
msgid "Contract Tags"
msgstr "合同类型"

#. module: contract
#: model:ir.model,name:contract.model_contract_template
#: model:ir.model.fields,field_description:contract.field_contract_contract__contract_template_id
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "Contract Template"
msgstr "合同模板"

#. module: contract
#: model:ir.model,name:contract.model_contract_template_line
#: model_terms:ir.ui.view,arch_db:contract.contract_template_line_form_view
msgid "Contract Template Line"
msgstr "合同模板行"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "Contract Template Lines"
msgstr "合同模板行"

#. module: contract
#: model:ir.actions.act_window,name:contract.contract_template_action
#: model:ir.ui.menu,name:contract.contract_template_menu
msgid "Contract Templates"
msgstr "合同模板"

#. module: contract
#: model:ir.actions.act_window,name:contract.contract_terminate_reason_act_window
#: model:ir.model,name:contract.model_contract_terminate_reason
#: model:ir.ui.menu,name:contract.contract_terminate_reason_menu
#, fuzzy
msgid "Contract Termination Reason"
msgstr "合同模板行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__contract_to_invoice_ids
#, fuzzy
msgid "Contract To Invoice"
msgstr "合同类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__contract_to_invoice_count
#, fuzzy
msgid "Contract To Invoice Count"
msgstr "发票数量"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__contract_type
#: model:ir.model.fields,field_description:contract.field_contract_contract__contract_type
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__contract_type
#: model:ir.model.fields,field_description:contract.field_contract_template__contract_type
#: model_terms:ir.ui.view,arch_db:contract.contract_template_search_view
msgid "Contract Type"
msgstr "合同类型"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Contract line '%s' start date can't be later than end date"
msgstr "合同行'%s'的开始日期不能晚于结束日期"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Contract line Un-canceled: %s<br/>- "
msgstr "合同行未取消：%s<br/>- "

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Contract line and its predecessor overlapped"
msgstr "与其原先合同行重叠"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Contract line and its successor overlapped"
msgstr "与接替的合同行重叠"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Contract line canceled: %s<br/>- "
msgstr "合同行取消：%s<br/>- "

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"Contract line for <strong>{product}</strong>\n"
"                            stopped: <br/>\n"
"                            - <strong>End</strong>: {old_end} -- {new_end}\n"
"                            "
msgstr ""
"合同行<strong>{product}</strong>\n"
"                            已停止：<br/>\n"
"                            - <strong>结束</strong>: {old_end} -- {new_end}\n"
"                            "

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"Contract line for <strong>{product}</strong>\n"
"                    planned a successor: <br/>\n"
"                    - <strong>Start</strong>: {new_date_start}\n"
"                    <br/>\n"
"                    - <strong>End</strong>: {new_date_end}\n"
"                    "
msgstr ""
"合同行 <strong>{product}</strong>\n"
"                    计划继任者：<br/>\n"
"                    - <strong>开始</strong>: {new_date_start}\n"
"                    <br/>\n"
"                    - <strong>结束</strong>: {new_date_end}\n"
"                    "

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"Contract line for <strong>{product}</strong>\n"
"                renewed: <br/>\n"
"                - <strong>Start</strong>: {new_date_start}\n"
"                <br/>\n"
"                - <strong>End</strong>: {new_date_end}\n"
"                "
msgstr ""
"计划继任者： <strong>{product}</strong>\n"
"                更新： <br/>\n"
"                - <strong>开始</strong>: {new_date_start}\n"
"                <br/>\n"
"                - <strong>结束</strong>: {new_date_end}\n"
"                "

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"Contract line for <strong>{product}</strong>\n"
"                suspended: <br/>\n"
"                - <strong>Suspension Start</strong>: {new_date_start}\n"
"                <br/>\n"
"                - <strong>Suspension End</strong>: {new_date_end}\n"
"                "
msgstr ""
"合同行<strong>{product}</strong>\n"
"                暂停：<br/>\n"
"                - <strong>暂停开始</strong>: {new_date_start}\n"
"                <br/>\n"
"                - <strong>暂停结束</strong>: {new_date_end}\n"
"                "

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Contract line must be canceled before delete"
msgstr "必须在删除前取消合同行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__contract_line_ids
msgid "Contract lines"
msgstr "合同行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__contract_line_fixed_ids
msgid "Contract lines (fixed)"
msgstr ""

#. module: contract
#: code:addons/contract/models/contract.py:0
#, python-format
msgid ""
"Contract manually invoiced: <a href=\"#\" data-oe-model=\"%s\" data-oe-id="
"\"%s\">Invoice</a>"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_template__contract_line_ids
msgid "Contract template lines"
msgstr "合同模板行"

#. module: contract
#: model:res.groups,name:contract.can_terminate_contract
#, fuzzy
msgid "Contract: Can Terminate Contracts"
msgstr "合同模板行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_res_partner__contract_ids
#: model:ir.model.fields,field_description:contract.field_res_users__contract_ids
#: model:ir.ui.menu,name:contract.menu_config_contract
msgid "Contracts"
msgstr "合同"

#. module: contract
#: code:addons/contract/wizards/contract_manually_create_invoice.py:0
#, fuzzy, python-format
msgid "Contracts to invoice"
msgstr "合同行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__create_invoice_visibility
#: model:ir.model.fields,field_description:contract.field_contract_line__create_invoice_visibility
msgid "Create Invoice Visibility"
msgstr "创建可见性发票"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_manually_create_invoice_form_view
#, fuzzy
msgid "Create Invoices"
msgstr "创建发票"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_res_company__create_new_line_at_contract_line_renew
#: model:ir.model.fields,field_description:contract.field_res_config_settings__create_new_line_at_contract_line_renew
#, fuzzy
msgid "Create New Line At Contract Line Renew"
msgstr "抽象定期合同行"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "Create invoices"
msgstr "创建发票"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_line__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_tag__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_template__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_template_line__create_uid
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__create_uid
msgid "Created by"
msgstr "创建者"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__create_date
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__create_date
#: model:ir.model.fields,field_description:contract.field_contract_line__create_date
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__create_date
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__create_date
#: model:ir.model.fields,field_description:contract.field_contract_tag__create_date
#: model:ir.model.fields,field_description:contract.field_contract_template__create_date
#: model:ir.model.fields,field_description:contract.field_contract_template_line__create_date
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__create_date
msgid "Created on"
msgstr "创建时间"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__currency_id
msgid "Currency"
msgstr "货币"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__note_invoicing_mode__custom
#: model:ir.model.fields.selection,name:contract.selection__contract_line__note_invoicing_mode__custom
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__note_invoicing_mode__custom
#, fuzzy
msgid "Custom"
msgstr "客户"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__contract_type__sale
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__contract_type__sale
#: model:ir.model.fields.selection,name:contract.selection__contract_manually_create_invoice__contract_type__sale
#: model:ir.model.fields.selection,name:contract.selection__contract_template__contract_type__sale
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_customer_form_view
msgid "Customer"
msgstr "客户"

#. module: contract
#: model:ir.actions.act_window,name:contract.action_customer_contract
#: model:ir.ui.menu,name:contract.menu_contract_contract_customer
msgid "Customer Contracts"
msgstr "客户合同"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__date_end
#: model:ir.model.fields,field_description:contract.field_contract_line__date_end
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__date_end
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__date_end
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Date End"
msgstr "结束于"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__date_start
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__date_start
#: model:ir.model.fields,field_description:contract.field_contract_contract__date_start
#: model:ir.model.fields,field_description:contract.field_contract_line__date_start
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__date_start
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__date_start
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__date_start
#: model:ir.model.fields,field_description:contract.field_contract_template__date_start
#: model:ir.model.fields,field_description:contract.field_contract_template_line__date_start
msgid "Date Start"
msgstr "开始日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_contract__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_line__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_template__recurring_next_date
#: model:ir.model.fields,field_description:contract.field_contract_template_line__recurring_next_date
msgid "Date of Next Invoice"
msgstr "下一个发票的日期"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__auto_renew_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__termination_notice_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_line__auto_renew_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_line__termination_notice_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__auto_renew_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__daily
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__termination_notice_rule_type__daily
msgid "Day(s)"
msgstr "天"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__note_invoicing_mode
#: model:ir.model.fields,help:contract.field_contract_line__note_invoicing_mode
#: model:ir.model.fields,help:contract.field_contract_template_line__note_invoicing_mode
msgid ""
"Defines when the Note is invoiced:\n"
"- With previous line: If the previous line can be invoiced.\n"
"- With next line: If the next line can be invoiced.\n"
"- Custom: Depending on the recurrence to be define."
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__name
#: model:ir.model.fields,field_description:contract.field_contract_line__name
#: model:ir.model.fields,field_description:contract.field_contract_template_line__name
#: model_terms:ir.ui.view,arch_db:contract.contract_abstract_contract_line_form_view
msgid "Description"
msgstr "说明"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__discount
#: model:ir.model.fields,field_description:contract.field_contract_line__discount
#: model:ir.model.fields,field_description:contract.field_contract_template_line__discount
msgid "Discount (%)"
msgstr "折扣(%)"

#. module: contract
#: code:addons/contract/models/abstract_contract_line.py:0
#, python-format
msgid "Discount should be less or equal to 100"
msgstr "折扣应小于或等于100"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__discount
#: model:ir.model.fields,help:contract.field_contract_line__discount
#: model:ir.model.fields,help:contract.field_contract_template_line__discount
msgid ""
"Discount that is applied in generated invoices. It should be less or equal "
"to 100"
msgstr "在生成的发票中应用的折扣。它应该小于或等于100"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__display_name
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__display_name
#: model:ir.model.fields,field_description:contract.field_contract_contract__display_name
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__display_name
#: model:ir.model.fields,field_description:contract.field_contract_line__display_name
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__display_name
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__display_name
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__display_name
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__display_name
#: model:ir.model.fields,field_description:contract.field_contract_tag__display_name
#: model:ir.model.fields,field_description:contract.field_contract_template__display_name
#: model:ir.model.fields,field_description:contract.field_contract_template_line__display_name
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__display_type
#: model:ir.model.fields,field_description:contract.field_contract_line__display_type
#: model:ir.model.fields,field_description:contract.field_contract_template_line__display_type
#, fuzzy
msgid "Display Type"
msgstr "显示名称"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Finished"
msgstr "已完成"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__fiscal_position_id
msgid "Fiscal Position"
msgstr "税科目调整"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_channel_ids
msgid "Followers (Channels)"
msgstr "税科目调整"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: contract
#: model:ir.actions.server,name:contract.contract_cron_for_invoice_ir_actions_server
#: model:ir.cron,cron_name:contract.contract_cron_for_invoice
#: model:ir.cron,name:contract.contract_cron_for_invoice
msgid "Generate Recurring Invoices from Contracts"
msgstr "从合同生成定期发票"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__group_id
msgid "Group"
msgstr "组"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Group By..."
msgstr "分组..."

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__id
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__id
#: model:ir.model.fields,field_description:contract.field_contract_contract__id
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__id
#: model:ir.model.fields,field_description:contract.field_contract_line__id
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__id
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__id
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__id
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__id
#: model:ir.model.fields,field_description:contract.field_contract_tag__id
#: model:ir.model.fields,field_description:contract.field_contract_template__id
#: model:ir.model.fields,field_description:contract.field_contract_template_line__id
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__id
msgid "ID"
msgstr "ID"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: contract
#: model:ir.model.fields,help:contract.field_res_company__create_new_line_at_contract_line_renew
#: model:ir.model.fields,help:contract.field_res_config_settings__create_new_line_at_contract_line_renew
msgid ""
"If checked, a new line will be generated at contract line renew and linked "
"to the original one as successor. The default behavior is to extend the end "
"date of the contract by a new subscription period"
msgstr ""

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__message_needaction
#: model:ir.model.fields,help:contract.field_contract_contract__message_unread
msgid "If checked, new messages require your attention."
msgstr "如果选中，则需要您注意新消息。"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__message_has_error
#: model:ir.model.fields,help:contract.field_contract_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果选中，则某些邮件会发送传递错误。"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__automatic_price
#: model:ir.model.fields,help:contract.field_contract_line__automatic_price
#: model:ir.model.fields,help:contract.field_contract_template_line__automatic_price
msgid ""
"If this is marked, the price will be obtained automatically applying the "
"pricelist to the product. If not, you will be able to introduce a manual "
"price"
msgstr "如果已标记，将自动将价格表应用于产品。如果没有，您将能够引入手动价格"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_line__successor_contract_line_id
msgid ""
"In case of restart after suspension, this field contain the new contract "
"line created."
msgstr "如果在暂停后重新开始，则此字段包含创建的新合同行。"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "In progress"
msgstr "进行中"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_line__state__in-progress
msgid "In-progress"
msgstr "进行中"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__invoice_count
msgid "Invoice Count"
msgstr "发票数量"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__invoice_date
#, fuzzy
msgid "Invoice Date"
msgstr "下一个发票日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_contract__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_line__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_template__recurring_interval
#: model:ir.model.fields,field_description:contract.field_contract_template_line__recurring_interval
msgid "Invoice Every"
msgstr "发票间隔"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_contract__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_line__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_recurrency_basic_mixin__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_recurrency_mixin__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_template__recurring_interval
#: model:ir.model.fields,help:contract.field_contract_template_line__recurring_interval
msgid "Invoice every (Days/Week/Month/Year)"
msgstr "发票间隔 (天/周/月/年)"

#. module: contract
#: code:addons/contract/wizards/contract_manually_create_invoice.py:0
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#, python-format
msgid "Invoices"
msgstr "发票"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__invoice_partner_id
msgid "Invoicing contact"
msgstr "发票联系人"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_contract__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_line__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_template__recurring_invoicing_offset
#: model:ir.model.fields,field_description:contract.field_contract_template_line__recurring_invoicing_offset
#, fuzzy
msgid "Invoicing offset"
msgstr "发票类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_contract__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_line__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_template__recurring_invoicing_type
#: model:ir.model.fields,field_description:contract.field_contract_template_line__recurring_invoicing_type
msgid "Invoicing type"
msgstr "发票类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__is_recurring_note
#: model:ir.model.fields,field_description:contract.field_contract_line__is_recurring_note
#: model:ir.model.fields,field_description:contract.field_contract_template_line__is_recurring_note
msgid "Is Recurring Note"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_form_view
msgid "Is suspension without end date"
msgstr "暂停没有结束日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__journal_id
#: model:ir.model.fields,field_description:contract.field_contract_contract__journal_id
#: model:ir.model.fields,field_description:contract.field_contract_template__journal_id
#: model_terms:ir.ui.view,arch_db:contract.contract_template_search_view
msgid "Journal"
msgstr "日记账"

#. module: contract
#: model:ir.model,name:contract.model_account_move
msgid "Journal Entries"
msgstr ""

#. module: contract
#: model:ir.model,name:contract.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__last_date_invoiced
#: model:ir.model.fields,field_description:contract.field_contract_contract__last_date_invoiced
#: model:ir.model.fields,field_description:contract.field_contract_line__last_date_invoiced
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__last_date_invoiced
#: model:ir.model.fields,field_description:contract.field_contract_template_line__last_date_invoiced
msgid "Last Date Invoiced"
msgstr "已开票的最后日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract____last_update
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line____last_update
#: model:ir.model.fields,field_description:contract.field_contract_contract____last_update
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate____last_update
#: model:ir.model.fields,field_description:contract.field_contract_line____last_update
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard____last_update
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice____last_update
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin____last_update
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin____last_update
#: model:ir.model.fields,field_description:contract.field_contract_tag____last_update
#: model:ir.model.fields,field_description:contract.field_contract_template____last_update
#: model:ir.model.fields,field_description:contract.field_contract_template_line____last_update
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_line__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_tag__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_template__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_template_line__write_uid
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__write_date
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__write_date
#: model:ir.model.fields,field_description:contract.field_contract_line__write_date
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__write_date
#: model:ir.model.fields,field_description:contract.field_contract_manually_create_invoice__write_date
#: model:ir.model.fields,field_description:contract.field_contract_tag__write_date
#: model:ir.model.fields,field_description:contract.field_contract_template__write_date
#: model:ir.model.fields,field_description:contract.field_contract_template_line__write_date
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_template_form_view
msgid "Legend (for the markers inside invoice lines description)"
msgstr "图例（用于发票行内的标记）"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "附件"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__manual_currency_id
msgid "Manual Currency"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__manual_renew_needed
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__manual_renew_needed
msgid "Manual renew needed"
msgstr "需要手动更新"

#. module: contract
#: model:ir.actions.act_window,name:contract.purchase_contract_manually_create_invoice_act_window
#: model:ir.ui.menu,name:contract.purchase_contract_manually_create_invoice_menu
#, fuzzy
msgid "Manually Invoice Purchase Contracts"
msgstr "采购合同"

#. module: contract
#: model:ir.actions.act_window,name:contract.sale_contract_manually_create_invoice_act_window
#: model:ir.ui.menu,name:contract.sale_contract_manually_create_invoice_menu
#, fuzzy
msgid "Manually Invoice Sale Contracts"
msgstr "销售合同"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract__line_recurrence
#: model:ir.model.fields,help:contract.field_contract_contract__line_recurrence
#: model:ir.model.fields,help:contract.field_contract_template__line_recurrence
msgid ""
"Mark this check if you want to control recurrrence at line level instead of "
"all together for the whole contract."
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_ids
msgid "Messages"
msgstr "消息"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__auto_renew_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__termination_notice_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__auto_renew_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__termination_notice_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__auto_renew_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__monthly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__termination_notice_rule_type__monthly
msgid "Month(s)"
msgstr "月"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__monthlylastday
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__monthlylastday
msgid "Month(s) last day"
msgstr "月底"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__name
#: model:ir.model.fields,field_description:contract.field_contract_contract__name
#: model:ir.model.fields,field_description:contract.field_contract_tag__name
#: model:ir.model.fields,field_description:contract.field_contract_template__name
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__name
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_tree_view
msgid "Name"
msgstr "名称"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一个活动截止日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_summary
msgid "Next Activity Summary"
msgstr "下一个活动摘要"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_type_id
msgid "Next Activity Type"
msgstr "下一个活动类型"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_search_view
msgid "Next Invoice"
msgstr "下一张发票"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line_wizard__recurring_next_date
msgid "Next Invoice Date"
msgstr "下一个发票日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__next_period_date_end
#: model:ir.model.fields,field_description:contract.field_contract_line__next_period_date_end
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__next_period_date_end
msgid "Next Period End"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__next_period_date_start
#: model:ir.model.fields,field_description:contract.field_contract_line__next_period_date_start
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__next_period_date_start
msgid "Next Period Start"
msgstr ""

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__display_type__line_note
#: model:ir.model.fields.selection,name:contract.selection__contract_line__display_type__line_note
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:contract.contract_abstract_contract_line_form_view
msgid "Note"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__note_invoicing_mode
#: model:ir.model.fields,field_description:contract.field_contract_line__note_invoicing_mode
#: model:ir.model.fields,field_description:contract.field_contract_template_line__note_invoicing_mode
#, fuzzy
msgid "Note Invoicing Mode"
msgstr "发票类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__note
msgid "Notes"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "行动数量"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_contract__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_line__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_recurrency_basic_mixin__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_recurrency_mixin__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_template__recurring_invoicing_offset
#: model:ir.model.fields,help:contract.field_contract_template_line__recurring_invoicing_offset
msgid ""
"Number of days to offset the invoice from the period end date (in post-paid "
"mode) or start date (in pre-paid mode)."
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要操作的消息数"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息的数量"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_account_move__old_contract_id
msgid "Old Contract"
msgstr "旧合同"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "Other Information"
msgstr "其他信息"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__partner_id
#: model:ir.model.fields,field_description:contract.field_contract_contract__partner_id
#: model:ir.model.fields,field_description:contract.field_contract_template__partner_id
msgid "Partner"
msgstr "合作伙伴"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__payment_term_id
msgid "Payment Terms"
msgstr "付款条款"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
msgid "Plan Start"
msgstr "计划开始"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__is_plan_successor_allowed
msgid "Plan successor allowed?"
msgstr "计划接替允许吗？"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Plan successor not allowed for this line"
msgstr "计划接替不允许此行"

#. module: contract
#: code:addons/contract/models/contract.py:0
#, python-format
msgid "Please define a %s journal for the company '%s'."
msgstr "请为定义一个%s日记账，公司 '%s'。"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_invoicing_type__post-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_invoicing_type__post-paid
msgid "Post-paid"
msgstr "后付"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_invoicing_type__pre-paid
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_invoicing_type__pre-paid
msgid "Pre-paid"
msgstr "预付"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__predecessor_contract_line_id
msgid "Predecessor Contract Line"
msgstr "原先合同行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__pricelist_id
#: model:ir.model.fields,field_description:contract.field_contract_contract__pricelist_id
#: model:ir.model.fields,field_description:contract.field_contract_template__pricelist_id
#: model_terms:ir.ui.view,arch_db:contract.contract_template_search_view
msgid "Pricelist"
msgstr "价格表"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__product_id
#: model:ir.model.fields,field_description:contract.field_contract_line__product_id
#: model:ir.model.fields,field_description:contract.field_contract_template_line__product_id
msgid "Product"
msgstr "产品"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_res_partner__purchase_contract_count
#: model:ir.model.fields,field_description:contract.field_res_users__purchase_contract_count
#: model_terms:ir.ui.view,arch_db:contract.view_partner_form
msgid "Purchase Contracts"
msgstr "采购合同"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__quantity
#: model:ir.model.fields,field_description:contract.field_contract_line__quantity
#: model:ir.model.fields,field_description:contract.field_contract_template_line__quantity
msgid "Quantity"
msgstr "数量"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__quarterly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__quarterly
msgid "Quarter(s)"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_contract__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_line__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_basic_mixin__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_recurrency_mixin__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_template__recurring_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_template_line__recurring_rule_type
msgid "Recurrence"
msgstr "重新发起"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract__line_recurrence
#: model:ir.model.fields,field_description:contract.field_contract_contract__line_recurrence
#: model:ir.model.fields,field_description:contract.field_contract_template__line_recurrence
msgid "Recurrence at line level?"
msgstr ""

#. module: contract
#: model:ir.model,name:contract.model_contract_recurrency_mixin
msgid "Recurrency mixin for contract models"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "Recurring Invoices"
msgstr "重新发起发票"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__code
msgid "Reference"
msgstr "参考"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
msgid "Renew"
msgstr "更新"

#. module: contract
#: model:ir.actions.server,name:contract.contract_line_cron_for_renew_ir_actions_server
#: model:ir.cron,cron_name:contract.contract_line_cron_for_renew
#: model:ir.cron,name:contract.contract_line_cron_for_renew
msgid "Renew Contract lines"
msgstr "更新合同行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__auto_renew_interval
#: model:ir.model.fields,field_description:contract.field_contract_line__auto_renew_interval
#: model:ir.model.fields,field_description:contract.field_contract_template_line__auto_renew_interval
msgid "Renew Every"
msgstr "更新间隔"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__auto_renew_interval
#: model:ir.model.fields,help:contract.field_contract_line__auto_renew_interval
#: model:ir.model.fields,help:contract.field_contract_template_line__auto_renew_interval
msgid "Renew every (Days/Week/Month/Year)"
msgstr "更新间隔 (天/周/月/年)"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__auto_renew_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_line__auto_renew_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_template_line__auto_renew_rule_type
msgid "Renewal type"
msgstr "更新类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__terminate_comment_required
#: model:ir.model.fields,field_description:contract.field_contract_terminate_reason__terminate_comment_required
msgid "Require a termination comment"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__user_id
msgid "Responsible"
msgstr "负责人"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_res_partner__sale_contract_count
#: model:ir.model.fields,field_description:contract.field_res_users__sale_contract_count
#: model_terms:ir.ui.view,arch_db:contract.view_partner_form
msgid "Sale Contracts"
msgstr "销售合同"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__display_type__line_section
#: model:ir.model.fields.selection,name:contract.selection__contract_line__display_type__line_section
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:contract.contract_abstract_contract_line_form_view
#, fuzzy
msgid "Section"
msgstr "说明"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__semesterly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__semesterly
msgid "Semester(s)"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "Send by Email"
msgstr "通过Email发送"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__sequence
#: model:ir.model.fields,field_description:contract.field_contract_line__sequence
#: model:ir.model.fields,field_description:contract.field_contract_template_line__sequence
msgid "Sequence"
msgstr "序列"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__sequence
#: model:ir.model.fields,help:contract.field_contract_line__sequence
#: model:ir.model.fields,help:contract.field_contract_template_line__sequence
msgid "Sequence of the contract line when displaying contracts"
msgstr "显示合同时合同行的序列"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.view_partner_form
msgid "Show the purchase contracts for this partner"
msgstr "显示此合作伙伴的采购合同"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.view_partner_form
msgid "Show the sale contracts for this partner"
msgstr "显示此合作伙伴的销售合同"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__specific_price
#: model:ir.model.fields,field_description:contract.field_contract_line__specific_price
#: model:ir.model.fields,field_description:contract.field_contract_template_line__specific_price
msgid "Specific Price"
msgstr "实际价格"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_contract__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_line__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_recurrency_basic_mixin__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_recurrency_mixin__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_template__recurring_rule_type
#: model:ir.model.fields,help:contract.field_contract_template_line__recurring_rule_type
msgid "Specify Interval for automatic invoice generation."
msgstr "指定自动发票生成的间隔。"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__auto_renew_rule_type
#: model:ir.model.fields,help:contract.field_contract_line__auto_renew_rule_type
#: model:ir.model.fields,help:contract.field_contract_template_line__auto_renew_rule_type
msgid "Specify Interval for automatic renewal."
msgstr "指定自动续订的间隔。"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_contract__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_line__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_recurrency_basic_mixin__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_recurrency_mixin__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_template__recurring_invoicing_type
#: model:ir.model.fields,help:contract.field_contract_template_line__recurring_invoicing_type
msgid ""
"Specify if the invoice must be generated at the beginning (pre-paid) or end "
"(post-paid) of the period."
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__state
msgid "State"
msgstr "状态"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：已经超过截止日期\n"
"今天：活动日期是今天\n"
"计划：未来的活动。"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
msgid "Stop"
msgstr "停止"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_form_view
msgid "Stop Date"
msgstr "停止日期"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
msgid "Stop Plan Successor"
msgstr "停止计划继任者"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__is_stop_allowed
msgid "Stop allowed?"
msgstr "停止允许？"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Stop not allowed for this line"
msgstr "停止不允许此行"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__is_stop_plan_successor_allowed
msgid "Stop/Plan successor allowed?"
msgstr "允许停止/计划接替？"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Stop/Plan successor not allowed for this line"
msgstr "此线路不允许停止/计划接替"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__price_subtotal
#: model:ir.model.fields,field_description:contract.field_contract_line__price_subtotal
#: model:ir.model.fields,field_description:contract.field_contract_template_line__price_subtotal
msgid "Sub Total"
msgstr "小计"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__successor_contract_line_id
msgid "Successor Contract Line"
msgstr "接替合同行"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__contract_type__purchase
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__contract_type__purchase
#: model:ir.model.fields.selection,name:contract.selection__contract_manually_create_invoice__contract_type__purchase
#: model:ir.model.fields.selection,name:contract.selection__contract_template__contract_type__purchase
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_supplier_form_view
msgid "Supplier"
msgstr "供应商"

#. module: contract
#: model:ir.actions.act_window,name:contract.action_supplier_contract
#: model:ir.ui.menu,name:contract.menu_contract_contract_supplier
msgid "Supplier Contracts"
msgstr "供应商合同"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_plan_successor_form_view
msgid "Suspension End Date"
msgstr "暂停结束日期"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_plan_successor_form_view
msgid "Suspension Start Date"
msgstr "暂停开始日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__tag_ids
msgid "Tags"
msgstr ""

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_abstract_contract_line__display_type
#: model:ir.model.fields,help:contract.field_contract_line__display_type
#: model:ir.model.fields,help:contract.field_contract_template_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: contract
#: code:addons/contract/models/contract.py:0
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_terminate_form_view
#, fuzzy, python-format
msgid "Terminate Contract"
msgstr "销售合同"

#. module: contract
#: model:ir.model,name:contract.model_contract_contract_terminate
#, fuzzy
msgid "Terminate Contract Wizard"
msgstr "合同行向导"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__is_terminated
#, fuzzy
msgid "Terminated"
msgstr "终止通知日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__terminate_comment
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__terminate_comment
#, fuzzy
msgid "Termination Comment"
msgstr "终止通知类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__terminate_date
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__terminate_date
#, fuzzy
msgid "Termination Date"
msgstr "终止通知日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__termination_notice_interval
#: model:ir.model.fields,field_description:contract.field_contract_line__termination_notice_interval
#: model:ir.model.fields,field_description:contract.field_contract_template_line__termination_notice_interval
msgid "Termination Notice Before"
msgstr "终止通知之前"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__termination_notice_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_line__termination_notice_rule_type
#: model:ir.model.fields,field_description:contract.field_contract_template_line__termination_notice_rule_type
msgid "Termination Notice type"
msgstr "终止通知类型"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__terminate_reason_id
#: model:ir.model.fields,field_description:contract.field_contract_contract_terminate__terminate_reason_id
#, fuzzy
msgid "Termination Reason"
msgstr "终止通知日期"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__termination_notice_date
msgid "Termination notice date"
msgstr "终止通知日期"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "This contract was terminated for the reason"
msgstr ""

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_line__manual_renew_needed
#: model:ir.model.fields,help:contract.field_contract_line_wizard__manual_renew_needed
msgid ""
"This flag is used to make a difference between a definitive stopand "
"temporary one for which a user is not able to plan asuccessor in advance"
msgstr "此标志用于区分用户无法提前计划访问器的最终停止和临时停止"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_line__state__to-renew
msgid "To renew"
msgstr "更新"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_line__is_un_cancel_allowed
msgid "Un-Cancel allowed?"
msgstr "不要取消允许？"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_tree_view
msgid "Un-cancel"
msgstr "不要取消"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "Un-cancel not allowed for this line"
msgstr "此行不允许取消"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__price_unit
#: model:ir.model.fields,field_description:contract.field_contract_line__price_unit
#: model:ir.model.fields,field_description:contract.field_contract_template_line__price_unit
msgid "Unit Price"
msgstr "单价"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_abstract_contract_line__uom_id
#: model:ir.model.fields,field_description:contract.field_contract_line__uom_id
#: model:ir.model.fields,field_description:contract.field_contract_template_line__uom_id
msgid "Unit of Measure"
msgstr "计量单位"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息计数器"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_line__state__upcoming
msgid "Upcoming"
msgstr "即将到来"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_line__state__upcoming-close
msgid "Upcoming Close"
msgstr "即将到来"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "Update Termination Details"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.report_contract_document
msgid "VAT:"
msgstr "增值税："

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_plan_successor_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_stop_plan_successor_form_view
#: model_terms:ir.ui.view,arch_db:contract.contract_line_wizard_uncancel_form_view
msgid "Validate"
msgstr "验证"

#. module: contract
#: model:ir.model.fields,field_description:contract.field_contract_contract__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: contract
#: model:ir.model.fields,help:contract.field_contract_contract__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__auto_renew_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__termination_notice_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__auto_renew_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__termination_notice_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__auto_renew_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__weekly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__termination_notice_rule_type__weekly
msgid "Week(s)"
msgstr "周"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__note_invoicing_mode__with_next_line
#: model:ir.model.fields.selection,name:contract.selection__contract_line__note_invoicing_mode__with_next_line
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__note_invoicing_mode__with_next_line
msgid "With next line"
msgstr ""

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__note_invoicing_mode__with_previous_line
#: model:ir.model.fields.selection,name:contract.selection__contract_line__note_invoicing_mode__with_previous_line
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__note_invoicing_mode__with_previous_line
msgid "With previous line"
msgstr ""

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.view_res_partner_filter
msgid "With running contracts"
msgstr "有运行的合同"

#. module: contract
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__auto_renew_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_abstract_contract_line__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_contract__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__auto_renew_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_line__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_basic_mixin__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_recurrency_mixin__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_template__recurring_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__auto_renew_rule_type__yearly
#: model:ir.model.fields.selection,name:contract.selection__contract_template_line__recurring_rule_type__yearly
msgid "Year(s)"
msgstr "年"

#. module: contract
#: code:addons/contract/models/contract.py:0
#, python-format
msgid "You are not allowed to terminate contracts."
msgstr ""

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "You can't delay a contract line invoiced at least one time."
msgstr "您不能延迟至少一次开具发票的合同行。"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"You can't have a date of next invoice anterior to the start of the contract "
"line '%s'"
msgstr "在合同行'%s'开头之前，您不能拥有下一张发票的日期"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"You can't have the end date before the date of last invoice for the contract "
"line '%s'"
msgstr "您不能在合同行'%s'的最后一张发票日期之前有结束日期"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, fuzzy, python-format
msgid ""
"You can't have the next invoice date before the date of last invoice for the "
"contract line '%s'"
msgstr "您不能在合同行'%s'的最后一张发票日期之前有结束日期"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid ""
"You can't have the start date after the date of last invoice for the "
"contract line '%s'"
msgstr "在合同行'%s'的最后一张发票日期之后，您不能拥有开始日期"

#. module: contract
#: code:addons/contract/models/contract_line.py:0
#, python-format
msgid "You must supply a date of next invoice for contract line '%s'"
msgstr "您必须为合同行'%s'提供下一张发票的日期"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "e.g. Contract XYZ"
msgstr "例如 合同XYZ"

#. module: contract
#: model_terms:ir.ui.view,arch_db:contract.contract_contract_form_view
msgid "on"
msgstr ""

#~ msgid ""
#~ "<strong>#END#</strong>: End date\n"
#~ "                                    of\n"
#~ "                                    the\n"
#~ "                                    invoiced period"
#~ msgstr "<strong>#结束#</strong>: 发票期结束日期"

#~ msgid ""
#~ "<strong>#START#</strong>: Start\n"
#~ "                                    date\n"
#~ "                                    of the\n"
#~ "                                    invoiced period"
#~ msgstr "<strong>#开始#</strong>: 发票期开始日期"

#~ msgid "If checked new messages require your attention."
#~ msgstr "如果选中，则需要您注意新消息。"

#~ msgid "Invoice"
#~ msgstr "发票"

#~ msgid "Invoice Line"
#~ msgstr "发票行"

#~ msgid "Number of error"
#~ msgstr "错误数量"

#~ msgid "Overdue"
#~ msgstr "逾期"

#~ msgid "Planned"
#~ msgstr "计划"

#~ msgid "Today"
#~ msgstr "今天"

#~ msgid "Specify if process date is 'from' or 'to' invoicing date"
#~ msgstr "指定处理日期是“从”还是“到”发票日期"

#~ msgid "Account Analytic Contract"
#~ msgstr "账户分析合同"

#~ msgid "Account Analytic Lines"
#~ msgstr "账户分析行"

#~ msgid "Date From"
#~ msgstr "开始于"

#~ msgid "Date To"
#~ msgstr "日期到"

#~ msgid "Date from invoiced period"
#~ msgstr "发票开具日期"

#~ msgid "Date to invoiced period"
#~ msgstr "发票期限的日期"

#~ msgid "Generate recurring invoices automatically"
#~ msgstr "自动生成定期发票"

#~ msgid "Partner and dependents"
#~ msgstr "合作伙伴和合作伙伴关联的"

#~ msgid "Vendor Bills"
#~ msgstr "供应商账单"

#~ msgid "You must first select a Customer for Contract %s!"
#~ msgstr "您必须先选择合同%s的客户！"

#~ msgid "You must first select a Supplier for Contract %s!"
#~ msgstr "您必须先选择合同%s的供应商！"

#~ msgid ""
#~ "You must review start and end dates!\n"
#~ "%s"
#~ msgstr ""
#~ "您必须查看开始和结束日期！\n"
#~ "%s"

#~ msgid "You must supply a customer for the contract '%s'"
#~ msgstr "您必须为合同'%s'提供客户"

#~ msgid "You must supply a start date for contract '%s'"
#~ msgstr "必须提供合同'%s'的开始日期"

#~ msgid "account.analytic.invoice.line"
#~ msgstr "account.analytic.invoice.line"

#~ msgid "⇒ Show recurring invoices"
#~ msgstr "⇒ 显示定期发票"
