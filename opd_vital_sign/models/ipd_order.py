# Part of Softprime Consulting Pvt Ltd.
from odoo import api, fields, models
from datetime import datetime, date
from odoo.exceptions import UserError


class NuroAppointment(models.Model):
    _inherit = "nuro.appointment"

    opd_vital_sign_ids = fields.One2many("vital.sign.data", "appointment_id", string="Vital Sign Line")

    def write(self, vals):
        """Override write method to check patient's age in days when specific fields change"""
        result = super(NuroAppointment, self).write(vals)
        
        # Skip validation for certain states or when specific fields are updated
        excluded_fields = {'prescription_line', 'imaging_appointment_line', 'lab_appointment_line', 'op_entry_request_line', 'surgery_appointment_line', 'health_checkup_appointment_line', 'dental_service_line'}

        # Process with restriction check if any excluded field is updated
        if any(field in vals for field in excluded_fields):
            # Get age UOM references once
            age_uoms = {
                'weeks': self.env.ref('nuro_patients.age_group_uom_weeks').id,
                'months': self.env.ref('nuro_patients.age_group_uom_months').id,
                'years': self.env.ref('nuro_patients.age_group_uom_years').id
            }
            
            # Get vital sign lines with age requirements once
            vital_sign_lines_required = self.env['vital.sign.line'].search([
                ('vital_sign_required', '=', True), 
                ('vital_sign_id', '!=', False), 
                ('ag_uom_id', '!=', False),
                '|', ('min_age', '>', 0), ('max_age', '>', 0)
            ])
            
            if not vital_sign_lines_required:
                return result
                
            today = date.today()
            
            for rec in self.filtered(lambda r: r.patient_id and r.patient_id.dob and not r.opd_vital_sign_ids):
                dob = fields.Date.from_string(rec.patient_id.dob)
                age_in_days = (today - dob).days
                
                # Check all vital sign lines at once
                for vsl in vital_sign_lines_required:
                    patient_age = age_in_days
                    
                    # Convert days to appropriate unit
                    ag_uom_id = vsl.ag_uom_id.id
                    if ag_uom_id == age_uoms['weeks']:
                        patient_age /= 7
                    elif ag_uom_id == age_uoms['months']:
                        patient_age /= 30.44
                    elif ag_uom_id == age_uoms['years']:
                        patient_age /= 365.25
                    
                    # If patient's age matches any required vital sign range, show error
                    if vsl.min_age <= patient_age <= vsl.max_age:
                        raise UserError(f"Vital Sign is required for {rec.patient_id.name} as per the age group for {vsl.vital_sign_id.name}")
                        
        return result

    def print_vital_sign_report(self):
        """Method to print vital sign report"""
        return self.env.ref("opd_vital_sign.action_vital_sign_report_opd").report_action(self)

    def get_opd_ventilator_diagnosis(self):
        """Get Diagnosis"""
        name = ""
        if self.disease_ids:
            name = ', '.join(self.disease_ids.mapped('name'))
        return name

    def get_vital_sign_record(self):
        """Method to get vital sign record"""
        vital_sign_obj = self.env["vital.sign"].search([], order="name ASC")
        return vital_sign_obj

    def get_record(self):
        """Method to get record"""
        master = self.get_vital_sign_record()
        query = """
                SELECT 
                    date_trunc('second', d.create_date) AS truncated_time,
                    jsonb_agg(jsonb_build_object('Datetime', date_trunc('second', d.create_date), 'id', m.id, 'name', m.name, 'value', d.value)) AS name_value_pairs
                FROM vital_sign_data d 
                LEFT JOIN vital_sign m ON m.id = d.vital_sign_id
                WHERE d.appointment_id = %s 
                GROUP BY truncated_time
                ORDER BY truncated_time DESC
                """ % self.id
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        vital_sign_result = [i[1] for i in result]
        master_name = [line.name for line in master]
        result_lst = []
        for vt_rs in vital_sign_result:
            result_dict = {}
            for item in vt_rs:
                formatted_datetime = datetime.strptime(item['Datetime'], '%Y-%m-%dT%H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
                datetime_date = datetime.strptime(formatted_datetime, '%Y-%m-%d %H:%M:%S')
                new_date = fields.Datetime.context_timestamp(self, timestamp=datetime_date).replace(tzinfo=None)
                name = item['name']
                value = item['value']
                result_dict['Datetime'] = new_date
                result_dict[name] = value
            for item in master_name:
                if item not in result_dict:
                    result_dict[item] = False
            ordered_dict = {'Datetime': result_dict.pop('Datetime')}
            for item in master_name:
                if item in result_dict:
                    ordered_dict[item] = result_dict[item]
            result_lst.append(ordered_dict)
        return result_lst


class VitalSignData(models.Model):
    _inherit = "vital.sign.data"

    appointment_id = fields.Many2one("nuro.appointment", string="OPD Order", index=True)

    @api.model
    def _get_default_patient(self):
        """IPD Vital Sign Data Default Patient"""
        res = super(VitalSignData, self)._get_default_patient()
        for rec in self:
            if rec.appointment_id:
                return rec.appointment_id.patient_id.id
            else:
                return res
