id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
nuro_access_surgery_ipd_user,nuro.surgery,nuro_surgery.model_nuro_surgery,nuro_inpatient.group_ipd_user,1,1,1,1
nuro_access_surgery_ipd_user_manager,nuro.surgery,nuro_surgery.model_nuro_surgery,nuro_inpatient.group_ipd_manager,1,1,1,1
nuro_access_nuro_surgery_rcri_ipd_user,nuro.surgery.rcri,nuro_surgery.model_nuro_surgery_rcri,nuro_inpatient.group_ipd_user,1,0,0,0
nuro_access_nuro_surgery_master_ipd_user,nuro.surgery.master,nuro_surgery.model_nuro_surgery_master,nuro_inpatient.group_ipd_user,1,1,1,0
nuro_access_nuro_surgery_entry_ipd_user,nuro.surgery.entry,nuro_surgery_cashier.model_nuro_surgery_entry,nuro_inpatient.group_ipd_user,1,1,1,0
nuro_access_nuro_surgery_entry_ipd_user_manager,nuro.surgery.entry,nuro_surgery_cashier.model_nuro_surgery_entry,nuro_inpatient.group_ipd_manager,1,1,1,0
access_nuro_inpatient_request_surgery_user,nuro.inpatient.request,nuro_inpatient.model_nuro_inpatient_request,nuro_surgery.group_surgery_user,1,1,1,0
access_nuro_ward_shifting_request_surgery_user,nuro.ward.shifting.request,nuro_inpatient.model_nuro_ward_shifting_request,nuro_surgery.group_surgery_user,1,1,1,0
