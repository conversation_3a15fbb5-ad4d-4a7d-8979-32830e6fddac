# -*- coding: utf-8 -*-
# Part of SOFTPRIMECONSULTING PRIVATE LIMITED
from odoo import api, fields, models


class NuroPrescriptionRequest(models.Model):
    _inherit = 'nuro.prescription.request'

    direct_prescription_line = fields.One2many('medicine.required', 'prescription_id', string='Direct Prescription')
    prescription_hide = fields.Boolean(default=False)

    def add_line(self):
        """
        add line prescription
        """
        self.prescription_hide = True

    def action_create_sale_order(self):
        """
        Action Create Sale Order
        """
        res = super(NuroPrescriptionRequest, self).action_create_sale_order()
        if self.sale_id and self.direct_prescription_line.filtered(lambda x: not x.sale_id):
            self.sale_id.direct_prescription = True
            self.direct_prescription_line.filtered(lambda x: not x.sale_id).write({
                'sale_id': self.sale_id.id
            })
        return res

