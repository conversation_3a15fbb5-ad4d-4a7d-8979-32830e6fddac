# Part of NUROSOLUTION PRIVATE LIMITED
from odoo import fields, models


class Company(models.Model):
    _inherit = "res.company"

    employee_medical_benefit = fields.Selection([('discount', 'Discount'), ('free', 'Free')],
                                                string="Employee Medical Benefit", tracking=True, index=True)
    employee_medical_discount_type = fields.Selection([
        ("fixed", "Fixed"),
        ("different_discount", "Different Discount")
    ], string="Employee Medical Discount Type", tracking=True, index=True)
    discount = fields.Float("Discount", tracking=True, index=True)
    accounting_entries = fields.Selection([
        ("no_entries", "No Entries"),
        ("post_entries", "Post Entries")
    ], tracking=True, index=True)
    benefit_policy = fields.Selection([
        ('for_all', 'Same For All Employee'),
        ('differ', 'Different employee can have Different benefit')
    ], string="Benefit Policy", index=True)

    panel_imaging = fields.Boolean('Imaging', index=True)
    panel_imaging_discount = fields.Float('Imaging Discount', index=True)
    imaging_doctor_exclusion = fields.Many2many('nuro.doctor', 'img_doc_master_rel', 'company_id', 'doctor_id',
                                                string='Exclusion(Doctor)', index=True)
    imaging_service_exclusion = fields.Many2many('hms.master', 'img_comp_master_rel', 'company_id', 'master_id',
                                                 string='Exclusion(Service)', index=True)

    panel_lab = fields.Boolean('Lab', index=True)
    panel_lab_discount = fields.Float('Lab Discount', index=True)
    lab_doctor_exclusion = fields.Many2many('nuro.doctor', 'lab_doc_master_rel', 'company_id', 'doctor_id',
                                            string='Exclusion(Doctor)', index=True)
    lab_service_exclusion = fields.Many2many('hms.master', 'lab_comp_master_rel', 'company_id', 'master_id',
                                             string='Exclusion(Service)', index=True)

    panel_op = fields.Boolean('Other Procedure', index=True)
    panel_op_discount = fields.Float('Other Procedure Discount', index=True)
    op_doctor_exclusion = fields.Many2many('nuro.doctor', 'op_doc_master_rel', 'company_id', 'doctor_id',
                                           string='Exclusion(Doctor)', index=True)
    op_service_exclusion = fields.Many2many('hms.master', 'op_comp_master_rel', 'company_id', 'master_id',
                                            string='Exclusion(Service)', index=True)

    panel_surgery = fields.Boolean('Surgery', index=True)
    panel_surgery_discount = fields.Float('Surgery Discount', index=True)
    surgery_doctor_exclusion = fields.Many2many('nuro.doctor', 'surgery_doc_master_rel', 'company_id', 'doctor_id',
                                                string='Exclusion(Doctor)', index=True)
    surgery_service_exclusion = fields.Many2many('hms.master', 'sur_comp_master_rel', 'company_id', 'master_id',
                                                 string='Exclusion(Service)', index=True)

    panel_health_checkup = fields.Boolean('Health Checkup', index=True)
    panel_health_checkup_discount = fields.Float('Health Checkup Discount', index=True)
    health_checkup_doctor_exclusion = fields.Many2many('nuro.doctor', 'hc_doc_master_rel', 'company_id', 'doctor_id',
                                                       string='Exclusion(Doctor)', index=True)
    health_checkup_service_exclusion = fields.Many2many('hms.master', 'hc_comp_master_rel', 'company_id', 'master_id',
                                                        string='Exclusion(Service)', index=True)

    panel_appointment = fields.Boolean('Appointment', index=True)
    panel_appointment_discount = fields.Float('Appointment Discount', index=True)
    appointment_doctor_exclusion = fields.Many2many('nuro.doctor', 'app_doc_master_rel', 'company_id', 'doctor_id',
                                                    string='Exclusion(Doctor)', index=True)

    panel_dental = fields.Boolean('Dental', index=True)
    panel_dental_discount = fields.Float('Dental Discount', index=True)
    dental_doctor_exclusion = fields.Many2many('nuro.doctor', 'dent_doc_master_rel', 'company_id', 'doctor_id',
                                               string='Exclusion(Doctor)', index=True)

    panel_bt = fields.Boolean('Blood Transfusion', index=True)
    panel_bt_discount = fields.Float('Blood Transfusion Discount', index=True)
    bt_doctor_exclusion = fields.Many2many('nuro.doctor', 'bt_doc_master_rel', 'company_id', 'doctor_id',
                                           string='Exclusion(Doctor)', index=True)

    panel_ws = fields.Boolean('WS', index=True)
    panel_ws_discount = fields.Float('WS Discount', index=True)
    ws_doctor_exclusion = fields.Many2many('nuro.doctor', 'ws_doc_master_rel', 'company_id', 'doctor_id',
                                           string='Exclusion(Doctor)', index=True)

    panel_vaccine = fields.Boolean('Vaccine', index=True)
    panel_vaccine_discount = fields.Float('Vaccine Discount', index=True)
    vaccine_doctor_exclusion = fields.Many2many('nuro.doctor', 'vaccine_doc_master_rel', 'company_id', 'doctor_id',
                                                string='Exclusion(Doctor)', index=True)

    panel_inpatient = fields.Boolean('Inpatient', index=True)
    panel_inpatient_discount = fields.Float('Inpatient Discount', index=True)
    inpatient_doctor_exclusion = fields.Many2many('nuro.doctor', 'inp_doc_master_rel', 'company_id', 'doctor_id',
                                                  string='Exclusion(Doctor)', index=True)

    panel_os = fields.Boolean('Other Service', index=True)
    panel_os_discount = fields.Float('OS Discount', index=True)
    os_doctor_exclusion = fields.Many2many('nuro.doctor', 'os_doc_master_rel', 'company_id', 'doctor_id',
                                           string='Exclusion(Doctor)', index=True)

    panel_maternity = fields.Boolean('Maternity', index=True)
    panel_maternity_discount = fields.Float('Maternity Discount', index=True)
    maternity_doctor_exclusion = fields.Many2many('nuro.doctor', 'maternity_doc_master_rel', 'company_id', 'doctor_id',
                                                  string='Exclusion(Doctor)', index=True)

    panel_emergency = fields.Boolean('Emergency', index=True)
    panel_emergency_discount = fields.Float('Emergency Discount', index=True)
    emergency_doctor_exclusion = fields.Many2many('nuro.doctor', 'emergency_doc_master_rel', 'company_id', 'doctor_id',
                                                  string='Exclusion(Doctor)', index=True)

    def write(self, vals):
        """Write Method Super Call"""
        res = super().write(vals)
        if self.employee_medical_discount_type == 'fixed' and 'discount' in vals:
            query = """update employee_family_line set discount = %s""" % self.discount
            self.env.cr.execute(query)
        return res


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    employee_medical_benefit = fields.Selection([('discount', 'Discount'), ('free', 'Free')],
                                                string="Employee Medical Benefit",
                                                related="company_id.employee_medical_benefit",
                                                readonly=False)
    employee_medical_discount_type = fields.Selection([
        ("fixed", "Fixed"),
        ("different_discount", "Different Discount")
    ], string="Employee Medical Discount Type",
        related="company_id.employee_medical_discount_type",
        readonly=False)
    discount = fields.Float("Discount", related="company_id.discount",
                            readonly=False)
    accounting_entries = fields.Selection(
        [("no_entries", "No Entries"), ("post_entries", "Post Entries")],
        related="company_id.accounting_entries",
        readonly=False)
    benefit_policy = fields.Selection(string="Benefit Policy", related='company_id.benefit_policy',
                                      readonly=False)

    panel_imaging = fields.Boolean('Imaging', related='company_id.panel_imaging', readonly=False)
    panel_imaging_discount = fields.Float('Imaging Discount', related='company_id.panel_imaging_discount',
                                          readonly=False)
    imaging_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                related='company_id.imaging_doctor_exclusion', readonly=False)
    imaging_service_exclusion = fields.Many2many('hms.master', string='Exclusion(Service)',
                                                 related='company_id.imaging_service_exclusion', readonly=False)

    panel_lab = fields.Boolean('Lab', related='company_id.panel_lab', readonly=False)
    panel_lab_discount = fields.Float('Lab Discount', related='company_id.panel_lab_discount', readonly=False)
    lab_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                            related='company_id.lab_doctor_exclusion',
                                            readonly=False)
    lab_service_exclusion = fields.Many2many('hms.master', string='Exclusion(Service)',
                                             related='company_id.lab_service_exclusion', readonly=False)

    panel_op = fields.Boolean('Other Procedure', related='company_id.panel_op', readonly=False)
    panel_op_discount = fields.Float('Other Procedure Discount', related='company_id.panel_op_discount', readonly=False)
    op_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                           related='company_id.op_doctor_exclusion',
                                           readonly=False)
    op_service_exclusion = fields.Many2many('hms.master', string='Exclusion(Service)',
                                            related='company_id.op_service_exclusion', readonly=False)

    panel_surgery = fields.Boolean('Surgery', related='company_id.panel_surgery', readonly=False)
    panel_surgery_discount = fields.Float('Surgery Discount', related='company_id.panel_surgery_discount',
                                          readonly=False)
    surgery_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                related='company_id.surgery_doctor_exclusion', readonly=False)
    surgery_service_exclusion = fields.Many2many('hms.master', string='Exclusion(Service)',
                                                 related='company_id.surgery_service_exclusion', readonly=False)

    panel_health_checkup = fields.Boolean('Health Checkup', related='company_id.panel_health_checkup', readonly=False)
    panel_health_checkup_discount = fields.Float('Health Checkup Discount',
                                                 related='company_id.panel_health_checkup_discount', readonly=False)
    health_checkup_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                       related='company_id.health_checkup_doctor_exclusion',
                                                       readonly=False)
    health_checkup_service_exclusion = fields.Many2many('hms.master', string='Exclusion(Service)',
                                                        related='company_id.health_checkup_service_exclusion',
                                                        readonly=False)

    panel_appointment = fields.Boolean('Appointment', related='company_id.panel_appointment', readonly=False)
    panel_appointment_discount = fields.Float('Appointment Discount', related='company_id.panel_appointment_discount',
                                              readonly=False)
    appointment_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                    related='company_id.appointment_doctor_exclusion', readonly=False)

    panel_dental = fields.Boolean('Dental', related='company_id.panel_dental', readonly=False)
    panel_dental_discount = fields.Float('Dental Discount', related='company_id.panel_dental_discount', readonly=False)
    dental_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                               related='company_id.dental_doctor_exclusion',
                                               readonly=False)

    panel_bt = fields.Boolean('Blood Transfusion', related='company_id.panel_bt', readonly=False)
    panel_bt_discount = fields.Float('Blood Transfusion Discount', related='company_id.panel_bt_discount',
                                     readonly=False)
    bt_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                           related='company_id.bt_doctor_exclusion',
                                           readonly=False)

    panel_ws = fields.Boolean('WS', related='company_id.panel_ws', readonly=False)
    panel_ws_discount = fields.Float('WS Discount', related='company_id.panel_ws_discount', readonly=False)
    ws_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                           related='company_id.ws_doctor_exclusion',
                                           readonly=False)

    panel_vaccine = fields.Boolean('Vaccine', related='company_id.panel_vaccine', readonly=False)
    panel_vaccine_discount = fields.Float('Vaccine Discount', related='company_id.panel_vaccine_discount',
                                          readonly=False)
    vaccine_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                related='company_id.vaccine_doctor_exclusion',
                                                readonly=False)

    panel_inpatient = fields.Boolean('Inpatient', related='company_id.panel_inpatient', readonly=False)
    panel_inpatient_discount = fields.Float('Inpatient Discount', related='company_id.panel_inpatient_discount',
                                            readonly=False)
    inpatient_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                  related='company_id.inpatient_doctor_exclusion', readonly=False)

    panel_os = fields.Boolean('Other Service', related='company_id.panel_os', readonly=False)
    panel_os_discount = fields.Float('OS Discount', related='company_id.panel_os_discount',
                                     readonly=False)
    os_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                           related='company_id.os_doctor_exclusion', readonly=False)

    panel_maternity = fields.Boolean('Maternity', related='company_id.panel_maternity', readonly=False)
    panel_maternity_discount = fields.Float('Maternity Discount', related='company_id.panel_maternity_discount',
                                            readonly=False)
    maternity_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                  related='company_id.maternity_doctor_exclusion', readonly=False)

    panel_emergency = fields.Boolean('Emergency', related='company_id.panel_emergency', readonly=False)
    panel_emergency_discount = fields.Float('Emergency Discount', related='company_id.panel_emergency_discount',
                                            readonly=False)
    emergency_doctor_exclusion = fields.Many2many('nuro.doctor', string='Exclusion(Doctor)',
                                                  related='company_id.emergency_doctor_exclusion', readonly=False)
