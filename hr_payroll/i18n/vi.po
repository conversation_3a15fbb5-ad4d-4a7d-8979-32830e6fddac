# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll
# 
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON>, 2017
# <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-03 15:06+0000\n"
"PO-Revision-Date: 2017-09-20 10:18+0000\n"
"Last-Translator: Duy BQ <<EMAIL>>, 2019\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:41
#, python-format
msgid "%s (copy)"
msgstr "%s (sao chép)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When user cancel payslip the status is 'Rejected'."
msgstr ""
"* Khi phiếu lương được tạo, trạng thái là 'Dự thảo'.\n"
"* Nếu phiếu lương đang thẩm định, trạng thái là 'Chờ'.\n"
"* Nếu phiếu lương được xác nhận, trạng thái là 'Hoàn thành'.\n"
"* Khi người dùng hủy phiếu lương, trạng thái là 'Bị từ chối'."

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Address</strong>"
msgstr "<strong>Địa chỉ</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>Chữ ký xác nhận</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Bank Account</strong>"
msgstr "<strong>Tài khoản Ngân hàng</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "<strong>Date From:</strong>"
msgstr "<strong>Từ ngày:</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Date From</strong>"
msgstr "<strong>Từ ngày</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "<strong>Date To:</strong>"
msgstr "<strong>Đến ngày:</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Date To</strong>"
msgstr "<strong>Đến ngày</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Designation</strong>"
msgstr "<strong>Chức vụ</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Email</strong>"
msgstr "<strong>Email</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Identification No</strong>"
msgstr "<strong>Số CMND</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Name</strong>"
msgstr "<strong>Tên</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "<strong>Reference</strong>"
msgstr "<strong>Tham chiếu</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "<strong>Register Name:</strong>"
msgstr "<strong>Tên Ghi nhận Đóng góp:</strong>"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "<strong>Total</strong>"
msgstr "<strong>Tổng</strong>"

#. module: hr_payroll
#: model:ir.actions.act_window,help:hr_payroll.action_contribution_register_form
msgid ""
"A contribution register is a third party involved in the salary\n"
"            payment of the employees. It can be the social security, the\n"
"            estate or anyone that collect or inject money on payslips."
msgstr ""
"Một đăng ký đóng góp là một bên thứ ba liên quan đến mức lương\n"
"             thanh toán của nhân viên. Nó có thể là an sinh xã hội,\n"
"             bất động sản hoặc bất cứ ai thu thập hoặc bơm tiền vào phiếu lương."

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Accounting"
msgstr "Kế toán"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Accounting Information"
msgstr "Thông tin kế toán"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_active
msgid "Active"
msgstr "Hiệu lực"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr "Thêm ghi chú nội bộ..."

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_contract_advantage_template_view_form
msgid "Advantage Name"
msgstr "Tên phụ cấp/phúc lợi"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_children_salary_rules
msgid "All Children Rules"
msgstr "Tất cả Quy tắc Con"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr "Phụ cấp"

#. module: hr_payroll
#: selection:hr.payslip.line,condition_select:0
#: selection:hr.salary.rule,condition_select:0
msgid "Always True"
msgstr "Luôn đúng"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_amount
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Amount"
msgstr "Tổng số"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_amount_select
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Amount Type"
msgstr "Kiểu Tổng sổ"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Annually"
msgstr "Hàng năm"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_appears_on_payslip
msgid "Appears on Payslip"
msgstr "Hiển thị ở phiếu lương"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_condition_python
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"Quy tắc này được áp dụng cho việc tính toán nếu điều kiện là đúng. Bạn có "
"thể chỉ ra điều kiện như luong_co_ban > 10000000."

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr "Cơ sở"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_rule_basic
msgid "Basic Salary"
msgstr "Lương Cơ bản"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings_module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr "Bảng Lương Vương Quốc Bỉ"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Bi-monthly"
msgstr "Hai tháng một lần"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Bi-weekly"
msgstr "Hai tuần một lần"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr "Tính toán"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_payslip_lines_contribution_register
msgid "Cancel"
msgstr "Hủy"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel Payslip"
msgstr "Hủy Phiếu lương"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:98
#, python-format
msgid "Cannot cancel a payslip that is done."
msgstr "Không thể huỷ phiếu lương mà đã hoàn thành."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_id_7804
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr "Nhóm"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Child Rules"
msgstr "Quy tắc con"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_child_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_child_ids
msgid "Child Salary Rule"
msgstr "Quy tắc con"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_children_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_children_ids
msgid "Children"
msgstr "Con"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Children Definition"
msgstr "Định nghĩa các Quy tắc con"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr "Chọn Quy tắc Tiền lương Bản địa"

#. module: hr_payroll
#: model:ir.actions.act_window,help:hr_payroll.action_contribution_register_form
msgid "Click to add a new contribution register."
msgstr "Bấm để tạo mới một contribution register."

#. module: hr_payroll
#: selection:hr.payslip.run,state:0
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Close"
msgstr "Ðóng"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_code
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Code"
msgstr "Mã"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_view_kanban
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "Mã:"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Companies"
msgstr "Công ty"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_company_id
msgid "Company"
msgstr "Công ty"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr "Đóng góp Công ty"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr "Tính toán"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr "Tính lương"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_condition_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_condition_select
msgid "Condition Based on"
msgstr "Điều kiện dựa trên"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr "Điều kiện"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr "Xác nhận"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_contract_id
msgid "Contract"
msgstr "Hợp đồng"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_contract_advantage_template_action
#: model:ir.ui.menu,name:hr_payroll.hr_contract_advantage_template_menu_action
msgid "Contract Advantage Templates"
msgstr "Chế độ đãi ngộ/phúc lợi mẫu cho hợp đồng"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_contribution_register_form
msgid "Contribution"
msgstr "Đóng góp"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contribution_register
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_register_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_register_id
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr "Ghi nhận đóng góp"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_payslip_lines_contribution_register
msgid "Contribution Register's Payslip Lines"
msgstr "Dòng Phiếu lương liên quan đến Ghi nhận Đóng góp"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_register_form
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_contribution_register_form
#: model:ir.ui.view,arch_db:hr_payroll.hr_contribution_register_filter
#: model:ir.ui.view,arch_db:hr_payroll.hr_contribution_register_tree
msgid "Contribution Registers"
msgstr "Ghi nhận Đóng góp"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "Trợ cấp chuyển nhượng"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr "Trợ cấp chuyển nhượng cho Gravie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_create_date
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_credit_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_credit_note
msgid "Credit Note"
msgstr "Hoàn trả (Credit Note)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_date_start
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_date_from
msgid "Date From"
msgstr "Từ ngày"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_date_end
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_date_to
msgid "Date To"
msgstr "Đến ngày"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.DED
msgid "Deduction"
msgstr "Giảm trừ"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_default_value
msgid "Default value for this advantage"
msgstr "Giá trị mặc định cho Chế độ đãi ngộ này"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "Xác định tần suất thanh toán khoản lương này."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, accordingly to "
"the contract chosen. If you let empty the field contract, this field isn't "
"mandatory anymore and thus the rules applied will be all the rules set on "
"the structure of all contracts of the employee valid for the chosen period"
msgstr ""
"Định nghĩa một quy tắc mà được áp dụng cho phiếu lương này, tùy theo hợp "
"đồng lao động được chọn. Nếu bạn để trống trường hợp đồng lao động, trường "
"này sẽ không còn bắt buộc nữa và do đó quy tắc được áp dụng  sẽ là tất cả "
"quy tắc trên cấu trúc lương của tất cả các hợp đồng lao động của nhân viên "
"trong kỳ được chọn"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_note
#: model:ir.ui.view,arch_db:hr_payroll.hr_contribution_register_form
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "Mô tả"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Details By Salary Rule Category"
msgstr "Chi tiết theo nhóm quy tắc lương"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_details_by_salary_rule_category
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Details by Salary Rule Category"
msgstr "Chi tiết theo nhóm quy tắc lương"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_report_contributionregister_display_name
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_report_payslipdetails_display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: hr_payroll
#: selection:hr.payslip,state:0
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr "Hoàn thành"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr "Hoàn thành Bảng lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr "Phiếu lương đã hoàn thành"

#. module: hr_payroll
#: selection:hr.payslip,state:0 selection:hr.payslip.run,state:0
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft"
msgstr "Dự thảo"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr "Bảng lương dự thảo"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr "Phiếu lương dự thảo"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_employee_id
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr "Nhân viên"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Hợp đồng nhân viên"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr "Chức vụ nhân viên"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.ui.menu,name:hr_payroll.menu_department_tree
msgid "Employee Payslips"
msgstr "Phiếu lương nhân viên"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_advantage_template
msgid "Employee's Advantage on Contract"
msgstr "Chế độ đãi ngộ nhân viên trên hợp đồng"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_employee_ids
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr "Nhân viên"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:36
#, python-format
msgid "Error ! You cannot create a recursive Salary Structure."
msgstr ""
"Lỗi! Bạn không thể tạo các cấu trúc lương đệ quy (gà có trước hay trứng có "
"trước)"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:91
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr "Lỗi! Bạn không thể tạo phân cấp đệ quy của Danh mục quy tắc lương."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:176
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rules."
msgstr "Lỗi! Bạn không thể tạo phân cấp đệ quy của Quy tắc Lương."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_register_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_register_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr "Bên thứ ba mà có liên quan đến việc thanh toán lương của nhân viên."

#. module: hr_payroll
#: selection:hr.payslip.line,amount_select:0
#: selection:hr.salary.rule,amount_select:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_amount_fix
msgid "Fixed Amount"
msgstr "Giá trị cố định"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "Ví dụ, nhập 50,0 để sử dụng 50%"

#. module: hr_payroll
#: code:addons/hr_payroll/report/report_contribution_register.py:34
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Nội dung form bị mất, báo cáo này không thể in được."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings_module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr "Tiền lương Pháp"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr "Tổng quát"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "Tạo"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generate Payslips"
msgstr "Tạo phiếu lương"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Tạo phiếu lương cho tất cả nhân viên được chọn"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sales_commission
msgid "Get 1% of sales"
msgstr "Nhận 1% Doanh số bán hàng"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:183
#, python-format
msgid "Global Leaves"
msgstr "Nghỉ Toàn cục"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_rule_taxable
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
msgid "Gross"
msgstr "Tổng"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Nhóm theo"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "Phụ cấp thuê nhà"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_id
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_report_contributionregister_id
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_report_payslipdetails_id
msgid "ID"
msgstr "ID"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run_credit_note
msgid ""
"If its checked, indicates that all payslips generated from here are refund "
"payslips."
msgstr ""
"Nếu được đánh dấu kiểm, chỉ ra rằng tất cả các phiếu lương được tạo từ đây "
"là các phiếu hoàn tiền."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_active
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"Nếu bỏ chọn trường Hiệu lực, nó sẽ cho phép bạn ẩn quy tắc tính lương mà "
"không cần xóa bỏ nó."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings_module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr "Bảng lương Ấn độ"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "Chỉ ra rằng phiếu lương này có một khoản hoàn tiền từ một phiếu khác"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr "Dữ liệu nhập ngoài"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_input_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_input_ids
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Inputs"
msgstr "Đầu vào"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_note
msgid "Internal Note"
msgstr "Ghi chú nội bộ"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_view_kanban
msgid "Is a Blocking Reason?"
msgstr "Là một Lý do Phong toả?"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_quantity
msgid ""
"It is used in computation for percentage and fixed amount. For e.g. A rule "
"for Meal Voucher having fixed amount of 1€ per worked day can have its "
"quantity defined in expression like worked_days.WORK100.number_of_days."
msgstr ""
"Được sử dụng để tính toán tổng số theo phần trăm và cố định. Ví dụ, một quy "
"tắc Phiếu Ăn trưa có giá trị cố định 30000đ mỗi ngày làm việc có thể có số "
"lượng được xác định bằng biểu thức kiểu như "
"worked_days.WORK100.number_of_days."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_amount
msgid ""
"It is used in computation. For e.g. A rule for sales having 1% commission of"
" basic salary for per product can defined in expression like result = "
"inputs.SALEURO.amount * contract.wage*0.01."
msgstr ""
"Được sử dụng trong tính toán. Ví dụ, môt quy tắc cho nhân viên bán hàng có "
"hoa hồng bằng 1% lương cơ bản nhân giá trị sản phẩm bán ra có thể được thể "
"hiện bằng biểu thức result = inputs.SALEURO.amount * contract.wage*0.01."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_report_contributionregister___last_update
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_report_payslipdetails___last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_write_date
#: model:ir.model.fields,field_description:hr_payroll.field_payslip_lines_contribution_register_write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category_parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"Liên kết một nhóm lương đến cấp cha của nó chỉ được sử dụng cho mục đích báo"
" cáo."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_lower_bound
msgid "Lower Bound"
msgstr "Mức tối thiểu"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_advantage_template_lower_bound
msgid "Lower bound authorized by the employer for this advantage"
msgstr "Mức ràng buộc thấp hơn được nhà tuyển dụng ủy quyền vì lợi thế này"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_paid
msgid "Made Payment Order ? "
msgstr "Đã tạo lệnh thanh toán/UNC? "

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Manager"
msgstr "Quản lý"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_condition_range_max
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_condition_range_max
msgid "Maximum Range"
msgstr "Phạm vi tối đa"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "Phiếu Ăn"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_condition_range_min
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_condition_range_min
msgid "Minimum Range"
msgstr "Phạm vi tối thiểu"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Miscellaneous"
msgstr "Khác"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Monthly"
msgstr "Hàng tháng"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_name
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Name"
msgstr "Tên"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr "Thực Lĩnh"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_rule_net
msgid "Net Salary"
msgstr "Lương Thực Lĩnh"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:199
#, python-format
msgid "Normal Working Days paid at 100%"
msgstr "Ngày làm việc thông thường được trả 100%"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr "Ghi chú"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_number_of_days
msgid "Number of Days"
msgstr "Số ngày"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_number_of_hours
msgid "Number of Hours"
msgstr "Số giờ"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer"
msgstr "Cán bộ"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr "Dữ liệu nhập ngoài khác"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_parent_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category_parent_id
msgid "Parent"
msgstr "Cấp cha"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_parent_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_parent_rule_id
msgid "Parent Salary Rule"
msgstr "Quy tắc Lương Cấp cha"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_partner_id
msgid "Partner"
msgstr "Đối tác"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_payslip_id
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Pay Slip"
msgstr "Phiếu lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "PaySlip Batch"
msgstr "Bảng lương"

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.payslip_details_report
msgid "PaySlip Details"
msgstr "Chi tiết phiếu lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_payslip_lines_contribution_register
msgid "PaySlip Lines"
msgstr "Chi tiết phiếu lương"

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.action_contribution_register
msgid "PaySlip Lines By Conribution Register"
msgstr "Chi tiết phiếu lương theo Conribution Register"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "PaySlip Lines by Contribution Register"
msgstr "Chi tiết phiếu lương theo Contribution Register"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_payslip_lines_contribution_register
msgid "PaySlip Lines by Contribution Registers"
msgstr "Chi tiết phiếu lương theo Contribution Registers"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "PaySlip Name"
msgstr "Tên phiếu lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_root
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "Bảng lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "Kế toán Lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Rules"
msgstr "Quy tắc Tiền lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr "Cấu trúc lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr "Các quy tắc tiền lương mà áp dụng ở đất nước bạn"

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Payslip"
msgstr "Phiếu lương"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:84
#, python-format
msgid "Payslip 'Date From' must be before 'Date To'."
msgstr "'Ngày đầu' của Phiếu lương phải trước 'Ngày cuối' của nó."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_payslip_run_id
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batches"
msgstr "Bảng lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_payslip_count
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Payslip Computation Details"
msgstr "Chi tiết phiếu lương"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
msgid "Payslip Input"
msgstr "Dữ liệu phiếu lương nhập ngoài"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_line_ids
msgid "Payslip Inputs"
msgstr "Dữ liệu phiếu lương nhập ngoài"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr "Dòng Phiếu lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_ids
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr "Dòng Phiếu lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Payslip Lines by Contribution Register"
msgstr "Dòng Phiếu lương theo Contribution Register"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_name
msgid "Payslip Name"
msgstr "Tên phiếu lương"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "Ngày làm việc trên Phiếu lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee_payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee_slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_slip_ids
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Payslips"
msgstr "Phiếu lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr "Bảng lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "Phiếu lương theo Nhân viên"

#. module: hr_payroll
#: selection:hr.payslip.line,amount_select:0
#: selection:hr.salary.rule,amount_select:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_amount_percentage
msgid "Percentage (%)"
msgstr "Phần trăm (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_amount_percentage_base
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_amount_percentage_base
msgid "Percentage based on"
msgstr "Phần trăm dựa trên"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr "Chu kỳ"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "Tự động tạo các bút toán lương cho kế toán"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_payslip_lines_contribution_register
msgid "Print"
msgstr "In"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "Thuế Nhà thầu"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "Quỹ Dự phòng"

#. module: hr_payroll
#: selection:hr.payslip.line,amount_select:0
#: selection:hr.salary.rule,amount_select:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_amount_python_compute
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_amount_python_compute
msgid "Python Code"
msgstr "Mã Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_condition_python
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_condition_python
msgid "Python Condition"
msgstr "Điều kiện Python"

#. module: hr_payroll
#: selection:hr.payslip.line,condition_select:0
#: selection:hr.salary.rule,condition_select:0
msgid "Python Expression"
msgstr "Biểu thức Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_quantity
msgid "Quantity"
msgstr "Số lượng"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
msgid "Quantity/Rate"
msgstr "Số lượng/Tỷ lệ"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Quantity/rate"
msgstr "Số lượng/Tỷ lệ"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Quarterly"
msgstr "Hàng quý"

#. module: hr_payroll
#: selection:hr.payslip.line,condition_select:0
#: selection:hr.salary.rule,condition_select:0
msgid "Range"
msgstr "Phạm vi"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_condition_range
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_condition_range
msgid "Range Based on"
msgstr "Phạm vi dựa trên"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_rate
msgid "Rate (%)"
msgstr "Tỷ lệ (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_number
msgid "Reference"
msgstr "Tham chiếu"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr "Hoàn trả"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:104
#, python-format
msgid "Refund: "
msgstr "Hoàn trả: "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contribution_register_register_line_ids
msgid "Register Line"
msgstr "Chi tiết ghi nhận"

#. module: hr_payroll
#: selection:hr.payslip,state:0
msgid "Rejected"
msgstr "Bị từ chối"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_salary_rule_id
msgid "Rule"
msgstr "Quy tắc"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr "Nhóm lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr "Tính toán Lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr "Nhóm quy tắc Lương"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr "Nhóm quy tắc Lương"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_input
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_input_input_id
msgid "Salary Rule Input"
msgstr "Đầu vào Quy tắc Lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_rule_ids
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_tree
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr "Quy tắc lương"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:396
#: code:addons/hr_payroll/models/hr_payslip.py:446
#, python-format
msgid "Salary Slip of %s for %s"
msgstr "Phiếu lương của %s cho %s"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_struct_id
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_tree
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
msgid "Salary Structure"
msgstr "Cấu trúc lương"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
msgid "Salary Structures"
msgstr "Cấu trúc lương"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_schedule_pay
msgid "Scheduled Pay"
msgstr "Thanh toán"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr "Tìm kiếm bảng lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr "Tìm Các dòng phiếu lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr "Tìm Phiếu lương"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr "Tìm Quy tắc lương"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Semi-annually"
msgstr "Nửa năm một"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days_sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_sequence
msgid "Sequence"
msgstr "Thứ tự"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr "Đặt về dự thảo"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr "Thiết lập"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "States"
msgstr "Trạng thái"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run_state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_state
msgid "Status"
msgstr "Tình trạng"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_struct_id
msgid "Structure"
msgstr "Cấu trúc lương"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"Mã của quy tắc lương có thể được sử dụng như tham chiếu trong việc tính toán"
" các quy tắc khác."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days_code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_input_code
msgid "The code that can be used in the salary rules"
msgstr "Mã được sử dụng trong quy tắc lương"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_amount_select
msgid "The computation method for the rule amount."
msgstr "Phương pháp tính toán cho tổng tiền của một quy tắc."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_contract_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days_contract_id
msgid "The contract for which applied this input"
msgstr "Hợp đồng mà input này áp dụng cho"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_condition_range_max
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "Tổng tiền tối đa được áp dụng trên quy tắc này."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_condition_range_min
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "Tổng tiền tối thiểu được áp dụng trên quy tắc này."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_condition_range
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""
"Cái này sẽ được sử dụng để tính toán giá trị trường %; in general it is on "
"basic, but you can also use categories code fields in lowercase as a "
"variable names (hra, ma, lta, etc.) and the variable basic."

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"This wizard will generate payslips for all selected employee(s) based on the"
" dates and credit note specified on Payslips Run."
msgstr ""
"Đồ thuật này sẽ tạo phiếu lương cho tất cả nhân viên được chọn dựa trên ngày"
" và phiếu hoàn trả được chỉ định khi 'Chạy phiếu lương'."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line_total
#: model:ir.ui.view,arch_db:hr_payroll.report_contributionregister
#: model:ir.ui.view,arch_db:hr_payroll.report_payslip
#: model:ir.ui.view,arch_db:hr_payroll.report_payslipdetails
msgid "Total"
msgstr "Tổng"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr "Tổng số ngày làm việc"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_advantage_template_upper_bound
msgid "Upper Bound"
msgstr "Mức tối đa"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_advantage_template_upper_bound
msgid "Upper bound authorized by the employer for this advantage"
msgstr "Mức tối đa mà nhà tuyển dụng có thể trả dành cho khoản phúc lợi này"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_sequence
msgid "Use to arrange calculation sequence"
msgstr "Sử dụng để sắp xếp trình tự tính toán"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "Được sử dụng để hiển thị quy tắc lương trên phiếu lương."

#. module: hr_payroll
#: selection:hr.payslip,state:0
msgid "Waiting"
msgstr "Chờ"

#. module: hr_payroll
#: selection:hr.contract,schedule_pay:0
msgid "Weekly"
msgstr "Hàng tuần"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr "Ngày làm việc"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr "Ngày làm việc"

#. module: hr_payroll
#: model:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr "Ngày làm việc & Dữ liệu nhập ngoài"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:198
#, python-format
msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
msgstr ""
"Sai cơ sở lấy phần trăm hoặc số lượng định nghĩa trong quy tắc lương %s "
"(%s)."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:204
#, python-format
msgid "Wrong python code defined for salary rule %s (%s)."
msgstr "Sai mã python định nghĩa trong quy tắc lương %s (%s)."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:227
#, python-format
msgid "Wrong python condition defined for salary rule %s (%s)."
msgstr "Sai điều kiện python định nghĩa trong quy tắc lương %s (%s)."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:191
#, python-format
msgid "Wrong quantity defined for salary rule %s (%s)."
msgstr "Sai số lượng định nghĩa trong quy tắc lương %s (%s)."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:221
#, python-format
msgid "Wrong range condition defined for salary rule %s (%s)."
msgstr "Sai phạm vi định nghĩa trong quy tắc lương %s (%s)."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:128
#, python-format
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr ""
"Bạn không thể xoá một phiếu lương mà không ở trạng thái dự thảo hoặc huỷ!"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:24
#, python-format
msgid "You must select employee(s) to generate payslip(s)."
msgstr "Bạn phải chọn ít nhất một nhân viên để tạo (các) phiếu lương."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:517
#, python-format
msgid "You must set a contract to create a payslip line."
msgstr "Bạn phải thiết lập một hợp đồng để có thể tạo chi tiết phiếu lương."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "hr.salary.rule"
msgstr "hr.salary.rule"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_report_contributionregister
msgid "report.hr_payroll.report_contributionregister"
msgstr "report.hr_payroll.report_contributionregister"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_report_payslipdetails
msgid "report.hr_payroll.report_payslipdetails"
msgstr "report.hr_payroll.report_payslipdetails"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "res.config.settings"
msgstr "res.config.settings"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line_amount_percentage_base
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_amount_percentage_base
msgid "result will be affected to a variable"
msgstr "kết quả sẽ bị ảnh hưởng tới một biến số"
