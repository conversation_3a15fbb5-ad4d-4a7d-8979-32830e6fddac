<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_harmuud_api_config_tree" model="ir.ui.view">
            <field name="name">Harmuud Config</field>
            <field name="model">harmuud.api.config</field>
            <field name="arch" type="xml">
                <tree string="Harmuud Bank Configuration">
                    <field name="name"/>
                    <field name="machine_id"/>
                </tree>
            </field>
        </record>

        <record id="view_harmuud_api_config_form" model="ir.ui.view">
            <field name="name">Harmuud Api Config</field>
            <field name="model">harmuud.api.config</field>
            <field name="arch" type="xml">
                <form string="Repair Order">
                    <sheet string="Harmuud Bank Configuration">
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="machine_id"/>
                                <field name="api_id"/>
                                <field name="api_key" password="True"/>
                                <field name="url"/>
                                <field name="journal_id" required="1" domain="[('type', '=', 'bank')]"
                                       options="{'no_create': True}"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_harmuud_api_configuration" model="ir.actions.act_window">
            <field name="name">Harmuud Api Configuration</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">harmuud.api.config</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'create': False}</field>
        </record>

        <menuitem id="menu_harmuud_wallet" name="Harmuud Wallet"
                  action="action_harmuud_api_configuration"
                  parent="base.menu_custom" sequence="7"/>

    </data>
</odoo>
