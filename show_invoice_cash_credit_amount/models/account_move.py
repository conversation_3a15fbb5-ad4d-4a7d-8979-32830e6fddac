# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import api, models, fields, _


class AccountMove(models.Model):
    _inherit = 'account.move'

    cash_sale_amount = fields.Float('Cash Sales Amount', compute='_compute_cash_sale_amounts',
                                    store=True)
    credit_sale_amount = fields.Float('Credit Sales Amount', compute='_compute_cash_sale_amounts',
                                    store=True)
    sales_type = fields.Selection([('cash', 'Cash'),
                                   ('credit', 'Credit'),
                                   ('partial_cash', 'Partial Cash')],
                                  compute='_compute_sales_type', store=True)

    def compute_remaining_amount(self):
        """Compute Remaining Amount"""
        self._compute_amount()

    @api.depends('cash_sale_amount', 'credit_sale_amount')
    def _compute_sales_type(self):
        """get sales type based on payment"""
        for rec in self:
            rec.sales_type = False
            if rec.type == 'out_invoice':
                if not rec.credit_sale_amount and not rec.cash_sale_amount:
                    rec.sales_type = 'credit'
                if rec.cash_sale_amount:
                    if rec.amount_total_signed == rec.cash_sale_amount:
                        rec.sales_type = 'cash'
                    elif rec.amount_total_signed > rec.cash_sale_amount:
                        rec.sales_type = 'partial_cash'
                if rec.credit_sale_amount and not rec.sales_type == 'partial_cash':
                    if (rec.amount_total_signed == rec.credit_sale_amount or rec.amount_total_signed >
                            rec.credit_sale_amount):
                        rec.sales_type = 'credit'

    @api.depends('line_ids.matched_debit_ids', 'line_ids.amount_residual',
                 'line_ids.matched_credit_ids', 'line_ids.full_reconcile_id',
                 'amount_total_signed', 'amount_residual')
    def _compute_cash_sale_amounts(self):
        """get cash credit amount if invoice date and payment date
         are same then we count as cash"""
        for rec in self:
            rec.cash_sale_amount = 0.0
            rec.credit_sale_amount = 0.0
            if rec.credit_sale_amount == 0.0:
                rec.credit_sale_amount = rec.amount_total_signed
            aml_receive = rec.line_ids.filtered(lambda line: line.account_internal_type == 'receivable')
            if rec.type == 'out_invoice' and aml_receive and rec.state == 'posted':
                # sql = """
                #             SELECT  sum(apc.amount) as amount
                #             from account_partial_reconcile apc
                #             inner join account_move_line
                #             aml on aml.id = apc.debit_move_id
                #             inner join account_move_line
                #             aml1 on aml1.id = apc.credit_move_id
                #             inner join account_move am on am.id = aml.move_id
                #             inner join account_move am1 on am1.id = aml1.move_id
                #             WHERE aml.id = %s
                #             and aml.parent_state = 'posted'
                #             and aml.date != apc.max_date
                #             and am.move_type = 'out_invoice'
                #             and am1.move_type = 'entry'
                #         """ % (aml_receive.id,)
                # self.env.cr.execute(sql)
                # all_data = self.env.cr.fetchall()
                # credit_amount = 0.0
                # if all_data[0][0]:
                #     credit_amount = all_data[0][0]
                #     rec.credit_sale_amount = credit_amount
                precon_ids = aml_receive.matched_debit_ids + aml_receive.matched_credit_ids
                recon_lines = precon_ids.filtered(lambda x: x.max_date == aml_receive.date)
                cash_amount = recon_lines and sum(recon_lines.mapped('amount')) or 0.0
                # sql1 = """
                #         SELECT  sum(apc.amount) as amount
                #         from account_partial_reconcile apc
                #         inner join account_move_line aml on aml.id = apc.debit_move_id
                #         inner join account_move_line aml1 on aml1.id = apc.credit_move_id
                #         inner join account_move am on am.id = aml.move_id
                #         inner join account_move am1 on am1.id = aml1.move_id
                #         WHERE aml.id = %s
                #         and aml.parent_state = 'posted'
                #         and aml.date = apc.max_date
                #         and am.type = 'out_invoice'
                #         and am1.type = 'entry'
                #     """ % (aml_receive.id,)
                # self.env.cr.execute(sql1)
                # cash_data = self.env.cr.fetchall()
                # cash_amount = 0.0
                # if cash_data[0][0]:
                #     cash_amount = cash_data[0][0]
                rec.cash_sale_amount = cash_amount
                rec.credit_sale_amount -= cash_amount

    @api.depends('type', 'line_ids.amount_residual')
    def _compute_payments_widget_reconciled_info(self):
        """inherit for call cash credit amount method"""
        res = super()._compute_payments_widget_reconciled_info()
        for rec in self:
            rec._compute_cash_sale_amounts()
        return res


class AccountPartialReconcile(models.Model):
    _inherit = "account.partial.reconcile"

    def correct_calculation_line_sale_cash_credit(self):
        """Correction Calculation Line sale Cash Credit"""
        if self.credit_move_id:
            self.credit_move_id.move_id._compute_cash_sale_amounts()
            self.credit_move_id.move_id._compute_sales_type()
            if self.credit_move_id.move_id.type == 'out_invoice':
                self.credit_move_id.move_id._compute_amount()
        if self.debit_move_id:
            self.debit_move_id.move_id._compute_cash_sale_amounts()
            self.debit_move_id.move_id._compute_sales_type()
            if self.debit_move_id.move_id.type == 'out_invoice':
                self.debit_move_id.move_id._compute_amount()

    @api.model
    def create(self, vals):
        """Create Super Call"""
        res = super().create(vals)
        for rec in res:
            rec.correct_calculation_line_sale_cash_credit()
        return res

    def write(self, vals):
        """Write Super Call"""
        res = super().write(vals)
        for rec in self:
            rec.correct_calculation_line_sale_cash_credit()
        return res

    def unlink(self):
        """Unlink Super call"""
        for rec in self:
            move_id = rec.credit_move_id.move_id
            if rec.debit_move_id.move_id.type == 'out_invoice':
                move_id = rec.debit_move_id.move_id
            res = super().unlink()
            if move_id and move_id.type == 'out_invoice':
                move_id._compute_amount()
            return res



