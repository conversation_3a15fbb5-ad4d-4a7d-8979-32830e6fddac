# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Letlakala le fetilego
previous_label=Fetilego
next.title=Letlakala le latelago
next_label=Latelago

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=BuÅ¡etÅ¡a ka gare
zoom_out_label=BuÅ¡etÅ¡a ka gare
zoom_in.title=GodiÅ¡etÅ¡a ka ntle
zoom_in_label=GodiÅ¡etÅ¡a ka ntle
zoom.title=GodiÅ¡a
presentation_mode.title=Fetogela go mokgwa wa tlhagiÅ¡o
presentation_mode_label=Mokgwa wa tlhagiÅ¡o
open_file.title=Bula faele
open_file_label=Bula
print.title=GatiÅ¡a
print_label=GatiÅ¡a
download.title=Laolla
download_label=Laolla
bookmark.title=Pono ya bjale (kopiÅ¡a le go bula lefasetereng le leswa)
bookmark_label=Tebelelo ya gona bjale

# Secondary toolbar and context menu


# Document properties dialog box
document_properties_file_name=Leina la faele:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_title=Thaetlele:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Å ielanya para ya ka thoko
toggle_sidebar_label=Å ielanya para ya ka thoko
document_outline_label=KakaretÅ¡o ya tokumente
thumbs.title=LaetÅ¡a dikhutÅ¡ofatÅ¡o
thumbs_label=DikhutÅ¡ofatÅ¡o
findbar.title=HwetÅ¡a go tokumente

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Letlakala {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=KhutÅ¡ofatÅ¡o ya letlakala {{page}}

# Find panel button title and messages
find_previous.title=HwetÅ¡a tiragalo e fetilego ya sekafoko
find_previous_label=Fetilego
find_next.title=HwetÅ¡a tiragalo e latelago ya sekafoko
find_next_label=Latelago
find_highlight=BonagatÅ¡a tÅ¡ohle
find_match_case_label=SwantÅ¡ha kheisi
find_reached_top=Fihlile godimo ga tokumente, go tÅ¡wetÅ¡we pele go tloga tlase
find_reached_bottom=Fihlile mafelelong a tokumente, go tÅ¡wetÅ¡we pele go tloga godimo
find_not_found=Sekafoko ga sa hwetÅ¡wa

# Error panel labels
error_more_info=TshedimoÅ¡o e oketÅ¡egilego
error_less_info=TshedimoÅ¡o ya tlasana
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=MolaetÅ¡a: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Mokgobo: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Faele: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Mothaladi: {{line}}
rendering_error=Go diregile phoÅ¡o ge go be go gafelwa letlakala.

# Predefined zoom values
page_scale_width=Bophara bja letlakala
page_scale_fit=Go lekana ga letlakala
page_scale_auto=KgodiÅ¡o ya maitiriÅ¡o
page_scale_actual=Bogolo bja kgonthe
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=PhoÅ¡o
loading_error=Go diregile phoÅ¡o ge go hlahlelwa PDF.
invalid_file_error=Faele ye e sa Å¡omego goba e senyegilego ya PDF.
missing_file_error=Faele yeo e sego gona ya PDF.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Tlhaloso]
password_ok=LOKILE

printing_not_supported=TemoÅ¡o: Go gatiÅ¡a ga go thekgwe ke praosara ye ka botlalo.
printing_not_ready=TemoÅ¡o: PDF ga ya hlahlelwa ka botlalo bakeng sa go gatiÅ¡wa.
web_fonts_disabled=Difonte tÅ¡a wepe di Å¡itiÅ¡itÅ¡we: ga e kgone go diriÅ¡a difonte tÅ¡a PDF tÅ¡e khutiÅ¡itÅ¡wego.
