<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="inherit_attendance_device_configs" model="ir.ui.view">
        <field name="model">attendance.device</field>
        <field name="inherit_id" ref="to_attendance_device.view_device_record_form"/>
        <field name="arch" type="xml">
            <field name="unaccent_user_name" position="after">
                <field name="config_base"/>
            </field>
            <xpath expr="//page[@name='attendance_status_code']" position="attributes">
                <attribute name="attrs">{'invisible': [('config_base', '=', False)]}</attribute>
            </xpath>
            <button name="action_show_time" position="after">
                <button name="%(zktechno_sync_attendance_changes.action_update_date)d"
                        string="Set Time"
                        class="oe_highlight" type="action"/>
            </button>
            <button name="action_show_time" position="after">
                <button name="%(zktechno_sync_attendance_changes.action_enrollment_finger_print)d"
                        string="Enroll Finger"
                        class="oe_highlight" type="action"/>
                <button name="%(zktechno_sync_attendance_changes.action_delete_transfer)d"
                        string="Delete &amp; Transfer user"
                        context="{'current_device_id': id}"
                        class="oe_highlight" type="action"/>
            </button>
        </field>
    </record>
</odoo>