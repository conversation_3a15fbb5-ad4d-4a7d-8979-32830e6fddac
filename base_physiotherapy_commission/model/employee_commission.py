from odoo import fields, models
from datetime import datetime, time


class NuroDoctor(models.Model):
    _inherit = "hr.employee"

    product_commission_line_physio = fields.One2many("nuro.product.commission.line", "physio_employee_id",
                                                     string="Product Commission Line")
    overtime_commission = fields.Boolean('Overtime Commission', default=False)
    ot_from_time = fields.Float('From Overtime')
    ot_to_time = fields.Float('To Overtime')


class NuroFixedDoctorCommission(models.Model):
    _inherit = "nuro.fixed.doctor.commission"

    panel = fields.Selection(selection_add=[("physio", "Physio")])


class NuroProductCommissionLine(models.Model):
    _inherit = "nuro.product.commission.line"

    panel = fields.Selection(selection_add=[("physio", "Physio")])
    physio_employee_id = fields.Many2one("hr.employee", string="Employee #")
    physio_master_ids = fields.Many2many("physio.master", string="Physio Service Master")
    percentage_ot = fields.Float('OT %')


class NuroDoctorCommission(models.Model):
    _inherit = "nuro.doctor.commission"

    physio_id = fields.Many2one('physio.process')
    panel = fields.Selection(selection_add=[("physio", "Physio")])


class NuroMedicalPhysio(models.Model):
    _inherit = 'physio.process'

    pt_commission_create = fields.Boolean('Commission Created', default=False)
    pt_trainer_commission_create = fields.Boolean('Commission Created', default=False)

    @staticmethod
    def float_to_time(float_time):
        hours = int(float_time)
        minutes = int((float_time - hours) * 60)
        return time(hour=hours, minute=minutes)

    def create_physio_doctor_commission_employee(self, date=False):
        """Method to create Commission"""
        search_commission_product_wise = self.pt_employee_id.product_commission_line_physio
        fixed_commission_obj = self.env["nuro.fixed.doctor.commission"]
        search_commission = fixed_commission_obj.search(
            [("employee_id", "=", self.pt_employee_id.id), ("panel", "=", "physio")]
        )
        product_percentage = search_commission_product_wise.filtered(
            lambda commission: self.physio_master_id.id in commission.physio_master_ids.ids
        )
        doctor_commission_obj = self.env["nuro.doctor.commission"]
        if product_percentage:
            if self.pt_employee_id.overtime_commission:
                from_time = self.float_to_time(self.pt_employee_id.ot_from_time)
                to_time = self.float_to_time(self.pt_employee_id.ot_to_time)
                today_datetime = fields.Datetime.now()
                if date:
                    today_datetime = date
                current_time = fields.Datetime.context_timestamp(self, today_datetime).replace(tzinfo=None).time()
                request_datetime = fields.Datetime.context_timestamp(self, self.request_datetime).replace(tzinfo=None).time()
                if from_time <= current_time <= to_time and from_time <= request_datetime <= to_time and today_datetime.date() == self.request_datetime.date():
                    percentage = product_percentage.percentage_ot
                else:
                    percentage = product_percentage.percentage
            else:
                percentage = product_percentage.percentage
        else:
            percentage = search_commission.percentage or 0.0
        if (percentage > 0.0 and not self.pt_commission_create and self.request_id
                and self.request_id.billed_amount > 0.0 and self.pt_employee_id.commission_based_on == "service"):
            doctor_commission_obj.create(
                {
                    "name": self.name,
                    "physio_id": self.id,
                    "doctor_id": self.doctor_id and self.doctor_id.id or False,
                    "partner_id": self.pt_employee_id.address_home_id.id,
                    "employee_id": self.pt_employee_id.id,
                    "patient_id": self.patient_id.id,
                    "date": self.date,
                    "panel": "physio",
                    "object_name": self.request_id._name,
                    "object_id": self.request_id.id,
                    "accounting_date": self.request_id.accounting_date,
                    "service_name": self.physio_master_id.name,
                    "total_amount": self.request_id.unit_price,
                    "discount": self.request_id.discount_amount,
                    "net_amount": self.request_id.billed_amount,
                    "commission_rate": percentage,
                    "amount": self.request_id.billed_amount / 100 * percentage,
                }
            )
            self.pt_commission_create = True

    def create_physio_doctor_commission_employee_trainer(self, date=False):
        """Method to create Commission"""
        search_commission_product_wise = self.pt_employee_trainer_id.product_commission_line_physio
        fixed_commission_obj = self.env["nuro.fixed.doctor.commission"]
        search_commission = fixed_commission_obj.search(
            [("employee_id", "=", self.pt_employee_trainer_id.id), ("panel", "=", "physio")]
        )
        product_percentage = search_commission_product_wise.filtered(
            lambda commission: self.physio_master_id.id in commission.physio_master_ids.ids
        )
        doctor_commission_obj = self.env["nuro.doctor.commission"]
        if product_percentage:
            if self.pt_employee_trainer_id.overtime_commission:
                from_time = self.float_to_time(self.pt_employee_trainer_id.ot_from_time)
                to_time = self.float_to_time(self.pt_employee_trainer_id.ot_to_time)
                today_datetime = fields.Datetime.now()
                if date:
                    today_datetime = date
                current_time = fields.Datetime.context_timestamp(self, today_datetime).replace(tzinfo=None).time()
                request_datetime = fields.Datetime.context_timestamp(self, self.request_datetime).replace(tzinfo=None).time()
                if from_time <= current_time <= to_time and from_time <= request_datetime <= to_time and today_datetime.date() == self.request_datetime.date():
                    percentage = product_percentage.percentage_ot
                else:
                    percentage = product_percentage.percentage
            else:
                percentage = product_percentage.percentage
        else:
            percentage = search_commission.percentage or 0.0
        if (percentage > 0.0 and not self.pt_trainer_commission_create and self.request_id
                and self.request_id.billed_amount > 0.0
                and self.pt_employee_trainer_id.commission_based_on == "service"
                and self.pt_employee_trainer_id.id != self.pt_employee_id.id):
            doctor_commission_obj.create(
                {
                    "name": self.name,
                    "physio_id": self.id,
                    "doctor_id": self.doctor_id and self.doctor_id.id or False,
                    "partner_id": self.pt_employee_trainer_id.address_home_id.id,
                    "employee_id": self.pt_employee_trainer_id.id,
                    "patient_id": self.patient_id.id,
                    "date": self.date,
                    "panel": "physio",
                    "object_name": self.request_id._name,
                    "object_id": self.request_id.id,
                    "accounting_date": self.request_id.accounting_date,
                    "service_name": self.physio_master_id.name,
                    "total_amount": self.request_id.unit_price,
                    "discount": self.request_id.discount_amount,
                    "net_amount": self.request_id.billed_amount,
                    "commission_rate": percentage,
                    "amount": self.request_id.billed_amount / 100 * percentage,
                }
            )
            self.pt_trainer_commission_create = True

    def create_commission_record(self):
        """Correction Commission Record"""
        self.write({
            'pt_commission_create': False,
            'pt_trainer_commission_create': False,
        })
        if not self.request_datetime:
            message_id = self.env['mail.tracking.value'].search([
                ('mail_message_id.model', '=', 'physio.process'),
                ('mail_message_id.res_id', '=', self.id),
                ('old_value_char', '=', 'Not Paid'),
                ('new_value_char', '=', 'Paid'),
            ], limit=1)
            print(self.id)
            print(message_id)
            if message_id:
                self.request_datetime = message_id.write_date
            else:
                self.request_datetime = self.write_date
        self.create_physio_doctor_commission_employee(date=self.end)
        self.create_physio_doctor_commission_employee_trainer(date=self.end)

    def action_end_physio_process(self):
        """Action Complete Physio Process"""
        res = super().action_end_physio_process()
        self.create_physio_doctor_commission_employee()
        self.create_physio_doctor_commission_employee_trainer()
        return res
