# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from odoo.exceptions import UserError

from odoo import api, fields, models, _


# noinspection PyProtectedMember
class NuroVaccineCreditPayment(models.TransientModel):
    _inherit = 'nuro.vaccine.credit.payment'

    @api.model
    def default_get(self, vals):
        """
        Default Get for lab Insurance Cashier Wizard
        :param vals:
        :return:
        """
        res = super(NuroVaccineCreditPayment, self).default_get(vals)
        vaccine_id = self.env['nuro.cashier.vaccine'].browse(self.env.context.get('active_id'))
        if vaccine_id:
            res['vaccine_id'] = vaccine_id.id
            if vaccine_id.patient_id.patient_type == 'insurance' and \
                    vaccine_id.patient_id.insurance_line:
                res['bill_to_type'] = 'insurance'
                insurance_id = vaccine_id.patient_id.insurance_line[0]
                if insurance_id.insurance_cover == 'full_cover':
                    res['percentage'] = 100.0
                if insurance_id.insurance_cover == 'percentage':
                    res['percentage'] = insurance_id.percentage
        return res

    bill_to_type = fields.Selection(selection_add=[('insurance', 'Insurance')], default='patient',
                                    string='Bill To Type')
    cash_payment = fields.Boolean('Cash payment')
    percentage = fields.Float('Cover Percentage')
    patient_amount = fields.Float('Patient Amount')
    insurance_amount = fields.Float('Insurance Amount')

    @api.onchange('discount_amount')
    def onchange_discount_amount(self):
        """
        Onchange of Discount Amount Value Applied on lines
        :return:
        """
        res = super(NuroVaccineCreditPayment, self).onchange_discount_amount()
        if self.bill_to_type == 'insurance':
            self.patient_amount = self.amount / 100 * (100 - self.percentage)
            self.insurance_amount = self.amount / 100 * self.percentage
        return res

    @api.onchange('employee_id', 'bill_to_type')
    def onchange_employee_bill_to_type(self):
        """
        Onchange for the employee Id, Bill to Type and company Type to get the relevant partner to be billed by super
        :return:
        """
        if self.bill_to_type == 'insurance':
            # noinspection PyAttributeOutsideInit
            self.employee_id = False
            current_ins = self.vaccine_id.patient_id.insurance_line.filtered(
                lambda insurance: insurance.state == 'approved')
            if not current_ins:
                message_post = 'There no insurance for this patient'
                self.bill_to_type = False
                return {'warning': {'title': 'Insurance Not Available', "message": message_post}}
            else:
                # noinspection PyAttributeOutsideInit
                self.bill_to_user_id = current_ins[0].insurance_company_id.partner_id.id
                return {
                    'domain': {'bill_to_user_id': [('id', 'in', current_ins[0].insurance_company_id.partner_id.ids)]}}
        return super(NuroVaccineCreditPayment, self).onchange_employee_bill_to_type()
    
    def create_insurance_credit(self):
        """
        Create Insurance Payment
        :return:
        """
        default_methods = self.env['hms.methods.library']
        if not self.bill_to_user_id.is_credit_allow:
            raise UserError(_('Credit is not allowed for %s!!!') % self.bill_to_user_id.name)
        if not self.cash_payment:
            self.vaccine_id.patient_id.partner_id._check_utilized_limit(credit_amount=self.patient_amount)
        self.vaccine_id.insurance_partner_id = self.bill_to_user_id.id
        self.vaccine_id.insurance = True
        self.vaccine_id.percentage = self.percentage
        self.vaccine_id.patient_amount = self.patient_amount
        self.vaccine_id.insurance_amount = self.insurance_amount
        patient_amount = self.patient_amount
        insurance_amount = self.insurance_amount
        insurance_discount = self.discount_amount / 100 * self.percentage
        patient_discount = self.discount_amount / 100 * (100 - self.percentage)
        if insurance_amount > 0.0:
            inv_ids = self.vaccine_id.create_invoice(
                amount=round(insurance_amount, 2),
                discount_amount=insurance_discount,
                partner_id=self.bill_to_user_id,
                invoice_type='out_invoice'
            )
            inv_ids.action_post()
        if patient_amount > 0.0:
            inv_ids = self.vaccine_id.create_invoice(
                amount=round(patient_amount, 2),
                discount_amount=patient_discount,
                partner_id=self.vaccine_id.patient_id.partner_id,
                invoice_type='out_invoice'
            )
            inv_ids.action_post()
            if self.cash_payment and patient_amount > 0.0:
                inv_ids.cash_payment = True
                payment_method_ids = self.env['account.journal'].sudo().search([
                    ('type', '=', 'cash'),
                    ('cashier_journal', '=', True)
                ], limit=1)
                payment_method_id = default_methods.get_payment_method(payment_method_id=payment_method_ids)
                payment_account = payment_method_ids.default_credit_account_id.id
                payment_method_ids.sudo().write({'default_credit_account_id': self.env.user.account_id.id})
                # noinspection PyProtectedMember
                payments = self.vaccine_id.create_payment(
                    partner_id=self.vaccine_id.patient_id.partner_id,
                    amount=round(patient_amount, 2),
                    journal_id=payment_method_ids,
                    payment_method_id=payment_method_id,
                    payment_type='inbound',
                    invoice_ids=inv_ids
                )
                payments.cash_payment = True
                payments.post()
                payment_method_ids.sudo().write({'default_credit_account_id': payment_account})

    def create_credit_payment(self):
        """
        Appointment Credit Create
        :return:
        """
        res = super(NuroVaccineCreditPayment, self).create_credit_payment()
        if self.bill_to_type == 'insurance':
            self.create_insurance_credit()
            paid_amount = 0.0
            if self.cash_payment:
                paid_amount = self.patient_amount
            self.vaccine_id.write({
                'payment_method': 'credit',
                'state': 'send_to_lab',
                'discount_amount': self.discount_amount,
                'credit_amount': self.insurance_amount,
                'paid_amount': paid_amount
            })
        return res
