<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="res_partner_corporates_view" model="ir.ui.view">
            <field name="name">Corporates</field>
            <field name="model">res.partner</field>
            <field name="priority">35</field>
            <field name="arch" type="xml">
                <form string="Corporates">
                    <header>
                        <field name="registration_state" invisible="1"/>
                        <!--                        <button name="on_boarding_corporate" type="object" class="oe_highlight" string="On Boarding"-->
                        <!--                                attrs="{'invisible': [('registration_state', '!=', 'draft')]}"/>-->
                        <!--                        <button name="export_import_patient" type="object" class="oe_highlight" string="Patient On-Boarding"-->
                        <!--                                attrs="{'invisible': [('registration_state', '!=', 'registered')]}"/>-->
                    </header>
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger"
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="image_1920" widget="image" class="oe_avatar"
                               options="{'preview_image': 'image_128'}"/>
                        <div class="oe_button_box" name="button_box">
                            <button name="create_create_corporate_employee" type="object" string="Member"
                                    icon="fa-list">
                            </button>
                        </div>
                        <div class="oe_title">
                            <field name="is_company" invisible="1"/>
                            <field name="is_corporate" invisible="1"/>
                            <field name="commercial_partner_id" invisible="1"/>
                            <field name="active" invisible="1"/>
                            <field name="company_type" widget="radio" class="oe_edit_only" invisible="1"
                                   options="{'horizontal': true}"/>
                            <h1>
                                <field name="name" default_focus="1" placeholder="Name"
                                       attrs="{'required' : [('type', '=', 'contact')], 'readonly': [('registration_state', '!=', 'draft')]}"/>
                            </h1>
                            <div class="o_row">
                                <field name="parent_id" invisible="1" widget="res_partner_many2one"
                                       placeholder="Company" domain="[('is_company', '=', True)]"
                                       context="{'default_is_company': True, 'show_vat': True}"
                                       attrs="{'invisible': ['|', '&amp;', ('is_company','=', True),('parent_id', '=', False),('company_name', '!=', False),('company_name', '!=', '')]}"/>
                                <field name="company_name"
                                       attrs="{'invisible': ['|', '|', ('company_name', '=', False), ('company_name', '=', ''), ('is_company', '=', True)]}"/>
                                <button name="create_company" type="object" class="oe_edit_only btn-link"
                                        attrs="{'invisible': ['|', '|', ('is_company','=', True), ('company_name', '=', ''), ('company_name', '=', False)]}">
                                    <span class="fa fa-plus-square"/>
                                    Create company
                                </button>
                            </div>
                        </div>

                        <group>
                            <group>
                                <field name="type" groups="base.group_no_one"
                                       attrs="{'readonly': [('registration_state', '!=', 'draft')]}"/>
                                <label for="" name="address_name">
                                    <b attrs="{'invisible': [('type', '!=', 'contact')]}">Company Address</b>
                                    <b attrs="{'invisible': [('type', '!=', 'invoice')]}">Invoice Address</b>
                                    <b attrs="{'invisible': [('type', '!=', 'delivery')]}">Delivery Address</b>
                                    <b attrs="{'invisible': [('type', '!=', 'other')]}">Other Address</b>
                                    <b attrs="{'invisible': [('type', '!=', 'private')]}">Private Address</b>
                                    <b attrs="{'invisible': [('type', '!=', False)]}">Address</b>
                                </label>
                                <div class="o_address_format">
                                    <field name="street" placeholder="Street..." class="o_address_street"
                                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                    <field name="street2" placeholder="Street 2..." class="o_address_street"
                                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                    <field name="city" placeholder="City" class="o_address_city"
                                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                    <field name="state_id" class="o_address_state" placeholder="State"
                                           options="{'no_open': True}"
                                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"
                                           context="{'country_id': country_id, 'zip': zip}"/>
                                    <field name="zip" placeholder="ZIP" class="o_address_zip"
                                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                    <field name="country_id" placeholder="Country" class="o_address_country"
                                           options="{'no_open': True, 'no_create': True}"
                                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                </div>
                                <field name="corporate_identification_number" readonly="1"/>
                            </group>
                            <group>
                                <field name="function" placeholder="e.g. Sales Director"
                                       attrs="{'invisible': [('is_company','=', True)]}"/>
                                <field name="phone" widget="phone"
                                       attrs="{'readonly': [('registration_state', '!=', 'draft')]}"/>
                                <field name="mobile" widget="phone"
                                       attrs="{'readonly': [('registration_state', '!=', 'draft')]}"/>
                                <field name="user_ids" invisible="1"/>
                                <field name="email" widget="email" context="{'gravatar_image': True}"
                                       attrs="{'required': [('user_ids','!=', [])]}"/>
                                <field name="website" widget="url" placeholder="e.g. https://www.odoo.com"
                                       attrs="{'readonly': [('registration_state', '!=', 'draft')]}"/>
                                <field name="title" options="{'no_open': True}" placeholder="e.g. Mister"
                                       attrs="{'invisible': [('is_company', '=', True)]}"/>
                                <field name="active_lang_count" invisible="1"/>
                                <label for="lang" attrs="{'invisible': [('active_lang_count', '&lt;=', 1)]}"/>
                                <div class="o_row" attrs="{'invisible': [('active_lang_count', '&lt;=', 1)]}">
                                    <field name="lang"/>
                                    <button type="action" name="55" class="btn-sm btn-link mb4 fa fa-globe"
                                            aria-label="More languages" groups="base.group_system"
                                            title="More languages"/>
                                </div>
                                <field name="category_id" widget="many2many_tags"
                                       options="{'color_field': 'color', 'no_create_edit': True}"
                                       placeholder="Tags..."
                                       attrs="{'readonly': [('registration_state', '!=', 'draft')]}"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="sale_and_commission" string="Sales">
                                <group>
                                    <group name="sales_person_info" string="Sales Person information">
                                        <field name="user_id" options="{'no_create': True, 'no_open': True}"
                                               attrs="{'readonly': [('registration_state', '!=', 'draft')]}"/>
                                        <field name="registration_state" readonly="1" invisible="1"/>
                                        <!--                                        <field name="registration_charges" readonly="1" invisible="1"/>-->
                                        <!--                                        <field name="utilized_registration_charges" readonly="1" invisible="1"/>-->
                                    </group>
                                    <group name="sale_commission_info">

                                    </group>
                                </group>
                            </page>
                            <page name="panel_information_corporates" string="Offers &amp; Discount">
                                <!--                                <group>-->
                                <!--                                    <group string="Registration Type">-->
                                <!--                                        <field name="patient_registration_type" required="1"/>-->
                                <!--                                    </group>-->
                                <!--                                </group>-->

                            </page>
                            <page string="Credit Information">
                                <group>
                                    <field name="credit_limit"/>
                                    <field name="utilized_credit_limit"/>
                                    <field name="remaining_credit_limit"/>
                                </group>
                                <group>
                                    <field name="credit_expiry_date"/>
                                    <field name="responsible_person_id"/>
                                    <field name="credit_given_by_user_id"/>
                                    <field name="remark"/>
                                </group>
                            </page>
                            <!--                            <page name="related_partner" string="Related Patient">-->
                            <!--                                <field name="partner_line_ids" readonly="1">-->
                            <!--                                    <tree>-->
                            <!--                                        <field name="name"/>-->
                            <!--                                        <field name="corporate_identification_number"/>-->
                            <!--                                        <field name="dob"/>-->
                            <!--                                    </tree>-->
                            <!--                                </field>-->
                            <!--                            </page>-->
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="hms_corporates_company_list_view" model="ir.ui.view">
            <field name="name">Corporates</field>
            <field name="model">res.partner</field>
            <field name="priority">35</field>
            <field name="arch" type="xml">
                <tree string="Corporates Company">
                    <field name="display_name" string="Name"/>
                    <field name="function" invisible="1"/>
                    <field name="phone" optional="show"/>
                    <field name="email" optional="show"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="city"/>
                    <field name="state_id"/>
                    <field name="country_id"/>
                    <field name="vat"/>
                    <field name="user_id" invisible="1"/>
                    <field name="is_company" invisible="1"/>
                    <field name="parent_id" invisible="1"/>
                    <field name="active" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="hms_corporates_company_kanban_view" model="ir.ui.view">
            <field name="name">Corporates</field>
            <field name="model">res.partner</field>
            <field name="priority">35</field>
            <field name="arch" type="xml">
                <kanban class="o_res_partner_kanban">
                    <field name="id"/>
                    <field name="color"/>
                    <field name="display_name"/>
                    <field name="title"/>
                    <field name="email"/>
                    <field name="parent_id"/>
                    <field name="is_company"/>
                    <field name="function"/>
                    <field name="phone"/>
                    <field name="street"/>
                    <field name="street2"/>
                    <field name="zip"/>
                    <field name="city"/>
                    <field name="country_id"/>
                    <field name="mobile"/>
                    <field name="state_id"/>
                    <field name="category_id"/>
                    <field name="image_128"/>
                    <field name="type"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click o_kanban_record_has_image_fill o_res_partner_kanban">
                                <t t-if="!record.is_company.raw_value">
                                    <t t-if="record.type.raw_value === 'delivery'" t-set="placeholder"
                                       t-value="'/base/static/img/truck.png'"/>
                                    <t t-elif="record.type.raw_value === 'invoice'" t-set="placeholder"
                                       t-value="'/base/static/img/money.png'"/>
                                    <t t-else="" t-set="placeholder" t-value="'/base/static/img/avatar_grey.png'"/>
                                    <div class="o_kanban_image_fill_left d-none d-md-block"
                                         t-attf-style="background-image:url('#{kanban_image('res.partner', 'image_128', record.id.raw_value,  placeholder)}')">
                                        <img class="o_kanban_image_inner_pic" t-if="record.parent_id.raw_value"
                                             t-att-alt="record.parent_id.value"
                                             t-att-src="kanban_image('res.partner', 'image_128', record.parent_id.raw_value)"/>
                                    </div>
                                    <div class="o_kanban_image d-md-none"
                                         t-attf-style="background-image:url('#{kanban_image('res.partner', 'image_128', record.id.raw_value,  placeholder)}')">
                                        <img class="o_kanban_image_inner_pic" t-if="record.parent_id.raw_value"
                                             t-att-alt="record.parent_id.value"
                                             t-att-src="kanban_image('res.partner', 'image_128', record.parent_id.raw_value)"/>
                                    </div>
                                </t>
                                <t t-else="">
                                    <t t-set="placeholder" t-value="'/base/static/img/company_image.png'"/>
                                    <div class="o_kanban_image_fill_left o_kanban_image_full"
                                         t-attf-style="background-image: url(#{kanban_image('res.partner', 'image_128', record.id.raw_value, placeholder)})"
                                         role="img"/>
                                </t>
                                <div class="oe_kanban_details">
                                    <strong class="o_kanban_record_title oe_partner_heading">
                                        <field name="display_name"/>
                                    </strong>
                                    <div class="o_kanban_tags_section oe_kanban_partner_categories"/>
                                    <ul>
                                        <li t-if="record.parent_id.raw_value and !record.function.raw_value">
                                            <field name="parent_id"/>
                                        </li>
                                        <li t-if="!record.parent_id.raw_value and record.function.raw_value">
                                            <field name="function"/>
                                        </li>
                                        <li t-if="record.parent_id.raw_value and record.function.raw_value">
                                            <field name="function"/>
                                            at
                                            <field name="parent_id"/>
                                        </li>
                                        <li t-if="record.city.raw_value and !record.country_id.raw_value">
                                            <field name="city"/>
                                        </li>
                                        <li t-if="!record.city.raw_value and record.country_id.raw_value">
                                            <field name="country_id"/>
                                        </li>
                                        <li t-if="record.city.raw_value and record.country_id.raw_value"><field
                                                name="city"/>,
                                            <field name="country_id"/>
                                        </li>
                                        <li t-if="record.email.raw_value" class="o_text_overflow">
                                            <field name="email"/>
                                        </li>
                                    </ul>
                                    <div class="oe_kanban_partner_links"/>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="action_corporates_view_company" model="ir.actions.act_window">
            <field name="name">Corporates</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="domain">[('is_corporate', '=', True)]</field>
            <field name="context">{'default_is_corporate': True, 'default_company_type': 'company',
                'default_is_company': True, 'delete': False}
            </field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('hms_corporate.hms_corporates_company_kanban_view')}),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('hms_corporate.hms_corporates_company_list_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('hms_corporate.res_partner_corporates_view')})]"/>
        </record>

    </data>
</odoo>
