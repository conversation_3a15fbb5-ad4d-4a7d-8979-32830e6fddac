<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="nuro_emergency_payments_form" model="ir.ui.view">
            <field name="name">emergency.payments</field>
            <field name="model">emergency.payments</field>
            <field name="arch" type="xml">
                <form string="emergency.payments">
                    <group>
                        <group>
                            <field name="emergency_admission_id" readonly="1" invisible="1"/>
                            <field name="cash_payment_method_id" readonly="1" invisible="1"/>
                            <field name="patient_id" readonly="1"/>
                            <field name="credit_discharge" groups="nuro_cashier_closing.group_cashier_manager"
                                   invisible="1"/>
                        </group>
                        <group>
                            <field name="approved_discount" invisible="1"/>
                            <field name="total_amount" force_save="1"/>
                            <field name="partial_payment_amount"
                                   attrs="{'invisible': [('credit_discharge', '=', False)]}"/>
                            <field name="emergency_bill" force_save="1"
                                   attrs="{'invisible': ['|', ('credit_discharge', '!=', True), ('bill_to_type', '=', 'patient')]}"/>
                            <field name="discount_amount" force_save="1" attrs="{'readonly': [('approved_discount', '=', True)]}"/>
                            <field name="amount" force_save="1" attrs="{'invisible': [('credit_discharge', '=', True)]}"/>
                            <field name="balance_amount" force_save="1" attrs="{'invisible': [('credit_discharge', '!=', True)]}"/>
                            <field name="bill_to_type" attrs="{'invisible': [('credit_discharge', '!=', True)]}"/>
                            <field name="invoices"
                                   attrs="{'invisible': ['|', ('credit_discharge', '!=', True), ('bill_to_type', '=', 'patient')], 'required': [('credit_discharge', '=', True), ('bill_to_type', '!=', 'patient')]}"/>
                            <field name="employee_id"
                                   attrs="{'invisible':[('bill_to_type', 'not in', ('employee', 'hospital_employee'))], 'required':[('bill_to_type','in',('employee', 'hospital_employee'))]}"
                                   options='{"no_open": True, "no_create": True}'/>
                            <field name="description_hospital"
                                   attrs="{'invisible':[('bill_to_type','in', ('patient'))], 'required':[('bill_to_type','not in',('patient'))]}"/>
                            <field name="bill_to_user_id" options="{'no_create': True,'no_open': True}"
                                   attrs="{'readonly':[('bill_to_type', 'in', ('patient'))], 'invisible':['|', ('bill_to_type', 'in',('employee', 'hospital_employee')), ('credit_discharge', '!=', True)]}"
                                   force_save="1"/>
                            <field name="responsible_person_id" options="{'no_create': True,'no_open': True}"
                                   attrs="{'invisible': ['|', ('bill_to_type', '!=', 'patient'), ('credit_discharge', '!=', True)], 'required': [('bill_to_type', '=', 'patient'), ('credit_discharge', '=', True)]}"/>
                            <field name="due_date"
                                   attrs="{'invisible': ['|', ('bill_to_type', '!=', 'patient'), ('credit_discharge', '!=', True)], 'required': [('bill_to_type', '=', 'patient'), ('credit_discharge', '=', True)]}"/>
                            <field name="description"
                                   attrs="{'invisible': ['|', ('bill_to_type', '!=', 'patient'), ('credit_discharge', '!=', True)], 'required': [('bill_to_type', '=', 'patient'), ('credit_discharge', '=', True)]}"/>
                            <field name="expense_employee_amount"
                                   attrs="{'invisible': [('bill_to_type', '!=', 'hospital_employee')]}"/>
                            <field name="expense_patient_amount"
                                   attrs="{'invisible': [('bill_to_type', '!=', 'hospital_employee')]}"/>
                            <separator string="Discountable Amount Summary"/>
                            <field name="dams_total_amount" readonly="1"/>
                            <field name="dams_phs_amount" readonly="1"/>
                            <field name="discountable_amount" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="request_discount"/>
                            <field name="requested_to_id" options="{'no_open': True, 'no_create': True}"
                                   attrs="{'invisible': [('request_discount', '!=', True)], 'required': [('request_discount', '=', True)]}"/>
                        </group>
                        <group>
                            <button name="create_discount_request" type="object" string="Request Discount"
                                    class="oe_highlight" attrs="{'invisible': [('request_discount', '!=', True)]}"/>
                        </group>
                    </group>
                    <footer>
                        <button string="Cash Discharge" type="object"
                                name="cash_discharge_patient" class="oe_highlight"
                                attrs="{'invisible': [('credit_discharge', '=', True)]}"/>
                        <button string="Credit Discharge" type="object" name="credit_discharge_patient"
                                class="oe_highlight" attrs="{'invisible': [('credit_discharge', '!=', True)]}"
                                groups="nuro_cashier_closing.group_cashier_manager"/>
                        <button string="Close" class="btn-default oe_highlight" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>


        <record id="action_emergency_payments" model="ir.actions.act_window">
            <field name="name">emergency.payments</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.payments</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>