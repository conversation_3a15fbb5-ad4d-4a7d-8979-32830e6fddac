<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form View -->
        <record id="view_payment_commission_wizard_form" model="ir.ui.view">
            <field name="name">payment.commission.wizard.form</field>
            <field name="model">payment.commission.wizard</field>
            <field name="arch" type="xml">
                <form string="Payment Commission">
                    <sheet>
                        <div class="oe_title">
                            <h1>Payment Commission</h1>
                        </div>
                        <group>
                            <group>
                                <field name="from_date" required="1"/>
                                <field name="to_date" required="1"/>
                            </group>
                            <group>
                                <field name="employee_id"/>
                                <field name="payment_count" readonly="1"/>
                            </group>
                        </group>
                        <div class="alert alert-info" role="alert" attrs="{'invisible': [('payment_count', '=', 0)]}">
                            <p>This will process <field name="payment_count" readonly="1"/> payment(s) and create commission records for users based on their contract settings.</p>
                        </div>
                        <div class="alert alert-warning" role="alert" attrs="{'invisible': [('payment_count', '>', 0)]}">
                            <p>No payments found matching the criteria.</p>
                        </div>
                    </sheet>
                    <footer>
                        <button name="create_user_commission_record" string="Create Commission" type="object"
                                class="oe_highlight" attrs="{'invisible': [('payment_count', '=', 0)]}"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Action -->
        <record id="action_payment_commission_wizard" model="ir.actions.act_window">
            <field name="name">Payment Commission</field>
            <field name="res_model">payment.commission.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
