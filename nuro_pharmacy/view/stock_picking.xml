<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="stock_picking_from_view_inherit" model="ir.ui.view">
            <field name="name">Stock Picking</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='button_scrap']" position="attributes">
                    <attribute name="groups">stock.group_stock_manager</attribute>
                </xpath>

                <xpath expr="//button[@name='action_cancel']" position="attributes">
                    <attribute name="groups">stock.group_stock_manager</attribute>
                </xpath>

                <xpath expr="//button[@name='%(stock.act_stock_return_picking)d']" position="attributes">
                    <attribute name="groups">base.group_system</attribute>
                </xpath>

                <xpath expr="//field[@name='move_line_ids_without_package']" position="attributes">
                    <attribute name="attrs">{'readonly': [('state', '=', 'done')]}</attribute>
                </xpath>

                <xpath expr="//field[@name='move_ids_without_package']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', ('show_operations', '=', False), ('state', 'in',
                        ('cancel', 'done'))]}
                    </attribute>
                </xpath>

                <xpath expr="//field[@name='move_ids_without_package']" position="before">
                    <field name="sale_id" invisible="1"/>
                </xpath>

                <xpath expr="//field[@name='move_ids_without_package']//tree//field[@name='product_uom_qty']"
                       position="attributes">
                    <attribute name="attrs">{'readonly': ['|', '|', ('show_operations', '=', False), ('state', 'in',
                        ('cancel', 'done')), ('parent.sale_id', '!=', False)]}
                    </attribute>
                </xpath>

            </field>
        </record>

    </data>
</odoo>