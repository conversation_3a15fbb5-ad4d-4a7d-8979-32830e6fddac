# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo.exceptions import UserError
from datetime import datetime, timedelta


from odoo import api, fields, models, _

FETUS = [
    ('correct', 'Correct'),
    ('occiput_cephalic_posterior', 'Occiput / Cephalic Posterior'),
    ('frank_breech', '<PERSON>'),
    ('complete_breech', 'Complete Breech'),
    ('transverse_lie', 'Transverse Lie'),
    ('footling_breech', 'Footling Breech'),
]

LOCHIA_AMOUNT = [
    ('normal', 'Normal'),
    ('abundant', 'Abundant'),
    ('hemorrhage', 'Hemorrhage'),
]

LOCHIA_COLOR = [
    ('rubra', 'Rubra'),
    ('serosa', 'Serosa'),
    ('alba', 'Alba'),
]

LOCHIA_ODOR = [
    ('normal', 'Normal'),
    ('offensive', 'Offensive'),
]

DECISION = [
    ('yes', 'Yes'),
    ('no', 'No')
]

LIVE_STATUS = [
    ('dead', 'Dead'),
    ('alive', 'Alive')
]

STATE = [
    ('draft', 'Draft'),
    ('initiated', 'Initiated'),
    ('cashier_request', 'Cashier Request'),
    ('payment_done', 'Payment Done'),
    ('doctor_request', 'Doctor Request'),
    ('send_to_ipd', 'Send To IPD'),
    ('admitted', 'Admitted'),
    ('done', 'Done'),
    ('cancel', 'Cancel'),
]

PATIENT_STATE = [
    ('new', 'New'),
    ('labor', 'IN Labor'),
    ('delivery', 'Delivery'),
    ('done', 'Done')
]

LABOR_MODE = [
    ('normal', 'Normal'),
    ('induced', 'Induced'),
    ('c-section', 'C-section'),
]

DISCHARGE_TYPE = [
    ('refer', 'Transferred'),
    ('home', 'Home'),
    ('medical_advice', 'Against Medical Advice'),
    ('died', 'Died'),
    ('absconded', 'Absconded'),
    ('admitted', 'Admitted')
]

TYPE = [
    ('1', 'Single'),
    ('2', 'Twins'),
    ('3', 'Triple'),
    ('4', 'Quadruple'),
]


class NuroMedicalGynecology(models.Model):
    _name = "nuro.medical.gyneco"
    _description = "Gynecology Management"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id DESC'

    name = fields.Char(string='Internal Code', size=128, store=True, required=True,
                       readonly=True, default='/')
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    delivery_done = fields.Selection([('no', 'NO'), ('yes', 'YES')], string='Delivery Done', tracking=True,
                                     copy='False')
    gravida_number = fields.Integer(string='Gravida #')
    abortion = fields.Boolean(string='Abortion')
    date = fields.Date(string="Date", default=fields.Date.today)
    abortion_reason = fields.Char(string='Abortion Reason', size=128)
    admission_date = fields.Datetime(string='Admission Date', help="Date when she was admitted to give birth")
    prenatal_evaluations = fields.Integer(string='# of Visit to Doctor',
                                          help="Number of visits to the doctor during pregnancy")
    labor_mode = fields.Selection(LABOR_MODE, string='Labor Starting Mode')
    gestational_weeks = fields.Integer(string='Gestational Weeks')
    gestational_days = fields.Integer(string='Gestational Days')
    fetus_presentation = fields.Selection(FETUS, string='Fetus Presentation')
    episiotomy = fields.Boolean(string='Episiotomy')
    vaginal_tearing = fields.Boolean(string='Vaginal Tearing')
    forceps = fields.Boolean(string='Use of Forceps')
    perinatal_ids = fields.One2many('nuro.medical.perinatal.monitor', 'gyneco_id', string='Perinatal')
    puerperium_ids = fields.One2many('nuro.medical.puerperium.monitor', 'gyneco_id', string='Puerperium')
    dismissed = fields.Datetime(string='Dismissed from Hospital')
    died_at_delivery = fields.Boolean(string='Died at Delivery Room')
    died_at_the_hospital = fields.Boolean(string='Died at the Hospital')
    died_being_transferred = fields.Boolean(string='Died being Transferred',
                                            help="The mother died being transferred to another health institution")
    notes = fields.Text(string='Notes')
    patient_id = fields.Many2one('nuro.patient', string='Patient', ondelete='cascade', required=True, domain=[('deceased', '!=', True)])
    identification_code = fields.Char(string='ID#', related='patient_id.identification_code', store=True)
    appointment_id = fields.Many2one("nuro.appointment", string="Appointment #")
    doctor_id = fields.Many2one(comodel_name="nuro.doctor", string="Doctor", tracking=True)
    delivery_request_id = fields.Many2one("nuro.surgery.entry", string="Delivery Request #")
    delivery_request_ids = fields.One2many("nuro.surgery.entry", "gyne_id", string="Delivery Request List",
                                           readonly=True)
    delivery_surgery_ids = fields.One2many("nuro.surgery", "gyne_id", string="Delivery List")
    appointment_ids = fields.One2many("nuro.appointment", "gyne_id", string="Appointment ID#")
    delivery_count = fields.Integer(string="Delivery Count", compute='_delivery_count')
    newborn_ids = fields.One2many("nuro.patient", "gyne_id", string="New Born List", )
    newborn_count = fields.Integer(string="Newborn Count", compute='_newborn_count')
    state = fields.Selection(STATE, default='draft', tracking=True)
    state_patient = fields.Selection(PATIENT_STATE, default='new', compute='_compute_patient_state')
    treatment_gyne = fields.Text('Treatment Gyne')
    investigation = fields.Text('Investigation')
    initiated_datetime = fields.Datetime('Initiated Datetime')
    discharged_datetime = fields.Datetime('Discharged Datetime')
    discharge_type = fields.Selection(DISCHARGE_TYPE, string="Discharged Type")
    refer_hospital = fields.Char('Refer To')
    refer_reason = fields.Char('Reason for Referral')
    dod = fields.Date(string='Date of Death')
    cod = fields.Char(string='Cause of Death')
    admission_condition = fields.Text(string='Condition Before Admission')
    admission_physical_condition = fields.Text(string='Physical Examination on admission')
    final_diagnosis = fields.Text(string='Final Diagnosis')
    condition_on_discharge = fields.Text(string='Patient Condition on Discharge')
    followup_advice = fields.Text(string='Follow UP Advice')
    discharge_medication_line = fields.One2many('discharge.medication', 'gyne_id', string='Discharge Medication')
    discharge_sale_id = fields.Many2one('sale.order')
    # IPD fields
    inpatient_id = fields.Many2one("nuro.inpatient", string="IPD #", )
    ipd_request_id = fields.Many2one("nuro.inpatient.request", string="IPD Request #", )
    hiv_council = fields.Selection(DECISION, string='HIV Counselled')
    hiv_referred = fields.Selection(DECISION, string='HIV Testing Reffered')
    treatment = fields.Char('Treatment')
    outcome_mother = fields.Selection(LIVE_STATUS, string='Outcome Mother')
    parity_det = fields.Integer('Parity')
    placenta_type = fields.Selection(DECISION, string='Placenta Complete')
    treatment_plan = fields.Text('Treatment Plan')
    invoice_line_ids = fields.One2many('account.move', 'gyne_id', string='Invoice Line IDS')
    invoice_amount = fields.Float('Invoice Amount', compute='_amount_invoice_bill')
    delivery_type = fields.Selection(TYPE, string='Delivery')
    delivery_processed = fields.Boolean('Delivery Processed', default=False)
    delivery_date = fields.Datetime('Delivery Date and Time')
    cause = fields.Char('Cause')
    perinatal = fields.Selection([('yes', 'Yes'), ('no', 'No')], string='Perinatal')
    comment = fields.Text('Comment')
    placenta = fields.Selection([
        ('complete', 'Complete'),
        ('need', 'Need Intervention')
    ], string='Placenta')
    comments = fields.Text('Comment')
    estimated_blood_loss = fields.Selection([
        ('less', 'Less 500'),
        ('ml', '500ml - 1000ml'),
        ('above', 'Above1000ml')
    ], string='Estimated Blood Loss')

    # OBS Gyno
    chief_complaint = fields.Text(string='Chief Complain')
    family_history = fields.Text(string='Family History')
    past_medical_history = fields.Text(string='Past Medical History')
    drug_history = fields.Text(string='Drug History')
    allergies = fields.Text(string='Allergies')
    chif_complaint_obs = fields.Text(string='CHIEF COMPLAINT')
    g_obs = fields.Text(string='G')
    p_obs = fields.Text(string='P')
    one_obs = fields.Char(string='1')
    two_obs = fields.Char(string='2')
    three_obs = fields.Char(string='3')
    four_obs = fields.Char(string='4')
    five_obs = fields.Char(string='5')
    six_obs = fields.Char(string='6')
    seven_obs = fields.Char(string='7')
    eight_obs = fields.Char(string='8')
    nine_obs = fields.Char(string='9')
    ten_obs = fields.Char(string='10')
    obstetrical_history = fields.Char('Obstetrical History')
    gynecological_history = fields.Char('Gynecological History')
    surgery_history = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Surgery History')
    surgery_history_comment = fields.Char('Surgery History Comment')
    contraception = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Contraception')
    contraception_comment = fields.Char('Contraception Comment')
    anc = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='ANC')
    anc_comment = fields.Char('ANC Comment')
    tt = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='TT')
    tt_comment = fields.Char('TT Comment')
    prev_cs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Previous C/S')
    prev_cs_comment = fields.Text('Prev CS Comment')
    abortion_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Abortion')
    abortion_obs_comment = fields.Text('Abortion Comment')
    iufd_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='IUFD')
    iufd_obs_comment = fields.Text('IUFD Comment')
    dm_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='DM')
    dm_obs_comment = fields.Text('DM Comment')
    htn_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='HTN')
    htn_obs_comment = fields.Text('HTN Comment')
    dvt_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='DVT')
    dvt_obs_comment = fields.Text('DVT Comment')
    pph_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='PPH')
    pph_obs_comment = fields.Text('PPH Comment')
    others_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='OTHERS')
    others_obs_comment = fields.Text('Others Comment')
    lmp_obs = fields.Text(string='LMP')
    edd_obs = fields.Text(string='EDD')
    ga_obs = fields.Text(string='GA')
    trimester = fields.Selection([('First', 'First'), ('Second and Third', 'Second and Third')], string='Trimester')
    nausea_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Nausea')
    nausea_obs_comment = fields.Text('Nausea Comment')
    vomiting_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Vomiting')
    vomiting_obs_comment = fields.Text('Vomiting Comment')
    bleeding_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Bleeding')
    bleeding_obs_comment = fields.Text('Bleeding Comment')
    lap_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Lower Abdominal Pain')
    lap_obs_comment = fields.Text('Lap Comment')
    discharge_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Discharge')
    discharge_obs_comment = fields.Text('Discharge Comment')
    itching_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Itching')
    itching_obs_comment = fields.Text('Itching Comment')
    fever_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Fever')
    fever_obs_comment = fields.Text('Fever Comment')
    bleeding_2_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Bleeding')
    bleeding_2_obs_comment = fields.Text('Bleeding Comment')
    fetal_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Fetal Movement')
    fetal_obs_comment = fields.Text('Fetal Comment')
    lap_2_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Lower Abdominal Pain')
    lap_2_obs_comment = fields.Text('Lap Comment')
    discharge_2_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Discharge')
    discharge_2_obs_comment = fields.Text('Discharge Comment')
    itching_2_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Itching')
    itching_2_obs_comment = fields.Text('itching Comment')
    vaccine_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Vaccination')
    vaccine_obs_comment = fields.Text('vaccine Comment')
    water_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Water Breaking')
    water_obs_comment = fields.Text('Water Comment')
    contraction_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Contraction')
    contraction_obs_comment = fields.Text('Contraction Comment')
    swelling_obs = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Swelling')
    swelling_obs_comment = fields.Text('Swelling Comment')
    ph_obs = fields.Text(string='Past Medical History')
    drg_obs = fields.Text(string='Drug History')
    family_obs = fields.Text(string='Family History')
    pulse_obs = fields.Char(string='PULSE')
    pb_obs = fields.Char(string='PB')
    temp_obs = fields.Char(string='TEMP')
    rr_obs = fields.Char(string='RR')
    fundal_obs = fields.Char(string='Fundal Level')
    presentation_obs = fields.Char(string='Presentation')
    fhr_obs = fields.Char(string='FHR')
    dilation_obs = fields.Char(string='Dilation')
    length_obs = fields.Char(string='Length')
    consistency_obs = fields.Char(string='Consistency')
    comments_obs = fields.Text(string='Comments')
    diagnoses_obs = fields.Text(string='Diagnosis')
    discharge_summery = fields.Text(string='Discharge Summery')
    status_discharged = fields.Selection([('delayed', 'Delayed'), ('on_time', 'On Time')], string="Discharge Status",
                                         compute="_compute_status_discharge", store=True)
    delay_reason_id = fields.Many2one('mat.eme.delay.reason', string='Delay Reason')
    other = fields.Boolean(related='delay_reason_id.other', string='Other', store=True)
    delay_reason = fields.Char('Delay Reason')
    care_taker = fields.Char(string='Care Taker')
    child_ids = fields.Many2many('nuro.doctor', 'maternity_doctor_rel', 'gyne_id', 'doctor_id',
                                 string='Assistant Doctor', related='doctor_id.child_ids')
    doctor_assistant_id = fields.Many2one('nuro.doctor', string='Assistant')
    overall_comments = fields.Text('Comment')
    doctor_history_change_line = fields.One2many('doctor.history.change', 'medical_gyneco_id',
                                                 string='Doctor History Line')
    discharge_with_credit = fields.Boolean('Discharge With Credit', default=False)
    all_new_born = fields.Integer('All Newborns', compute='_compute_all_newborn')

    def validation_restriction_maternity(self):
        """Validation Restriction Emergency"""
        if self.env.company.max_maternity_restriction:
            allowed_days = self.env.company.max_maternity_allowed_days
            maternity_query = """
                                SELECT 
                                    id, initiated_datetime, CURRENT_DATE AS current_date, 
                                    CURRENT_DATE - initiated_datetime::date AS days_difference
                                FROM nuro_medical_gyneco where state = 'initiated' 
                                group by id, initiated_datetime 
                                having CURRENT_DATE - initiated_datetime::date > %s limit 1
                                """ % allowed_days
            self.env.cr.execute(maternity_query)
            result = self.env.cr.fetchall()
            record_details = [i[0] for i in result]
            if record_details:
                raise UserError(_('There are Patients admitted to the Maternity for more than the allowed %s days should be discharged.') % allowed_days)

    def _compute_all_newborn(self):
        """Compute All Newborn"""
        self.ensure_one()
        self.all_new_born = self.env['nuro.patient'].search_count([('related_mother_id', '=', self.patient_id.id)])


    def get_diagnosis_name(self):
        """Get Diagnosis Name"""
        diagnosis_name = ', '.join(self.disease_ids.mapped('name'))
        return diagnosis_name

    def create_new_born_baby_invoice_return(self):
        """
        Create refund Invoice
        :return:
        """
        if not self.invoice_line_ids:
            raise UserError(_('There is nothing to refund'))
        if len(self.invoice_line_ids) == 2:
            raise UserError(_('Invoice has been return already.'))
        invoice_id = self.invoice_line_ids[0]
        account_move_reversal = self.env['account.move.reversal']
        move_dict = {
            'move_id': invoice_id.id,
            'date': fields.Date.today()
        }
        context = {
            "active_id": invoice_id.id,
        }
        reversal = account_move_reversal.with_context(context).create(move_dict)
        invoice = reversal.reverse_moves()
        ret_invoice_id = self.env[invoice['res_model']].browse(invoice['res_id'])
        ret_invoice_id.action_post()
        return_line_receivable_id = ret_invoice_id.line_ids.filtered(
            lambda x: x.account_internal_type == 'receivable' and not x.reconciled)
        invoice_line_receivable_id = invoice_id.line_ids.filtered(
            lambda x: x.account_internal_type == 'receivable' and not x.reconciled)
        moves = return_line_receivable_id + invoice_line_receivable_id
        moves.reconcile()

    def _amount_invoice_bill(self):
        """
        Compute Billing Amount
        """
        for rec in self:
            rec.invoice_amount = sum(rec.invoice_line_ids.filtered(lambda x: x.partner_id.id == self.patient_id.partner_id.id).mapped('amount_residual'))

    def _create_sale_order(self, order_line):
        """
        Create Sale Order Vals
        :return:
        """
        pharmacy_sale_vals = {
            'patient_id': self.patient_id.id,
            'partner_id': self.patient_id.partner_id.id,
            'age': self.patient_id.age,
            'identification_code': self.patient_id.identification_code,
            'gender': self.patient_id.gender,
            'mobile': self.patient_id.mobile,
            'doctor_id': self.doctor_id.id,
            'gyne_id': self.id,
            'pharmacy_sale': True,
            'order_line': order_line,
        }
        return pharmacy_sale_vals

    def create_pharmacy_request(self):
        """
        Create Pharmacy Request
        :return:
        """
        order_line = []
        for line in self.discharge_medication_line.filtered(lambda x: x.state == 'draft'):
            vals = line._create_sale_order_line_vals()
            order_line.append((0, 0, vals))
        if order_line:
            order_vals = self._create_sale_order(order_line=order_line)
            sale_id = self.env['sale.order'].create(order_vals)
            self.discharge_sale_id = sale_id.id
        if self.discharge_sale_id:
            return self.discharge_sale_id.print_pharmacy_doctor_receipt()
        else:
            raise UserError(_('There is no Pharmacy Requested.!!!'))


    def create_payment(self, partner_id, amount, discount, journal_id, payment_method_id, invoice_ids):

        payment_vals = {
            'amount': round(amount, 2),
            'discount': round(discount, 2),
            'journal_id': journal_id.id,
            'payment_method_id': payment_method_id,
            'invoice_payment_type': 'automated',
            'currency_id': invoice_ids.currency_id.id,
            'payment_type': 'inbound',
            'partner_id': partner_id.id,
            'partner_type': 'customer',
            'communication': ' '.join([ref for ref in invoice_ids.mapped('ref') if ref]),
            'invoice_ids': [(6, 0, invoice_ids.ids)]
        }
        ctx = {'active_model': self._name, 'active_id': self.id}
        payment = self.env['account.payment'].with_context(ctx).create(payment_vals)
        return payment


    @api.depends('state')
    def _compute_status_discharge(self):
        """
        Depends Stats
        :return:
        """
        for rec in self:
            maximum_allowed_hrs = self.env.company.maximum_maternity_allowed_hr
            if rec.initiated_datetime and rec.discharged_datetime:
                diff = rec.discharged_datetime - rec.initiated_datetime
                diff_time = diff.total_seconds()
                total_diff = divmod(diff_time, 3600)[0]
                if total_diff <= maximum_allowed_hrs:
                    rec.status_discharged = 'on_time'
                if total_diff > maximum_allowed_hrs:
                    rec.status_discharged = 'delayed'
            else:
                rec.status_discharged = False

    def open_change_doctor_wizard(self):
        """
        Change Doctor
        :return:
        """
        if self.state != 'initiated':
            raise UserError(_('You Can only change the doctor if stage is initiated.!!!'))
        action = self.env.ref('nuro_maternity.action_change_doctor_view_maternity').read()[0]
        action['context'] = {'default_gyne_id': self.id}
        return action

    def change_doctor_gyne_details(self, doctor_id):
        """
        Change Doctor ID
        :param doctor_id:
        :return:
        """
        if self.state == 'initiated':
            self.doctor_id = doctor_id.id
        else:
            raise UserError(_('You are not allowed to change doctor if current stage is not initiated.!!!'))

    def change_doctor_assistant_gyne_details(self, doctor_id):
        """
        Change Doctor ID
        :param doctor_id:
        :return:
        """
        if self.state == 'initiated':
            self.doctor_assistant_id = doctor_id.id
        else:
            raise UserError(_('You are not allowed to change Assistant if current stage is not initiated.!!!'))

    def reopen_record(self):
        """
        Reopen Record
        :return:
        """
        company_id = self.env.company
        days = company_id.emergency_reopen_days
        if not self.discharged_datetime:
            mail_message = self.env['mail.message'].search([
                ('model', '=', self._name),('res_id', '=', self.id)], order='id DESC', limit=1)
            self.discharged_datetime = mail_message.date
        date = self.discharged_datetime + timedelta(days=days)
        today_date = datetime.today()
        if date > today_date :
            if self.state != 'done':
                raise UserError(_('Document Has been Processed already.!!!'))
            self.state = 'initiated'
        else:
            raise UserError(_('You can not reopen after configure days!!!'))
    def action_labetst_result_view(self):
        """
        Method to Create Labtest Entry
        :return:
        """
        domain = [('patient_id', '=', self.patient_id.id), ('parent_id', '=', False)]
        return {
            'name': _('Labtest'),
            'domain': domain,
            'res_model': 'nuro.medical.labtest.result',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'view_type': 'form',
            'context': {'create': False, 'edit': False}
        }

    def action_imaging_result_view(self):
        """
        Method to Get Imaging Entry
        :return:
        """
        domain = [('patient_id', '=', self.patient_id.id), ('state', '!=', 'cancelled')]
        return {
            'name': _('Imaging'),
            'domain': domain,
            'res_model': 'nuro.imaging.test',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'view_type': 'form',
            'context': {'create': False, 'edit': False},
        }

    def action_view_patient_surgery(self):
        """
        Surgery For the Partners
        :return:
        """
        self.ensure_one()
        action = self.env.ref('nuro_surgery.action_surgery_all_new_surgery_view').read()[0]
        action['domain'] = [('patient_id', '=', self.patient_id.id)]
        doctor_user = self.env.user.has_group('nuro_hms_groups.group_doctor_user')
        edit = False
        if doctor_user:
            edit = True
        action['context'] = {
            'create': False,
            'edit': edit
        }
        return action

    @api.constrains('patient_id', 'state')
    def constrains_duplicate_request(self):
        """
        Constrains on duplicate request
        :return:
        """
        duplicate_record_id = self.search([
            ('patient_id', '=', self.patient_id.id),
            ('id', '!=', self.id),
            ('state', 'in', ('draft', 'initiated', 'cashier_request')),
        ])
        if duplicate_record_id:
            raise UserError(_('Duplicate Record Exist.!!!'))

    def print_discharge_request(self):
        """
        Maternity Discharge Request
        :return:
        """
        return self.env.ref('nuro_maternity.action_maternity_discharge_receipt').report_action(self)

    def print_discharge_card_request(self):
        """
        Blood Transfusion Receipt printing
        :return:
        """
        return self.env.ref('nuro_maternity.maternity_discharge_card_discharge_receipt').report_action(self)

    def process_initiated(self):
        """
        Process Initiated
        :return:
        """
        if self.state != 'draft':
            raise UserError(_('Record has been processed already.!!!'))
        self.initiated_datetime = fields.Datetime.now()
        self.state = 'initiated'

    def discharge_now(self):
        """
        Discharge Now
        :return:
        """
        if self.state != 'payment_done':
            raise UserError(_('Document has been processed already.!!!'))
        self.write({'state': 'done', 'discharged_datetime': fields.Datetime.now()})

    @api.model
    def create(self, vals):
        """Create Method Super call"""
        sequence = self.env['ir.sequence'].next_by_code('nuro.medical.gyneco')
        vals['name'] = sequence or '/'
        # res = super().create(vals)
        # for rec in res:
        #     rec.validation_restriction_maternity()
        return super().create(vals)

    # def write(self, vals):
    #     """Write Super Call"""
    #     res = super().write(vals)
    #     for rec in self:
    #         rec.validation_restriction_maternity()
    #     return res

    def action_cancel(self):
        '''
        method to cancel gyneco request if its send by mistake from appointment.
        It will cancel only new state records
        :return:
        '''
        if self.state != 'draft':
            raise UserError(_('You can cancel only draft request !'))
        self.write({'appointment_id': False, 'state': 'cancel'})

    def _newborn_count(self):
        """
        New Born Count
        :return:
        """
        for rec in self:
            if rec.newborn_ids:
                rec.newborn_count = len(rec.newborn_ids)
            else:
                rec.newborn_count = 0
        return True

    def _delivery_count(self):
        """
        Delivery Count
        :return:
        """
        for rec in self:
            rec.delivery_count = len(rec.delivery_surgery_ids)

    def view_other_procedure_tree(self):
        """
        View OP Procedure Tree
        :return:
        """
        ctx = self._context.copy()
        for record in self:
            ctx.update({'default_patient_id': record.patient_id.id,
                        'default_gyne_id': record.id,
                        'default_doctor_id': record.doctor_id.id})
        return {
            'name': _('View OP Procedure Tree'),
            'view_type': 'form',
            'view_mode': 'tree,form',
            'domain': [('gyne_id', '=', (self.id))],
            'res_model': 'nuro.op.entry',
            'type': 'ir.actions.act_window',
            'context': ctx,
            'target': 'current'
        }

    def view_lab_test_tree(self):
        """
        View Lab Tree
        :return:
        """
        ctx = self._context.copy()
        for record in self:
            ctx.update({'default_patient_id': record.patient_id.id,
                        'default_gyne_id': record.id,
                        'default_doctor': record.doctor_id.id})
        return {
            'name': _('View Lab Test Tree'),
            'view_type': 'form',
            'view_mode': 'tree,form',
            'domain': [('gyne_id', '=', (self.id))],
            'res_model': 'nuro.lab.entry',
            'type': 'ir.actions.act_window',
            'context': ctx,
            'target': 'current'
        }

    def view_imaging_test_tree(self):
        """
        View Imaging Tree
        :return:
        """
        ctx = self._context.copy()
        for record in self:
            ctx.update({'default_patient_id': record.patient_id.id,
                        'default_gyne_id': record.id,
                        'default_doctor_id': record.doctor_id.id})
        return {
            'name': _('View Imaging Entry Tree'),
            'view_type': 'form',
            'view_mode': 'tree,form',
            'domain': [('gyne_id', '=', (self.id))],
            'res_model': 'nuro.imaging.entry',
            'type': 'ir.actions.act_window',
            'context': ctx,
            'target': 'current'
        }

    @api.depends('state')
    def _compute_patient_state(self):
        """
        Compute state
        :return:
        """
        for rec in self:
            if rec.state == 'draft':
                rec.state_patient = 'new'
            elif rec.state in ('admitted', 'doctor_request'):
                rec.state_patient = 'labor'
            elif rec.state == 'send_to_ipd':
                rec.state_patient = 'delivery'
            else:
                rec.state_patient = 'done'

    # def action_set_to_done(self):
    #     """
    #     Action Set to be Done
    #     :return:
    #     """
    #     action = self.env.ref('nuro_maternity.action_nuro_discharge_request_mat_eme').read()[0]
    #     action['context'] = {'default_gyne_id': self.id}
    #     return action

    # def action_set_to_done_primary(self):
    def action_set_to_done(self):
        """
        Set to Done
        :return:
        """
        if not self.discharge_type:
            raise UserError(_('Please Update Discharge Summery..!!!!'))
        if self.newborn_count > 0.0:
            self.delivery_done = 'yes'
        if self.delivery_done == 'yes' and self.newborn_count == 0:
            raise UserError(_('Please Add New Born.!!!'))
        if self.discharge_type == 'died':
            self.patient_id.write({
                'deceased': True,
                'dod': self.dod,
                'cod': self.cod,
                'record_name': self.name,
            })
        if not self.delivery_done:
            raise UserError(_('Please add delivery before processing.!!!'))
        residual = sum(self.invoice_line_ids.mapped('amount_residual_signed'))
        if residual > 0.0:
            self.state = 'cashier_request'
            self.discharged_datetime = fields.Datetime.now()
            return self.print_discharge_request()
        self.write({'state': 'payment_done', 'discharged_datetime': fields.Datetime.now()})
        return self.print_discharge_request()

    def open_payment_process_invoice(self):
        """Open Payment Process Invoice"""
        if self.state != 'cashier_request':
            raise UserError(_('Record has been processed already.!!!'))
        if self.discharge_with_credit:
            if not self.patient_id.partner_id.is_credit_allow:
                raise UserError(_('Credit is not allowed for %s!!!') % self.patient_id.partner_id.name)
            self.state = 'payment_done'
            return True
        query = """
                SELECT sum(line.debit-line.credit) as balance
                FROM account_move_line line LEFT JOIN account_move m ON (m.id=line.move_id)
                LEFT JOIN account_account ac ON (ac.id=line.account_id)
                where line.partner_id=%s and m.state='posted'
                and ac.internal_type = 'receivable'
                """ % self.patient_id.partner_id.id
        self.env.cr.execute(query)
        result = self.env.cr.fetchone()
        balance = result and result[0] or 0.0
        if balance <= 0.0:
            self.state = 'payment_done'
            return True
        residual = sum(self.invoice_line_ids.mapped('amount_residual_signed'))
        invoice_ids = self.invoice_line_ids.filtered(lambda x: x.amount_residual_signed > 0.0 and x.partner_id.id == self.patient_id.partner_id.id)
        action = self.env.ref('nuro_patients.action_account_payments_cashier_payment_patient').read()[0]
        action['context'] = {
            'default_gyne_id': self.id,
            'default_cashier_boolean': True,
            'default_invoice_payment_type': 'manual',
            'default_payment_type': 'inbound',
            'default_partner_type': 'customer',
            'res_partner_search_mode': 'customer',
            'default_partner_id': self.patient_id.partner_id.id,
            'default_invoice_ids': [(6, 0, invoice_ids and invoice_ids.ids or [])],
            'default_amount': residual
        }
        return action

    def action_new_born_count(self):
        """Action new Born"""
        ctx = self._context.copy()
        for record in self:
            search_c_sec_surgery = self.env['nuro.surgery'].search([
                ('cesarean_section', '!=', False),
                ('surgery_entry_id.gyne_id', '=', record.id),
                ('state', 'not in', ('cancelled', 'reject', 'not_paid'))
            ])
            if search_c_sec_surgery:
                delivery_type = 'c_section'
            else:
                delivery_type = False
            ctx.update({
                'default_name': "Baby Of " + record.patient_id.name,
                'default_dob': fields.Datetime.now(),
                'default_gyne_id': record.id,
                'default_new_born': True,
                'default_patient': True,
                'default_delivery_type': delivery_type,
                'create': False,
                'edit': False,
            })
        domain = [('id', 'in', self.newborn_ids.ids)]
        return {
            'name': _('New Born'),
            'domain': domain,
            'res_model': 'nuro.patient',
            'type': 'ir.actions.act_window',
            'view_id': False,
            'views': [(self.env.ref('nuro_patients.initial_kanban_view_patient').id, 'kanban'),
                      (self.env.ref('nuro_patients.initial_tree_view_patient').id, 'tree'),
                      (self.env.ref('nuro_maternity.initial_form_view_patient_new_born').id, 'form')],
            'view_type': 'form',
            'view_mode': 'kanban,tree,form',
            'context': ctx,
        }

    def action_new_born_count_all(self):
        """Action new Born"""
        ctx = self._context.copy()
        action = self.env.ref('nuro_maternity.nuro_patient_new_born_view').read()[0]
        domain = [('related_mother_id', '=', self.patient_id.id)]
        ctx.update({
            'create': False,
            'edit': False,
            'delete': False,
        })
        action['domain'] = domain
        action['context'] = ctx
        return action

    def create_new_born_list(self, number):
        list = []
        if number == 1:
            val1 = {
                "name": "Baby of " + self.patient_id.name,
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val1))
        if number == 2:
            val1 = {
                "name": "Baby of " + self.patient_id.name + " One",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val1))
            val2 = {
                "name": "Baby of " + self.patient_id.name + " Two",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val2))
        if number == 3:
            val1 = {
                "name": "Baby of " + self.patient_id.name + " One",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val1))
            val2 = {
                "name": "Baby of " + self.patient_id.name + " Two",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val2))
            val3 = {
                "name": "Baby of " + self.patient_id.name + " Three",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val3))
        if number == 4:
            val1 = {
                "name": "Baby of " + self.patient_id.name + " One",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val1))
            val2 = {
                "name": "Baby of " + self.patient_id.name + " Two",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val2))
            val3 = {
                "name": "Baby of " + self.patient_id.name + " Three",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val3))
            val4 = {
                "name": "Baby of " + self.patient_id.name + " Four",
                "gender": False,
                "dob": fields.Date.today()
            }
            list.append((0, 0, val4))
        return list

    def action_create_new_born(self):
        """
        Create New Born
        """
        if self.delivery_processed:
            raise UserError(_('Delivery Has been Created already.!!!'))
        if not self.delivery_type:
            raise UserError(_('Please Select Delivery Type Before Proceeding'))
        action = self.env.ref('nuro_maternity.action_new_born_create').read()[0]
        child_list = self.create_new_born_list(number=int(self.delivery_type))
        ctx = self.env.context.copy()
        ctx.update({
            'default_gyne_id': self.id,
            'default_child_line': child_list
        })
        action['context'] = ctx
        return action

    def action_admit_now(self):
        """
        Action Admit Now
        :return:
        """
        query_inpatient = _(
            "select bed_id from nuro_inpatient where patient_id = %s and state != 'discharged' limit 1") % (
                              self.patient_id.id)
        self.env.cr.execute(query_inpatient)
        vals = self.env.cr.fetchall()
        if vals:
            bed_name = self.env['nuro.inpatient.bed'].sudo().browse(vals[0][0])
            raise UserError(_('Patient %s-%s Already Admitted On Bed %s !' % (
                self.patient_id.name, self.patient_id.identification_code, bed_name.name)))
        search_ipd_request = self.env['nuro.inpatient.request'].search(
            [('patient_id', '=', self.patient_id.id), ('state', '=', 'draft')])
        if search_ipd_request:
            raise UserError(_('There are already IPD request available kindly admit patient from IPD Request Menu'))
        ctx = self._context.copy()
        for record in self:
            ctx.update({'default_patient_id': record.patient_id.id,
                        'default_gyne_id': record.id,
                        'default_appointment_id': record.appointment_id.id,
                        'default_admission_type': 'maternity',
                        'create': False,
                        'edit': False,
                        })
        bed_obj = self.env['nuro.inpatient.bed'].search([
            ('state', '=', 'free'),
            ('ward_id.is_maternity', '=', True)
        ])
        action = self.env.ref('nuro_inpatient.nuro_inpatient_bed_view_admission_action').read()[0]
        action['domain'] = [('id', 'in', bed_obj.ids)]
        action['context'] = ctx
        return action

    def view_operation_form(self):
        """
        View Operation Form
        :return:
        """
        ctx = self._context.copy()
        for record in self:
            ctx.update({'default_patient_id': record.patient_id.id,
                        'default_cesarean_section': 'elective',
                        'default_gyne_id': record.id,
                        'default_doctor_id': record.doctor_id.id})
        return {
            'name': _('View Operation form'),
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'nuro.surgery.entry',
            'view_id': self.env.ref('nuro_surgery_cashier.surgery_entry_form_view').id,
            'type': 'ir.actions.act_window',
            'context': ctx,
            'target': 'current'
        }

    def action_prescription_entry_create(self):
        """
        Method to Create Prescription
        :return:
        """
        ctx = self._context.copy()
        for record in self:
            ctx.update({
                'default_gyne_id': record.id,
                'default_patient_id': record.patient_id.id,
                'default_doctor_id': record.doctor_id.id,
            })
        domain = [('gyne_id', '=', self.id), ('tools', '=', False)]
        action = self.env.ref('nuro_pharmacy.action_medical_sale_prescription').read()[0]
        action['context'] = ctx
        action['domain'] = domain
        return action


class NuroPerinatalMonitor(models.Model):
    _name = "nuro.medical.perinatal.monitor"
    _description = "Gyneco Perinatal Monitor"

    inpatient_id = fields.Many2one('nuro.inpatient')
    name = fields.Char(string='Internal Code', size=128, required=True, readonly=True, default='/')
    date = fields.Datetime(string='Date and Time', required=True, default=fields.Datetime.now)
    systolic = fields.Integer(string='Systolic Pressure')
    diastolic = fields.Integer(string='Diastolic Pressure')
    contractions = fields.Integer(string='Contractions')
    frequency = fields.Integer(string='Mother\'s Heart Frequency')
    dilation = fields.Integer(string='Cervix Dilation')
    f_frequency = fields.Integer(string='Fetus Heart Frequency')
    meconium = fields.Boolean(string='Meconium')
    bleeding = fields.Boolean(string='Bleeding')
    fundal_height = fields.Integer(string='Fundal Height')
    fetus_position = fields.Selection(FETUS, string='Fetus Position', index=True)
    gyneco_id = fields.Many2one('nuro.medical.gyneco', string='Gynecology', ondelete='cascade')

    @api.model
    def create(self, vals):
        """
        Create Sequence
        :param vals:
        :return:
        """
        sequence = self.env['ir.sequence'].next_by_code('nuro.medical.perinatal.monitor')
        vals['name'] = sequence or '/'
        return super(NuroPerinatalMonitor, self).create(vals)


class NuroPuerperiumMonitor(models.Model):
    _name = "nuro.medical.puerperium.monitor"
    _description = "Gyneco Puerperium Monitor"

    inpatient_id = fields.Many2one('nuro.inpatient')
    name = fields.Char(string='Internal Code', size=128, required=True, readonly=True, default=lambda *a: '/')
    date = fields.Datetime(string='Date and Time', required=True, default=fields.Datetime.now)
    systolic = fields.Integer(string='Systolic Pressure')
    diastolic = fields.Integer(string='Diastolic Pressure')
    frequency = fields.Integer(string='Heart Frequency')
    lochia_amount = fields.Selection(LOCHIA_AMOUNT, string='Lochia Amount')
    lochia_color = fields.Selection(LOCHIA_COLOR, string='Lochia Color')
    lochia_odor = fields.Selection(LOCHIA_ODOR, string='Lochia Odor')
    uterus_involution = fields.Integer(string='Fundal Height',
                                       help="Distance between the symphysis pubis and the uterine fundus (S-FD) in cm")
    temperature = fields.Float(string='Temperature')
    gyneco_id = fields.Many2one('nuro.medical.gyneco', string='Gynecology', ondelete='cascade')

    @api.model
    def create(self, vals):
        """
        Create Sequence
        :param vals:
        :return:
        """
        sequence = self.env['ir.sequence'].next_by_code('nuro.medical.puerperium.monitor')
        vals['name'] = sequence or '/'
        return super(NuroPuerperiumMonitor, self).create(vals)


class DischargeMedication(models.Model):
    _inherit = 'discharge.medication'

    gyne_id = fields.Many2one("nuro.medical.gyneco", string="Gyne ID#")


class History(models.Model):
    _name = 'doctor.history.change'
    _description = 'Doctor Change History'
    _order = 'id DESC'

    old_value = fields.Char(string='Old Value')
    new_value = fields.Char(string='New Value')
    date = fields.Datetime(string='Date')
    medical_gyneco_id = fields.Many2one('nuro.medical.gyneco', string='Medical Gyneco')
