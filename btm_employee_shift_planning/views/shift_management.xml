<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!-- Inherit Form View to Modify it -->
        <record id="shift_management_form_inherit" model="ir.ui.view">
            <field name="name">Shift Management</field>
            <field name="model">nuro.shift.management</field>
            <field name="inherit_id" ref="nuro_shift_planning.nuro_shift_management_module_form_view"/>
            <field name="arch" type="xml">
                <field name="department_id" position="after">
                    <field name="employee_id" groups="hr.group_hr_user"
                           attrs="{'readonly': [('state', '!=', 'draft')]}"
                           domain="[('shift_based', '=', 'on_shift')]"
                           options="{'no_open': True, 'no_create': True}"/>
                    <field name="company_id" readonly="1" options="{'no_open': True, 'no_create': True}"/>
                    <field name="is_hide" invisible="1"/>
                    <field name="is_show" invisible="1"/>
                    <field name="added_dept_ids" invisible="1"/>
                </field>
                <field name="department_id" position="attributes">
                    <attribute name="domain">[('id', 'in', added_dept_ids)]</attribute>
                </field>
                <button name="button_approve" position="replace">
                    <button string="Approve" name="button_approve" type="object" states="draft,waiting"
                                class="oe_highlight" groups="hr.group_hr_manager"/>
                </button>
<!--                <button name="button_approve" position="attributes">-->
<!--                    <attribute name="groups">hr.group_hr_user</attribute>-->
<!--                </button>-->
                <button name="get_one2many_line_employee" position="attributes">
                    <attribute name="groups">hr.group_hr_user</attribute>
                </button>
                <button name="button_approve" position="after">
                    <button string="Send To HR Manager" name="button_send_for_approval"
                            type="object" attrs="{'invisible': ['|',('state', '!=', 'draft'),
                                                                ('is_hide', '=', True)]}"
                                class="oe_highlight"/>
                    <button string="Reset Draft" name="button_reset_draft"
                            confirm="Are you sure you want reset to draft?"
                            type="object" attrs="{'invisible': [('state', '!=', 'approved')]}"
                                class="oe_highlight" groups="hr.group_hr_manager"/>

                    <button string="Reset Draft" name="button_reset_draft"
                            confirm="Are you sure you want reset to draft?"
                            type="object" attrs="{'invisible': [('state', '!=', 'waiting')]}"
                                class="oe_highlight"/>

                </button>
            </field>
        </record>
    </data>
</odoo>