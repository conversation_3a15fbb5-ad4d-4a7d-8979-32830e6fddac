<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="physical_examination_form_view" model="ir.ui.view">
            <field name="name">Physical Examination</field>
            <field name="model">physical.examination</field>
            <field name="arch" type="xml">
                <form string="Physical Examination">
                    <sheet>
                        <style>
                            .o_form_view .o_horizontal_separator {
                            color: #174290;
                            font-weight: bold;
                            }
                            a {
                            color: #174290;
                            text-decoration: none;
                            background-color: transparent;
                            }
                            h1 {
                            color: #174290;
                            }
                        </style>
                        <div name="button_box" class="oe_button_box">
                        </div>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Code #"/>
                                <h1>
                                    <field name="name" class="oe_inline"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group string="Patient Information">
                                <field name="patient_id" readonly="1" force_save="1"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                                <field name="user_id" readonly="1" force_save="1" invisible="1"/>
                            </group>
                            <group string="Doctor Information">
                                <field name="date" readonly="1" force_save="1"/>
                                <field name="doctor_id" readonly="1" force_save="1"/>
                                <field name="speciality_id" readonly="1" force_save="1"/>
                                <field name="examination_type" readonly="1" force_save="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Pediatric" name="pediatric_page"
                                  attrs="{'invisible': [('examination_type', '!=', 'pediatric')]}">
                                <group string="Vital Signs">
                                    <group>
                                        <field name="pr"/>
                                        <field name="rr"/>
                                        <field name="temp"/>
                                    </group>
                                    <group>
                                        <field name="bp"/>
                                        <field name="so2"/>
                                    </group>
                                </group>
                                <group string="Anthropometry">
                                    <group>
                                        <field name="weight_anthropometry"/>
                                        <field name="height_length_anthropometry"/>
                                        <field name="head_circumference_anthropometry"/>
                                        <field name="muac_anthropometry"/>
                                    </group>
                                    <group>
                                        <field name="wt_age_anthropometry"/>
                                        <field name="ht_age_anthropometry"/>
                                        <field name="hc_age_anthropometry"/>
                                        <field name="wt_ht_anthropometry"/>
                                        <field name="bmi_anthropometry"/>
                                    </group>
                                </group>
                                <group>
                                    <group>
                                        <field name="heent"/>
                                        <field name="lgs"/>
                                        <field name="respiratory_system"/>
                                        <field name="cvs"/>
                                        <field name="abdomen"/>
                                        <field name="gus"/>
                                    </group>
                                    <group>
                                        <field name="integumentary"/>
                                        <field name="musculoskeletal"/>
                                        <field name="nervous_system"/>
                                        <field name="diagnosis"/>
                                        <field name="plan"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

    </data>
</odoo>