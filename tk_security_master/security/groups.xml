<?xml version="1.0" encoding="UTF-8"?>
<!--
    Copyright (C) 2023-TODAY TechKhedut (<https://www.techkhedut.com>)
    Part of TechKhedut. See LICENSE file for full copyright and licensing details.
-->
<odoo>
    <data>
        <data noupdate="0">
            <record id="module_tk_security_master" model="ir.module.category">
                <field name="name">Security Master</field>
                <field name="description">Access rights for TK Security Master</field>
            </record>
            <record id="t_k_security_master_user" model="res.groups">
                <field name="name">User</field>
                <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
                <field name="category_id" ref="module_tk_security_master"/>
            </record>
            <record id="t_k_security_master_admin" model="res.groups">
                <field name="name">Administrator</field>
                <field name="implied_ids" eval="[(4, ref('t_k_security_master_user'))]"/>
                <field name="category_id" ref="module_tk_security_master"/>
                <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
            </record>
            <record id="base.user_admin" model="res.users">
                <field name="groups_id" eval="[(4,ref('t_k_security_master_admin'))]"/>
            </record>
        </data>
    </data>
</odoo>