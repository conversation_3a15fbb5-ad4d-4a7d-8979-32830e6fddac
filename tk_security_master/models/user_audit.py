# -*- coding: utf-8 -*-
# Copyright (C) 2023-TODAY TechKhedut (<https://www.techkhedut.com>)
# Part of TechKhedut. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta
import logging
from odoo import fields, models, api, http, _
from odoo.exceptions import UserError, ValidationError
from odoo.http import request

_logger = logging.getLogger(__name__)


class UserAudit(models.Model):
    """User Actions Logs Details"""
    _name = 'user.audit'
    _description = __doc__
    _order = 'id desc'
    _rec_name = 'title'

    title = fields.Char(string="Record Details")
    user_session_id = fields.Many2one('user.sign.in.details', string="User Sessions", index=True)
    user_id = fields.Many2one('res.users', string="User", index=True)
    res_model = fields.Char(string="Res Model", index=True)
    res_id = fields.Char(string="Record IDs", index=True)
    action_type = fields.Selection([('create', 'Create'), ('read', 'Read'), ('update', 'Update'), ('delete', 'Delete')],
                                   string="Action", index=True)
    res_url = fields.Char(string="Url", index=True)
    view_type = fields.Char(string="View", index=True)
    user_audit_log_ids = fields.One2many('user.audit.logs', 'user_audit_id', string="Logs Details", index=True)

    def auto_delete_user_logs(self):
        config_param = self.env['ir.config_parameter'].sudo()
        auto_delete_log = config_param.get_param('tk_security_master.enable_auto_delete')

        if not auto_delete_log:
            return

        create_log = config_param.get_param('tk_security_master.create_log')
        read_log = config_param.get_param('tk_security_master.read_log')
        update_log = config_param.get_param('tk_security_master.update_log')
        delete_log = config_param.get_param('tk_security_master.delete_log')
        auto_delete_log_days = int(config_param.get_param('tk_security_master.auto_delete_log_days', default=30))
        # Calculate the date threshold
        date_threshold = datetime.now() - timedelta(days=auto_delete_log_days)
        # Search for logs older than the threshold
        old_logs = self.search([('create_date', '<', date_threshold)])

        action_flags = {
            'create': create_log,
            'read': read_log,
            'update': update_log,
            'delete': delete_log
        }

        for log in old_logs:
            if action_flags.get(log.action_type, False):
                log.unlink()
            else:
                return

    def open_record(self):
        if self.action_type in ('create', 'update') and self.res_id is not None:
            action = {
                'name': _('View Logs'),
                'view_mode': 'form',
                'res_model': self.res_model,
                'res_id': int(self.res_id),
                'type': 'ir.actions.act_window',
                'target': 'current'
            }
            return action
        elif self.action_type == 'read' and self.res_url:
            action = {
                'type': 'ir.actions.act_url',
                'url': self.res_url,
                'target': 'new'
            }
            return action
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'warning',
                    'message': 'You can not able to view deleted record',
                    'next': {'type': 'ir.actions.act_window_close'},
                }
            }


class UserAuditLogs(models.Model):
    _name = 'user.audit.logs'
    _description = "User Logs"

    name = fields.Char(string="Title", required=True, index=True)
    previous_value = fields.Char(string="Previous Value")
    new_value = fields.Char(string="New Value")
    operation_type = fields.Selection([('new', "New Add"), ('update', "Update"), ('remove', "Removed")], index=True)
    user_audit_id = fields.Many2one('user.audit', string="User Audit", index=True)


class UserSignInDetails(models.Model):
    """User Sign In Details"""
    _name = 'user.sign.in.details'
    _description = __doc__
    _order = 'id desc'

    name = fields.Char(string='Sequence', readonly=True, copy=False, index=True)

    user_id = fields.Many2one('res.users', string='User', index=True)
    # Address
    city = fields.Char('City', index=True)
    region = fields.Char('Region', index=True)
    country = fields.Char('Country', index=True)
    timezone = fields.Char('TimeZone', index=True)
    postal_code = fields.Char(string="Postal Code", index=True)
    latitude = fields.Char(string="Latitude", index=True)
    longitude = fields.Char(string="Longitude", index=True)
    active = fields.Boolean(index=True)
    # sign-in sign-out
    logged_datetime = fields.Datetime(string='Logged Datetime', index=True)
    logout_datetime = fields.Datetime(string='Logout Datetime', index=True)
    last_active_time = fields.Datetime(string='Last Active Time', index=True)
    # technical details
    ip_address = fields.Char(string='IP Address', index=True)
    platform = fields.Selection(
        [('Windows', "Windows"), ('Linux', "Linux"), ('Mac OS', "Mac OS"), ('Android', "Android"),
         ('iOS', "iOS"), ('Other', "Other")],
        string='Operating System', index=True)
    other_platform = fields.Char(string='Other Operating System', index=True)
    platform_version = fields.Char(string='OS Version', index=True)
    is_bot = fields.Boolean(index=True)
    browser = fields.Selection([('Safari', "Safari"), ('Chrome', "Chrome"), ('Firefox', "Firefox"), ('Opera', "Opera"),
                                ('ChromiumEdge', "Edge"),
                                ('Other', "Other")], string='Browser', index=True)
    other_browser = fields.Char(string="Other Browser", index=True)
    browser_version = fields.Char(string='Browser Version', index=True)
    isp = fields.Char(string='ISP Providers', index=True)
    is_anonymous = fields.Boolean(string="Anonymous User", index=True)
    # Odoo session
    session = fields.Char(string='Session ID', readonly=True, store=True, index=True)
    # Status
    status = fields.Selection([('active', 'Active'), ('inactive', 'Terminated')], string='Status', index=True)

    def session_info(self):
        pass
        
    @api.model
    def cleanup_null_session_records(self):
        """Remove records with null session values automatically"""
        # Check what sessions exist and log them for debugging
        self.env.cr.execute("""
            SELECT id, session, active, status FROM user_sign_in_details 
            WHERE session IS NULL OR session = ''
            LIMIT 50
        """)
        results = self.env.cr.dictfetchall()
        if results:
            _logger.info("Found %s records with null/empty session values", len(results))
            for record in results:
                _logger.info("Session record: ID=%s, session=%s, active=%s, status=%s", 
                            record['id'], record['session'], record['active'], record['status'])
            
            # Get the IDs of all records with null or empty sessions
            self.env.cr.execute("""
                SELECT id FROM user_sign_in_details 
                WHERE session IS NULL OR session = ''
            """)
            null_session_ids = [r[0] for r in self.env.cr.fetchall()]
            
            if null_session_ids:
                # Mark as inactive first
                self.env.cr.execute("""
                    UPDATE user_sign_in_details
                    SET status = 'inactive', active = FALSE, logout_datetime = NOW()
                    WHERE id IN %s
                """, (tuple(null_session_ids),))
                
                # Then delete the records
                self.env.cr.execute("""
                    DELETE FROM user_sign_in_details
                    WHERE id IN %s
                """, (tuple(null_session_ids),))
                
                self.env.cr.commit()
                _logger.info("Deleted %s UserSignInDetails records with null/empty session values", len(null_session_ids))
        else:
            _logger.info("No records found with null/empty session values")
        
        return True

    def action_view_user_logs(self):
        action = {
            'name': _('Activity Logs'),
            'view_mode': 'tree,form',
            'res_model': 'user.audit',
            'type': 'ir.actions.act_window',
            'domain': [('user_session_id', '=', self.id)],
            'target': 'current'
        }
        return action

    def terminate_user_active_session(self):
        for rec in self:
            if rec.session:  # Only try to terminate if session is not False/None
                session_obj = http.root.session_store
                try:
                    session_id = session_obj.get(rec.session)
                    if session_id and hasattr(session_id, 'db') and session_id.db and session_id.uid == rec.user_id.id and session_id.sid == rec.session:
                        session_obj.delete(session_id)
                except (TypeError, AttributeError):
                    _logger.warning("Could not terminate session %s for user %s", rec.session, rec.user_id.name if rec.user_id else "Unknown")
            rec.sudo().write({'status': 'inactive', 'active': False, 'logout_datetime': datetime.now()})

    def action_gmap_location(self):
        if not self.latitude or not self.longitude:
            raise ValidationError(_("Location coordinates required"))
        http_url = 'https://maps.google.com/maps?q=loc:' + self.latitude + "," + self.longitude
        return {
            'type': 'ir.actions.act_url',
            'target': 'new',
            'url': http_url,
        }

    def unlink(self):
        for rec in self:
            try:
                # Access request.params only if we're in an HTTP context
                ir_model = request.params.get('model', False) if request else False
                if rec.user_id.id == self.env.user.id and ir_model and ir_model != 'base.module.uninstall':
                    raise UserError(_("you can't delete your own session"))
                if rec.session:  # Only terminate if session exists
                    rec.terminate_user_active_session()
            except (AttributeError, KeyError):
                # Handle exceptions when not in an HTTP context or params missing
                _logger.debug("Deleting session record outside of HTTP context")
                rec.write({'status': 'inactive', 'active': False})
        return super(UserSignInDetails, self).unlink()

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code(
                    'user.sign.in.details') or 'New'
        res = super(UserSignInDetails, self).create(vals_list)
        is_login_alert = self.env['ir.config_parameter'].get_param('tk_security_master.login_alert')
        if is_login_alert:
            tmpl_id = self.env.ref('tk_security_master.login_user_alert_template', raise_if_not_found=False)
            if res and tmpl_id:
                tmpl_id.sudo().send_mail(res.id, force_send=True)
        return res

    @api.model
    def terminate_inactive_session_cron(self):
        config_param = self.env['ir.config_parameter'].sudo()
        auto_terminate = config_param.get_param('tk_security_master.auto_terminate_session')

        # First clean up any null session records
        self.cleanup_null_session_records()

        if not auto_terminate:
            return

        session_timeout = config_param.get_param('tk_security_master.session_timeout')

        if not session_timeout or int(session_timeout) <= 0:
            return

        sessions = self.env['user.sign.in.details'].sudo().search([('active', '=', True)])
        for sess in sessions:
            if sess.session:
                time_diff = datetime.now() - sess.last_active_time
                float_diff = time_diff.total_seconds() / 60
                if float_diff > int(session_timeout):
                    session_obj = http.root.session_store
                    session_id = session_obj.get(sess.session)
                    if session_id.db and session_id.uid == sess.user_id.id and session_id.sid == sess.session:
                        session_obj.delete(session_id)
                    sess.sudo().write({'status': 'inactive', 'active': False, 'logout_datetime': datetime.now()})

    @api.model
    def get_users_sessions_stats(self):
        data = []
        sessions = self.env['user.sign.in.details'].sudo().search([('active', '=', True)])
        for sess in sessions:
            if not sess.latitude or not sess.longitude or not sess.user_id:
                continue
            title = "%s\n--------------------- \nIP: %s \nBrowser: %s \nOS: %s" % (
                sess.user_id.name, sess.ip_address, sess.browser, sess.platform)
            data.append({
                'title': title,
                'latitude': sess.latitude,
                'longitude': sess.longitude,
            })
        return data

    @api.model
    def get_browser_wise_session_stats(self):
        os_counts = {
            'windows': self.get_os_wise_session_counts('Windows'),
            'linux': self.get_os_wise_session_counts('Linux'),
            'mac': self.get_os_wise_session_counts('Mac OS'),
            'android': self.get_os_wise_session_counts('Android'),
            'ios': self.get_os_wise_session_counts('iOS'),
            'other_os': self.get_os_wise_session_counts('Other'),
        }
        data = {
            'chrome': self.get_browser_wise_session_counts('Chrome'),
            'safari': self.get_browser_wise_session_counts('Safari'),
            'firefox': self.get_browser_wise_session_counts('Firefox'),
            'edge': self.get_browser_wise_session_counts('ChromiumEdge'),
            'opera': self.get_browser_wise_session_counts('Opera'),
            'other': self.get_browser_wise_session_counts(),
            'all': self.get_session_status_wise('all'),
            'active': self.get_session_status_wise('active'),
            'terminate': self.get_session_status_wise('terminate'),
            'os': os_counts,
        }
        return data

    def get_browser_wise_session_counts(self, browser_name="Other"):
        browser_counts = self.env['user.sign.in.details'].sudo().search_count(
            [('active', '=', True), ('browser', '=', browser_name)])
        return browser_counts

    def get_session_status_wise(self, sess_domain="active"):
        if sess_domain == 'active':
            domain = [('active', '=', True)]
        elif sess_domain == 'terminate':
            domain = [('active', '=', False)]
        else:
            domain = [('active', 'in', [True, False])]
        session_counts = self.env['user.sign.in.details'].sudo().search_count(domain)
        return session_counts

    def get_os_wise_session_counts(self, os_name="Other"):
        os_counts = self.env['user.sign.in.details'].sudo().search_count(
            [('active', '=', True), ('platform', '=', os_name)])
        return os_counts


class DoNotTrackModels(models.Model):
    """Do not track models"""
    _name = 'do.not.track.models'
    _description = __doc__
    _rec_name = 'res_model'

    res_model = fields.Char(string="Model", required=True, index=True)
