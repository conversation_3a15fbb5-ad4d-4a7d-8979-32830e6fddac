from odoo import fields, models, api


class AccountMove(models.Model):
    _inherit = 'account.move'

    patient_id = fields.Many2one('nuro.patient', string='Patient', domain=[('deceased', '!=', True)])
    covid_test_id = fields.Many2one('covid.test.model', string='Covid Test')


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    covid_test_id = fields.Many2one('covid.test.model', string='Covid Test')
    