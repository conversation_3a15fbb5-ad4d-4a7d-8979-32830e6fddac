# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_analytic_parent
#
# Translators:
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-15 12:59+0000\n"
"PO-Revision-Date: 2018-01-15 12:59+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Russian (https://www.transifex.com/oca/teams/23907/ru/)\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "%(name)s - %(partner)s"
msgstr ""

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "%(parent)s / %(own)s"
msgstr ""

#. module: account_analytic_parent
#: model:ir.model,name:account_analytic_parent.model_account_analytic_account
msgid "Analytic Account"
msgstr ""

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__child_ids
msgid "Child Accounts"
msgstr ""

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__complete_name
msgid "Complete Name"
msgstr ""

#. module: account_analytic_parent
#: model_terms:ir.ui.view,arch_db:account_analytic_parent.view_account_analytic_account_list
msgid "Name"
msgstr ""

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__parent_id
#: model_terms:ir.ui.view,arch_db:account_analytic_parent.view_account_analytic_account_form
msgid "Parent Analytic Account"
msgstr ""

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__parent_path
msgid "Parent Path"
msgstr ""

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "Please activate first parent account %s"
msgstr ""

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "You can not create recursive analytic accounts."
msgstr ""

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "[%(code)s] %(name)s"
msgstr ""

#~ msgid "Created by"
#~ msgstr "Автор"

#~ msgid "Created on"
#~ msgstr "Платформа"

#~ msgid "ID"
#~ msgstr "ID"
