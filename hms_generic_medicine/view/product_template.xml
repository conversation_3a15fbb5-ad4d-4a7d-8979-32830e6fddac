<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="odoo_nuro_pharmacy_medicine_report" model="ir.ui.view">
            <field name="name">Product</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="nuro_pharmacy.product_template_form_inherit"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='is_medicine']" position="after">
                    <field name="medicine_category_id" options="{'no_open': True, 'no_create': True}"
                           attrs="{'invisible': [('is_medicine', '=', False)], 'required': [('is_medicine', '=', True)]}"/>
                    <field name="generic_id" options="{'no_open': True, 'no_create': True}"
                           attrs="{'invisible': [('is_medicine', '=', False)], 'required': [('is_medicine', '=', True)]}"/>
                </xpath>

                <xpath expr="//field[@name='drug_form_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                    <attribute name="attrs">{'required': [('is_medicine', '=', True)]}</attribute>
                </xpath>

                <xpath expr="//field[@name='drug_route_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                    <attribute name="attrs">{'required': [('is_medicine', '=', True)]}</attribute>
                </xpath>

            </field>
        </record>

        <record id="product_template_list_view_inherit_generic" model="ir.ui.view">
            <field name="name">Product</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='type']" position="after">
                    <field name="medicine_category_id"/>
                    <field name="generic_id"/>
                </xpath>
            </field>
        </record>

        <record id="product_template_search_form_view_stock_view" model="ir.ui.view">
            <field name="name">product.template.search.stock.form</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="stock.product_template_search_form_view_stock"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='warehouse_id']" position="after">
                    <field name="medicine_category_id"/>
                    <field name="generic_id"/>
                </xpath>
                <xpath expr="//group[1]" position="inside">
                    <filter string="Medical Category" name="medicine_category_id_group_by"
                            context="{'group_by':'medicine_category_id'}"/>
                    <filter string="Generic Name" name="generic_id_group_by" context="{'group_by':'generic_id'}"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>