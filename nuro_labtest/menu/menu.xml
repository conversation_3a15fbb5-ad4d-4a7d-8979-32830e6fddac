<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="labtest_main_menu" name="Labtest"
                  web_icon="nuro_labtest,static/description/icon.png"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_sample_collection_action" name="Waiting Samples" parent="labtest_main_menu"
                  action="nuro_labtest.action_nuro_labtest_sample_collection_view_action"
                  groups="nuro_labtest.group_labtest_sample_collector" sequence="12"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_sample_collected_action" name="Samples Collected" parent="labtest_main_menu"
                  action="nuro_labtest.action_nuro_labtest_sample_collected"
                  groups="nuro_labtest.group_labtest_sample_collector" sequence="16"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_cancelled_labtest_sample_collection_action" name="Cancelled"
                  parent="labtest_main_menu"
                  action="nuro_labtest.action_nuro_cancelled_labtest_sample_collection_view_action"
                  groups="nuro_labtest.group_labtest_sample_collector" sequence="17"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_sample_batch_receive_action" name="Sample Receive" parent="labtest_main_menu"
                  action="nuro_labtest.action_hms_labtest_batch_sample_received"
                  groups="nuro_labtest.group_labtest_user" sequence="18"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_logged_in_result_action" name="Lab Tests" parent="labtest_main_menu"
                  action="nuro_labtest.action_nuro_labtest_logged_in_view_action"
                  groups="nuro_labtest.group_labtest_user" sequence="20"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_completed_result_action" name="Completed" parent="labtest_main_menu"
                  action="nuro_labtest.action_nuro_labtest_completed_view_action"
                  groups="nuro_labtest.group_labtest_user" sequence="22"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_chaneg_result_view" name="Change Result Report" parent="labtest_main_menu"
                  action="nuro_labtest.action_labtest_result_change_report"
                  groups="nuro_labtest.group_labtest_supervisor" sequence="50"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="labtest_menu_configuration" name="Configuration" parent="labtest_main_menu" sequence="55"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_change_result_reason" name="Change Result Reason" parent="labtest_menu_configuration"
                  action="nuro_labtest.view_action_lab_result_change_reason_view" sequence="-2"
                  groups="nuro_labtest.group_labtest_supervisor,nuro_hms_groups.group_hospital_admin"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_unit" name="Lab Unit" parent="labtest_menu_configuration"
                  action="nuro_labtest.action_labtest_menu_unit" sequence="1"
                  groups="nuro_labtest.group_labtest_supervisor,nuro_hms_groups.group_hospital_admin"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_department" name="Department" parent="labtest_menu_configuration"
                  action="nuro_labtest.action_labtest_department" sequence="2"
                  groups="nuro_labtest.group_labtest_supervisor,nuro_hms_groups.group_hospital_admin"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_master_menu" name="Lab Test" parent="labtest_menu_configuration"
                  action="nuro_labtest.action_labtest_master_view" sequence="3"
                  groups="nuro_labtest.group_labtest_supervisor,nuro_hms_groups.group_hospital_admin"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="other_hospital_menu_labtest" name="Other Hospital" parent="labtest_menu_configuration"
                  action="nuro_labtest.res_partner_action_other_hospital" sequence="4"
                  groups="nuro_labtest.group_labtest_supervisor,nuro_hms_groups.group_hospital_admin"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_culture_master_lab" name="Culture Master" parent="labtest_menu_configuration"
                  action="nuro_labtest.action_culture_master_view" sequence="5"
                  groups="nuro_labtest.group_labtest_supervisor,nuro_hms_groups.group_hospital_admin"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="lab_test_cashier_configuration" name="Labtest"
                  parent="nuro_cashier_payment_setting.cashier_configuration_menu"
                  groups="nuro_cashier_closing.group_cashier_manager" sequence="8"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_unit_cashier" name="Lab Unit" parent="lab_test_cashier_configuration"
                  action="nuro_labtest.action_labtest_menu_unit" sequence="1"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_department_cashier" name="Department" parent="lab_test_cashier_configuration"
                  action="nuro_labtest.action_labtest_department" sequence="2"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_labtest_master_menu_cashier" name="Lab Test" parent="lab_test_cashier_configuration"
                  action="nuro_labtest.action_labtest_master_view" sequence="3"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_culture_master_lab_cashier" name="Culture Master" parent="lab_test_cashier_configuration"
                  action="nuro_labtest.action_culture_master_view" sequence="5"/>

    </data>
</odoo>