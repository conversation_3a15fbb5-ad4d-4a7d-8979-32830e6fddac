from odoo import api, fields, models


class NuroInpatient(models.Model):
    _inherit = 'nuro.inpatient'

    def create_bt_vals(self):
        """Create BT Vals"""
        res = super().create_bt_vals()
        res.update({
            'branch_id': self.branch_id and self.branch_id.id or False
        })
        return res

    def create_blood_request_vals(self):
        """Create BR Vals"""
        res = super().create_blood_request_vals()
        res.update({
            'branch_id': self.branch_id and self.branch_id.id or False
        })
        return res
