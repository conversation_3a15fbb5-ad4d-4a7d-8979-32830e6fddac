<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <template id="surgery_credit_receipt_thermal_inherit"
                  inherit_id="nuro_surgery_cashier.surgery_credit_receipt_thermal">
            <xpath expr="//div[@class='page']" position="replace">
                <div class="page">
                    <center>
                        <!-- START TABLE 1 : DATE - ORDER NO -->
                        <table style="width: 100%;">
                            <tr>
                                <td style="text-align: center;border-bottom:1px solid;">
                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                        <span t-field="o.surgery_entry_id.date"/> &#160;
                                        <span t-field="o.surgery_entry_id.name"/>
                                    </p>
                                </td>
                            </tr>
                        </table>
                        <!-- END TABLE 1 : DATE - ORDER NO -->
                        <h6 string="margin-top:-20px;">
                            <p>
                                <b>
                                    <span t-field="o.surgery_entry_id.company_id"
                                          style="text-transform:uppercase;"/>
                                </b>
                                <br/>
                                <span t-field="o.surgery_entry_id.company_id.phone"/>
                                <br/>
                                <span t-field="o.surgery_entry_id.company_id.city"/>,
                                <span t-field="o.surgery_entry_id.company_id.state_id"/>,
                                <span t-field="o.surgery_entry_id.company_id.country_id"/>
                                <br/>
                                <span t-field="o.surgery_entry_id.company_id.website"/>
                                <br/>
                                <span t-field="o.surgery_entry_id.company_id.email"/>
                            </p>
                        </h6>

                        <!-- START TABLE 2 : CUSTOMER DETAILS , USER DETAILS -->
                        <table style="width: 100%;">
                            <tr>
                                <td>
                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                        <b>Name:</b>
                                        <span t-field="o.surgery_entry_id.patient_id.name"/>
                                        <br/>
                                        <b>ID:</b>
                                        <span t-field="o.surgery_entry_id.patient_id.identification_code"/>
                                        <br/>
                                        <b>surgery#</b>
                                        <span t-field="o.surgery_entry_id.name"/>
                                        <br/>
                                    </p>
                                </td>
                            </tr>
                        </table>
                        <!-- END TABLE 2 : CUSTOMER DETAILS , USER DETAILS -->
                        <br/>

                        <!-- START TABLE 3 : PRODUCTS DETAILS -->
                        <table style="width: 100%;">
                            <t t-foreach="o.credit_payment_line" t-as="surgery">
                                <tr>
                                    <td>
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                            &#160;&#160;<span t-field="surgery.surgery_id.name"/>
                                        </p>
                                    </td>
                                    <td style="text-align:right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                            <span t-field="surgery.surgery_id.charges"
                                                  t-options='{"widget": "monetary",
                                                      "display_currency": res_company.currency_id}'/>
                                        </p>
                                    </td>
                                </tr>
                                <tr t-if="surgery.surgery_line_id.anesthesia_type_id.charges > 0.0">
                                    <td>
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                            &#160;&#160;<span
                                                t-field="surgery.surgery_line_id.anesthesia_type_id.name"/>
                                        </p>
                                    </td>
                                    <td style="text-align:right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                            <span t-field="surgery.surgery_line_id.anesthesia_type_id.charges"
                                                  t-options='{"widget": "monetary",
                                                      "display_currency": res_company.currency_id}'/>
                                        </p>
                                    </td>
                                </tr>
                            </t>
                        </table>
                        <!-- END TABLE 3 : PRODUCTS DETAILS -->
                        <!-- START TABLE 4 : AMOUNT DETAILS -->
                        <table style="width: 100%;">
                            <tbody>
                                <tr>
                                    <td style="width:45%">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            Total:
                                        </p>
                                    </td>
                                    <td style="width:55%" class="text-right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            <span t-esc="sum([line.surgery_line_id.charges
                                                    for line in o.credit_payment_line])"
                                                  t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width:45%">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            Discount:
                                        </p>
                                    </td>
                                    <td style="width:55%" class="text-right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            <span t-esc="sum([line.discount_amount
                                                    for line in o.credit_payment_line])"
                                                  t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width:45%">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            Patient:
                                        </p>
                                    </td>
                                    <td style="width:55%" class="text-right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            <span t-esc="sum([line.patient_amount
                                                    for line in o.credit_payment_line])"
                                                  t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width:45%">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            Insurance:
                                        </p>
                                    </td>
                                    <td style="width:55%" class="text-right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            <span t-esc="sum([line.insurance_amount
                                                    for line in o.credit_payment_line])"
                                                  t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width:45%">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            Balance:
                                        </p>
                                    </td>
                                    <td style="width:55%" class="text-right">
                                        <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                            <span t-esc="sum([line.surgery_line_id.total_amount
                                                    for line in o.credit_payment_line])"
                                                  t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                        </p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- END TABLE 2 :  USER DETAILS -->
                        <br/>
                        <table style="width: 100%;">
                            <tr>
                                <td>
                                    <br/>
                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                        <b>User:</b>
                                        <span t-field="o.user_id.name"
                                              style="text-transform:uppercase;"/>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </center>
                </div>
            </xpath>
        </template>
    </data>
</odoo>