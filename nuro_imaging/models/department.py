# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo import fields, models


class NuroImagingDepartment(models.Model):

    _name = 'nuro.imaging.department'
    _description = 'Imaging Department'
    _order = 'id DESC'

    name = fields.Char(string="Department", index=True)
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    signature = fields.Image(string="Signature", max_width=1024, max_height=1024)
    stamp = fields.Image(string="Stamp", max_width=1024, max_height=1024)
    authorized = fields.Char(string="Authorized")
    active = fields.Boolean(string='Active', default=True)


    _sql_constraints = [('name_uniq', 'unique(name)', 'The Imaging Department name must be unique')]
