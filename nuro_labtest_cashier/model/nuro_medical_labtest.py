# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo.exceptions import UserError

from odoo import models, fields, _


class NuroMedicalLabtest(models.Model):

    _inherit = 'nuro.medical.labtest.result'

    move_id = fields.Many2one('account.move')

    def create_vendor_bill_for_labtest(self):
        """
        Vendor Bill for Labtest Other Hospital
        :return:
        """
        curr_invoice_line = []
        for rec in self:
            product_id = self.env['product.product'].browse(self.env.company.lab_product_id.id)
            if not product_id:
                raise UserError(_('Please configure the Product for the lab!!!'))
            other_lab_expense = product_id.property_account_expense_id.id or \
                                product_id.categ_id.property_account_expense_categ_id.id,

            curr_invoice_line.append((0, 0, {
                'name': rec.labtest_master_id.name,
                'quantity': 1,
                'account_id': other_lab_expense,
                'price_unit': 1
            }))
            curr_invoice = {
                'partner_id': rec.other_hospital_id.id,
                'labtest_id': rec.id,
                'state': 'draft',
                'type': 'in_invoice',
                'invoice_date': fields.datetime.now().date(),
                'ref': "Labtest Vendor Bill for Lab# " + rec.name + " For Labtest " + rec.labtest_master_id.name,
                'invoice_line_ids': curr_invoice_line,
            }
            inv_id = self.env["account.move"].create(curr_invoice)
            rec.move_id = inv_id.id
            return {
                'res_model': 'account.move',
                'res_id': inv_id.id,
                'domain': [('id', '=', inv_id.id)],
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
            }

    def action_vendor_bill(self):
        domain = [('labtest_id', '=', self.id)]
        return {
            'name': _('Invoice for Other Hospital Lab Test'),
            'domain': domain,
            'res_model': 'account.move',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
        }
