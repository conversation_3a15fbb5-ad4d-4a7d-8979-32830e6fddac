# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo.exceptions import UserError

from odoo import fields, models, api, _


class NuroLabCreditWizard(models.TransientModel):

    _name = 'nuro.lab.credit.wizard'
    _description = 'lab Credit Wizard'

    @api.model
    def default_get(self, fields):
        """
        Default Get for lab Credit Wizard
        :param fields:
        :return:
        """
        res = super(NuroLabCreditWizard, self).default_get(fields)
        lab_entry_id = self.env['nuro.lab.entry'].browse(self.env.context.get('active_id'))
        res['lab_entry_id'] = lab_entry_id.id
        if lab_entry_id.patient_parent_id:
            patient = lab_entry_id.patient_parent_id
        else:
            patient = lab_entry_id.patient_id
        if patient.parent_id:
            res['bill_to_user_id'] = patient.parent_id.id
            res['bill_to_type'] = 'company'
        else:
            res['bill_to_user_id'] = patient.partner_id.id
            res['bill_to_type'] = 'patient'
        lab_lines = []
        for line in lab_entry_id.lab_entry_line:
            if line.lab_state == 'draft':
                lab_lines.append((0, 0,
                                  {
                                      'department_id': line.department_id.id,
                                      'labtest_master_id': line.labtest_master_id.id,
                                      'test_charge': line.test_charge,
                                      'discount_amount': line.discount_amount,
                                      'total_amount': line.total_amount,
                                      'lab_line_id': line.id,
                                  }))
        res.update({'credit_payment_line': lab_lines})
        return res

    BILL_TO_TYPE = [
        ('patient', 'Patient'),
        ('employee', 'Employee'),
        ('company', 'Company'),
        ('regular', 'Regular'),
        ('hospital', 'Hospital'),
        ('hospital_employee', 'Hospital Employee Expense')
    ]

    user_id = fields.Many2one('res.users', 'User', default=lambda self: self.env.user, store=True, readonly=True)
    bill_to_user_id = fields.Many2one('res.partner', string='Bill To', required=True)
    lab_entry_id = fields.Many2one('nuro.lab.entry', string="lab Entry Id")
    bill_to_type = fields.Selection(BILL_TO_TYPE, default='patient')
    memo = fields.Char('Memo')
    employee_id = fields.Many2one('hr.employee', string='Employee')
    description_hospital = fields.Char('Description')
    payment_method_id = fields.Many2one('account.journal', string='Payment Method', domain=[('type', '=', 'general')],
                                        default=lambda self:
                                        self.env['account.journal'].search([('type', '=', 'general')], limit=1).id,
                                        required=True, readonly=True)
    amount = fields.Float("Amount To Pay", readonly=True, compute='_compute_paid_amount')
    discount_amount = fields.Float('Discount')
    total_amount = fields.Float('Amount', compute='_compute_paid_amount')
    credit_payment_line = fields.One2many('nuro.lab.credit.wizard.line', 'credit_payment_id',
                                          string='Credit Payment Line')
    expense_cash_payment = fields.Boolean('Cash Payment')
    expense_employee_amount = fields.Float('Employee Expense Amount', compute='_compute_expense_amount')
    expense_patient_amount = fields.Float('Patient Amount', compute='_compute_expense_amount')
    expense_partner_id = fields.Many2one('res.partner', string='Charge To')

    # credit limit extension
    extend_credit = fields.Boolean('Credit Extend')
    is_credit_allow = fields.Boolean(string="Allow Credit")
    description = fields.Char('Reason')
    current_credit_limit = fields.Float(string='Current Credit Limit', related='bill_to_user_id.credit_limit',
                                        readonly=True)
    extended_credit_amount = fields.Float(string="Extended Credit Limit", default=200.0)
    responsible_person_id = fields.Many2one('nuro.responsible.person', 'Responsible Person')

    @api.depends('bill_to_type', 'employee_id')
    def _compute_expense_amount(self):
        """
        Compute Expense Amount
        :return:
        """
        for rec in self:
            percentage = self.env.company.maximum_employee_expense
            rec.expense_employee_amount = (rec.amount / 100.0) * percentage
            rec.expense_patient_amount = (rec.amount / 100.0) * (100.0 - percentage)

    @api.onchange('extend_credit')
    def onchange_extend_credit(self):
        """
        Onchange of the Extend Credit
        :return:
        """
        if self.extend_credit:
            self.is_credit_allow = True
        else:
            self.is_credit_allow = False

    def print_lab_credit_receipt(self):
        """
        receipt printing for the user to get the receipt for the lab
        :return:
        """
        return self.env.ref('nuro_labtest_cashier.action_nuro_lab_credit_thermal_receipt').report_action(self)

    def extend_allow_credit_limit(self):
        """
        Method to Increase Credit Limit for the Patient
        :return:
        """
        if self.extended_credit_amount < 0.0:
            raise UserError(_('You Can not Assign Negative Credit Amount to user'))
        if self.bill_to_user_id and self.extend_credit:
            if self.bill_to_type in ('patient', 'employee', 'company', 'regular', 'hospital'):
                bill_to_user_id = self.bill_to_user_id
            else:
                bill_to_user_id = self.lab_entry_id.patient_id.partner_id
            bill_to_user_id.write({'is_credit_allow': self.is_credit_allow,
                                   'credit_limit': self.extended_credit_amount,
                                   'responsible_person_id': self.responsible_person_id.id,
                                   'credit_given_by_user_id': self.env.user.id,
                                   'credit_assigned_date': fields.Date.today(),
                                   'remark': self.description})
            
            bill_to_user_id._compute_credit_utilization_remaining()
        self.extend_credit = False
        self.extended_credit_amount = False

    @api.depends('credit_payment_line', 'credit_payment_line.total_amount')
    def _compute_paid_amount(self):
        """
        Compute Method to calculate the Amount
        :return:
        """
        amount = 0.0
        total = 0.0
        for rec in self.credit_payment_line:
            amount += rec.total_amount
            total += rec.test_charge
        self.amount = amount
        self.total_amount = total

    @api.onchange('discount_amount', 'credit_payment_line')
    def onchange_discount_amount(self):
        """
        Onchange of Discount Amount Value Applied on lines
        :return:
        """
        total = 0.0
        for amt in self.credit_payment_line:
            total += amt.test_charge
        for rec in self.credit_payment_line:
            rec.discount_amount = self.discount_amount / total * rec.test_charge

    @api.onchange('employee_id', 'bill_to_type')
    def onchange_employee_bill_to_type(self):
        """
        Onchange for the employee Id, Bill to Type and company Type to get the relevant partner to be billed
        :return:
        """
        if self.bill_to_type == 'company':
            if self.bill_to_user_id:
                self.bill_to_user_id = False
            self.employee_id = False
            self.bill_to_user_id = self.lab_entry_id.patient_id.parent_id.id
            return {'domain': {'bill_to_user_id': [('id', '=', self.lab_entry_id.patient_id.parent_id.id)]}}
        if self.bill_to_type == 'employee':
            if self.bill_to_user_id:
                self.bill_to_user_id = False
            self.bill_to_user_id = self.employee_id.user_id.partner_id.id or self.employee_id.address_home_id.id
            return {'domain': {'bill_to_user_id': [
                ('id', '=', self.employee_id.user_id.partner_id.id or self.employee_id.address_home_id.id)]}}
        cashier_manager = self.env.user.has_group('nuro_cashier_closing.group_cashier_manager')
        if self.bill_to_type == 'hospital' and cashier_manager:
            if self.bill_to_user_id:
                self.bill_to_user_id = False
                self.discount_amount = 0.0
                self.employee_id = False
            self.bill_to_user_id = self.lab_entry_id.company_id.partner_id.id
        if self.bill_to_type == 'hospital_employee' and cashier_manager:
            if self.bill_to_user_id:
                self.bill_to_user_id = False
                self.discount_amount = 0.0
            self.bill_to_user_id = self.lab_entry_id.company_id.partner_id.id
        if self.bill_to_type in ('hospital', 'hospital_employee') and not cashier_manager:
            self.bill_to_user_id = False
            self.discount_amount = 0.0
            self.bill_to_type = 'patient'
            self.employee_id = False
            message = 'Only Cashier Manager can Create Bill to Hospital.!!!'
            return {'warning': {'title': 'Expense To Hospital', "message": message}}
        if self.bill_to_type == 'regular':
            self.employee_id = False
            if self.bill_to_user_id:
                self.bill_to_user_id = False
            return {'domain': {'bill_to_user_id': [('is_regular_customer', '=', True)]}}
        if self.bill_to_type == 'patient':
            self.employee_id = False
            if self.lab_entry_id.patient_parent_id:
                self.bill_to_user_id = self.lab_entry_id.patient_parent_id.partner_id.parent_id.id or \
                                       self.lab_entry_id.patient_parent_id.partner_id.id
            else:
                self.bill_to_user_id = self.lab_entry_id.patient_id.partner_id.parent_id.id or \
                                       self.lab_entry_id.patient_id.partner_id.id

    def _user_payment_validation(self):
        """
        Payment Validation
        :return:
        """
        default_methods = self.env['hms.methods.library']
        if default_methods.check_discount_comparison(discount_amount=self.discount_amount, amount=self.amount):
            raise UserError(_('You can not Assign Discount Amount more the total amount!!!'))
        if default_methods.check_negative_discount(discount_amount=self.discount_amount):
            raise UserError(_('You can not Assign Discount in Negative!!!'))
        if default_methods.amount_check(amount=self.amount):
            raise UserError(_('No Amount for Cash Payment!'))
        if default_methods.check_credit_payment_line(credit_payment_line=self.credit_payment_line):
            raise UserError(_('NO Lab Lines!'))
        if default_methods.check_payment_method_id(payment_method_id=self.payment_method_id):
            raise UserError(_('Please Select Payment Method!'))
        if default_methods.account_check(account=self.user_id.account_id):
            raise UserError(_("Please Configure cashier account!"))

    def perform_lab_operation(self):
        """
        Lab test Operation
        :return:
        """
        if all(lab_line.lab_state in ('send', 'refunded') for lab_line in self.lab_entry_id.lab_entry_line):
            self.lab_entry_id.state = 'send_to_lab'
        if any(lab_line.lab_state == 'draft' for lab_line in self.lab_entry_id.lab_entry_line):
            self.lab_entry_id.state = 'partial_paid'
        self.lab_entry_id.credit_amount += self.amount

    def action_lab_credit(self):
        """
        Action Labtest line List
        :return:
        """
        default_methods = self.env['hms.methods.library']
        self.lab_entry_id.submit_receipt_labtest()
        if self.extend_credit:
            self.extend_allow_credit_limit()
        self._user_payment_validation()
        labtest_line_list = []
        for lab in self.credit_payment_line:
            labtest_line_list.append(lab.lab_line_id.id)
            if default_methods.state_check(state=lab.lab_line_id.lab_state, name='draft'):
                raise UserError(_("You Can not make the same payment Twice"))
            lab.lab_line_id.discount_amount = lab.discount_amount
            lab.lab_line_id.onchange_charges()
            lab.lab_line_id.lab_state = 'initiated'
            lab.lab_line_id.accounting_date = fields.Date.today()
        partner_id = self.bill_to_user_id
        expense = False
        if self.lab_entry_id and self.bill_to_type in ('patient', 'employee', 'company', 'regular'):
            if not self.bill_to_user_id.is_credit_allow:
                raise UserError(_('Credit is not allowed for %s!!!') % self.bill_to_user_id.name)
            
            self.bill_to_user_id._check_utilized_limit(credit_amount=self.amount)
            inv_ids = self.lab_entry_id.create_invoice(
                amount=self.amount,
                discount_amount=self.discount_amount,
                partner_id=partner_id,
                invoice_type='out_invoice',
                labtest_line_list=labtest_line_list
            )
            inv_ids.action_post()
            inv_ids.line_ids.write({
                'panel': 'lab'
            })
        if self.bill_to_type in ('hospital', 'hospital_employee'):
            expense = True
            if self.bill_to_type == 'hospital_employee':
                patient_partner = self.lab_entry_id.patient_id.partner_id
                if self.expense_partner_id:
                    patient_partner = self.expense_partner_id
                if not self.expense_cash_payment:
                    patient_partner._check_utilized_limit(credit_amount=self.expense_patient_amount)
                if 0.0 < self.env.company.maximum_employee_expense < 100.0:
                    patient_line = []
                    for rec_line in self.credit_payment_line:
                        rec_line.lab_line_id.write({'non_refundable': True})
                        pat_amt = (rec_line.test_charge / 100.0) * (100.0 - self.env.company.maximum_employee_expense)
                        pat = self.lab_entry_id._create_move_lines_lab(
                            amount=pat_amt,
                            labtest_line_id=rec_line.lab_line_id.id
                        )
                        pat.update({
                            'price_unit': pat_amt
                        })
                        patient_line.append((0, 0, pat))
                    inv_dict = self.lab_entry_id._create_invoice_dict(
                        partner_id=patient_partner,
                        invoice_type='out_invoice',
                        invoice_line_ids=patient_line
                    )
                    ctx = {'active_model': self.lab_entry_id._name, 'active_id': self.lab_entry_id.id}
                    inv_ids = self.env['account.move'].with_context(ctx).create(inv_dict)
                    inv_ids.action_post()
                    inv_ids.line_ids.write({
                        'panel': 'lab'
                    })
                    if self.expense_cash_payment:
                        method = self.env['account.journal'].search([
                            ('type', '=', 'cash'),
                            ('cashier_journal', '=', True)], limit=1)
                        payment_method_id = default_methods.get_payment_method(payment_method_id=method)
                        # payment_account = method.default_credit_account_id.id
                        # method.sudo().write({'default_credit_account_id': self.env.user.account_id.id})
                        payments = self.lab_entry_id.create_payment(
                            partner_id=patient_partner, amount=inv_ids.amount_residual, journal_id=method,
                            payment_type='inbound', payment_method_id=payment_method_id, invoice_ids=inv_ids
                        )
                        inv_ids.cash_payment = True
                        payments.cash_payment = True
                        payments.post()
                        # payment_line_receivable_id = payments.move_line_ids.filtered(
                        #     lambda x: x.account_internal_type == 'receivable' and not x.reconciled)
                        # invoice_line_receivable_id = inv_ids[0].line_ids.filtered(
                        #     lambda x: x.account_internal_type == 'receivable' and not x.reconciled)
                        # moves = payment_line_receivable_id + invoice_line_receivable_id
                        # moves.reconcile()
                        payments.move_line_ids.write({
                            'panel': 'lab'
                        })
                        # method.sudo().write({'default_credit_account_id': payment_account})
                move_line = []
                product_id = self.env['product.product'].browse(self.env.company.lab_product_id.id)
                employee_credit_account_id = product_id.property_account_income_id or \
                                             product_id.categ_id.property_account_income_categ_id
                employee_debit_account_id = self.env['account.account'].browse(self.env.company.hospital_employee_expense_account_id.id)
                for employee_line in self.credit_payment_line:
                    employee_line.lab_line_id.write({'non_refundable': True})
                    pat_amt = (employee_line.test_charge / 100.0) * self.env.company.maximum_employee_expense
                    credit_move = default_methods.create_debit_credit_line_entry(
                        account_id=employee_credit_account_id, amount=pat_amt, line_name='medical_labtest_result_id',
                        line_id=employee_line.lab_line_id, master_name='labtest_master_id',
                        master_id=employee_line.lab_line_id.labtest_master_id,
                        name=employee_line.lab_line_id.labtest_master_id.name, partner_id=self.bill_to_user_id,
                        credit=True
                    )
                    move_line.append((0, 0, credit_move))
                pat_debit_amt = (self.amount / 100.0) * self.env.company.maximum_employee_expense
                debit_move = default_methods.create_debit_credit_line_entry(
                    account_id=employee_debit_account_id, amount=pat_debit_amt, line_name='medical_labtest_result_id',
                    line_id=False, master_name='labtest_master_id',
                    master_id=False,
                    name=self.lab_entry_id.name, partner_id=self.bill_to_user_id,
                    credit=False
                )
                move_line.append((0, 0, debit_move))
                if move_line:
                    move = default_methods.dict_over_all_move(
                        partner_id=self.bill_to_user_id,
                        line_ids=move_line,
                        journal_id=self.payment_method_id,
                        object_field_name='lab_entry_id',
                        object_field_id=self.lab_entry_id,
                        label="lab # : "
                    )
                    ctx = {'active_model': self.lab_entry_id._name, 'active_id': self.lab_entry_id.id}
                    emp_move_id = self.env['account.move'].with_context(ctx).create(move)
                    emp_move_id.post()
                    emp_move_id.line_ids.write({
                        'panel': 'lab'
                    })
            else:
                move_ids = self.lab_entry_id.create_account_move(
                    journal_id=self.payment_method_id,
                    amount=self.amount,
                    partner_id=partner_id,
                    employee_id=self.employee_id or False,
                    labtest_line_list=labtest_line_list,
                    refund=False
                )
                move_ids.post()
                move_ids.line_ids.write({
                    'panel': 'lab'
                })
        self.lab_entry_id.description_hospital = self.description_hospital
        
        self.lab_entry_id._create_labtest_records(
            partner_id=partner_id,
            labtest_line_list=labtest_line_list,
            expense=expense,
            employee_id=self.employee_id.id or False
        )
        self.perform_lab_operation()
        return self.print_lab_credit_receipt()


class NuroLabPartialCreditWizardLine(models.TransientModel):

    _name = 'nuro.lab.credit.wizard.line'
    _description = 'lab Credit Wizard Line'

    department_id = fields.Many2one("nuro.labtest.department", string="Department")
    labtest_master_id = fields.Many2one("nuro.labtest.master", string="Labtest")
    test_charge = fields.Float(string='Charge', related='labtest_master_id.test_charge')
    discount_amount = fields.Float(string="Discount Amount", required=False)
    total_amount = fields.Float(string="Total Amount", compute='_compute_amount')
    lab_line_id = fields.Many2one('nuro.medical.labtest.result', string='Line')
    credit_payment_id = fields.Many2one('nuro.lab.credit.wizard', string='Credit Payment Line')

    @api.depends('discount_amount', 'test_charge')
    def _compute_amount(self):
        """
        Total Amount computation for discount amount
        :return:
        """
        for rec in self:
            rec.total_amount = rec.test_charge - rec.discount_amount
