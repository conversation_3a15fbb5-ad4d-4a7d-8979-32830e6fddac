<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Manage Assets with Ease</h2>
        <h3 class="oe_slogan oe_mb32">Create and Track Assets</h3>
            <p class="oe_mt32">
                Give your asset a name and enter all information about the asset;Warranty Dates, Barcode, Asset Location, Asset Model and Category
            </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture oe_screenshot" src="create_asset.png" height="950"/>
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <!--<h2 class="oe_slogan" style="color:#875A7B;">Asset Request and Approval Levels</h2>-->
        <h3 class="oe_slogan">Asset Request</h3>
        <p class="oe_mt32">
            Easy Request of Assets with Different Levels of Approval depending on the Access Rights given to the User.
            An employee request is made by the manager and the employee is notified of the request.
        </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture" src="asset_request.png" height="950">
        </div>
    </div>
    <div class="oe_row oe_spaced">
        <p class="oe_mt32">
            Employee gets notification when Request is approved. The user clicks on the link attached to the request approved
            and confirms he/she has received the Assets.
        </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture" src="received_asset.png" height="950">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
       <!--<h2 class="oe_slogan" style="color:#875A7B;">Asset Return</h2>-->
       <h3 class="oe_slogan">Asset Return</h3>
        <p class="oe_mt32">
            Easy Return of Assets which will be approved by the department manager
        </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture oe_screenshot" src="asset_return.png" height="950">
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h3 class="oe_slogan">Asset Disposal Made Simple</h3>
            <p class='oe_mt32'>
                Asset Disposal can be handled either disposing one assets at a time or by disposing all assets.
                This updates the assets and closes the asset.
            </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture" src="batch_asset_disposal.png" height="950">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Configurations</h2>
        <h3 class="oe_slogan oe_mb32">Access Rights for the Module</h3>
            <p class="oe_mt16">
                For you to be able to see some menus and approval buttons, the necessary groups must be selected
                <li>Requester</li>
                <li>Warehouse Manager</li>
                <li>Approval Manager</li>
            </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture oe_screenshot" src="access_right.png" height="950"/>
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h3 class="oe_slogan">Asset Model Setup</h3>
            <p class='oe_mt32 text-justify'>
                Every asset created must have a category and every category must have a model,
                so you need to setup models of assets and the category they belong
            </p>
        <div class="oe_row_img oe_centered oe_mt32">
            <img class="oe_picture" src="manage_model.png" height="950">
        </div>
    </div>
</section>



<div class="oe_row" style="background-color: #1EB580;">
   <div style="margin-bottom:10px;" class="oe_slogan text-center">
       <div class="oe_span12">
       <h3 class="text-center" style="color: white; font-size:20px">Contact us for support, query, customization</h3>
   </div>
       <span>
           <a target="new" href="http://herlabytes.com" style="color: black !important;font-weight:bold;">Website</a> |
           <a href="mailto:<EMAIL>" style="color: black !important;font-weight:bold;">Write us</a> |
       </span>
   </div>
</div>


<section class="oe_container oe_separator">
</section>
