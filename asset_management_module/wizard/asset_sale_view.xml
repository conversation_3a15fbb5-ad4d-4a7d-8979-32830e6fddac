<odoo>
    <data>
        <record id="form_asset_sale" model="ir.ui.view">
            <field name="name">asset.sale.form</field>
            <field name="model">asset.sale.wz</field>
            <field name="arch" type="xml">
                <form>
                    <group col="4" colspan="4">
                        <field name="amount" required="1" readonly="0"/>
                        <field name="pay_type" required="1"/>
                        <field name="partner_id" domain="[('customer_rank', '!=', 0)]"
                               attrs="{'invisible': [('pay_type', '=', 'cash')], 'required': [('pay_type', '=', 'credit')]}"
                                options="{'no_create': True, 'no_open': True}"/>
                        <field name="journal_id" required="0" readonly="0"
                               domain="[('type', 'in', ['bank', 'cash'])]"
                               options="{'no_create': True, 'no_open': True}"
                                attrs="{'invisible': [('pay_type', '=', 'credit')], 'required': [('pay_type', '=', 'cash')]}"/>
                    </group>
                    <footer>
                        <button name="action_asset_sale_confirm" string="Confirm"
                                 type="object" class="oe_highlight"></button>
                        <button string="Cancel"  special="cancel" />
                    </footer>
                </form>
            </field>
        </record>
        <record id="action_asset_sale" model="ir.actions.act_window">
            <field name="name">Asset Sale</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">asset.sale.wz</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>