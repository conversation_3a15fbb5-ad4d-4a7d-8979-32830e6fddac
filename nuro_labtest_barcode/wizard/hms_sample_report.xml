<odoo>
    <data>
        <record id="view_hms_sample_print_report_form" model="ir.ui.view">
            <field name="name">nuro.sample.print.report.form</field>
            <field name="model">nuro.sample.print.report</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group>
                            <field name="company_id" invisible="1"/>
                            <field name="print_zpl" invisible="1"/>
                            <field name="labtest_id" readonly="1"/>
                            <field name="department_ids" widget="many2many_tags" readonly="1" invisible="1"/>
                            <field name="department_line_ids" widget="many2many_tags"
                                   options="{'no_create': True,'no_open': True}"
                                   domain="[('id', 'in', department_ids)]"/>
                            <field name="copies"/>
                        </group>
                    </group>
                    <footer>
                        <button string="Print Sample Label" type="object" name="print_sample" class="oe_highlight"
                                attrs="{'invisible': [('print_zpl', '=', True)]}"/>
                        <button string="Sample Label ZPL" type="object" name="print_sample_zpl" class="oe_highlight"
                                attrs="{'invisible': [('print_zpl', '!=', True)]}"/>
                        OR
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_hms_sample_print_report" model="ir.actions.act_window">
            <field name="name">HMS Sample Report</field>
            <field name="res_model">nuro.sample.print.report</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_hms_sample_print_report_form"/>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
