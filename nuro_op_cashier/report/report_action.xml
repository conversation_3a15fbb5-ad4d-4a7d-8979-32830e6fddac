<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!--        OP thermal receipt paper format-->
        <record id="report_qweb_paperformat_op_thermal_report" model="report.paperformat">
            <field name="name">HMS : OP Receipt Thermal</field>
            <field name="default" eval="True"/>
            <field name="format">custom</field>
            <field name="page_height">150</field>
            <field name="page_width">65</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">3</field>
            <field name="margin_bottom">10</field>
            <field name="margin_left">3</field>
            <field name="margin_right">3</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">45</field>
            <field name="dpi">130</field>
        </record>

        <!--        OP Procedure paper format-->
        <record id="report_qweb_paperformat_op_inpatient_receipt" model="report.paperformat">
            <field name="name">HMS: OP Receipt</field>
            <field name="default" eval="True"/>
            <field name="format">A5</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">45</field>
            <field name="margin_bottom">30</field>
            <field name="margin_left">6</field>
            <field name="margin_right">6</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">40</field>
            <field name="dpi">105</field>
        </record>

        <!--        Inpatient Cash receipt action-->
        <report id="action_nuro_op_afive_receipt"
                string="OP Receipt"
                model="nuro.op.entry"
                report_type="qweb-pdf"
                name="nuro_op_cashier.nuro_op_afive_template"
                file="nuro_op_cashier.nuro_op_afive_template"
                paperformat="report_qweb_paperformat_op_inpatient_receipt"
                menu="False"/>

        <!--        OP Cash thermal receipt action-->
        <report id="action_nuro_op_cash_thermal_receipt"
                string="OP Procedure Receipt"
                model="nuro.op.cash.wizard"
                report_type="qweb-pdf"
                name="nuro_op_cashier.op_cash_receipt_thermal"
                file="nuro_op_cashier.op_cash_receipt_thermal"
                paperformat="report_qweb_paperformat_op_thermal_report"
                menu="False"/>

        <!--        OP Credit thermal receipt action-->
        <report id="action_nuro_op_credit_thermal_receipt"
                string="OP Procedure Receipt"
                model="nuro.op.credit.wizard"
                report_type="qweb-pdf"
                name="nuro_op_cashier.op_credit_receipt_thermal"
                file="nuro_op_cashier.op_credit_receipt_thermal"
                paperformat="report_qweb_paperformat_op_thermal_report"
                menu="False"/>

        <!--        OP thermal receipt action-->
        <report id="action_nuro_op_thermal_receipt"
                string="OP Procedure Receipt"
                model="nuro.op.entry"
                report_type="qweb-pdf"
                name="nuro_op_cashier.op_receipt_thermal"
                file="nuro_op_cashier.op_receipt_thermal"
                paperformat="report_qweb_paperformat_op_thermal_report"
                menu="False"/>

        <!--        OP thermal Refund receipt action-->
        <report id="action_nuro_op_refund_thermal_receipt"
                string="OP Refund Receipt"
                model="nuro.op.refund.wizard"
                report_type="qweb-pdf"
                name="nuro_op_cashier.template_op_refund_receipt"
                file="nuro_op_cashier.template_op_refund_receipt"
                paperformat="report_qweb_paperformat_op_thermal_report"
                menu="False"/>

    </data>
</odoo>