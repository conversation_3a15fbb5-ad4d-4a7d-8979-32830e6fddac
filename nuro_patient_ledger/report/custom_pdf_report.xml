<odoo>
    <data>

        <!--Papaer format for sample label-->
        <record id="paper_format_account_report_patient_ledger_report" model="report.paperformat">
            <field name="name">Partner Ledger</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">50</field>
            <field name="margin_bottom">30</field>
            <field name="margin_left">6</field>
            <field name="margin_right">6</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">40</field>
            <field name="dpi">90</field>
        </record>

        <report id="print_pdf_patient_statement_report"
                model="partner.ledger"
                name="nuro_patient_ledger.pdf_patient_statement_template"
                file="nuro_patient_ledger.pdf_patient_statement_template"
                string="Pdf Report"
                report_type="qweb-pdf"
                paperformat="paper_format_account_report_patient_ledger_report"/>

        <template id="pdf_patient_statement_template">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <t t-foreach="docs" t-as="o">
                        <t t-foreach="request.env['res.partner'].search([('id', 'in', o.partner_ids.ids)])"
                           t-as="partner">
                            <div class="row">
                                <div class="col-xs-3">
                                    <t t-if="o.start_date">
                                        <strong>Date from :</strong>
                                        <span t-esc="o.start_date" t-options="{'widget': 'date'}"/>
                                        <br/>
                                    </t>
                                </div>
                                <div class="col-xs-3">
                                    <t t-if="o.end_date">
                                        <strong>Date to :</strong>
                                        <span t-esc="o.end_date" t-options="{'widget': 'date'}"/>
                                    </t>
                                </div>
                            </div>
                            <br/>
                            <br/>
                            <t t-set="opening_balance" t-value="o._open_balance(partner.id, o.start_date, o.type)"/>
                            <t t-set="total_debit"
                               t-value="sum([round(d.debit, 2) for d in o._get_report(partner.id,  o.type, o.start_date, o.end_date)])"/>
                            <t t-set="total_credit"
                               t-value="sum([round(d.credit, 2) for d in o._get_report(partner.id,  o.type, o.start_date, o.end_date)])"/>
                            <t t-set="balance" t-value="o._open_balance(partner.id, o.start_date, o.type) + sum([round(d.debit, 2) - round(d.credit, 2)
                                                    for d in o._get_report(partner.id,  o.type, o.start_date, o.end_date)])"/>
                            <t t-set="com_total" t-value="0"/>
                            <t t-set="rb" t-value="0"/>
                            <table style="width: 100%;">
                                <tbody>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                        <td class="text-left">
                                            <strong>Opening Balance:</strong>
                                        </td>

                                        <td class="text-right">
                                            <strong t-esc="round(opening_balance, 2)"
                                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>Patient</strong>
                                        </td>
                                        <td>
                                            <strong t-field="partner.name"/>
                                        </td>
                                        <td class="text-left">
                                            <strong>Debit:</strong>
                                        </td>
                                        <td class="text-right">
                                            <strong t-esc="round(total_debit, 2)"
                                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <strong>ID#</strong>
                                        </td>
                                        <td>
                                            <strong t-field="partner.identification_code"/>
                                        </td>
                                        <td class="text-left">
                                            <strong>Credit:</strong>
                                        </td>
                                        <td class="text-right">
                                            <strong t-esc="round(total_credit, 2)"
                                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                        <td class="text-left">
                                            <strong>Total Discount:</strong>
                                        </td>
                                        <td class="text-right">
                                            <strong t-esc="round(sum([line['discount'] or 0.0 for line in partner.get_patient_discount_amount()]), 2)"
                                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                        <td class="text-left">
                                            <strong>Balance:</strong>
                                        </td>
                                        <td class="text-right">
                                            <strong t-esc="round(balance, 2)"
                                                    t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <br/>
                            <br/>
                            <br/>
                            <table class="table table-condensed">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Ref</th>
                                        <th class="text-right">Debit</th>
                                        <th class="text-right">Credit</th>
                                        <th class="text-right">Balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-if="opening_balance > 0.0">
                                        <td></td>
                                        <td>Opening Balance</td>
                                        <td></td>
                                        <td></td>
                                        <td class="text-right">
                                            <t t-esc="round(opening_balance, 2)"
                                               t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                    </tr>
                                    <tr t-foreach="o._get_report_patient(partner.id,  o.type, o.start_date, o.end_date)"
                                        t-as="d">
                                        <t t-set="rb" t-value="rb + d.credit - d.debit"/>
                                        <t t-set="com_total"
                                           t-value="com_total + opening_balance + d.debit - d.credit"/>
                                        <td>
                                            <span t-esc="d['date']" t-options="{'widget': 'date'}"/>
                                        </td>

                                        <td>
                                            <t t-if="d.panel and not d.payment_id and not d.move_id.bed_invoice">
                                                <span t-field="d.panel"/>
                                                /
                                                <span t-esc="d.move_id.name"/>
                                            </t>
                                            <t t-if="not d.panel and not d.payment_id and not d.move_id.bed_invoice">
                                                <span t-esc="d.move_id.name"/>
                                                /
                                                <span t-esc="d.name"/>
                                            </t>
                                            <t t-if="d.payment_id and not d.move_id.bed_invoice">
                                                Payment/
                                                <span t-esc="d.payment_id.name"/>
                                            </t>
                                            <t t-if="d.move_id.bed_invoice and not d.payment_id">
                                                Bed Charge/
                                                <span t-esc="d.move_id.name"/>
                                            </t>
                                        </td>

                                        <td class="text-right">
                                            <t t-esc="round(d.debit, 2)"
                                               t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                        <td class="text-right">
                                            <t t-esc="round(d.credit, 2)"
                                               t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                        <td class="text-right">
                                            <t t-esc="round(com_total, 2)"
                                               t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                        </td>
                                        <t t-set="com_total" t-value="com_total - opening_balance"/>
                                    </tr>

                                </tbody>
                            </table>
                            <p style="page-break-after: always"></p>
                        </t>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>