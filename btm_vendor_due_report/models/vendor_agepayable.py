# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
from odoo import api, models, fields, _, tools
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
import calendar
from lxml import etree


class VendorDueReport(models.Model):
    _name = 'vendor.due.report'
    _description = 'Vendor Future Payment Report'

    vendor_id = fields.Many2one('res.partner', 'Vendor')
    days_0_30 = fields.Float('0-30')
    date_0 = fields.Date()
    days_31_60 = fields.Float('31-60')
    date_31 = fields.Date()
    days_61_90 = fields.Float('61-90')
    date_61 = fields.Date()
    days_91_120 = fields.Float('91-120')
    days_121 = fields.Float('121-')
    date_91 = fields.Date()
    date_121 = fields.Date()
    initial_bal = fields.Float(compute='_calculate_initial_balance')
    total_pay = fields.Float(compute='_total_month_amt_pay', store=True)

    def _calculate_initial_balance(self):
        for rec in self:
            current_date = fields.Date.context_today(self)
            previous_month = current_date + relativedelta(months=-1)
            _, num_days = calendar.monthrange(previous_month.year, previous_month.month)
            last_month_day = date(previous_month.year, previous_month.month, num_days)
            rec.initial_bal = 0
            amls = self.env['account.move.line'].search([('partner_id', '=', rec.vendor_id.id),
                                                         ('parent_state', '=', 'posted'),
                                                         ('date', '<=', last_month_day)])
            if amls:
                amt = 0.0
                for aml in amls:
                    amt += (aml.debit - aml.credit)
                rec.initial_bal = amt

    @api.depends('days_0_30', 'days_31_60', 'days_61_90', 'days_91_120', 'days_121')
    def _total_month_amt_pay(self):
        for rec in self:
            total = 0.0
            total += rec.days_0_30 + rec.days_31_60 + rec.days_61_90 + rec.days_91_120 + rec.days_121
            rec.total_pay = total

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        res = super(VendorDueReport, self).fields_view_get(view_id=view_id, view_type=view_type,
                                                           toolbar=toolbar,
                                                           submenu=submenu)
        if view_type == 'tree':
            doc = etree.XML(res['arch'])
            #     ==============================================================
            current_date = fields.Date.context_today(self)
            _, num_days = calendar.monthrange(current_date.year, current_date.month)
            first_month_day = date(current_date.year, current_date.month, 1)
            # =====================================================================
            sec_month = current_date + relativedelta(months=+1)
            _, num_days = calendar.monthrange(sec_month.year, sec_month.month)
            sec_month_day = date(sec_month.year, sec_month.month, 1)
            # ====================================================================
            third_month = current_date + relativedelta(months=+2)
            _, num_days = calendar.monthrange(third_month.year, third_month.month)
            third_month_day = date(third_month.year, third_month.month, 1)
            # ====================================================================
            fourth_month = current_date + relativedelta(months=+3)
            _, num_days = calendar.monthrange(fourth_month.year, fourth_month.month)
            fourth_month_day = date(fourth_month.year, fourth_month.month, 1)
            # ======================================================================
            fifth_month = current_date + relativedelta(months=+4)
            _, num_days = calendar.monthrange(fifth_month.year, fifth_month.month)
            fifth_month_day = date(fifth_month.year, fifth_month.month, 1)
            # ======================================================================
            for node in doc.xpath("//field[@name='days_0_30']"):
                first_name = first_month_day.strftime("%B")
                node.set("string", str(first_name))
            for node in doc.xpath("//field[@name='days_31_60']"):
                sec_name = sec_month_day.strftime("%B")
                node.set("string", str(sec_name))
            for node in doc.xpath("//field[@name='days_61_90']"):
                third_name = third_month_day.strftime("%B")
                node.set("string", str(third_name))
            for node in doc.xpath("//field[@name='days_91_120']"):
                fourth_name = fourth_month_day.strftime("%B")
                node.set("string", str(fourth_name))
            for node in doc.xpath("//field[@name='days_121']"):
                fifth_name = 'After' + ' ' + fourth_month_day.strftime("%B")
                node.set("string", str(fifth_name))
            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res
