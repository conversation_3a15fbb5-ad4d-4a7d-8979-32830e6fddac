<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

	<t t-extend="ClientLine">
		<t t-jquery=".client-line td:nth-child(2)" t-operation="replace"/>
		<t t-jquery=".client-line td:nth-child(2)" t-operation="after">
			<td class="credit"><t t-esc='partner.credit'/></td>
		</t>
		<t t-jquery=".client-line td:nth-child(3)" t-operation="after">
			<td><button class="btn-pay"><i class="fa fa-money"/></button></td>
		</t>
	</t>
	
	<t t-extend="ClientListScreenWidget">
		<t t-jquery=".client-list thead > tr > th:nth-child(2)" t-operation="replace"/>
		<t t-jquery=".client-list thead > tr > th:nth-child(2)" t-operation="after">
			<th>Credit (Due)</th>
		</t>
		<t t-jquery=".client-list thead > tr > th:nth-child(3)" t-operation="after">
			<th>Pay</th>
		</t>
	</t>

<!-- 	payment popup widget -->
	<t t-name="PaymentPopupWidget">
        <div class="modal-dialog">
            <div class="popup popup-confirm payment-popup">
                <p class="title"><t t-esc=" widget.options.title || 'Payment ?' " /></p>
                <div class="payment-box clearfix">
	                <div class="payment-box-left-1">
	                	<div class="payment-detail">
	                		<span class="label">Method</span>
	                		<select class="paymentmethod needsclick" name="paymentmethod">
	                			<t t-foreach="widget.pos.payment_methods" t-as="payment_method">
			                		<option t-att-data-id="payment_method.id">
			                    		<t t-esc="payment_method.name" />
			                		</option>
			            		</t>
			            	</select>
			            </div>
	                </div>
	                <div class="payment-box-right-1">
	                	<div class="payment-detail">
	                		<span class="label">Due</span>
	                		<input class="payment-due" type="text" name="due" t-att-value="widget.options.credit"/>
	                	</div>
	                </div>
                </div>
                <div class="footer">
                    <div class="button confirm">
                        Confirm 
                    </div>
                    <div class="button cancel">
                        Cancel 
                    </div>
                </div>
            </div>
        </div>
    </t>


<!--	Paymentline widget inherited to add extra column to payment line for payment remark recording	-->
	<t t-extend="PaymentScreen-Paymentlines">
		<!--<t t-jquery="table > thead > tr > th:nth-child(4)" t-operation="after">
			<th>Remark</th>
		</t>-->
		<t t-jquery="table > tbody > t:nth-child(1)" t-operation="replace">
			<t t-foreach='paymentlines' t-as='line'>
				<t t-if='line.selected'>
					<tr class='paymentline selected'>
						<td class='col-due'> <t t-esc='widget.format_currency_no_symbol(order.get_due(line))' /> </td>
						<td class='col-tendered edit'>
							<t t-esc='widget.inputbuffer' />
						</td>
						<t t-if='order.get_change(line)'>
							<td class='col-change highlight' >
								<t t-esc='widget.format_currency_no_symbol(order.get_change(line))' />
							</td>
						</t>
						<t t-if='!order.get_change(line)'>
							<td class='col-change' ></td>
						</t>

						<td class='col-name' > <t t-esc='line.name' /> </td>
						<!--<td class="col-remark remark-edit">
							<input type="text" name="remark-input" t-att-value="line.get_remark()"/>
						</td>-->
						<td class='delete-button' t-att-data-cid='line.cid'> <i class='fa fa-times-circle' /> </td>
					</tr>
					<tr class='paymentline selected'>
						<td colspan="3" class="col-remark remark-edit">
							<input type="text" name="remark-input" placeholder="Remark" t-att-value="line.get_remark()"/>
						</td>
						<td colspan="2" class="col-mobile mobile-edit">
							<input type="text" name="mobile-input" placeholder="Mobile" t-att-value="line.get_mobile()"/>
						</td>
					</tr>
				</t>
				<t t-if='!line.selected'>
					<tr class='paymentline' t-att-data-cid='line.cid'>
						<td class='col-due'> <t t-esc='widget.format_currency_no_symbol(order.get_due(line))' /> </td>
						<td class='col-tendered'> <t t-esc='widget.format_currency_no_symbol(line.get_amount())' /> </td>
						<td class='col-change'>
							<t t-if='order.get_change(line)'>
								<t t-esc='widget.format_currency_no_symbol(order.get_change(line))' />
							 </t>
						</td>
						<td class='col-name'> <t t-esc='line.name' /> </td>
						<!--<td class="col-remark">
							<input type="text" name="remark-input" t-att-value="line.get_remark()"/>
						</td>-->
						<td class='delete-button' t-att-data-cid='line.cid'> <i class='fa fa-times-circle' /> </td>
					</tr>
					<tr class='paymentline'>
						<td colspan="3" class="col-remark">
							<input type="text" name="remark-input" readonly="1" placeholder="Remark" t-att-value="line.get_remark()"/>
						</td>
						<td colspan="2" class="col-mobile">
							<input type="text" name="mobile-input" readonly="1" placeholder="Mobile" t-att-value="line.get_mobile()"/>
						</td>
					</tr>
				</t>
			</t>
		</t>
	</t>


<!--	Credit due payment receipt	-->
	<t t-name="PaymentReceipt">
		<div class="pos-receipt">
			<div class="pos-center-align"><t t-esc="order.formatted_validation_date"/> <span>Payment Receipt</span></div>
			<br/>
			<t t-if='receipt.company.logo'>
				<img class="pos-receipt-logo" t-att-src='receipt.company.logo' alt="Logo"/>
				<br/>
			</t>
			<t t-if='!receipt.company.logo'>
				<h2 class="pos-receipt-center-align">
					<t t-esc='receipt.company.name' />
				</h2>
				<br/>
			</t>
			<div class="pos-receipt-contact">
				<t t-if='receipt.company.contact_address'>
					<div><t t-esc='receipt.company.contact_address' /></div>
				</t>
				<t t-if='receipt.company.phone'>
					<div>Tel:<t t-esc='receipt.company.phone' /></div>
				</t>
				<t t-if='receipt.company.vat'>
					<div>VAT:<t t-esc='receipt.company.vat' /></div>
				</t>
				<t t-if='receipt.company.email'>
					<div><t t-esc='receipt.company.email' /></div>
				</t>
				<t t-if='receipt.company.website'>
					<div><t t-esc='receipt.company.website' /></div>
				</t>
				<t t-if='receipt.header_html'>
					<t t-raw='receipt.header_html' />
				</t>
				<t t-if='!receipt.header_html and receipt.header'>
					<div><t t-esc='receipt.header' /></div>
				</t>
				<t t-if='receipt.cashier'>
					<div class='cashier'>
						<div>--------------------------------</div>
						<div>Served by <t t-esc='receipt.cashier' /></div>
					</div>
				</t>
			</div>
			<br /><br />
			<div class='orderlines'>
				<colgroup>
					<col width='50%' />
					<col width='50%' />
					<!--<col width='25%' />-->
				</colgroup>
				<div>
					<span><b>Payment Method</b></span>
					<span class="pos-receipt-right-align"><b>Payment Due</b></span>
				</div>
				<br/>
				<div class="Credit-due-line">
					<span>
						<t t-esc="payment.payment_method_name"/>
					</span>
					<span class="pos-receipt-right-align">
						<t t-esc="widget.format_currency(payment.due)"/>
					</span>
				</div>
			</div>
			<br />
			<t t-if="receipt.footer">
				<br />
				<div style='text-align:center'>
					<t t-esc="receipt.footer" />
				</div>
			</t>
		</div>
    </t>


</templates>