# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
from odoo import models, fields, api


# noinspection PyProtectedMember
class NuroMedicalTest(models.Model):
    """
    Class Docstring
    """
    _inherit = 'nuro.medical.test'

    @api.onchange('user_id')
    def _get_default_branch(self):
        branch_lst = []
        if self.env.user.branch_ids:
            if len(self.env.user.branch_ids) == 1:
                self.branch_id = self.env.user.branch_ids[0]
            for branch in self.env.user.branch_ids:
                branch_lst.append(branch.id)
        else:
            for b in self.env['res.branch'].search([]):
                branch_lst.append(b.id)
        return {'domain': {'branch_id': [('id', 'in', branch_lst)]}}

    # def write(self, vals):
    #     self.clear_caches()
    #     return super(NuroMedicalTest, self).write(vals)

    branch_id = fields.Many2one('res.branch', string='Branch')

    def prepare_labtest_entry(self, lab_entry_line):
        """
        Private Method to create Invoice Payment
        :param lab_entry_line:
        :return:
        """
        res = super(NuroMedicalTest, self).prepare_labtest_entry(lab_entry_line)
        res.update({
            'branch_id': self.branch_id.id or False
        })
        return res

    def prepare_imaging_entry(self, imaging_line):
        """
        Private Method to create Invoice Payment
        :param imaging_line:
        :return:
        """
        res = super(NuroMedicalTest, self).prepare_imaging_entry(imaging_line)
        res.update({
            'branch_id': self.branch_id.id or False
        })
        return res

    def vals_create_other_service_entry(self, lines):
        """
        Create Other Service Entry Vals
        :param lines:
        :return:
        """
        res = super(NuroMedicalTest, self).vals_create_other_service_entry(lines)
        res.update({
            'branch_id': self.branch_id.id or False
        })
        return res


class NuroLabtestLines(models.Model):
    """
    Class Docstring
    """
    _inherit = 'nuro.lab.line'

    branch_id = fields.Many2one('res.branch', string='Branch', related='medical_test_id.branch_id', store=True)


class NuroImagingTestLines(models.Model):
    """
    Class Docstring
    """
    _inherit = 'nuro.imaging.line'

    branch_id = fields.Many2one('res.branch', string='Branch', related='medical_test_id.branch_id', store=True)


class CashierPanel(models.Model):
    _inherit = 'cashier.panel'

    def mc_create_parent_labtest_result_vals(self):
        """
        Labtest Parent Branch
        """
        res = super(CashierPanel, self).mc_create_parent_labtest_result_vals()
        res.update({
            'branch_id': self.branch_id.id
        })
        return res

    def mc_create_child_labtest_result_vals(self,labtest_id, labtest_line):
        """
        Labtest Parent Branch
        """
        res = super(CashierPanel, self).mc_create_child_labtest_result_vals(labtest_id=labtest_id, labtest_line=labtest_line)
        res.update({
            'branch_id': self.branch_id.id
        })
        return res

    def mc_create_imaging_test_lines(self, line):
        """
        Labtest Parent Branch
        """
        res = super(CashierPanel, self).mc_create_imaging_test_lines(line=line)
        res.update({
            'branch_id': self.branch_id.id
        })
        return res

    def mc_create_vals_other_service_entry_line(self, line):
        """
        Labtest Parent Branch
        """
        res = super(CashierPanel, self).mc_create_vals_other_service_entry_line(line=line)
        res.update({
            'branch_id': self.branch_id.id
        })
        return res

    def mc_vals_create_other_service_entry(self, lines):
        """
        Labtest Parent Branch
        """
        res = super(CashierPanel, self).mc_vals_create_other_service_entry(lines=lines)
        res.update({
            'branch_id': self.branch_id.id
        })
        return res
