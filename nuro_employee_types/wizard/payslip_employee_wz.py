from odoo.exceptions import UserError

from odoo import api, models, _


class HrPayslipEmployees(models.TransientModel):
    _inherit = 'hr.payslip.employees'

    @api.model
    def default_get(self, fields):
        domain = []
        rec = super(HrPayslipEmployees, self).default_get(fields)
        context = dict(self._context or {})
        active_ids = context.get('active_ids')
        employees = self.env['hr.employee']
        if active_ids:
            payslip_obj = self.env['hr.payslip.run'].browse(active_ids)
            if payslip_obj:
                contract_data = self.env['hr.contract'].sudo().search([('state', '=', 'open')])
                employee_ids = []
                for contract in contract_data:
                    employee_ids.append(contract.employee_id.id)
                if payslip_obj.department_id:
                    domain += [('department_id', '=', payslip_obj.department_id.id), ('id', 'in', employee_ids)]
                if payslip_obj.type_selection:
                    domain += [('type_selection', '=', payslip_obj.type_selection), ('id', 'in', employee_ids)]
                if domain:
                    emp_ids = employees.search(domain)
                    if emp_ids:
                        rec['employee_ids'] = [(6, 0, emp_ids.ids)]
                    else:
                        rec['employee_ids'] = [(6, 0, [])]
                else:
                    emp_ids = employees.search([('id', 'in', employee_ids)])
                    if emp_ids:
                        rec['employee_ids'] = [(6, 0, emp_ids.ids)]
        return rec
