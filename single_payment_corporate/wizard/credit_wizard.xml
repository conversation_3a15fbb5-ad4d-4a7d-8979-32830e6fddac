<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="view_single_payment_credit_wizard_form_corporate_form" model="ir.ui.view">
            <field name="name">Single Payment Credit Wizard</field>
            <field name="model">single.payment.credit.wizard</field>
            <field name="inherit_id" ref="nuro_single_payment.view_single_payment_credit_wizard_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='single_payment_id']" position="after">
                    <field name="corporate_discount" force_save="1" invisible="1"/>
                    <field name="corporate_id" options="{'no_open': True, 'no_create': True}" force_save="1"/>
                    <field name="corporate_identification_number" force_save="1" readonly="1"
                           attrs="{'invisible': [('corporate_id', '=', False)], 'required': [('corporate_id', '!=', False)]}"/>
                    <field name="id_number" force_save="1"
                           attrs="{'invisible': [('corporate_id', '=', False)], 'required': [('corporate_id', '!=', False)]}"/>
                    <field name="employee_name" force_save="1"
                           attrs="{'invisible': [('corporate_id', '=', False)], 'required': [('corporate_id', '!=', False)]}"/>
                </xpath>

                <field name="discount_amount" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', ('corporate_discount', '=', True),('approved_discount',
                        '=', True)], 'invisible': [('bill_to_type', 'in', ('hospital', 'hospital_employee',
                        'insurance'))]}
                    </attribute>
                </field>
            </field>
        </record>

    </data>
</odoo>