# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
from odoo import fields, api, models, _


class Scrap(models.Model):
    _inherit = 'stock.location'

    consume_analytic_id = fields.Many2one('account.analytic.account',
                                        string='Material Consume Analytic Account')


class Account(models.Model):
    _inherit = 'account.move'

    def update_line_details_stock_mat_consume_analytic_am(self):
        """Update Line Details Stock Analytic Account"""
        if self.stock_move_id and self.stock_move_id.picking_id.supply_consume_id:
            line_id = self.line_ids.filtered(lambda line: line.account_id.user_type_id.name == 'Expenses')
            if line_id:
                analytic = self.stock_move_id.location_id.consume_analytic_id
                line_id.update({'analytic_account_id': analytic and analytic.id or False})

    def _recompute_dynamic_lines(self, recompute_all_taxes=False, recompute_tax_base_amount=False):
        """Super call and update Stock Analytic Account data after recomputation."""
        res = super(Account, self)._recompute_dynamic_lines(recompute_all_taxes, recompute_tax_base_amount)
        # Update Stock Analytic Account data after dynamic recomputation
        # for move in self:
        self.update_line_details_stock_mat_consume_analytic_am()
        return res


