import logging
from datetime import timedel<PERSON>,datetime
from odoo.exceptions import UserError
from zk import ZK
import pytz
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo import models, fields, api, tools, _
_logger = logging.getLogger(__name__)
global _zk_lib_warning
_zk_lib_warning = False
global _zk_lib_imported
_zk_lib_imported = False

try:
    import ZK
    _zk_lib_imported = True

except ImportError:

    if not _zk_lib_warning:
        _logger.warning(
            "The `ZK` Python module is not installed, Try: pip install -U pyzk."
        )
        _zk_lib_warning = True


class NuroBioConnect(models.Model):
    _inherit = 'nuro.biometric.connect'

    def _check_checkout_not_found(self, attendance_id):
        """
        check last employee attendance if not checkout
        """
        if attendance_id:
            self.env['hr.attendance'].create({'employee_id': attendance_id.employee_id.id,
                                              'attendance_date': fields.Date.context_today(self),
                                              # 'machine_checkout': attendance_id.machine_checkin +
                                              #                     self.env.company.checkout_time,
                                              'attendance_remark': 'checkout',
                                              'type': 'checkout'})
        return True

    def _check_checkin_not_found(self, emp, check_out, check_in):
        """
        check last employee attendance if not checkin
        """
        if emp:
            checkout_att_id = self.env['hr.attendance'].create(
                {'employee_id': emp,
                  'attendance_date': fields.Date.context_today(self),
                  'attendance_remark': 'checkin',
                  'check_out': check_out,
                  'check_in': check_in,
                  'type': 'checkin'})
            if checkout_att_id:
                checkout_att_id.onchange_employee_shift()

    def download_attendance(self):
        # override for check checkout and checkin
        """
        Function to fetch get the machine attendance and update into the system and also delete from the machine
        :return:
        """
        att_obj = self.env['hr.attendance']
        emp_obj = self.env['hr.employee']
        utc_diff = timedelta(hours=self.utc_hour, minutes=self.utc_minute)
        zk = ZK(str(self.name), port=self.port_no, timeout=10, password=0, force_udp=False, ommit_ping=False)
        conn = zk.connect()
        zk.enable_device()
        conn.set_time(datetime.today() - utc_diff)
        if conn:
            att = conn.get_attendance()
            if att:
                for at in att:
                    search_empl = emp_obj.search([('barcode', '=', str(at.user_id)), ('active', '=', True)])
                    if search_empl:
                        user_tz = self.env.user.tz or pytz.utc
                        local = pytz.timezone(user_tz)
                        if at.punch == 0:
                            attendance_out = self.env['hr.attendance'].search([('employee_id', '=', search_empl.id),
                                                                               ('check_out', '=', False),
                                                                               ('check_in', '!=', False),
                                                                           ], order='id desc', limit=1)
                            if attendance_out:
                                diff = (at.timestamp + utc_diff) - attendance_out.check_in
                                if diff and diff.seconds:
                                    if diff.seconds < 59:
                                        continue
                                out_time = attendance_out.check_in + timedelta(seconds=self.env.company.checkout_time)
                                if out_time < attendance_out.check_in:
                                    out_time = attendance_out.check_in + timedelta(seconds=1)
                                attendance_out.update({'check_out': out_time,
                                                       'attendance_remark': 'checkout'})
                            try:
                                check = att_obj.search([('employee_id', '=', search_empl.id),
                                                        ('check_in', '=', (at.timestamp + utc_diff))])
                                if check:
                                    continue
                                checking_att_id = att_obj.create({
                                                    'employee_id': search_empl.id,
                                                    'check_in': (at.timestamp + utc_diff),
                                                    'type': 'checkin',
                                                    'attendance_remark': 'correct'
                                                })
                                if checking_att_id:
                                    date_attendance = datetime.strftime(
                                        pytz.utc.localize(datetime.strptime(str(checking_att_id.check_in),
                                                                            DEFAULT_SERVER_DATETIME_FORMAT)
                                                          ).astimezone(local), "%Y-%m-%d %H:%M:%S")
                                    checking_att_id.checkin_date = date_attendance
                                    checking_att_id.check_assign_shift()
                            except Exception:
                                pass

                        if at.punch == 1:
                            attendance_in = self.env['hr.attendance'].search([('employee_id', '=', search_empl.id),
                                                                              ('check_in', '!=', False),
                                                                              ('check_out', '=', False),
                                                                           ], order='id desc', limit=1)
                            if attendance_in:
                                attendance_in.update({'check_out': (at.timestamp + utc_diff),
                                                       'attendance_remark': 'checkin'})
                            else:
                                check = att_obj.search([('employee_id', '=', search_empl.id),
                                                        ('check_in', '=', (at.timestamp + utc_diff) - timedelta(seconds=self.env.company.checkin_time))])
                                if check:
                                    continue
                                try:
                                    checkout_atten_id = self._check_checkin_not_found(emp=search_empl.id,
                                                                                      check_in=(at.timestamp + utc_diff) - timedelta(seconds=self.env.company.checkin_time),
                                                                                      check_out=(at.timestamp + utc_diff))
                                    if checkout_atten_id:
                                        out_date_attendance = datetime.strftime(
                                            pytz.utc.localize(datetime.strptime(str(checkout_atten_id.check_in),
                                                                                DEFAULT_SERVER_DATETIME_FORMAT)
                                                              ).astimezone(local), "%Y-%m-%d %H:%M:%S")
                                        checkout_atten_id.checkin_date = out_date_attendance
                                        checkout_atten_id.check_assign_shift()
                                except Exception:
                                    pass
                conn.clear_attendance()
