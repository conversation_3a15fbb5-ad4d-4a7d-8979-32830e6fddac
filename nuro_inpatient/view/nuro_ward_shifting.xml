<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="nuro_ward_shifting_request_form_view" model="ir.ui.view">
            <field name="name">Ward Shifting Request</field>
            <field name="model">nuro.ward.shifting.request</field>
            <field name="arch" type="xml">
                <form string="Ward Shifting Request" create="false">
                    <header>
                        <button name="shift_patient" type="object" string="Shift" class="oe_highlight"
                                states="request" invisible="context.get('ipd_inv', False)"/>
                        <button name="action_reject_shift_request" type="object" string="Reject" class="oe_highlight"
                                states="request" invisible="context.get('ipd_inv', False)"/>
                        <field name="state" widget="statusbar" statusbar_visible="request,shifted"
                               statusbar_colors="{'request':'red','shifted':'red'}"/>
                    </header>
                    <sheet>
                        <style>
                            .o_form_view .o_horizontal_separator {
                            color: #174290;
                            font-weight: bold;
                            }
                            a {
                            color: #174290;
                            text-decoration: none;
                            background-color: transparent;
                            }
                            h1 {
                            color: #174290;
                            }
                        </style>
                        <group>
                            <group string="Patient Information">
                                <field name="patient_id" options="{'no_create': True, 'no_open': True}" readonly="1"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                                <field name="ward_id" readonly="1"/>
                                <field name="bed_id" options="{'no_create': True, 'no_open': True}"
                                       domain="[('ward_id', '=', ward_id), ('state', '=', 'free')]"/>
                                <field name="ward_rate_card_ids" widget="many2many_tags" invisible="1"/>
                                <field name="ward_rate_card_id" domain="[('id', 'in', ward_rate_card_ids)]"
                                       options="{'no_create': True, 'no_open': True}"/>
                            </group>
                            <group string="Admission Information">
                                <field name="inpatient_id" readonly="1"/>
                                <field name="list_price" attrs="{'readonly': [('ward_rate_card_id', '!=', False)]}"
                                       required="1" force_save="1"/>
                                <field name="date_request" readonly="1"/>
                                <field name="date_shift" readonly="1"
                                       attrs="{'invisible': [('date_shift', '=', False)]}"/>
                                <field name="reason" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="nuro_ward_shifting_request_list_view" model="ir.ui.view">
            <field name="name">Ward Shifting Request</field>
            <field name="model">nuro.ward.shifting.request</field>
            <field name="arch" type="xml">
                <tree string="Ward Shifting Request" create="false" edit="false">
                    <field name="patient_id"/>
                    <field name="date_request"/>
                    <field name="date_shift"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="nuro_ward_shifting_request_action_ipd" model="ir.actions.act_window">
            <field name="name">Ward Shifting Request</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.ward.shifting.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'request')]</field>
            <field name="context">{'ipd_inv': True, 'create': False, 'edit': False}</field>
        </record>

        <record id="nuro_ward_shifting_request_action_admission" model="ir.actions.act_window">
            <field name="name">Ward Shifting Request</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.ward.shifting.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'request')]</field>
            <field name="context">{'ipd_inv': False, 'create': False}</field>
        </record>
    </data>
</odoo>