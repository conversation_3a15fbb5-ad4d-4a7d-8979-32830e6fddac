# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from odoo import fields, models


class ResUser(models.Model):
    """
    Class Docstring
    """
    _inherit = 'res.users'

    ward_ids = fields.Many2many("nuro.inpatient.ward", "ward_users_rel", "ward_id", "users_id", string="Ward Access")

    def write(self, values):
        """
        Write Method for clear cache
        :param values:
        :return:
        """
        res = super(ResUser, self).write(values)
        self.clear_caches()
        return res
