<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!--Papaer format for sample label-->
        <record id="paperformat_partner_history_taking_report" model="report.paperformat">
            <field name="name">HMS: History Taking Report</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">40</field>
            <field name="margin_bottom">30</field>
            <field name="margin_left">6</field>
            <field name="margin_right">6</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">40</field>
            <field name="dpi">90</field>
        </record>

        <!--Appointment history Traking report menus-->
        <report string="History Tracking Report"
                id="action_report_patient_history_tracking"
                model="nuro.appointment"
                report_type="qweb-pdf"
                name="nuro_appointment_history_tracking.appointment_history_tacking_report_result"
                file="nuro_appointment_history_tracking.appointment_history_tacking_report_result"
                paperformat="paperformat_partner_history_taking_report" menu="False"/>

        <!--Appointment history Traking report menus-->
        <report string="Appointment Details"
                id="action_report_patient_details_appointment_all"
                model="nuro.appointment"
                report_type="qweb-pdf"
                name="nuro_appointment_history_tracking.appointment_history_tacking_report_result_all_appointment_receipt"
                file="nuro_appointment_history_tracking.appointment_history_tacking_report_result_all_appointment_receipt"
                paperformat="paperformat_partner_history_taking_report"/>

    </data>
</odoo>