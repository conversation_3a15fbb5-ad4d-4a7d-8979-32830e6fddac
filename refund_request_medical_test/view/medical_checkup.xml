<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="refund_request_form_inherit_medical_checkup" model="ir.ui.view">
            <field name="name">Medical Test</field>
            <field name="model">nuro.medical.test</field>
            <field name="inherit_id" ref="nuro_medical_test.nuro_medical_test_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//button[@name='%(nuro_medical_test.action_nuro_medical_test_refund_wizard)d']"
                       position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//button[@name='%(nuro_medical_test.action_nuro_medical_test_refund_wizard)d']"
                       position="after">
                    <field name="refund_request_id" invisible="1"/>
                    <field name="refund_request_state" invisible="1"/>
                    <button name="action_approve_refund_request" type="object" string="Approve Refund Request"
                            class="oe_highlight"
                            attrs="{'invisible': [('refund_request_state', '!=', 'requested')]}"
                            groups="refund_request.group_refund_approval"/>
                    <button name="action_approve_refund_request"
                            attrs="{'invisible':[('refund_request_state', '!=', 'awaiting_second_approval')]}"
                            string="Assign/Change Second Approval" type="object" class="oe_highlight"
                            groups="refund_request.group_refund_approval"/>
                    <button name="create_refund_payment_panel" type="object" string="Refund" class="oe_highlight"
                            attrs="{'invisible': ['|', ('state', '!=', 'send_to_test'), ('refund_request_state', '!=', 'approved')]}"
                            groups="refund_request.group_refund_cashier"/>
                    <button name="refund_request_wizard" type="object" string="Refund Request" class="oe_highlight"
                            states="send_to_test" groups="refund_request.group_refund_requester"/>
                </xpath>

                <xpath expr="//div[hasclass('oe_left')]" position="before">
                    <widget name="web_ribbon" title="Requested" bg_color="bg-danger"
                            attrs="{'invisible': [('refund_request_state', '!=', 'requested')]}"/>
                    <widget name="web_ribbon" title="Approved" bg_color="bg-danger"
                            attrs="{'invisible': [('refund_request_state', '!=', 'approved')]}"/>
                    <widget name="web_ribbon" title="Processed" bg_color="bg-danger"
                            attrs="{'invisible': [('refund_request_state', '!=', 'processed')]}"/>
                    <widget name="web_ribbon" title="Awaiting Second Approval" bg_color="bg-danger"
                            attrs="{'invisible': [('refund_request_state', '!=', 'awaiting_second_approval')]}"/>
                </xpath>

            </field>
        </record>

        <record id="refund_request_medical_checkup_form_view" model="ir.ui.view">
            <field name="name">Medical Checkup</field>
            <field name="model">nuro.medical.test</field>
            <field name="priority">179</field>
            <field name="arch" type="xml">
                <form string="Medical Checkup">
                    <header>
                        <field name="refund_request_id" invisible="1"/>
                        <field name="refund_request_state" invisible="1"/>
                        <button name="action_approve_refund_request" type="object" string="Approve Refund Request"
                                class="oe_highlight"
                                attrs="{'invisible': [('refund_request_state', '!=', 'requested')]}"
                                groups="refund_request.group_refund_approval"/>
                        <button name="action_approve_refund_request"
                                attrs="{'invisible':[('refund_request_state', '!=', 'awaiting_second_approval')]}"
                                string="Assign/Change Second Approval" type="object" class="oe_highlight"
                                groups="refund_request.group_refund_approval"/>
                        <button name="create_refund_payment_panel" type="object" string="Refund" class="oe_highlight"
                                attrs="{'invisible': ['|', ('state', '!=', 'send_to_test'), ('refund_request_state', '!=', 'approved')]}"
                                groups="refund_request.group_refund_cashier"/>
                        <button name="refund_request_wizard" type="object" string="Refund Request" class="oe_highlight"
                                states="send_to_test" groups="refund_request.group_refund_requester"/>
                        <field name="state" widget="statusbar" statusbar_visible="send_to_test"/>
                    </header>
                    <sheet>
                        <widget name="web_ribbon" title="Requested" bg_color="bg-danger"
                                attrs="{'invisible': [('refund_request_state', '!=', 'requested')]}"/>
                        <widget name="web_ribbon" title="Approved" bg_color="bg-danger"
                                attrs="{'invisible': [('refund_request_state', '!=', 'approved')]}"/>
                        <widget name="web_ribbon" title="Processed" bg_color="bg-danger"
                                attrs="{'invisible': [('refund_request_state', '!=', 'processed')]}"/>
                        <widget name="web_ribbon" title="Awaiting Second Approval" bg_color="bg-danger"
                                attrs="{'invisible': [('refund_request_state', '!=', 'awaiting_second_approval')]}"/>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Health Checkup #"/>
                                <h1>
                                    <field name="name" class="oe_inline" default_focus="1" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group string="Patient Information">
                                <field name="patient_id" readonly="1"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                                <field name="user_id" readonly="1" force_save="1" invisible="1"/>
                            </group>
                            <group string="Health Checkup Info">
                                <field name="doctor_id" readonly="1" options="{'no_open': True, 'no_create': True}"/>
                                <field name="date" readonly="1"/>
                                <field name="medical_master_id" readonly="1"
                                       options="{'no_create': True, 'no_open': True}"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="payment_method" readonly="1" invisible="1"/>
                            </group>
                            <group class="oe_subtotal_footer oe_right">
                                <field name="subtotal" class="oe_subtotal_footer oe_right" force_save="1" readonly="1"/>
                                <field name="discount_amount" class="oe_subtotal_footer oe_right" readonly="1"
                                       force_save="1"/>
                                <field name="total_amount" class="oe_subtotal_footer oe_right" force_save="1"/>
                                <field name="paid_amount" attrs="{'invisible': [('paid_amount', '=', 0.0)]}"
                                       class="oe_subtotal_footer oe_right"/>
                                <field name="credit_amount" attrs="{'invisible': [('credit_amount', '=', 0.0)]}"
                                       class="oe_subtotal_footer oe_right"/>
                                <field name="balance_amount" class="oe_subtotal_footer oe_right" force_save="1"/>
                                <field name="refund_amount" class="oe_subtotal_footer oe_right" force_save="1"
                                       readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="action_refund_request_medical_checkup_view" model="ir.actions.act_window">
            <field name="name">Medical Checkup</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.medical.test</field>
            <field name="search_view_id" ref="nuro_medical_test.nuro_medical_test_search_view"/>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'send_to_test')]</field>
            <field name="context">{'create': False, 'edit': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_medical_test.medical_test_list_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('refund_request_medical_checkup_form_view')})]"/>
        </record>

    </data>
</odoo>
