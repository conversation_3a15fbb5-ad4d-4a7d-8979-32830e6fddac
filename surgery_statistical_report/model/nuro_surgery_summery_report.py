# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
from odoo import fields, models, tools


class NuroSurgeryStatisticsReport(models.Model):
    _name = 'nuro.surgery.statistics.report'
    _description = 'Surgery Statistics Report'
    _auto = False
    _order = 'id desc'

    date = fields.Datetime('DATE', readonly=True)
    patient_id = fields.Many2one('nuro.patient', string='PATIENT', readonly=True)
    nuro_surgery_id = fields.Many2one('nuro.surgery', string='SURGERY', readonly=True)
    surgery_id = fields.Many2one('nuro.surgery.master', string='CASE', readonly=True)
    age = fields.Char('AGE', readonly=True)
    doctor_id = fields.Many2one('nuro.doctor', 'SURGEON', readonly=True)
    preop_oximeter = fields.Bo<PERSON>an(string='ON ROOM/O OXYGEN', readonly=True)
    treatment_medicine = fields.Char(string='TREATMENT & MEDICINE', compute='compute_profit_loss_value', readonly=True)
    material_cost = fields.Float('MATERIAL COST', compute='compute_profit_loss_value', readonly=True)
    surgery_income = fields.Float('SURGERY INCOME', readonly=True)
    discount = fields.Float('DISCOUNT', readonly=True)
    surgery_commission = fields.Float('SURGERY COMMISSION', compute='compute_profit_loss_value', readonly=True)
    surgery_profit = fields.Float('PROFIT', compute='compute_profit_loss_value', readonly=True)

    def compute_profit_loss_value(self):
        """
        Get current date profit Loss report
        :return:
        """
        commission_obj = self.env['nuro.doctor.commission']
        for rec in self:
            commission = commission_obj.search([
                ('surgery_id', '=', rec.surgery_id.id)
            ])
            medicine = []
            cost = 0.0
            surgery_medicare = rec.nuro_surgery_id.line_ids.filtered(lambda x: x.product_id.name)
            for line in surgery_medicare:
                medicine.append(line.product_id.name)
                cost += line.product_id.standard_price * line.done_qty
            rec.treatment_medicine = ', '.join(medicine)
            rec.material_cost = cost
            rec.surgery_commission = commission.amount
            rec.surgery_profit = rec.surgery_income - (commission.amount + cost + rec.discount)

    def get_surgery_statistical_value(self, from_date, to_date):
        tools.drop_view_if_exists(self._cr, 'nuro_surgery_statistics_report')
        self._cr.execute("""
                create view nuro_surgery_statistics_report as (
                    select 
                        id as id,
                        start::timestamp::date as date, 
                        patient_id as patient_id, 
                        id as nuro_surgery_id, 
                        surgery_id as surgery_id,
                        preop_oximeter as preop_oximeter,
                        age as age, 
                        doctor_id as doctor_id, 
                        charges as surgery_income, 
                        discount_amount as discount 
                    from nuro_surgery
                    where state in ('confirmed', 'in_progress', 'done', 'recovery') 
                    and start::timestamp::date >= '%s' 
                    and start::timestamp::date <= '%s'
                ) """ % (from_date, to_date))
