# -*- coding: utf-8 -*-
# Copyright  NUROSOLUTION Pvt Ltd
from odoo import api, fields, models, _
from odoo.exceptions import UserError


class NuroAppointment(models.Model):
    _inherit = 'nuro.appointment'

    vital_sign_line_ids = fields.One2many('appointment.vital.sign', 'appointment_id', string='Vital Sign')

    def button_action_appointment_vital_sign(self):
        """
        Button Action Appointment Vital Sign
        :return:
        """
        if self.state != 'scheduled':
            raise UserError(_('Appointment should be in Scheduled State to note Vital Sign.!!!'))
        action = self.env.ref('vital_sign_appointment.action_vital_sign_view').read()[0]
        ctx = {
            'default_appointment_id': self.id
        }
        action['context'] = ctx
        return action


class AppointmentVitalSign(models.Model):
    _name = 'appointment.vital.sign'
    _description = 'Appointment Vital Sign'

    appointment_id = fields.Many2one('nuro.appointment')
    date = fields.Datetime('Date', default=fields.Datetime.now)
    user_id = fields.Many2one('res.users', default=lambda self: self.env.user)
    body_temperature = fields.Float('Body temperature')
    pulse_rate = fields.Char('Pulse Rate')
    rbg = fields.Char('RBG')
    fbg = fields.Char('FBG')
    given_dose = fields.Char('Given Dose')
    spo = fields.Char('SPO2')
    respiration_rate = fields.Char('Respiration Rate')
    blood_pressure_systolic = fields.Char('BP Systolic')
    blood_pressure_diastolic = fields.Char('BP Diastolic')
    weight = fields.Float('Weight(kg)')
    height = fields.Float('Height(m)')
    bmi = fields.Float('BMI', compute='compute_bmi_value', store=True)

    @api.depends('weight', 'height')
    def compute_bmi_value(self):
        """
        Compute Vital Sign BMI value
        """
        for rec in self:
            if rec.height > 0.0 and rec.weight > 0.0:
                rec.bmi = rec.weight / (rec.height * rec.height)
            else:
                rec.bmi = 0.0

