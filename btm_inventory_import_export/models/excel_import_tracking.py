from odoo import fields,models,_,api
from datetime import date,datetime
from odoo.exceptions import UserError, ValidationError


class ExcelImport(models.Model):
    _name = 'excel.import.tracking'
    _order = 'id desc'

    name = fields.Char('Name')
    date = fields.Date('Date')
    stock_inv_id = fields.Many2one('stock.inventory', ondelete='cascade')
    not_import_tracking_line = fields.One2many('without.tracking.line', 'tracking_id')
    imported_tracking_line = fields.One2many('imported.tracking.line', 'tracking_id')
    user_id = fields.Many2one('res.users', string='Created User', default=lambda self: self.env.user)
    location_id = fields.Many2one('stock.location', 'Location')


class Trackingline(models.Model):
    _name = 'without.tracking.line'

    tracking_id = fields.Many2one('excel.import.tracking', ondelete='cascade')
    state = fields.Selection([('import', 'Imported'),
                              ('not_import', 'Not Import')])
    product_id = fields.Many2one('product.product', 'Product')
    onhand = fields.Float('On hand Qty')
    counted = fields.Float('Counted Qty')
    cost = fields.Float('Average Cost')
    name = fields.Char('Reason')
    is_product = fields.Boolean()
    is_counted = fields.Boolean()
    value = fields.Float('Value', compute='_calculate_value', store=True)

    @api.depends('cost', 'onhand')
    def _calculate_value(self):
        for rec in self:
            rec.value = rec.cost * rec.onhand

    @api.onchange('product_id')
    def onchange_product(self):
        if self.product_id:
            self.onhand = self.product_id.sudo().with_context(
                                                            location=self.tracking_id.location_id.id).qty_available
            self.cost = self.product_id.standard_price

    def create_stock_inventory_line(self):
        if self.product_id and self.counted >= 0.0:
            stock_line_id = self.env['stock.inventory.line'].sudo(). \
                create({'inventory_id': self.tracking_id.stock_inv_id.id,
                        'product_id': self.product_id.id,
                        'location_id': self.tracking_id.location_id.id,
                        'theoretical_qty': self.onhand,
                        'product_qty': self.counted,
                        'product_uom_id': self.product_id.uom_id.id})
            if stock_line_id:
                self.state = 'import'
                self.env['imported.tracking.line'].create({'product_id': self.product_id.id,
                                                           'onhand': self.onhand,
                                                           'counted': self.counted,
                                                           'cost': self.cost,
                                                           'tracking_id': self.tracking_id.id})
                self.tracking_id = False


class ImportedLine(models.Model):
    _name = 'imported.tracking.line'

    tracking_id = fields.Many2one('excel.import.tracking',ondelete='cascade')
    state = fields.Selection([('import', 'Imported'),
                              ('not_import', 'Not Import')])
    product_id = fields.Many2one('product.product', 'Product')
    onhand = fields.Float('On hand Qty')
    counted = fields.Float('Counted Qty')
    cost = fields.Float('Average Cost')
    value = fields.Float('Value', compute='_calculate_value', store=True)

    @api.depends('cost', 'onhand')
    def _calculate_value(self):
        for rec in self:
            rec.value = rec.cost * rec.onhand
