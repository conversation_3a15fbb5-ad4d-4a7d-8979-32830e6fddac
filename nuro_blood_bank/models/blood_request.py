# -*- coding: utf-8 -*-
# Part of nurosolution Pvt Ltd.
from odoo.exceptions import UserError

from odoo import models, fields, api, _

STATE = [
    ('draft', 'Draft'),
    ('lab_requested', 'Lab Requested'),
    ('in_process', 'In Process'),
    ('waiting_payment', 'Waiting Payment'),
    ('payment_done', 'Payment Done'),
    ('in_process', 'In Process'),
    ('done', 'Done'),
    ('transfusion_done', 'Transfusion Done'),
    ('cancel', 'Cancel'),
]


class BloodRequest(models.Model):
    _name = 'blood.request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    GENDER = [
        ('male', 'Male'),
        ('female', 'Female'),
    ]

    name = fields.Char('Ref', default='New')
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    doctor_id = fields.Many2one("nuro.doctor", string="Doctor")
    patient_id = fields.Many2one('nuro.patient', string='Patient', help="Patient Name",
                                 domain=[('deceased', '!=', True)])
    identification_code = fields.Char(string='ID#')
    gender = fields.Selection(GENDER, string='Sex', store=True)
    age = fields.Char('Patient Age', related='patient_id.age', store=True)
    mobile = fields.Char('Mobile')
    donor_request_lines = fields.One2many('donor.request.line', 'br_id', string='Donor Request Line')
    blood_group_id = fields.Many2one('blood.group', 'Blood Group', related="patient_id.blood_group_id", store=True)
    state = fields.Selection(STATE, string='Status', default='draft', readonly=True)
    blood_location_id = fields.Many2one('blood.location', string='Location', required=1, readonly=True,
                                        states={'draft': [('readonly', False)]}, domain=[('type', '=', 'internal')],
                                        default=lambda self: self.env.ref('nuro_blood_bank.blood_location_internal'))
    qty = fields.Integer('Request Units', readonly=True)
    date = fields.Date(string='Request Date', readonly=True, states={'draft': [('readonly', False)]},
                       default=fields.Datetime.now)
    bg_move_id = fields.Many2one('blood.group.move', 'Move Id')
    blood_collection_bag_id = fields.Many2one('blood.collection.bag', string='Blood Collection Bag')
    in_blood_move_line = fields.One2many('blood.group.move', 'in_br_id', string='Issue Move Line')
    out_blood_move_line = fields.One2many('blood.group.move', 'out_br_id', string='Consumed Move Line')
    in_blood_move_line_invisible = fields.Boolean(compute='_get_in_out_move_line_invisible')
    out_blood_move_line_invisible = fields.Boolean(compute='_get_in_out_move_line_invisible')
    other_hospital = fields.Boolean("Other Hospital", default=False)
    other_hospital_id = fields.Many2one("res.partner", string="Hospital",
                                        domain=[('is_partner_hospital', '=', True)])
    bdl_validation = fields.Boolean("BDL Validation", compute="_compute_bd_validation")
    qty_donor_number = fields.Integer("Qty Donor Number", compute="_compute_qty_donor_number")
    taken_by = fields.Selection([('nurse', 'Nurse'), ('family_member', 'Family Member')], string='Taken By')
    taken_by_user_id = fields.Many2one('res.users', string='Nurse')
    taken_by_family_member = fields.Char('Member Name')
    taken_date = fields.Datetime('Taken on')
    issued_qty = fields.Integer('Issued Qty', compute="compute_issued_qty_value")
    blood_donation_request_line = fields.One2many('blood.donation', 'request_id', string='Blood Donation Line')
    request_unit = fields.Integer('Request Unit')
    requested_unit = fields.Integer('Requested Unit')
    transfusion_qty = fields.Integer('Transfusion Qty')
    transfused_qty = fields.Integer('Transfused Qty')
    transfusion_running = fields.Boolean('Transfusion Running', default=False)
    request_validation = fields.Boolean('Request Validation', compute="_compute_request_validation")
    compatible_unit = fields.Integer('Compatible Unit', compute="_compute_compatible_unit_details")

    @api.depends('donor_request_lines')
    def _compute_compatible_unit_details(self):
        for rec in self:
            lines = rec.donor_request_lines.filtered(
                lambda x: x.cross_match_user_id and x.cross_match_status == 'compatible')
            rec.compatible_unit = lines and len(lines) or 0

    @api.depends('issued_qty', 'requested_unit')
    def _compute_request_validation(self):
        for rec in self:
            rec.request_validation = False
            lines = rec.donor_request_lines.filtered(
                lambda x: x.cross_match_user_id and x.cross_match_status == 'compatible')
            if rec.qty != 0 and rec.qty > rec.requested_unit and rec.state == 'in_process' and lines:
                rec.request_validation = True

    def request_unit_view(self):
        """Request Unit View"""
        allowed_qty = self.compatible_unit - self.requested_unit
        if self.request_unit > allowed_qty:
            raise UserError(_('You can not request more than %s') % allowed_qty)
        self.requested_unit += self.request_unit
        message = "%s unit of Blood has been requested by User %s" % (self.request_unit, self.env.user.name)
        self.sudo().message_post(body=message)
        self.request_unit = 0
        return message

    def start_transfusion(self):
        allowed_qty = self.issued_qty - self.transfused_qty
        if self.transfusion_qty > allowed_qty:
            raise UserError(_('Transfusion qty has not been issued yet.'))
        if self.transfusion_qty == 0:
            raise UserError(_('Please Input Transfusion qty.!!!'))
        message = "Transfusion has for %s unit of blood has been started by User %s" % (
            self.transfusion_qty, self.env.user.name)
        self.sudo().message_post(body=message)
        self.transfusion_running = True
        return message

    def update_transfusion_qty_details(self):
        """Update Transfusion Qty Details"""
        allowed_qty = self.issued_qty - self.transfused_qty
        if self.transfusion_qty > allowed_qty:
            raise UserError(_('Transfusion qty has not been issued yet.'))
        self.transfusion_running = False
        self.transfused_qty += self.transfusion_qty
        message = "Transfusion has for %s unit of blood has been Completed by User %s" % (
            self.transfusion_qty, self.env.user.name)
        self.sudo().message_post(body=message)
        self.transfusion_qty = 0
        if self.transfused_qty == self.qty:
            self.state = 'transfusion_done'
        return message

    def compute_issued_qty_value(self):
        """Compute Issued Qty value"""
        for rec in self:
            rec.issued_qty = sum(rec.donor_request_lines.mapped('issued_qty'))

    def _compute_qty_donor_number(self):
        """Compute Qty Donor Number"""
        for rec in self:
            donor_lines = rec.donor_request_lines
            incompatible_line = donor_lines.filtered(lambda x: x.cross_match_status == 'incompatible')
            qty = rec.qty - (len(donor_lines) - len(incompatible_line))
            rec.qty_donor_number = qty

    def _compute_bd_validation(self):
        """Compute BD Validation"""
        for rec in self:
            lines = rec.donor_request_lines.filtered(lambda x: not x.cross_match_user_id)
            if lines and rec.state in ('draft', 'in_process'):
                rec.bdl_validation = True
            else:
                rec.bdl_validation = False

    def update_cross_matching_result(self):
        """Update Cross-Matching Result"""
        donor_request_ids = self.donor_request_lines.filtered(lambda x: not x.cross_match_user_id).ids
        cross_match_readonly = False
        cross_match = 'done'
        if self.other_hospital:
            cross_match_readonly = True
            cross_match = False
        ctx = {
            'default_br_id': self.id,
            'default_donor_request_ids': [(6, 0, donor_request_ids)],
            'default_cross_match_readonly': cross_match_readonly,
            'default_cross_match': cross_match,
        }
        action = self.env.ref('nuro_blood_bank.action_cross_matching_view').read()[0]
        action['context'] = ctx
        return action


    def create_donor_line_addition_wizard(self):
        """Create Donor Line Addition Wizard"""
        if self.state not in ('draft', 'in_process'):
            raise UserError(_('You can not add donor if state is not in draft or in_process.'))

        # Fetch all eligible donor move lines
        donor_move_lines = self.env['blood.group.move'].search([
            ('type', '=', 'IN'),
            ('available_qty', '=', 1),
            ('state', 'in', ('done', 'reserved')),
            ('blood_group_id', '=', self.blood_group_id.id),
            ('blood_donation_id.result_details', '!=', False),
        ])

        # Filter: Reserved for same patient
        donation_lines_reserved = donor_move_lines.filtered(
            lambda x: x.state == 'reserved' and x.patient_id and self.patient_id and self.patient_id.id == x.patient_id.id
        )

        # Filter: Available 'done' lines
        donation_lines_done = donor_move_lines.filtered(lambda x: x.state == 'done')

        # Use reserved first, if available
        usable_lines = donation_lines_reserved or donation_lines_done

        # Filter move lines to include only those products that are needed in request
        request_product_ids = self.donor_request_lines.mapped('product_id').ids
        needed_products = self.request_line_ids.mapped('product_id').ids  # or any field representing requested products

        filtered_lines = usable_lines.filtered(
            lambda l: l.product_id.id in needed_products and l.donor_id
        )

        # Use exact blood group matches & needed products
        donor_ids = filtered_lines.mapped('donor_id').ids
        bd_line_ids = filtered_lines.ids  # key change: send all matching blood.move lines, even if same donor

        existing_lines = self.donor_request_lines
        incompatible_line = existing_lines.filtered(lambda x: x.cross_match_status == 'incompatible')
        qty_remaining = self.qty - (len(existing_lines) - len(incompatible_line))

        action = self.env.ref('nuro_blood_bank.action_donor_selection_wizard_view').read()[0]
        action['context'] = {
            'default_br_id': self.id,
            'default_qty': qty_remaining,
            'default_blood_group_id': self.blood_group_id.id,
            'default_bd_ids': [(6, 0, donor_ids)],
            'default_bd_line_ids': [(6, 0, bd_line_ids)],  # blood move lines
        }
        return action

    @api.onchange('patient_id')
    def onchange_patient_id(self):
        """onchange of patient fetching the information for patient as age mobile and gender"""
        if self.patient_id:
            self.identification_code = self.patient_id.identification_code
            self.age = self.patient_id.age
            self.mobile = self.patient_id.mobile
            self.gender = self.patient_id.gender

    def _get_in_out_move_line_invisible(self):
        """Get In Out Move Line Invisible Form"""
        for rec in self:
            if rec.in_blood_move_line:
                rec.in_blood_move_line_invisible = False
            else:
                rec.in_blood_move_line_invisible = True

            if rec.out_blood_move_line:
                rec.out_blood_move_line_invisible = False
            else:
                rec.out_blood_move_line_invisible = True

    def _prepare_bg_move_vals(self, qty, location_id, dest_location_id):
        """blood group move create vals dictionary"""
        return dict(blood_group_id=self.blood_group_id.id,
                    blood_collection_bag_id=self.blood_collection_bag_id and self.blood_collection_bag_id.id or False,
                    type='OUT',
                    location_id=location_id.id,
                    dest_location_id=dest_location_id.id,
                    quantity=qty,
                    state='draft',
                    request_date=fields.Date.today()
                    )

    def create_brood_group_move(self):
        """This method create blood group move entry"""
        bg_move_env = self.env['blood.group.move']
        location_id = self.env.ref('nuro_blood_bank.blood_location_consume')
        if not location_id:
            raise UserError(
                _('There is not any location define for blood Consume. Please create blood Consume location'))
        # for number in range(0, self.qty):
        bg_move_vals = self._prepare_bg_move_vals(self.qty, self.blood_location_id, location_id)
        bg_move_id = bg_move_env.create(bg_move_vals)
        return bg_move_id

    def action_done(self):
        """This method to done donation"""
        for donation in self:
            if donation.qty <= 0:
                raise UserError(_('Donation unit should be greater than 0.0 !'))
            move_id = donation.create_brood_group_move()
            move_id.action_confirm()
            move_id.action_done()
            donation.state = 'done'
            donation.bg_move_id = move_id.id

    # def action_confirm(self):
    #     """This method update status in process"""
    #     for donation in self:
    #         if not donation.date:
    #             raise UserError(_('Please Fill The Request Date !'))
    #         # sequence = self.env['ir.sequence'].next_by_code('blood.request')
    #         # donation.name = sequence
    #         donation.state = 'in_process'

    @api.model
    def create(self, values):
        """creating the sequence on creation of new Blood Transfusion"""
        sequence = self.env['ir.sequence'].next_by_code('blood.request')
        values['name'] = sequence
        res = super(BloodRequest, self).create(values)
        return res

    def create_donor_latest_request(self):
        """Create Donor Labtest Request"""
        donor_line = self.donor_request_lines.filtered(lambda x: x.assessment_result == 'fit')
        if not donor_line:
            raise UserError(_('Please Process with Assessment.!!!'))
        for line in donor_line:
            line.create_labtest_request()

    @api.constrains('donor_request_lines')
    def constrains_donor_request_lines(self):
        """Constrains Donor Request Lines"""
        if self.donor_request_lines and len(self.donor_request_lines) > self.qty:
            raise UserError(_('Donor Line Should not be more than total quanty.!!!'))


class DonorRequestLine(models.Model):
    _name = 'donor.request.line'
    _description = 'Donor Request Line'
    _order = 'id DESC'

    GENDER = [
        ('male', 'Male'),
        ('female', 'Female'),
    ]

    OPTIONS = [
        ('yes', 'Yes'),
        ('no', 'No'),
    ]

    name = fields.Char('Name')
    donor_id = fields.Many2one('blood.donor', string="Donor ID")
    bt_product_id = fields.Many2one('product.product', string="BT Type")
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    model_name = fields.Char('Model Name')
    model_id = fields.Integer('Model ID')
    doctor_id = fields.Many2one("nuro.doctor", string="Doctor")
    patient_id = fields.Many2one('nuro.patient', string='Patient', help="Patient Name",
                                 domain=[('deceased', '!=', True)])
    identification_code = fields.Char(string='ID#')
    gender = fields.Selection(GENDER, string='Sex', store=True)
    age = fields.Char('Patient Age', related='patient_id.age', store=True)
    mobile = fields.Char('Mobile')
    blood_group_id = fields.Many2one("blood.group", string="Blood Group", related="donor_id.blood_group_id", store=True)
    br_blood_group_id = fields.Many2one("blood.group", string="Blood Group")
    qty = fields.Integer('No. Of Units', default=1)
    issued_qty = fields.Integer('Issued Qty')
    weight = fields.Float('Weight')
    consent = fields.Selection([('yes', 'Yes'), ('no', 'No')], string="Consent")
    occupation = fields.Char('Occupation')
    contact_number = fields.Char(string='Mobile', related="donor_id.contact_number", store=True)
    date = fields.Date(string="Date", default=fields.Date.context_today)
    donor_type = fields.Selection([('first_donor', 'First Donor'), ('repeat_donor', 'Repeat Donor')],
                                  string='Donor Type')
    current_donation_date = fields.Date('Date')
    state = fields.Selection([
        ('new', 'New'),
        ('questionnaire', 'Questionnaire'),
        ('health_assessment', 'Health Assessment'),
        ('completed', 'Completed')
    ], string='State', default='new')
    no_donation = fields.Integer('No Of Donation Done Before')
    donation_last_time = fields.Selection([
        ('whole_blood', 'Whole blood'),
        ('apheresis', 'Apheresis Please Specify'),
        ('sd_red_cell', 'Single Donor Red Cell'),
        ('sd_platelets', 'Single Donor Platelets'),
        ('plasmapheresis', 'Plasmapheresis')
    ])
    apheresis = fields.Char('Specific Donation')
    place = fields.Char('Place')
    donation_date = fields.Date('Last Donation Date')
    problem_last_donation = fields.Selection([
        ('no', 'No Problem'),
        ('fainting', 'Fainting Bruise'),
        ('vain_finding', 'difficulties in finding Vein'),
        ('other', 'Other'),
    ])
    deferred_due_to = fields.Char('Deferred due to')
    br_id = fields.Many2one('blood.request', string="Request ID")
    screened_by = fields.Many2one('res.users', string='Screened By')
    date_screen = fields.Date(string='Date Screened')
    pregnant = fields.Selection(OPTIONS, string="Are you pregnant?")
    beast_feed = fields.Selection(OPTIONS, string="Do breast-feed?")
    comp_miss_birth_6_month = fields.Selection(OPTIONS, string="Gave birth or miscarriage in the last 6 months?")
    diarrhea_7_day = fields.Selection(OPTIONS, string="Had diarrhea in the last 7 days? -")
    wt_loss_3_month = fields.Selection(OPTIONS, string="Had unintendedly lost weight in rapidly the last 3 months?")
    dental_treat_3_days = fields.Selection(OPTIONS, string="Had dental treatments in the last 3 days ?")
    major_minor_surgeries_3_mnth_7_days = fields.Selection(OPTIONS,
                                                           string="Had major surgery in the last 6 months or minor surgery in the 7 days ?")
    drug_history_last_3_year = fields.Selection(OPTIONS,
                                                string="Had history of drug use or had you been imprisoned in the last 3 years?")
    bt_last_1_year = fields.Selection(OPTIONS, string="Had blood transfusion in the 1 year?")
    menstruation = fields.Selection(OPTIONS, string="Have menstruation? ( to be answered by female only)")
    enough_rest_last_night = fields.Selection(OPTIONS, string="Do you feel fit enough and have enough rest last night?")
    high_fat_diet_6_hrs = fields.Selection(OPTIONS, string="Had high-fat diet in the last 6 hours?")
    asprin_nsaids = fields.Selection(OPTIONS,
                                     string="Did you take aspirin, muscle relaxants or NSAIDS or any other Medicines?")
    antibiotics = fields.Selection(OPTIONS, string="Did you take antibiotics or any other medicine(s)?")
    hepatitis = fields.Selection(OPTIONS, string="Have you or any in your family member ever hepatitis?")
    cronic_sisease = fields.Selection(OPTIONS,
                                      string="Have you asthma ,epilepsy ,chronic skin disease, chronic cough, Tuberculosis, allergies, High blood pressure, heart/kidney/thyroid disease, cancer,bleeding disorder etc?")
    ear_body_piacing = fields.Selection(OPTIONS,
                                        string="Have ear/body piercing, tattoos made or removed or acupuncture?")
    vaccination_last_2_month = fields.Selection(OPTIONS, string="Did you get any vaccination in the last 2 months?")
    serum_injection = fields.Selection(OPTIONS, string="Did you receive serum injection in the last 1 year? -")
    other_comment = fields.Char('Other')
    assessment_result = fields.Selection([('fit', 'Fit'), ('not_fit', 'Not Fit')], string='Health Assessment',
                                         compute='get_assessment_result_value', store=True)
    labtest_id = fields.Many2one('nuro.medical.labtest.result', 'Labtest ID')
    questionnaire_result = fields.Selection([('fit', 'Fit'), ('not_fit', 'Not Fit')], string='Questionnaire Result')
    health_assessment_result = fields.Selection([('fit', 'Fit'), ('not_fit', 'Not Fit')],
                                                string='Health Assessment Result')
    issue_status = fields.Selection([
        ('not_issued', 'Not Issued'),
        ('partially_issued', 'Partially_Issued'),
        ('issued', 'Issued')
    ], default='not_issued', string='Issue Status')
    donation_receiving = fields.Boolean('Donation Receiving', compute='donation_receiving_method')
    blood_donation_id = fields.Many2one('blood.donation', string='Blood Donation ID')
    blood_move_id = fields.Many2one('blood.group.move', string='Blood Move ID')
    out_blood_move_id = fields.Many2one('blood.group.move', string='Blood Move ID')
    blood_bag_id = fields.Many2one("blood.collection.bag")
    blood_move_in_ids = fields.Many2many('blood.group.move', 'blood_group_move_in_rel', string='Blood Group Move IN')
    blood_move_out_ids = fields.Many2many('blood.group.move', 'blood_group_move_out_rel', string='Blood Group Move IN')

    question_relation_lines = fields.One2many('question.relation.lines', 'questionnaire_donor_request_id',
                                              string='Questionnaire')
    assessment_relation_lines = fields.One2many('question.relation.lines', 'assessment_donor_request_id',
                                                string='Health Assessment')
    cross_match = fields.Selection([('done', 'Done'), ('not_done', 'Not Done')], string='Cross Match')
    remark = fields.Char('Remark')
    cross_match_method = fields.Selection([('saline', 'Saline'), ('albumin', 'Albumin'), ('coombs', 'Coombs')],
                                          string='Cross Match Method')
    cross_match_method_ids = fields.Many2many('cross.matching.method', 'drl_cmm_rel', 'drl_id', 'cmm_id',
                                              string='Cross Match Method')
    cross_match_status = fields.Selection([('compatible', 'Compatible'), ('incompatible', 'Incompatible')],
                                          string="Cross Match Status")
    cross_match_date = fields.Datetime('Cross Match Date')
    cross_match_user_id = fields.Many2one('res.users', string='Cross Match By')
    issued_user_id = fields.Many2one('res.users', string='Issued By')
    issue_date = fields.Datetime('Issue Date')
    taken_by = fields.Selection([('nurse', 'Nurse'), ('family_member', 'Family Member')], string='Taken By')
    taken_by_user_id = fields.Many2one('res.users', string='Nurse')
    taken_by_family_member = fields.Char('Member Name')
    taken_date = fields.Datetime('Taken on')
    labtest_completed = fields.Boolean('Completed', compute='labtest_completed_field_compute')

    def update_cross_matching_result(self):
        """Update Cross-Matching Result"""
        donor_request_ids = self.ids
        cross_match_readonly = False
        cross_match = 'done'
        if self.br_id.other_hospital:
            cross_match_readonly = True
            cross_match = False
        ctx = {
            'default_br_id': self.br_id.id,
            'default_donor_request_ids': [(6, 0, donor_request_ids)],
            'default_cross_match_readonly': cross_match_readonly,
            'default_cross_match': cross_match,
        }
        action = self.env.ref('nuro_blood_bank.action_cross_matching_view').read()[0]
        action['context'] = ctx
        return action

    def name_get(self):
        result = []
        for record in self:
            name = record.name
            if record.donor_id:
                name = name + " " + record.donor_id.name
            result.append((record.id, name))
        return result

    # @api.constrains('blood_group_id')
    # def constrains_blood_group_details(self):
    #     """
    #     Constrains Blood Group Details
    #     """
    #     for rec in self:
    #         if rec.blood_group_id.id != rec.br_blood_group_id.id:
    #             raise UserError(_('Please Group should be same as Required.!!!'))

    @api.constrains('qty')
    def constrains_blood_unit_res(self):
        """Constrains Blood Unit"""
        for rec in self:
            if rec.qty <= 0.0:
                raise UserError(_('Please add the blood unit for the Donor request.!!!'))

    def get_cross_matching_names(self):
        """Get Cross Mathing Names"""
        cmm_name = self.cross_match_method_ids.mapped('name')
        name = ', '.join(cmm_name)
        return name

    def create_labtest_print_labtest_sample_view(self):
        """Create Labtest Sample Form"""
        return self.labtest_id.open_sample_wizard_form()

    def update_blood_request_status(self):
        """Update Blood Request Status"""
        return True

    def labtest_completed_field_compute(self):
        """Compute Labtest Fields"""
        for rec in self:
            if rec.labtest_id and rec.labtest_id.state == 'completed' and rec.donation_receiving and rec.br_id.state in (
                    'lab_requested', 'in_process'):
                rec.labtest_completed = True
            else:
                rec.labtest_completed = False

    def fit_questionnaire(self):
        """Fit Questionare"""
        self.questionnaire_result = 'fit'

    def not_fit_questionnaire(self):
        """Fit Questionare"""
        self.questionnaire_result = 'not_fit'

    def fit_assessment(self):
        """Fit assessment"""
        self.health_assessment_result = 'fit'

    def not_fit_assessment(self):
        """Fit assessment"""
        self.health_assessment_result = 'not_fit'

    def print_blood_received_label(self):
        """Print Blood Bank Label"""
        ctx = {'default_blood_move_in_ids': [(6, 0, self.blood_move_in_ids.ids)]}
        action = self.env.ref('nuro_blood_bank.action_print_label_view').read()[0]
        action['context'] = ctx
        return action

    def print_receipt_donor_form(self):
        """Appointment Receipt printing"""
        return self.env.ref('nuro_blood_bank.action_blood_transfusion_form_action').report_action(self)

    def get_blood_move_bag_name(self, name=False, date=False, labtest_line=False, master_name=False, condition=False):
        """Get Product IDS Name"""
        if master_name:
            master_name = ''
            master_ids_name = self.br_id.br_request_line.mapped('master_id').mapped('name')
            if master_ids_name:
                master_name = ', '.join(master_ids_name)
            return master_name
        if name:
            bag_ids_name = self.blood_move_id and self.blood_move_id.blood_bag_number or False
            if not bag_ids_name:
                bag_ids_name = ', '.join(self.br_id.blood_donation_request_line.filtered(
                    lambda x: x.donor_id.id == self.donor_id.id).bg_in_move_ids.mapped('blood_bag_number'))
            return bag_ids_name
        if date:
            bag_date = False
            if self.blood_move_in_ids:
                bag_id = self.blood_move_in_ids[0]
                bad_date = bag_id.create_date.date()
            return bag_date
        if labtest_line:
            query_donation = """
                            select 
                                labtest_id 
                            from blood_donation 
                            where 
                                donation_qty > 0 
                                and donor_id = %s and request_id = %s
                            order by id DESC limit 1
                            """ % (self.donor_id.id, self.br_id.id)
            self.env.cr.execute(query_donation)
            result = self.env.cr.fetchall()
            labtest_ids = [i[0] for i in result]
            labtest_ids = self.env['nuro.medical.labtest.result'].sudo().browse(labtest_ids)
            line_ids = labtest_ids.mapped('child_lines')
            if line_ids and line_ids.mapped('labtest_result_lines'):
                line_ids = line_ids.mapped('labtest_result_lines')
            return line_ids
        if condition:
            condition_ids = self.env['blood.transfusion.instruction'].search([], order='seq asc')
            return condition_ids

    def donation_receiving_method(self):
        """Donation Receiving Method"""
        for rec in self:
            rec.donation_receiving = True

    @api.depends('questionnaire_result', 'health_assessment_result')
    def get_assessment_result_value(self):
        """Get Assessment Result Value"""
        for rec in self:
            questionnaire = rec.questionnaire_result
            health_assessment = rec.health_assessment_result
            # screening_result = rec.screening_result
            assessment_result = False
            if questionnaire == 'fit' and health_assessment == 'fit':
                assessment_result = 'fit'
            if questionnaire == 'not_fit' or health_assessment == 'not_fit':
                assessment_result = 'not_fit'
            rec.assessment_result = assessment_result

    def update_assessment_result(self):
        """Update Health Assessment Result"""
        if self.assessment_result == 'not_fit':
            self.state = 'completed'

    def write(self, vals):
        """Update Assessment"""
        for rec in self:
            res = super(DonorRequestLine, rec).write(vals)
            if rec.assessment_result == 'not_fit' and (
                    'health_assessment_result' in vals or 'questionnaire_result' in vals):
                rec.update_assessment_result()
            return res

    def create_parent_labtest_result_vals(self):
        """Def Create parent Labtest Vals"""
        sequence = self.env['ir.sequence'].next_by_code('nuro.labtest.sequence')
        patient_id = self.donor_id.patient_id
        recipient_patient_id = self.br_id.patient_id
        val = {
            'name': sequence,
            'patient_id': patient_id.id,
            'identification_code': patient_id.identification_code,
            'age': patient_id.age,
            'mobile': patient_id.mobile,
            'gender': patient_id.gender,
            'doctor_id': self.br_id.doctor_id.id,
            'date': fields.Datetime.now(),
            'state': 'request',
            'sample_collection_state': 'request',
            'blood_unit': self.qty,
            'donor_request_id': self.id,
            'blood_request_id': self.br_id.id,
            'blood_donor_id': self.donor_id and self.donor_id.id or False,
            'blood_group_id': self.donor_id.blood_group_id and self.donor_id.blood_group_id.id or False,
            'recipient_patient_id': recipient_patient_id.id,
            'recipient_identification_code': recipient_patient_id.identification_code,
            'recipient_age': recipient_patient_id.age,
            'recipient_mobile': recipient_patient_id.mobile,
            'recipient_gender': recipient_patient_id.gender,
        }
        return val

    def create_child_labtest_result_vals(self, labtest_id, line):
        """Child labtest Result"""
        val = {
            'name': labtest_id.name,
            'labtest_master_id': line.id,
            'department_id': line.department_id.id,
            'sample_parent_id': labtest_id.id,
            'patient_id': labtest_id.patient_id.id,
            'doctor_id': labtest_id.doctor_id.id,
            'identification_code': labtest_id.patient_id.identification_code,
            'age': labtest_id.patient_id.age,
            'mobile': labtest_id.patient_id.mobile,
            'gender': labtest_id.patient_id.gender,
            'state': 'request',
            'sample_collection_state': 'request',
            'blood_unit': self.qty,
            'donor_request_id': self.id,
            'blood_request_id': labtest_id.blood_request_id.id,
            'blood_donor_id': labtest_id.blood_donor_id and labtest_id.blood_donor_id.id or False,
            'blood_group_id': labtest_id.blood_group_id and labtest_id.blood_group_id.id or False,
            'recipient_patient_id': labtest_id.recipient_patient_id.id,
            'recipient_identification_code': labtest_id.recipient_identification_code,
            'recipient_age': labtest_id.recipient_age,
            'recipient_mobile': labtest_id.recipient_mobile,
            'recipient_gender': labtest_id.recipient_gender
        }
        return val

    def create_labtest_request(self):
        """Create Labtest Request"""
        if not self.donor_id.patient_id:
            self.donor_id.create_patient_record()
        if not self.labtest_id:
            parent_vals = self.create_parent_labtest_result_vals()
            labtest_parent_id = self.env['nuro.medical.labtest.result'].create(parent_vals)
            master_ids = set(self.bt_product_id.mapped('labtest_line').mapped('labtest_master_id').ids)
            if not master_ids:
                raise UserError(_('There no Labtest configured in the BT Type.!!!'))
            labtest_master_ids = self.env['nuro.labtest.master'].browse(master_ids)
            for line in labtest_master_ids:
                vals_line = self.create_child_labtest_result_vals(labtest_id=labtest_parent_id, line=line)
                self.env['nuro.medical.labtest.result'].create(vals_line)
            self.labtest_id = labtest_parent_id.id
        else:
            raise UserError(_('Labtest Request Has been created Already.!!!'))

    def create_labtest_request_donation(self):
        """Create Labtest Request"""
        if not self.donor_id.patient_id:
            self.donor_id.create_patient_record()
        if not self.labtest_id:
            parent_vals = self.create_parent_labtest_result_vals()
            labtest_parent_id = self.env['nuro.medical.labtest.result'].create(parent_vals)
            master_ids = set(
                self.env.ref('nuro_blood_transfusion.blood_transfusion_product_charges_whole_blood').mapped(
                    'labtest_line').mapped('labtest_master_id').ids)
            labtest_master_ids = self.env['nuro.labtest.master'].browse(master_ids)
            for line in labtest_master_ids:
                vals_line = self.create_child_labtest_result_vals(labtest_id=labtest_parent_id, line=line)
                self.env['nuro.medical.labtest.result'].create(vals_line)
            self.labtest_id = labtest_parent_id.id
        else:
            raise UserError(_('Labtest Request Has been created Already.!!!'))

    def start_questionnaire(self):
        """Start questionnaire"""
        self.state = 'questionnaire'

    def start_health_assessment(self):
        """Start health_assessment"""
        self.state = 'health_assessment'

    def complete_assessment(self):
        """Complete Assessment"""
        self.state = 'completed'

    def create_options_lines(self):
        """Create Options Lines"""
        questionnaire_obj = self.env['question.relation.lines']
        get_question_line = self.env['questionnaire.master'].search([('type', '=', 'questionnaire')])
        if get_question_line:
            for line in get_question_line:
                if line.specific_to == 'both':
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'questionnaire_donor_request_id': self.id
                    })
                if line.specific_to == 'gender_specific' and line.gender == self.donor_id.gender:
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'questionnaire_donor_request_id': self.id
                    })

    def create_options_lines_assessment(self):
        """Create Options Lines"""
        questionnaire_obj = self.env['question.relation.lines']
        get_question_line = self.env['questionnaire.master'].search([('type', '=', 'health_assessment')])
        if get_question_line:
            for line in get_question_line:
                if line.specific_to == 'both':
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'assessment_donor_request_id': self.id
                    })
                if line.specific_to == 'gender_specific' and line.gender == self.donor_id.gender:
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'assessment_donor_request_id': self.id
                    })

    @api.model
    def create(self, values):
        """creating the sequence on creation of new Blood Transfusion"""
        sequence = self.env['ir.sequence'].next_by_code('blood.donor.seq')
        values['name'] = sequence
        res = super(DonorRequestLine, self).create(values)
        res.create_options_lines()
        res.create_options_lines_assessment()
        return res

    def action_labtest_result_create(self):
        """Method to Create Labtest Entry"""
        domain = [('id', '=', self.labtest_id.id)]
        return {
            'name': _('Labtest'),
            'domain': domain,
            'res_model': 'nuro.medical.labtest.result',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'view_type': 'form',
            'context': {'create': False, 'edit': False}
        }

    def open_assessment_form(self):
        """Open Assessment Form"""
        self.write({
            'doctor_id': self.br_id.doctor_id.id,
            'patient_id': self.br_id.patient_id.id,
            'identification_code': self.br_id.identification_code,
            'gender': self.br_id.gender,
            'age': self.br_id.age,
            'mobile': self.br_id.mobile,
        })
        view_id = self.env.ref('nuro_blood_bank.blood_request_donor_assessment_form').id
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'donor.request.line',
            'view_mode': 'form',
            'res_id': self.id,
            'views': [[view_id, 'form']],
        }

    def receive_blood_and_reserve(self):
        """Receive Blood and Reserve"""
        action = self.env.ref('nuro_blood_bank.action_donor_move_wizard_form').read()[0]
        action['context'] = {
            'default_donor_request_id': self.id,
            'default_blood_request_id': self.br_id.id,
            'default_qty': self.qty,
        }
        return action

    def _prepare_bg_move_vals(self, qty, location_id, dest_location_id):
        """blood group move create vals dictionary"""
        move_id = self.blood_move_id
        return dict(blood_group_id=self.blood_group_id.id,
                    blood_collection_bag_id=move_id.blood_collection_bag_id and move_id.blood_collection_bag_id.id or False,
                    type='OUT',
                    location_id=location_id.id,
                    dest_location_id=dest_location_id.id,
                    # patient_id=self.br_id.patient_id.id,
                    quantity=qty,
                    state='draft',
                    request_date=fields.Date.context_today(self)
                    )

    def create_brood_group_move(self, quantity):
        """This method create blood group move entry"""
        bg_move_env = self.env['blood.group.move']
        location_id = self.env.ref('nuro_blood_bank.blood_location_consume')
        if not location_id:
            raise UserError(
                _('There is not any location define for blood Consume. Please create blood Consume location'))
        move_list = []
        for line in range(0, quantity):
            bg_move_vals = self._prepare_bg_move_vals(qty=1, location_id=self.br_id.blood_location_id,
                                                      dest_location_id=location_id)
            bg_move_id = bg_move_env.create(bg_move_vals)
            move_list.append(bg_move_id.id)
        return move_list


class QuestionRelationLines(models.Model):
    _name = 'question.relation.lines'
    _description = 'Question Relation Lines'

    donation_id = fields.Many2one('blood.donation', 'Donation')
    assessment_donation_id = fields.Many2one('blood.donation', 'Donation')
    question_master_id = fields.Many2one('questionnaire.master', 'Question')
    selection_ids = fields.Many2many('questionnaire.selection', 'question_relation_questionnaire_rel',
                                     'question_id', 'selection_id', string='Selection',
                                     related='question_master_id.selection_ids', store=True)
    comment_readonly = fields.Boolean('Comment Readonly', compute='comment_readonly_method')
    response_id = fields.Many2one('questionnaire.selection', 'Response')
    comment = fields.Char('Comment')
    questionnaire_donor_request_id = fields.Many2one('donor.request.line', 'Donor Request')
    assessment_donor_request_id = fields.Many2one('donor.request.line', 'Donor Request')

    def comment_readonly_method(self):
        """Comment Readonly method"""
        for rec in self:
            if rec.selection_ids:
                rec.comment_readonly = True
            else:
                rec.comment_readonly = False


class BloodDonation(models.Model):
    _inherit = 'blood.donation'

    request_id = fields.Many2one('blood.request', string='Request ID')

    def create_parent_labtest_result_vals(self):
        """Create Parent Labtest Result Vals"""
        res = super().create_parent_labtest_result_vals()
        if self.request_id:
            recipient_patient_id = self.request_id.patient_id
            res.update({
                'recipient_patient_id': recipient_patient_id.id,
                'recipient_identification_code': recipient_patient_id.identification_code,
                'recipient_age': recipient_patient_id.age,
                'recipient_mobile': recipient_patient_id.mobile,
                'recipient_gender': recipient_patient_id.gender,
            })
        return res

    def update_donation_request_view(self):
        """update Donation"""
        res = super().update_donation_request_view()
        if self.request_id:
            if self.blood_group_id.id != self.request_id.blood_group_id.id:
                raise UserError(_('You can not make donation as the blood group is not same as recipient.!!!'))
            res['context'].update({
                'default_reserve': True,
                'default_patient_id': self.request_id.patient_id.id,
            })
        return res

    def open_form_request(self):
        """Open Form Request"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'blood.donation',
            'view_mode': 'form',
            'res_id': self.id,
            'views': [(False, 'form')],
        }

    # @api.constrains('blood_group_id', 'request_id')
    # def api_constrains_blood_group(self):
    #     for rec in self:
    #         if rec.request_id and rec.blood_group_id and rec.blood_group_id.id != rec.request_id.blood_group_id.id:
    #             raise UserError(_('Blood Group of Donor and Patient should be same.!!!'))
