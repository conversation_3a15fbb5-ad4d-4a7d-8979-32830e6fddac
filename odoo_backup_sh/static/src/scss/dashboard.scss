// Copyright 2018 <PERSON><PERSON> <https://it-projects.info/team/ufaks>
// Copyright 2019 <PERSON><PERSON> <https://it-projects.info/team/GabbasovDinar>
// License MIT (https://opensource.org/licenses/MIT).

$alert-warning-bg-color: #fff3cd;
$alert-warning-color: #856404;

.o_backup_dashboard {
    .kanban_group_buttons {
        float: right;
    }
    div.o_box {
        @include clearfix;
        color: $o-main-color-muted;
        background-color: $o-view-background-color;
        background-size: cover;
        padding-top: $o-horizontal-padding;
        position: static;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    }
    .graph_container h2 {
        float: left;
    }
    .o_dashboard_common {
        .o_box {
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
            padding: 8px 32px 0;
            > .o_inner_box {
                @include media-breakpoint-down(sm) {
                    flex: 1 1 200px;
                    display: block !important;
                }
                @include media-breakpoint-up(md) {
                    flex: 0 0 16.6%;
                }
            }
        }
        .o_inner_box {
            padding-top: 10px;
            text-align: center;
            border: 1px solid $o-view-background-color;
            height: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            color: white;
            background-color: $o-brand-primary;
            &:hover {
                background-color: darken($o-brand-primary, 10%);
            }
            &.o_primary {
                background-color: $o-brand-odoo;
                color: white;
                &:hover {
                    background-color: darken($o-brand-odoo, 10%);
                }
            }
            .o_highlight {
                font-size: 27px;
            }
        }
    }
}

.o_backup_dashboard_configs {
    display: flex;
    flex-direction: row;
    margin: 8px 0 4px;
}

.o_kanban_card_warning {
    background-color: $alert-warning-bg-color;
    border: 1px solid transparent;
    border-radius: 5px;
    color: $alert-warning-color;
    float: right;
    font-size: 12px;
    margin-right: 20px;
    padding: 0.75rem 1.25rem;
    position: relative;
    p {
        margin: 0px;
        margin-left: 28px;
    }
    i {
        position: absolute;
        top: 50%;
        transform: translate(0%, -50%);
        font-size: 22px;
    }
}

.o_backup_dashboard_configs .o_kanban_record:hover .backup_config_edit {
    opacity: 1;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
}

.backup_config_edit {
    position: absolute;
    cursor: pointer;
    opacity: 0;
    right: 14px;
    top: 8px;
}

.o_backup_dashboard_nocontent {
    padding: 15px;
    margin-top: 12px;
    color: #777777;
    font-size: 125%;
    max-width: 700px;

    .o_backup_dashboard_nocontent_create {
        margin-top: 0;
        padding-top: 35px;

        &:before {
            content: "";
            display: inline-block;
            position: absolute;
            width: 70px;
            height: 80px;
            margin-left: -70px;
            margin-top: -50px;
            background: transparent url(/web/static/src/img/view_empty_arrow.png)
                no-repeat 0 0;
        }
    }

    > p {
        padding-left: 78px;
    }
}

.o_backup_dashboard_notification {
    background-color: #eef6f8;
    border: 2px solid #d7eaef;
    color: #31708f;
    display: block;
    font-size: 15px;
    margin: 16px 16px 0;
    padding: 8px 16px;
    position: relative;

    .close {
        position: absolute;
        top: 4px;
        right: 4px;
    }
}

.graph_container {
    background-color: #fff;
    border: 1px solid #d9d7d7;
    color: #7c7bad;
    margin: 16px 16px 0;
    padding: 12px 16px;
    .graph_title {
        text-transform: uppercase;
    }
}

.backup_card_title {
    color: #7c7bad;
    font-size: 18px;
}

.backup_card_content {
    padding: 12px 0;
}
