id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_odoo_backup_sh_config1,access_odoo_backup_sh_config,model_odoo_backup_sh_config,odoo_backup_sh.group_manager,1,1,1,1
access_odoo_backup_sh_config_cron1,access_odoo_backup_sh_config_cron,model_odoo_backup_sh_config_cron,odoo_backup_sh.group_manager,1,1,1,1
access_odoo_backup_sh_backup_info1,access_odoo_backup_sh_backup_info,model_odoo_backup_sh_backup_info,odoo_backup_sh.group_manager,1,1,1,1
access_odoo_backup_sh_notification1,access_odoo_backup_sh_notification,model_odoo_backup_sh_notification,odoo_backup_sh.group_manager,1,1,1,1
access_odoo_backup_sh_remote_storage1,access_odoo_backup_sh_remote_storage,model_odoo_backup_sh_remote_storage,odoo_backup_sh.group_manager,1,1,1,1
access_odoo_backup_sh_config2,access_odoo_backup_sh_config,model_odoo_backup_sh_config,odoo_backup_sh.group_user,1,0,0,0
access_odoo_backup_sh_config_cron2,access_odoo_backup_sh_config_cron,model_odoo_backup_sh_config_cron,odoo_backup_sh.group_user,1,0,0,0
access_odoo_backup_sh_backup_info2,access_odoo_backup_sh_backup_info,model_odoo_backup_sh_backup_info,odoo_backup_sh.group_user,1,0,0,0
access_odoo_backup_sh_notification2,access_odoo_backup_sh_notification,model_odoo_backup_sh_notification,odoo_backup_sh.group_user,1,0,0,0
access_odoo_backup_sh_remote_storage2,access_odoo_backup_sh_remote_storage,model_odoo_backup_sh_remote_storage,odoo_backup_sh.group_user,1,0,0,0
