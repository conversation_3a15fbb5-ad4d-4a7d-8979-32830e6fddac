from odoo import fields, models, api,_
from odoo.exceptions import ValidationError


class ExtendMedicationDuration(models.TransientModel):
    _name = "extend.medication.duration"
    _description = "Extend Medication duration"

    inpatient_medication_plan_id = fields.Many2one('inpatient.medication.plan', string="Patient")
    from_date = fields.Date(string="From Date")
    to_date = fields.Date(string="To Date")

    def change_medication_duration(self):
        if not self.from_date:
            raise ValidationError(_('Please select from date'))
        if not self.to_date:
            raise ValidationError(_('Please select to date'))
        if self.from_date > self.to_date:
            raise ValidationError(_('To date should be greater than from date!'))
        if self.inpatient_medication_plan_id:
            self.inpatient_medication_plan_id.update_medication_duration(self.from_date, self.to_date)
            self.inpatient_medication_plan_id.update({
                'start_date': self.from_date,
                'end_date': self.to_date
            })
