<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!--  Form to update nursing activities from MAR table  -->
    <record id="update_nursing_activities_form_view" model="ir.ui.view">
        <field name="name">Update Nursing Activities</field>
        <field name="model">update.nursing.activities</field>
        <field name="priority">299</field>
        <field name="arch" type="xml">
            <form string="Nursing Activities">
                <sheet>
                    <field name="update_state" invisible="1"/>
                    <group attrs="{'invisible': [('update_state', '=', 'given')]}">
                        <group>
                            <field name="given_time" attrs="{'invisible': [('given_time', '=', False)]}"/>
                            <field name="given_user_id" attrs="{'invisible': [('given_user_id', '=', False)]}"/>
                            <field name="nursing_activities_id" invisible="1"/>
                            <field name="given_status" required="1"/>
                        </group>
                        <group>
                            <field name="user_id" readonly="1"/>
                            <field name="company_id" invisible="1"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('update_state', '=', 'given')]}">
                        <field name="description"/>
                    </group>

                    <group attrs="{'invisible': [('update_state', '=', 'not_given')]}">
                        <group>
                            <field name="given_time" attrs="{'invisible': [('given_time', '=', False)]}"/>
                            <field name="given_user_id" attrs="{'invisible': [('given_user_id', '=', False)]}"/>
                            <field name="nursing_activities_id" invisible="1"/>
                            <field name="given_status" required="1"/>
                        </group>
                        <group>
                            <field name="user_id" readonly="1"/>
                            <field name="company_id" invisible="1"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('update_state', '=', 'not_given')]}">
                        <field name="description" string="Reason For Change"/>
                    </group>
                </sheet>
                <footer>
                    <button name="update_medication" string="Update" type="object" class="oe_highlight"
                            data-hotkey="q"/>
                    <button string="Close" class="btn-default btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!--  Action to update nursing activities from MAR table  -->
    <record id="action_update_nursing_activities" model="ir.actions.act_window">
        <field name="name">Update MAR Summary</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">update.nursing.activities</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'form', 'view_id': ref('update_nursing_activities_form_view')})]"/>
    </record>
</odoo>