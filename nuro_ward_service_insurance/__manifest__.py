# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
{
    'name': "Ward Service Insurance Add-ons",
    'category': 'Custom',
    'version': '13.0.1',
    'summary': 'Nuro Ward Service Insurance',
    'description': """
        Nuro Ward Service Insurance Module is the addons which provide the extended feature of Insurance for 
        credit payment
    """,
    'author': 'Nurosolution Pvt Ltd',
    'website': 'http://www.nurosolution.com',
    'company': 'Nurosolution Pvt Ltd',
    'license': 'OPL-1',
    'depends': [
        'nuro_ward_services',
        'nuro_hms_insurance',
    ],
    'data': [

        # wizard
        'reports/ws_credit_thermal_receipt.xml',

        'wizard/ws_credit_wizard.xml',
        'wizard/ws_refund_wizard.xml',
    ],

    'installable': True,
    'application': True,
}
