# -*- coding: utf-8 -*-

from odoo.exceptions import UserError

# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, _


class MaterialRequestIssue(models.Model):
    _inherit = 'supply.material.issue'

    return_request = fields.Boolean('Return Request')
    return_id = fields.Many2one("supply.material.issue", string="Return ID")
    state = fields.Selection(selection_add=[
        ('partially_received', 'Partially Received'),
        ('return_received', 'Return Received')
    ])

    def get_issue_domain(self, state):
        """Get Domain Issue Server Action"""
        if state == 'new':
            domain = [
                ('state', 'in', ('draft', 'partial_issue', 'partially_received')),
                ('return_request', '!=', True),
                ('source_location_id', 'in', self.env.user.consume_location_ids.ids)
            ]
            return domain
        if state == 'return':
            domain = [
                ('state', 'in', ('draft', 'partial_issue', 'partially_received')),
                ('return_request', '=', True),
                ('dest_location_id', 'in', self.env.user.consume_location_ids.ids)
            ]
            return domain
        if state == 'closed':
            domain = [
                ('state', 'in', ('issued', 'return_received', 'cancel')),
                '|', ('source_location_id', 'in', self.env.user.consume_location_ids.ids),
                ('dest_location_id', 'in', self.env.user.consume_location_ids.ids)
            ]
            return domain

    def server_action_view_material_issue(self, state):
        """Server Action Material Request"""
        res = super().server_action_view_material_issue(state=state)
        if state == 'return':
            res['name'] = "Pending Material Receiving"
        return res

    def direct_consume_return(self):
        """
        Material Direct Consume Return
        """
        quant_obj = self.env['stock.quant']
        if self.material_issue_line and self.source_location_id and self.dest_location_id:
            for line in self.material_issue_line.filtered(lambda line: line.issue_qty > 0.0):
                source_location = self.source_location_id
                if source_location.usage == "internal":
                    available_qty = quant_obj._get_available_quantity(
                        product_id=line.product_id,
                        location_id=source_location,
                        strict=False)
                    if round(line.qty_approved, 2) > round(available_qty, 2):
                        raise UserError(_('%s product not have enough qty for issue' % (line.product_id.name)))
                move_vals = line.prep_stock_move_vals(location_id=self.source_location_id,
                                                      name=_('Supply request move for %s' % (self.name)),
                                                      dest_loac_id=self.sudo().supply_req_id.company_id.supply_transit_loc)
                move_vals.update({
                    'is_material_return': True
                })
                if move_vals:
                    move_id = self.env['stock.move'].create(move_vals)
                    move_id._action_confirm()
                    move_id._action_assign()
                    for mv_ln in move_id.move_line_ids:
                        mv_ln.unlink()
                    qty = line.qty_approved
                    for smvl in line.int_material_request_line_id.stock_move_line_ids.filtered(
                            lambda x: x.total_issued_qty > 0.0):
                        if qty > 0.0:
                            asg = qty
                            if smvl.total_issued_qty >= qty:
                                asg = qty
                            if smvl.total_issued_qty <= qty:
                                asg = smvl.total_issued_qty
                            if self.source_location_id.usage == 'internal':
                                available_qty = quant_obj._get_available_quantity(
                                    product_id=line.product_id,
                                    location_id=self.source_location_id,
                                    lot_id=smvl.lot_id,
                                    strict=False)
                                if not available_qty:
                                    raise UserError(
                                        _('%s product not have enough qty for issue' % (line.product_id.name)))
                            self.env['stock.move.line'].sudo().create({
                                'product_id': line.product_id.id,
                                'location_id': move_id.location_id.id,
                                'location_dest_id': move_id.location_dest_id.id,
                                'lot_id': smvl.lot_id and smvl.lot_id.id or False,
                                'lot_name': smvl.lot_id and smvl.lot_id.name or False,
                                'qty_done': asg,
                                'product_uom_id': line.product_id.uom_id.id,
                                'move_id': move_id.id,
                                'origin': move_id.name,
                            })
                            qty = qty - asg
                            smvl.total_issued_qty = smvl.total_issued_qty - asg
                    ctx = {'acc_dest': self.source_location_id.valuation_in_account_id.id, 'inv_it': True}
                    move_id.with_context(ctx)._action_done(cancel_backorder=True)
                    line.source_transit_move = move_id.id
                    line.source_transit_move_ids = [(4, move_id.id)]
                    self.supply_issue_move_ids = [(4, move_id.id)]
                    self.sudo().supply_req_id.supply_req_move_ids = [(4, move_id.id)]
                    line.total_issue_qty += line.issue_qty
                confirm_move_vals = line.prep_stock_move_vals(dest_loac_id=self.dest_location_id,
                                                              name=_('Supply request move for %s' % (self.name)),
                                                              location_id=self.sudo().supply_req_id.company_id.supply_transit_loc)
                confirm_move_vals.update({
                    'is_material_return': True
                })
                if confirm_move_vals:
                    if confirm_move_vals:
                        second_move_id = self.env['stock.move'].create(confirm_move_vals)
                        second_move_id._action_confirm()
                        second_move_id._action_assign()
                        for sec_mv_ln in second_move_id.move_line_ids:
                            sec_mv_ln.unlink()
                        qty = line.qty_approved
                        for smvl in line.int_material_request_line_id.stock_move_line_ids.filtered(
                                lambda x: x.sec_total_issued_qty > 0.0):
                            if qty > 0.0:
                                asg = qty
                                if smvl.sec_total_issued_qty >= qty:
                                    asg = qty
                                if smvl.sec_total_issued_qty <= qty:
                                    asg = smvl.sec_total_issued_qty
                                self.env['stock.move.line'].sudo().create({
                                    'product_id': line.product_id.id,
                                    'location_id': second_move_id.location_id.id,
                                    'location_dest_id': second_move_id.location_dest_id.id,
                                    'lot_id': smvl.lot_id and smvl.lot_id.id or False,
                                    'lot_name': smvl.lot_id and smvl.lot_id.name or False,
                                    'product_uom_qty': asg,
                                    'qty_done': asg,
                                    'product_uom_id': line.product_id.uom_id.id,
                                    'move_id': second_move_id.id,
                                    'origin': second_move_id.name,
                                })
                                if second_move_id.location_id.usage in ('internal', 'transit'):
                                    self.env['stock.quant'].sudo()._update_reserved_quantity(line.product_id,
                                                                                             second_move_id.location_id,
                                                                                             asg)
                                qty = qty - asg
                                smvl.sec_total_issued_qty = smvl.sec_total_issued_qty - asg
                        second_move_id._action_confirm()
                        second_move_id.state = 'assigned'
                        line.transit_dest_move = second_move_id.id
                        line.source_transit_move_ids = [(4, second_move_id.id)]
                        line.sudo().int_material_request_line_id.transit_stock_move_ids = [(4, second_move_id.id)]
                        self.supply_issue_move_ids = [(4, second_move_id.id)]
                        self.sudo().supply_req_id.supply_req_move_ids = [(4, second_move_id.id)]
                line.issue_qty = 0.0
        self.issued_by = self.env.user.id
        if all(line.qty_approved == line.total_issue_qty for line in self.material_issue_line):
            self.state = 'draft'

    def material_issue_return(self):
        """
        material issue and create stock move and link with material line
        """
        if self.state not in ['draft', 'partial_issue']:
            raise UserError(_('Document already process'))
        if all(line.issue_qty == 0.0 for line in self.material_issue_line):
            raise UserError(_('fill issue qty in request line'))
        if not self.sudo().supply_req_id.company_id.supply_transit_loc:
            raise UserError(_('Configure supply transit location'))
        self.direct_consume_return()

    def material_issue(self):
        """Material Issue"""
        res = super().material_issue()
        if self.return_id and self.return_id.state != 'return_received':
            raise UserError(_('Please Process Return First Before Issuance.!!!'))
        return res
    
    def reject_supply_material_issue(self):
        """Reject Supply Material Issue"""
        res = super().reject_supply_material_issue()
        if self.return_id:
            raise UserError(_("Related Issuance Record can not be reject Directly.!!!"))
        return res

    def reject_supply_material_issue_related(self):
        """Reject Supply Material Issue"""
        if not self.reason_to_reject:
            raise UserError(_('Please Add reason to reject.!!!'))
        if self.state != 'draft':
            raise UserError(_('Record has been Processed already.!!!'))
        self.state = 'cancel'
        # if self.supply_req_id:
        #     self.supply_req_id.button_rejected()

    def action_reject_supply_issue_return(self):
        """Action Reject Supply Issue Return"""
        if not self.reason_to_reject:
            raise UserError(_('Please Add reason to reject.!!!'))
        if self.state != 'draft':
            raise UserError(_('Record has been Processed already.!!!'))
        get_related_issuance = self.search([('return_id', '=', self.id)])
        if get_related_issuance:
            for issuance in get_related_issuance:
                issuance.reason_to_reject = self.reason_to_reject
                for sl in issuance.material_issue_line:
                    sl.int_material_request_line_id.write({
                        "total_received_qty": sl.int_material_request_line_id.total_received_qty + sl.total_qty,
                        "total_transfer_qty": sl.int_material_request_line_id.total_transfer_qty + sl.total_qty
                    })
                issuance.reject_supply_material_issue_related()
        for line in self.material_issue_line:
            move_assign = line.source_transit_move_ids.filtered(lambda x: x.state == 'assigned')
            move_done = line.source_transit_move_ids.filtered(lambda x: x.state == 'done')
            for assign in move_assign:
                assign._do_unreserve()
                assign._action_cancel()
                print(assign.state)
            for done in move_done:
                default_vals = {'location_id': done.location_dest_id.id, 'location_dest_id': done.location_id.id}
                new_move = done.copy(default=default_vals)
                new_move._action_confirm()
                new_move._action_assign()
                for mvl in new_move.move_line_ids:
                    mvl.qty_done = mvl.product_uom_qty
                ctx_mv = {'inv_it': True}
                new_move.with_context(ctx_mv)._action_done(cancel_backorder=True)
                line.source_transit_move = new_move.id
                line.source_transit_move_ids = [(4, new_move.id)]
                self.supply_issue_move_ids = [(4, new_move.id)]
                self.sudo().supply_req_id.supply_req_move_ids = [(4, new_move.id)]
                if line.int_material_request_line_id.return_process == 'return':
                    line.int_material_request_line_id.total_returned = line.int_material_request_line_id.total_returned - line.qty_approved
        self.state = 'cancel'

    def action_receive_all_qty(self):
        """
        receive all quantity
        """
        material_line = self.material_issue_line.filtered(lambda x: x.received_quantity > 0.0)
        if not material_line:
            raise UserError(_('There are no unit to receive.!!!'))
        for line in material_line:
            move_line = line.source_transit_move_ids.filtered(lambda move: move.state not in ('done', 'cancel'))
            if not move_line:
                continue
            if (line.received_quantity + line.total_received_quantity) > line.qty_approved:
                raise UserError(_('Receive qty should not More than issued qty'))
            move_line.write({'is_supply_receive': True})
            qty_line = line.received_quantity
            for mv in move_line:
                if qty_line == 0.0:
                    break
                mv_qty = mv.product_uom_qty
                if mv_qty > qty_line:
                    mv._do_unreserve()
                    mv.product_uom_qty = qty_line
                    new_move_id = mv.copy(default={'product_uom_qty': mv_qty - qty_line, 'conflict': True})
                    mv._action_confirm()
                    mv._action_assign()
                    new_move_id._action_confirm()
                    new_move_id._action_assign()
                    line.sudo().source_transit_move_ids = [(4, new_move_id.id)]
                    self.sudo().supply_issue_move_ids = [(4, new_move_id.id)]
                    line.sudo().int_material_request_line_id.transit_stock_move_ids = [(4, new_move_id.id)]
                    self.sudo().supply_req_id.supply_req_move_ids = [(4, new_move_id.id)]
                for mv_ln in mv.move_line_ids:
                    mv_ln.qty_done = mv_ln.product_uom_qty
                    mv_ln.total_issued_qty = mv_ln.product_uom_qty
                    mv_ln.sec_total_issued_qty = mv_ln.product_uom_qty
                ctx_mv = {'inv_it': True}
                mv.with_context(ctx_mv)._action_done(cancel_backorder=True)
                qty_line -= mv.product_uom_qty
            line.total_received_quantity += line.received_quantity
            line.current_received = line.received_quantity
            line.received_quantity = 0.0
        if all(line.state == 'done' for line in self.supply_issue_move_ids):
            self.state = 'return_received'
        else:
            self.state = 'partially_received'
        message = "Material Return Has been Received By User %s" % self.env.user.name
        self.supply_req_id.message_post(body=message, message_type="comment")
