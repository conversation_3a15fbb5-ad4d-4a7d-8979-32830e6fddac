# -*- coding: utf-8 -*-
# Copyright SOFTPRIMECONSULTING PRIVATE LIMITED
from odoo import fields, models


class PhysioServices(models.Model):
    _name = 'physio.services'
    _description = 'Physio Services'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id DESC'

    name = fields.Char('Name', tracking=True)
    charge = fields.Float('Charge', tracking=True)
    physio_activities_ids = fields.Many2many('physio.activities', 'services_activities_rel', 'service_id',
                                             'activities_id', string='Activities')
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
