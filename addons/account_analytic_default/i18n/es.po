# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_analytic_default
# 
# Translators:
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <jose<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 12:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:07+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<i class=\"fa fa-calendar\"/> From"
msgstr "<i class=\"fa fa-calendar\"/> De"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Customer</strong>"
msgstr "<strong>Cliente</strong>"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Product</strong>"
msgstr "<strong>Producto</strong>"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__account_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Account"
msgstr "Cuenta"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Accounts"
msgstr "Cuentas"

#. module: account_analytic_default
#: code:addons/account_analytic_default/models/account_analytic_default.py:0
#, python-format
msgid ""
"An analytic default requires at least an analytic account or an analytic "
"tag."
msgstr ""
"Un valor predeterminado analítico requiere al menos una cuenta analítica o "
"una etiqueta analítica. "

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__analytic_id
#: model:ir.model.fields,field_description:account_analytic_default.field_account_move_line__analytic_account_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Analytic Default Rule"
msgstr "Regla analítica por defecto"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_analytic_default_list
#: model:ir.ui.menu,name:account_analytic_default.menu_analytic_default_list
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_tree
msgid "Analytic Defaults"
msgstr "Análisis: Valores por defecto"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_analytic_default
msgid "Analytic Distribution"
msgstr "Distribución analítica"

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_product_default_list
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_user
msgid "Analytic Rules"
msgstr "Reglas analíticas"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__analytic_tag_ids
#: model:ir.model.fields,field_description:account_analytic_default.field_account_move_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Etiquetas analíticas"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__company_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Company"
msgstr "Compañía"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Conditions"
msgstr "Condiciones"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__date_stop
msgid "Default end date for this Analytic Account."
msgstr "Fecha de fin por defecto para esta cuenta analítica"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__date_start
msgid "Default start date for this Analytic Account."
msgstr "Fecha de inicio por defecto para esta cuenta analítica"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__date_stop
msgid "End Date"
msgstr "Fecha final"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__sequence
msgid ""
"Gives the sequence order when displaying a list of analytic distribution"
msgstr ""
"Indica el orden de la secuencia cuando se muestra una lista de distribución "
"analítica."

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Group By"
msgstr "Agrupar por"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__id
msgid "ID"
msgstr "ID"

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__partner_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Partner"
msgstr "Empresa"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__product_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Product"
msgstr "Producto"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__company_id
msgid ""
"Select a company which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"company, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione una compañía que utilizará la cuenta analítica especificada en la"
" analítica predeterminada (por ejemplo, creando una nueva factura de cliente"
" o un pedido de ventas si seleccionamos esta compañía, automáticamente la "
"tomará como una cuenta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__partner_id
msgid ""
"Select a partner which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"partner, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione una empresa que utilizará la cuenta analítica especificada en la "
"analítica por defecto (por ejemplo, creando una nueva factura de cliente o "
"un pedido de ventas, si se seleccionamos esta empresa, automáticamente la "
"tomará como una cuenta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__product_id
msgid ""
"Select a product which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"product, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione un producto que utilizará la cuenta analítica especificada en la "
"analítica por defecto (por ejemplo, creando una nueva factura de cliente o "
"un pedido de venta, si se selecciona este producto, automáticamente lo "
"tomará como una cuenta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__user_id
msgid ""
"Select a user which will use analytic account specified in analytic default."
msgstr ""
"Seleccione un usuario que usará la cuenta analítica especificada en la "
"analítica por defecto."

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__account_id
msgid ""
"Select an accounting account which will use analytic account specified in "
"analytic default (e.g. create new customer invoice or Sales order if we "
"select this account, it will automatically take this as an analytic account)"
msgstr ""
"Seleccione una cuenta contable que utilizará la cuenta analítica "
"especificada en el valor predeterminado analítico (por ejemplo, cree una "
"nueva factura de cliente o un pedido de ventas si seleccionamos esta cuenta,"
" se tomará automáticamente como una cuenta analítica)"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__date_start
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Tags"
msgstr "Categorías"

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__user_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "User"
msgstr "Usuario"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "to"
msgstr "a"

#. module: account_analytic_default
#: code:addons/account_analytic_default/models/account_move.py:30
#, python-format
msgid ""
"An analytic default requires an analytic account or an analytic tag used for"
" analytic distribution."
msgstr ""
"Un valor analítico predeterminado requiere al menos una cuenta analítica o "
"una etiqueta analítica que sea usada para distribución analítica."
