# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* membership
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:11+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Romanian (https://www.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_invoiced
msgid "# Invoiced"
msgstr "# Facturat"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_paid
msgid "# Paid"
msgstr "# Plătit"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__num_waiting
msgid "# Waiting"
msgstr "# În așteptare"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong> From: </strong>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" "
"title=\"Period\"/><strong>Din:</strong>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Price\" title=\"Price\"/>"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<strong> To:</strong>"
msgstr "<strong>La:</strong>"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__associate_member
#: model:ir.model.fields,help:membership.field_res_users__associate_member
msgid ""
"A member with whom you want to associate your membership.It will consider "
"the membership state of the associated member."
msgstr ""
"Un membru cu care doriti sa va asociati calitatea de membru. Va lua in "
"considerare starea apartenentei membrului asociat."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_line
msgid "Account Invoice line"
msgstr "Linie Factura Cont"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Add a description..."
msgstr "Adaugă o descriere..."

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid "Add a new member"
msgstr "Adăugați un membru nou"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__member_price
msgid "Amount for the membership"
msgstr "Taxa cotizatie de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__associate_member_id
#: model:ir.model.fields,field_description:membership.field_res_partner__associate_member
#: model:ir.model.fields,field_description:membership.field_res_users__associate_member
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Associate Member"
msgstr "Membru asociat"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Associated Partner"
msgstr "Partener asociat"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Buy Membership"
msgstr "Cumpara Calitatea de Membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Cancel"
msgstr "Anulează"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,field_description:membership.field_res_users__membership_cancel
msgid "Cancel Membership Date"
msgstr "Data de incetare a calității de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_cancel
msgid "Cancel date"
msgstr "Data anularii"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__canceled
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__canceled
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__canceled
msgid "Cancelled Member"
msgstr "Membru anulat"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Category"
msgstr "Categorie"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership
#: model:ir.model.fields,help:membership.field_product_template__membership
msgid "Check if the product is eligible for membership."
msgstr "Verificați dacă produsul este eligibil pentru a deveni membru."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__company_id
#: model:ir.model.fields,field_description:membership.field_report_membership__company_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Company"
msgstr "Companie"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__company_id
msgid "Company related to this journal"
msgstr "Compania asociată acestui jurnal"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_marketing_config_association
msgid "Configuration"
msgstr "Configurare"

#. module: membership
#: model:ir.model,name:membership.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_tree
msgid "Contacts"
msgstr "Contacte"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__create_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__create_date
msgid "Created on"
msgstr "Creat în"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_state
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Current Membership State"
msgstr "Starea actuala a calitatii de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_state
#: model:ir.model.fields,field_description:membership.field_res_users__membership_state
msgid "Current Membership Status"
msgstr "Starea actuală a calității de membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Customers"
msgstr "Clienți"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_from
#: model:ir.model.fields,help:membership.field_product_template__membership_date_from
#: model:ir.model.fields,help:membership.field_res_partner__membership_start
#: model:ir.model.fields,help:membership.field_res_users__membership_start
msgid "Date from which membership becomes active."
msgstr "Data de când calitatea de membru devine activă."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__date
msgid "Date on which member has joined the membership"
msgstr "Data aderarii membrului"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_cancel
#: model:ir.model.fields,help:membership.field_res_users__membership_cancel
msgid "Date on which membership has been cancelled"
msgstr "Data la care a fost anulată calitatea de membru"

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product__membership_date_to
#: model:ir.model.fields,help:membership.field_product_template__membership_date_to
#: model:ir.model.fields,help:membership.field_res_partner__membership_stop
#: model:ir.model.fields,help:membership.field_res_users__membership_stop
msgid "Date until which membership remains active."
msgstr "Data până când ramane activă calitatea de membru."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__display_name
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__display_name
#: model:ir.model.fields,field_description:membership.field_report_membership__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_earned
msgid "Earned Amount"
msgstr "Suma castigata"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__date_to
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Date"
msgstr "Dată sfârșit"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Membership Date"
msgstr "Data de incetare a calitatii de membru"

#. module: membership
#: model:ir.model.fields,help:membership.field_report_membership__date_to
msgid "End membership date"
msgstr "Data de incheiere a calitătii de membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Ending Date Of Membership"
msgstr "Data de încheiere a calității de membru"

#. module: membership
#: model:ir.model.constraint,message:membership.constraint_product_template_membership_date_greater
msgid "Error ! Ending Date cannot be set before Beginning Date."
msgstr ""
"Eroare ! Data de sfarsit nu poate fi setata inaintea Datei de inceput."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Forecast"
msgstr "Estimare"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__free_member
#: model:ir.model.fields,field_description:membership.field_res_users__free_member
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__free
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__free
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__free
msgid "Free Member"
msgstr "Membru necotizant"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_from
msgid "From"
msgstr "De la"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Group By"
msgstr "Grupează după"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Group by..."
msgstr "Grupează după..."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__id
#: model:ir.model.fields,field_description:membership.field_report_membership__id
msgid "ID"
msgstr "ID"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Inactive"
msgstr "Inactiv(ă)"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__account_invoice_id
msgid "Invoice"
msgstr "Factură"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Invoice Membership"
msgstr "Factura membru"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__invoiced
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__invoiced
msgid "Invoiced Member"
msgstr "Membru Facturat"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Invoiced/Paid/Free"
msgstr "Facturat/Plătit/Gratuit"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_state
#: model:ir.model.fields,help:membership.field_res_users__membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""
"Indică statul de membru.\n"
"-Non-membru: un partener care nu a solicitat niciun membru..\n"
"Membru anulat: un membru care și-a anulat calitatea de membru.\n"
"-Membru vechi: un membru a cărui dată de membru a expirat..\n"
"-Membru în așteptare: un membru care a solicitat calitatea de membru și a cărui factură va fi creată.\n"
"-Membru facturat: un membru a cărui factură a fost creată.\n"
"-Membru plătitor: Un membru care a plătit taxa de membru."

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__state
msgid ""
"It indicates the membership status.\n"
"-Non Member: A member who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paid Member: A member who has paid the membership amount."
msgstr ""
"Indică statul de membru.\n"
"-Non-membru: un partener care nu a solicitat niciun membru..\n"
"Membru anulat: un membru care și-a anulat calitatea de membru.\n"
"-Membru vechi: un membru a cărui dată de membru a expirat..\n"
"-Membru în așteptare: un membru care a solicitat calitatea de membru și a cărui factură va fi creată.\n"
"-Membru facturat: un membru a cărui factură a fost creată.\n"
"-Membru plătit: un membru care a plătit suma de membru."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date
msgid "Join Date"
msgstr "Data aderarii"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_invoice_view
msgid "Join Membership"
msgstr "Alăturați-vă calității de membru"

#. module: membership
#: model:ir.model,name:membership.model_account_move
msgid "Journal Entries"
msgstr "Note contabile"

#. module: membership
#: model:ir.model,name:membership.model_account_move_line
msgid "Journal Item"
msgstr "Notă contabilă"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice____last_update
#: model:ir.model.fields,field_description:membership.field_membership_membership_line____last_update
#: model:ir.model.fields,field_description:membership.field_report_membership____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__write_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__partner_id
msgid "Member"
msgstr "Membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__member_price
msgid "Member Price"
msgstr "Pret membru"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_members
#: model:ir.ui.menu,name:membership.menu_association
#: model:ir.ui.menu,name:membership.menu_membership
#: model_terms:ir.ui.view,arch_db:membership.membership_members_tree
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Members"
msgstr "Membri"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_report_membership_tree
msgid "Members Analysis"
msgstr "Analiza Membrilor"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice__product_id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__membership_id
#: model:ir.model.fields,field_description:membership.field_product_product__membership
#: model:ir.model.fields,field_description:membership.field_product_template__membership
#: model:ir.model.fields,field_description:membership.field_res_partner__member_lines
#: model:ir.model.fields,field_description:membership.field_res_users__member_lines
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_graph1
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_pivot
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership"
msgstr "Calitate de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_amount
#: model:ir.model.fields,field_description:membership.field_res_users__membership_amount
msgid "Membership Amount"
msgstr "Suma de Membru"

#. module: membership
#: model:ir.model,name:membership.model_report_membership
msgid "Membership Analysis"
msgstr "Analiza aderarii ca membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Membership Duration"
msgstr "Durata Calitatii de Membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_to
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_to
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_stop
#: model:ir.model.fields,field_description:membership.field_res_users__membership_stop
msgid "Membership End Date"
msgstr "Dată sfârșit a calității de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__member_price
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership Fee"
msgstr "Taxa de Membru"

#. module: membership
#: model:ir.model,name:membership.model_membership_invoice
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Membership Invoice"
msgstr "Factura de apartenenta"

#. module: membership
#: model:ir.model,name:membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Linia de membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership Partners"
msgstr "Parteneri membri"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__membership_id
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership Product"
msgstr "Produs de membru"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_products
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Membership Products"
msgstr "Produse de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product__membership_date_from
#: model:ir.model.fields,field_description:membership.field_product_template__membership_date_from
#: model:ir.model.fields,field_description:membership.field_res_partner__membership_start
#: model:ir.model.fields,field_description:membership.field_res_users__membership_start
msgid "Membership Start Date"
msgstr "Dată început a calității de membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership State"
msgstr "Starea calitatii de membru"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__state
msgid "Membership Status"
msgstr "Starea Calitatii de Membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership products"
msgstr "Produse de membru"

#. module: membership
#: model:ir.actions.server,name:membership.ir_cron_update_membership_ir_actions_server
#: model:ir.cron,cron_name:membership.ir_cron_update_membership
#: model:ir.cron,name:membership.ir_cron_update_membership
msgid "Membership: update memberships"
msgstr "Calitate de membru: actualizați calitatea de membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Memberships"
msgstr "Apartenente ca membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Month"
msgstr "Luna"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__none
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__none
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__none
msgid "Non Member"
msgstr "Non-membri"

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid ""
"Odoo helps you easily track all activities related to a member: \n"
"                  Current Membership Status, Discussions and History of Membership, etc."
msgstr ""
"Odoo vă ajută să urmăriți cu ușurință toate activitățile legate de un membru: \n"
"                  Starea actuală a calității de membru, discuții și istoricul calității de membru etc."

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__old
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__old
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__old
msgid "Old Member"
msgstr "Membru vechi"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__paid
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__paid
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__paid
msgid "Paid Member"
msgstr "Membru cotizant"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__partner
msgid "Partner"
msgstr "Partener"

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner doesn't have an address to make the invoice."
msgstr "Partenerul nu are o adresa pentru a face factura."

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "Partner is a free Member."
msgstr "Partenerul este Membru necotizant."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__tot_pending
msgid "Pending Amount"
msgstr "Suma în așteptare"

#. module: membership
#: model:ir.model,name:membership.model_product_template
msgid "Product Template"
msgstr "Șablon produs"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__quantity
msgid "Quantity"
msgstr "Cantitate"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_report_membership
msgid "Reporting"
msgstr "Raportare"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Revenue Done"
msgstr "Venituri efectuate"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__user_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Salesperson"
msgstr "Agent de vânzări"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__free_member
#: model:ir.model.fields,help:membership.field_res_users__free_member
msgid "Select if you want to give free membership."
msgstr "Selectați dacă doriti sa oferiți apartenență ca membru gratuit."

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership__start_date
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Start Date"
msgstr "Dată început"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Starting Date Of Membership"
msgstr "Data de începere a calității de membru"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Taxes"
msgstr "Taxe"

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line__account_invoice_id
msgid "The move of this entry line."
msgstr "Miscarea acestei linii a inregistrarii."

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner__membership_amount
#: model:ir.model.fields,help:membership.field_res_users__membership_amount
msgid "The price negotiated by the partner"
msgstr "Prețul negociat de către partener"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "This note will be displayed on quotations..."
msgstr "Aceasta nota va fi afisata la oferte..."

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display paid, old and total earned columns"
msgstr "Acesta va afișa coloanele plătit, vechi și total câștigat"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display waiting, invoiced and total pending columns"
msgstr "Acesta va afisa coloanele in asteptare, facturate si total in curs"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line__date_to
msgid "To"
msgstr "Către"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Vendors"
msgstr "Furnizori"

#. module: membership
#: model:ir.model.fields.selection,name:membership.selection__membership_membership_line__state__waiting
#: model:ir.model.fields.selection,name:membership.selection__report_membership__membership_state__waiting
#: model:ir.model.fields.selection,name:membership.selection__res_partner__membership_state__waiting
msgid "Waiting Member"
msgstr "Membru in asteptare"

#. module: membership
#: code:addons/membership/models/partner.py:0
#, python-format
msgid "You cannot create recursive associated members."
msgstr "Nu puteți crea membri asociați recursivi."
