# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_geolocalize
# 
# Translators:
# <PERSON> <must<PERSON><PERSON>@cubexco.com>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <o<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span class=\"oe_inline\"> ( On  </span>"
msgstr "<span class=\"oe_inline\"> ( على  </span>"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span> : Lat : </span>"
msgstr "<span> : خط العرض : </span>"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span> ;  Long:  </span>"
msgstr "<span> : خط الطول : </span>"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_id
msgid "API"
msgstr "API"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:104
#, python-format
msgid ""
"API key for GeoCoding (Places) required.\n"
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."
msgstr ""
"بحاجة إلى مفتاح الواجهة البرمجية للتطبيق للترميز الجغرافي (الأماكن).\n"
"قم بزيارة https://developers.google.com/maps/documentation/geocoding/get-api-key للمزيد من المعلومات. "

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "API:"
msgstr "الواجهو البرمجية للتطبيق: "

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الإعدادات"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__display_name
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geocoder__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:156
#, python-format
msgid "Error with geolocation server:"
msgstr "حدث خطأ في خادم الموقع الجغرافي: "

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geocoder
msgid "Geo Coder"
msgstr "الترميز الجغرافي "

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geo_provider
msgid "Geo Provider"
msgstr "المزود الجغرافي "

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocate"
msgstr "تحديد الموقع الجغرافي"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr "التموضع الجغرافي"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr "تاريخ التموضع الجغرافي"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid "Google Map API Key"
msgstr "مفتاح الواجهة البرمجية للتطبيق لخريطة Google "

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__id
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geocoder__id
msgid "ID"
msgstr "المُعرف"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "Key:"
msgstr "المفتاح: "

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider____last_update
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geocoder____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__name
msgid "Name"
msgstr "الاسم"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignation"
msgstr "تكليف الشريك"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:69
#, python-format
msgid "Provider %s is not implemented for geolocation service."
msgstr "المزود %s غير مطبق في خدمة المواقع الجغرافية. "

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__tech_name
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_techname
msgid "Tech Name"
msgstr "الاسم التقني "

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:123
#, python-format
msgid ""
"Unable to geolocate, received the error:\n"
"%s\n"
"\n"
"Google made this a paid feature.\n"
"You should first enable billing on your Google account.\n"
"Then, go to Developer Console, and enable the APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"
msgstr ""
"تعذر إيجاد الموقع الجغرافي، تم استلام رسالة الخطأ:\n"
"%s\n"
"\n"
"قام Google بجعل هذه الخاصية مدفوعة.\n"
"عليك أولاً تفعيل الفوترة في حساب Google الخاص بك.\n"
"ثم اذهب إلى أداة المطوِّر وقم بتفعيل الواجهة البرمجية للتطبيق:\n"
"الترميز الجغرافي، الخرائط الساكنة، خرائط جافا سكريبت.\n"

#. module: base_geolocalize
#: model:ir.model.fields,help:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid ""
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key"
" for more information."
msgstr ""
"قم بزيارة https://developers.google.com/maps/documentation/geocoding/get-"
"api-key للمزيد من المعلومات. "
