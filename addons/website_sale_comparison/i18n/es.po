# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_comparison
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# 36700cd705c35e1852bdffcd0b296648, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:13+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr "134,7 x 200 x 7,2 mm"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr "308 g"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"
msgstr ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<i class=\"fa fa-exchange\"/> Compare"
msgstr "<i class=\"fa fa-exchange\"/> Compara"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart\"/>&amp;nbsp;Add to Cart"
msgstr "<i class=\"fa fa-shopping-cart\"/>&amp;nbsp;Añadir a la Cesta"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"
msgstr "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
msgid "<span class=\"fa fa-exchange\"/> Add to compare"
msgstr "<span class=\"fa fa-exchange\"/> Agregue para comparar"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<span class=\"h3\">Suggested alternatives: </span>"
msgstr "<span class=\"h3\">Alternativas sugeridas: </span>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr "<strong>Precio:</strong>"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr "Apple"

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr "Categorías de atributos"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_1
msgid "Brand"
msgstr "Marca"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "Category"
msgstr "Categoría"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "Nombre de categoría"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#, python-format
msgid "Compare"
msgstr "Comparar"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr "Comparar Productos"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_2
msgid "Dimensions"
msgstr "Dimensiones"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_duration
msgid "Duration"
msgstr "Duración"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_general_features
msgid "General Features"
msgstr "Características Generales"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "ID"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model:ir.model,name:website_sale_comparison.model_product_product
#, python-format
msgid "Product"
msgstr "Producto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "Atributo de producto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr "Categoría del atributo de producto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Linea de atributo de la plantilla de producto"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr "Imagen del producto"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__attribute_ids
msgid "Related Attributes"
msgstr "Atributos relacionados"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "Eliminar"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce"
msgstr ""
"Establecer una categoría para reagrupar atributos similares bajo la misma "
"sección el la página comparativa de eCommerce "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr "Comparador de Tiendas"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications for"
msgstr "Especificaciones para"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Uncategorized"
msgstr "Sin categoría"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "Warning"
msgstr "Alerta"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "Peso"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "You can compare max 4 products."
msgstr "Puede comparar un máximo de 4 productos. "

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "o"
