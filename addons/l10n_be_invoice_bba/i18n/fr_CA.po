# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_be_invoice_bba
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-09-07 14:56+0000\n"
"PO-Revision-Date: 2016-02-23 06:16+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: French (Canada) (http://www.transifex.com/odoo/odoo-9/"
"language/fr_CA/)\n"
"Language: fr_CA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: l10n_be_invoice_bba
#: model:ir.model.fields,field_description:l10n_be_invoice_bba.field_res_partner_out_inv_comm_algorithm
msgid "Communication Algorithm"
msgstr ""

#. module: l10n_be_invoice_bba
#: model:ir.model.fields,field_description:l10n_be_invoice_bba.field_res_partner_out_inv_comm_type
msgid "Communication Type"
msgstr ""

#. module: l10n_be_invoice_bba
#: selection:res.partner,out_inv_comm_algorithm:0
msgid "Customer Reference"
msgstr ""

#. module: l10n_be_invoice_bba
#: selection:res.partner,out_inv_comm_algorithm:0
msgid "Date"
msgstr "Date"

#. module: l10n_be_invoice_bba
#: code:addons/l10n_be_invoice_bba/invoice.py:149
#, python-format
msgid ""
"Empty BBA Structured Communication!\n"
"Please fill in a unique BBA Structured Communication."
msgstr ""

#. module: l10n_be_invoice_bba
#: constraint:account.invoice:0
msgid "Invalid BBA Structured Communication !"
msgstr ""

#. module: l10n_be_invoice_bba
#: model:ir.model,name:l10n_be_invoice_bba.model_account_invoice
msgid "Invoice"
msgstr "Facture"

#. module: l10n_be_invoice_bba
#: model:ir.model,name:l10n_be_invoice_bba.model_res_partner
msgid "Partner"
msgstr "Partenaire"

#. module: l10n_be_invoice_bba
#: selection:res.partner,out_inv_comm_algorithm:0
msgid "Random"
msgstr ""

#. module: l10n_be_invoice_bba
#: model:ir.model.fields,help:l10n_be_invoice_bba.field_res_partner_out_inv_comm_algorithm
msgid ""
"Select Algorithm to generate the Structured Communication on Outgoing "
"Invoices."
msgstr ""

#. module: l10n_be_invoice_bba
#: model:ir.model.fields,help:l10n_be_invoice_bba.field_res_partner_out_inv_comm_type
msgid "Select Default Communication Type for Outgoing Invoices."
msgstr ""

#. module: l10n_be_invoice_bba
#: code:addons/l10n_be_invoice_bba/invoice.py:158
#: code:addons/l10n_be_invoice_bba/invoice.py:179
#, python-format
msgid ""
"The BBA Structured Communication has already been used!\n"
"Please create manually a unique BBA Structured Communication."
msgstr ""

#. module: l10n_be_invoice_bba
#: code:addons/l10n_be_invoice_bba/invoice.py:102
#, python-format
msgid ""
"The Partner should have a 3-7 digit Reference Number for the generation of "
"BBA Structured Communications!\n"
"Please correct the Partner record."
msgstr ""

#. module: l10n_be_invoice_bba
#: code:addons/l10n_be_invoice_bba/invoice.py:91
#: code:addons/l10n_be_invoice_bba/invoice.py:115
#, python-format
msgid ""
"The daily maximum of outgoing invoices with an automatically generated BBA "
"Structured Communications has been exceeded!\n"
"Please create manually a unique BBA Structured Communication."
msgstr ""

#. module: l10n_be_invoice_bba
#: code:addons/l10n_be_invoice_bba/invoice.py:130
#, python-format
msgid ""
"Unsupported Structured Communication Type Algorithm '%s' !\n"
"Please contact your Odoo support channel."
msgstr ""
