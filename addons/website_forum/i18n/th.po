# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2021
# Wichanon Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://www.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to accept or refuse an answer."
msgstr "%dคะแนนกรรมที่ต้องยอมรับหรือปฏิเสธคำตอบ"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to answer a question."
msgstr "%dคะแนน Karma ที่ต้องตอบคำถาม"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to close or reopen a post."
msgstr "%dคะแนน Karma เพื่่อปิดหรือเปิดโพสต์ใหม่อีกครั้ง"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to comment."
msgstr "%dคะแนน Karma เพื่อแสดงความคิดเห็น"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert a comment to an answer."
msgstr "%d คะแนน Karma เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert an answer to a comment."
msgstr "%dคะแนน Karma จำเป็น เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert your comment to an answer."
msgstr "%d  คะแนน Karma จำเป็น เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new Tag."
msgstr "%d  คะแนน Karma เพื่อสร้างแท็กใหม่"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new question."
msgstr "%d คะแนน Karma เพื่อสร้างคำถามใหม่"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to delete or reactivate a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อลบหรือเปิดใช้งานโพสต์อีกครั้ง"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to downvote."
msgstr "%d คะแนน Karma จำเป็น เพื่อโหวตไม่เห็นด้วย"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to edit a post."
msgstr "%d คะแนน Karma จำเป็น เพื่อแก้ไขโพสต์"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to flag a post."
msgstr "%d คะแนน Karma จำเป็น เพื่อตั้งค่าสถานะโพสต์"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to mark a post as offensive."
msgstr "%d คะแนน Karma จำเป็น เพื่อตั้งค่าโพสต์เป็นละเมิด"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to post an image or link."
msgstr "%d คะแนน Karma จำเป็น เพื่อโพสต์ภาพหรือลิงก์"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to refuse a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อปฏิเสธโพสต์"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to retag."
msgstr "%dคะแนน Karma จำเป็น เพื่อแท็กอีกครั้ง"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a comment."
msgstr "%dคะแนน Karma จำเป็น เพื่อยกเลิกการเชื่อมโยงความคิด้ห็น"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อยกเลิกการเชื่อมโยงโพสต์"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to upvote."
msgstr "%d คะแนน Karma จำเป็น เพื่อโหวตไม่เห็นด้วย"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to validate a post."
msgstr "%dคะแนน Karma จำเป็น เพื่อตรวจสอบโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "&amp;nbsp;and&amp;nbsp;"
msgstr "&amp;nbsp;and&amp;nbsp;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(ส่วนด้านบนนี้ดัดแปลงมาจากคำถามที่พบบ่อยของ Stackoverflow)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr "(โหวต - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ", by"
msgstr "โดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ", consider <b>adding an example</b>."
msgstr "พิจารณาเพื่อ<b>เพิ่มตัวอย่าง</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr "/ (วัน + 2) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "0 Answers"
msgstr "0 คำตอบ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "45% of questions shared"
msgstr "45% ของคำถามถูกแชร์"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"65% ของการเพิ่มโอกาสเพื่อได้รับ\n"
"        คำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">คุณมีโพสต์ที่ค้างอยู่</b>\n"
"                       โปรดรอให้ผู้ดูแลตรวจสอบโพสต์ก่อนหน้าของคุณจึงจะได้รับอนุญาตให้ตอบคำถามได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b> [Offensive]</b>"
msgstr "<b> [ละเมิด]</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>คำตอบไม่ควรเพิ่มหรือขยายคำถาม</b> "
"แก้ไขคำถามหรือเพิ่มความคิดเห็นของคำถามแทน"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Insteadeither edit the "
"question or add a comment."
msgstr ""
"<b>คำตอบไม่ควรเพิ่มหรือขยายคำถาม</b>ให้แก้ไขคำถามหรือเพิ่มความคิดเห็นแทน"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>คำตอบไม่ควรแสดงความคิดเห็นคำตอบอื่น ๆ </b> ให้เพิ่มความคิดเห็นในคำตอบอื่น"
" ๆ แทน"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>คำตอบไม่ควรเริ่มโต้เถียง</b> คำถามและคำตอบของชุมชนนี้ "
"ไม่ใช่กลุ่มสำหรับการสนทนา โปรดหลีกเลี่ยงการโต้เถียงในคำตอบของคุณ "
"เนื่องจากมักจะทำให้สาระสำคัญของคำถามและคำตอบเจือจางลง สำหรับการอภิปรายสั้น ๆ"
" โปรดใช้เครื่องมืออำนวยความสะดวกในการแสดงความคิดเห็น"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>คำตอบไม่ควรชี้ไปที่คำถามอื่น ๆ</b> "
"ให้เพิ่มการบ่งชี้ความคิดเห็นของคำถามแทน \"อาจซ้ำกับ...\" อย่างไรก็ตาม "
"การรวมลิงก์ไปยังคำถามหรือคำตอบอื่นๆ "
"ที่ให้ข้อมูลเพิ่มเติมที่เกี่ยวข้องนั้นเป็นเรื่องปกติ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>คำตอบไม่ควรชี้ไปที่คำถามอื่นเท่านั้น</b>แทนที่จะเพิ่มความคิดเห็นที่ระบุ "
"<i>\"อาจซ้ำซ้อนของ...\"</i>. อย่างไรก็ตาม "
"การรวมลิงก์ไปยังคำถามหรือคำตอบอื่นๆ "
"ที่ให้ข้อมูลเพิ่มเติมที่เกี่ยวข้องนั้นเป็นเรื่องปกติ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>คำตอบไม่ควรให้แค่ลิงก์เพื่อแก้ปัญหา</b> "
"ให้ใส่ข้อความคำอธิบายโซลูชันในคำตอบของคุณ "
"แม้ว่าจะเป็นเพียงการคัดลอก/วางก็ตาม ลิงก์ยินดีต้อนรับ แต่ควรเสริมเพื่อตอบ "
"อ้างอิงแหล่งที่มา หรือการอ่านเพิ่มเติม"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>ก่อนที่คุณจะถาม - โปรดตรวจสอบให้แน่ใจว่าได้ค้นหาคำถามที่คล้ายกัน</b> "
"คุณสามารถค้นหาคำถามตามชื่อหรือแท็กได้ "
"นอกจากนี้ยังสามารถตอบคำถามของคุณเองได้อีกด้วย"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>โปรดหลีกเลี่ยงคำถามที่เป็นความคิดเห็นส่วนตัวและมีข้อโต้แย้งมากเกินไป</b> "
"หรือไม่มีความเกี่ยวข้องกับชุมชนนี้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>โปรดพยายามให้คำตอบที่เป็นรูปธรรม</b>หากคุณต้องการแสดงความคิดเห็นเกี่ยวกับคำถามหรือคำตอบ เพียงแค่\n"
"            <b>ใช้เครื่องมือเพื่อแสดงความคิดเห็น</b> โปรดจำไว้ว่าคุณสามารถ <b>ทบทวนคำตอบของคุณ</b>ได้เสมอ\n"
"            - ไม่จำเป็นต้องตามคำถามเดิมเป็นครั้งที่สอง และโปรดจำไว้ว่า <b>อย่าลืมการโหวต</b>\n"
"            - มันดีที่สุดในการช่วยคัดเลือกคำถามและคำตอบที่ดีที่สุด!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<b>Tags</b> I Follow"
msgstr "<b>แท็ก</b> I ติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"<b>This forum is empty.</b><br/>\n"
"                    Be the first one asking a question"
msgstr ""
"<b>ฟอรั่มนี้ว่างเปล่า</b><br/>\n"
"                    ถามคำถามเป็นคนแรกสิ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>What kinds of questions can I ask here?</b> <i class=\"fa fa-caret-down "
"float-right\"></i>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>What should I avoid in my answers?</b> <i class=\"fa fa-caret-down float-"
"right\"></i>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>What should I avoid in my questions?</b> <i class=\"fa fa-caret-down "
"float-right\"></i>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid ""
"<b>Why can other people edit my questions/answers?</b> <i class=\"fa fa-"
"caret-down float-right\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>คุณมีโพสต์ที่่ค้างอยู่</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr "<b>[คำตอบ]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"ป้ายรางวัล Gold\" title=\"ป้ายรางวัล Gold\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<em class=\"d-block mb-2\">or</em>"
msgstr "<em class=\"d-block mb-2\">หรือ</em>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\" fa fa-comment text-muted mr-1\"/>Comment"
msgstr "<i class=\" fa fa-comment text-muted mr-1\"/>ความคิดเห็น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-bell fa-fw\"/> Followed Posts"
msgstr "<i class=\"fa fa-bell fa-fw\"/> โพสต์ที่ติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-bug\"/> Filter Tool"
msgstr "<i class=\"fa fa-bug\"/> เครื่องมือตัวกรอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw mr-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw mr-1\"/>ยอมรับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>You've Completely Caught Up!</b><br/>"
msgstr ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>คุณตามทันแล้ว!</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr "<i class=\"fa fa-check\"/> จะกำหนดค่าภาษีแคนาดาของ TPS และ TVQ ได้อย่างไร"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-check-square-o fa-fw\"/> To Validate"
msgstr "<i class=\"fa fa-check-square-o fa-fw\"/>เพื่อตรวจสอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<i class=\"fa fa-chevron-left mr-1\"/>Back"
msgstr "<i class=\"fa fa-chevron-left mr-1\"/>หลัง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-chevron-left mr-2\"/>Back to All Topics"
msgstr "<i class=\"fa fa-chevron-left mr-2\"/>กลับไปที่หัวข้อทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"fa fa-chevron-left small\"/> Back"
msgstr "<i class=\"fa fa-chevron-left small\"/> กลับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-flag fa-fw\"/> Flagged"
msgstr "<i class=\"fa fa-flag fa-fw\"/> ตั้งค่าสถานะ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    Flagged"
msgstr ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    ตั้งค่าสถานะ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> ประเทศ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                แก้ไข<span class=\"d-none d-lg-inline\">คำตอบของคุณ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-question-circle-o fa-fw\"/> My Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-reply mr-1\"/>Answer"
msgstr "<i class=\"fa fa-reply mr-1\"/>คำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-share-alt text-muted mr-1\"/>Share"
msgstr "<i class=\"fa fa-share-alt text-muted mr-1\"/>แชร์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Share"
msgstr ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                แชร์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-star fa-fw\"/> Favourites"
msgstr "<i class=\"fa fa-star fa-fw\"/> รายการโปรด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-tags fa-fw\"/> Followed Tags"
msgstr "<i class=\"fa fa-tags fa-fw\"/> แท็กที่ติดตาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"โหวตด้านลบ\" title=\"Negative votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-"
"label=\"โหวตด้านบวก\" title=\"Positive votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw mr-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw mr-1\"/>ปฏิเสธ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve solve my tax computation problem in Canada? Thanks!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> ผู้ใช้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"font-weight-bold\">Votes</small>"
msgstr "<small class=\"font-weight-bold\">โหวต</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                    ตั้งค่าสถานะ\n"
"                </small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span class=\"badge badge-info\">Closed</span>"
msgstr "<span class=\"badge badge-info\">ปิดแล้ว</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"ป้ายรางวัล Bronze \"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No answer posted yet.</span>"
msgstr "<span class=\"font-weight-bold\">ไม่มีการโพสต์คำตอบ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No question posted yet.</span>"
msgstr "<span class=\"font-weight-bold\">ยังไม่มีการโพสต์คำถาม</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"
msgstr "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<span class=\"navbar-text mr-1\">Go to:</span>"
msgstr "<span class=\"navbar-text mr-1\">ไปที่ :</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<span class=\"navbar-text mr-3\">Show Tags Starting By</span>"
msgstr "<span class=\"navbar-text mr-3\">แสดงแท็กเริ่มต้นโดย</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            Best Answer\n"
"                        </span>"
msgstr ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            คำตอบที่ดีที่สุด\n"
"                        </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted mx-3\">or</span>"
msgstr "<span class=\"text-muted mx-3\">หรือ</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>&amp;times;</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>By </span>"
msgstr "<span>ด้วย </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "A clear, explicit and concise question title"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "คำตอบใหม่บน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "คำถามใหม่"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "About"
msgstr "เกี่ยวกับ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "ยอมรับคำตอบสำหรับคำถามของตัวเอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "ยอมรับคำตอบของทุกคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr "ยอมรับคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "ยอมรับคำตอบแล้ว"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Access Denied"
msgstr "ไม่ได้รับอนุญาตให้เข้าใช้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "กิจกรรม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "กิจกรรม"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Add to menu"
msgstr "Add to menu"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"หลังจากโพสต์ ผู้ใช้จะได้รับการเสนอให้แชร์คำถามหรือคำตอบบนสื่อสังคมต่าง ๆ "
"ซึ่งทำให้สามารถเผยแพร่เนื้อหาในฟอรั่มสื่อสังคมได้"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "ทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All Forums"
msgstr "ฟอรั่มทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "All Tags"
msgstr "ป้ายกำกับทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "หัวข้อทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All forums"
msgstr "ฟอรั่มทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr "อนุญาตการชน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Amazing! There are not answered questions!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Answer"
msgstr "ตอบ"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr "แก้ไขคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "ยอมรับคำตอบแล้ว"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "คำตอบถูกยอมรับด้วย 15 โหวตหรือมากกว่า"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "คำตอบถูกโหวตไม่เห็นด้วย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "คำตอบถูกตั้งค่าสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "ตอบคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "คำตอบถูกโหวตเห็นด้วย"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "คำตอบถูกโหวตเห็นด้วย 15 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "คำตอบถูกโหวตเห็นด้วย 4 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "คำตอบถูกโหวตเห็นด้วย 6 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "ตำคอบถูกยอมรับด้วย 3 โหวตหรือมากกว่า"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "ตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr "ตอบแล้วโดย"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "ตอบคำถามของตัวเองด้วยคะแนนโหวตเห็นด้วยอย่างน้อย 4 คะแนน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "ตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
msgid "Answers Count"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__category
msgid "Appears in"
msgstr "ปรากฎใน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "ถูกเก็บ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Ask a Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "ถามคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "ถามคำถามโดยไม่ตรวจสอบความถูกต้อง"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Ask the question in this forum by clicking on the button."
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "ถามแล้วรับคำตอบ"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "ถามคำถามที่มียอดดูอย่างน้อย 150 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "ถามคำถามที่มียอดดูอย่างน้อย 250 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "ถามคำถามที่มียอดดูอย่างน้อย 500 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "ถามคำถามแรกด้วยคะแนนโหวตอย่างน้อยหนึ่งรายการ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "ถามบน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "ถามคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนไฟล์แนบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "ผู้แต่ง"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "อัตชีวประวัติ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Avatar"
msgstr ""
"สนามนี้ถือเป็นภาพที่ใช้เป็นสัญลักษณ์สำหรับการติดต่อนี้ จำกัด 1024x1024px"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Back"
msgstr "กลับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "กลับไปที่คำถาม"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Bad Request"
msgstr "คำขอที่ไม่ถูกต้อง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Badges"
msgstr "ป้าย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Be less specific in your wording for a wider search result"
msgstr "ใช้ถ้อยคำที่ไม่เฉพาะเจาะจงนักเพื่อผลลัพธ์การค้นหาที่กว้างขึ้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Browse All"
msgstr "เรียกดูทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr "ชนบน"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr "แชร์คำตอบของคุณ คุณจะได้รับเพิ่ม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "สามารถยอมรับ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "สามารถตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "สามารถถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "สามารถตรวจสอบได้โดยอัตโนมัติ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "สามารถปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "สามารถแสดงความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "สามารถแปลงเป็นความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "สามารถโหวตไม่เห็นด้วย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "สามารถแก้ไข"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "สามารถตั้งสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "สามารถกลั่นกรอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "สามารถยกเลิกการเชื่อมโยง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "สามารถโหวตเห็นด้วย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "สามารถดู"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "เปลี่ยนแท็กคำถาม"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""
"ทำเครื่องหมายที่ช่องนี้เพื่อแสดงป๊อปอัพสำหรับโพสต์ที่เก่ากว่า 10 "
"วันโดยไม่มีคำตอบ ป๊อปอัพจะเสนอให้แชร์บนสื่อสังคใต่าง ๆ "
"เมื่อแชร์แล้วคำถามจะขึ้นที่ด้านบนของฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Check your spelling and try again"
msgstr "ตรวจการสะกดของคุณแล้วลองอีกครั้ง"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "หัวหน้านักวิจารณ์"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click <em>Continue</em> to create the forum."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click here to accept this answer."
msgstr "คลิดที่นี้เพื่อยอมรับคำตอบ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your answer."
msgstr "คลิกเพื่อโพสต์คำตอบ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your question."
msgstr "คลิกเพื่อโพสต์คำถามของคุณ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
#, python-format
msgid "Close"
msgstr "ปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "ปิดโพสต์ทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "ปิดโพสต์ของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Close post"
msgstr "ปิดโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "ปิดโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "ปิดเมือ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Closing"
msgstr "ปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "เหตุผลการปิด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment"
msgstr "ความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "แสดงความคิดเห็นในทุกโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "แสดงความคิดเห็นบนโพสต์ของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr "แสดงความคิดเห็นบนโพสต์นี้"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.challenge.line,name:website_forum.line_chief_commentator
#: model:gamification.challenge.line,name:website_forum.line_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "ผู้แสดงความคิดเห็น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comments"
msgstr "ความคิดเห็น"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "กรอกประวัติส่วนตัว"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_1
#: model:gamification.challenge.line,name:website_forum.line_configure_profile
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
msgid "Completed own biography"
msgstr "กรอกประวัติตัวเอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "เนื้อหา"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr "แปลงคำตอบทั้งหมดเป็นความคิดเห็นและในทางกลับกัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as a answer"
msgstr "แปลงเป็นคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr "แปลงเป็นความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "แปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr "แปลงคำตอบของตัวเองเป็นความคิดเห็นและในทางกลับกัน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "ถูกต้อง"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "คำตอบที่ถูกต้องหรือคำตอบได้รับการยอมรับ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "Create"
msgstr "สร้าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "วันที่สร้าง"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr "สร้างโพสต์ในฟอรั่มใหม่"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "สร้างแท็กใหม่"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "สร้างแท็กใหม่"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "สร้างแท็กที่ใช้โดย 15 คำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "คำถามที่น่าเชื่อถือ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.challenge.line,name:website_forum.line_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "การวิจารณ์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "ค่าเริ่มต้น"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__category
msgid "Define the visibility of the challenge through menus"
msgstr "กำหนดการมองเห็นความท้าทายผ่านเมนู"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Delete"
msgstr "ลบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "ลบโพสต์ทั้งหมดด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "ลบโพสต์ของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Deleted"
msgstr "ลบ"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "ลบโพสต์ของตัวเองที่มีการโหวตไม่เห็นด้วย 3 ครั้งขึ้นไป"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "ลบโพสต์ของตัวเองที่มีการโหวตเห็นด้วย 3 ครั้งขึ้นไป"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "รายละเอียด"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#, python-format
msgid "Discard"
msgstr "ยกเลิก"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.challenge.line,name:website_forum.line_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "มีระเบียบวินัย"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
#, python-format
msgid "Discussions"
msgstr "การสนทนา"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "แสดงประวัติผู้ใช้โดยละเอียด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr "โหวตไม่เห็นด้วย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Edit"
msgstr "แก้ไข"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Answer"
msgstr "แก้ไขคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Edit Forum in Backend"
msgstr "แก้ไขฟอรั่มในส่วนหลัง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Question"
msgstr "แก้ไขคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "แก้ไขโพสต์ทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "แก้ไขโพสตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "แก้ไขโพสของคุณ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.challenge.line,name:website_forum.line_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "ผู้แก้ไข"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "ฟีเจอร์ตัวแก้ไข: รูปภาพและลิงก์"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.challenge.line,name:website_forum.line_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "แจ่มแจ้ง"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Enter a name for your new forum."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                        <i class=\"fa fa-question-circle\"/>"
msgstr ""
"ตัวอย่าง\n"
"                        <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "คำถามยอดฮิต"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite Count"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "คำถามโปรด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "Favorites Count"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "ทำให้เป็นรายการโปรด"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_1
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "คำถามโปรด (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_stellar_question_25
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "คำถามโปรด (25)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_5
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "คำถามโปรด (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "คำถามโปรด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Favourites"
msgstr "รายการโปรด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "กรองโดย:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "พารามิเตอร์ที่เกี่ยวข้องอันดับแรก"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "โหวตไม่เห็นด้วยแรก"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "แก้ไขครั้งแรก"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "โหวตไม่เห็นด้วยครั้งแรก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flag"
msgstr "ธง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "ตั้งค่าสถานะโพสต์ว่าเป็นการล่วงละเมิด"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Flagged"
msgstr "ตั้งค่าสถานะ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "ตั้งค่าสถานะโดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "คำถามที่ติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_channel_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_channel_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_channel_ids
msgid "Followers (Channels)"
msgstr "ผู้ติดตาม (ช่องทาง)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (คู่ค้า)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Following"
msgstr "กำลังติดตาม"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 10 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"ตัวอย่างเช่น หากคุณถามคำถามที่น่าสนใจหรือให้คำตอบที่เป็นประโยชน์ "
"ข้อมูลของคุณจะถูกโหวตให้เห็นด้วย ในทางกลับกัน ถ้าคำตอบทำให้เข้าใจผิด - จะถูก"
" downvoted โหวตเห็นด้วยแต่ละครั้งจะได้ 10 คะแนน โหวตไม่เห็นด้วย 10 คะแนน "
"จำกัด 200 คะแนนที่สามารถสะสมสำหรับคำถามหรือคำตอบต่อวัน "
"ตารางที่ระบุในตอนท้ายจะอธิบายข้อกำหนดคะแนนชื่อเสียงสำหรับงานกลั่นกรองแต่ละประเภท"

#. module: website_forum
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#: model:website.menu,name:website_forum.menu_website_forums
msgid "Forum"
msgstr "ฟอรัม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Forum Mode"
msgstr "โหมดฟอรั่ม"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Forum Mode:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
msgid "Forum Name"
msgstr "ชื่อฟอรัม"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Forum Name:"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr "โพสของฟอรัม"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr "โพสของฟอรัม"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "ป้ายกำกับฟอรัม"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr "ฟอรัม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forums_count
msgid "Forums Count"
msgstr "จำนวนฟอรั่ม"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "ความท้าทายในรูปแบบเกม"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Give your question title."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "คำตอบที่ดี"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_answer
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "คำตอบที่ดี (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "คำถามที่ดี"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr "กราฟของโพสต์"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "คำตอบที่่ยอดเยี่ยม"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_answer
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "คำตอบที่่ยอดเยี่ยม (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "คำถามที่ยอดเยี่ยม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "จัดกลุ่มโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Guidelines"
msgstr "หลักเกณฑ์"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "กูรู"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_guru
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "กูรู (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "ตอบคำถามแล้ว"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "ช่วยเหลือ"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr "ซ่อนบทนำ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "I'm <b>Following</b>"
msgstr "ฉัน<b>กำลังติดตาม</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "I'm Following"
msgstr "ฉันกำลังติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "รหัส"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "หากเลือกข้อความบางข้อความมีข้อผิดพลาดในการจัดส่ง"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"หากผู้เขียนมีคะแนน Karma ไม่เพียงพอ คุณลักษณะไม่ติดตามจะถูกเพิ่มลงในลิงก์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid ""
"If this approach is not for you, please respect the community and use "
"Google+ communities instead."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"หากคุณปิดโพสต์นี้ โพสต์นั้นจะถูกซ่อนสำหรับผู้ใช้ส่วนใหญ่ เฉพาะ\n"
"            ผู้ใช้ที่มีคะแนน Karma สูงสามารถเห็นโพสต์ที่ปิดเพื่อกลั่นกรอง\n"
"            ได้"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"หากคุณเหมาะสมกับตัวอย่างใดตัวอย่างหนึ่งเหล่านี้ "
"หรือถ้าแรงจูงใจของคุณในการถามคำถามคือ "
"“ฉันต้องการมีส่วนร่วมในการอภิปรายเกี่ยวกับ ______” "
"คุณไม่ควรถามที่นี่แต่อยู่ในรายชื่อผู้รับจดหมายของเรา อย่างไรก็ตาม "
"หากแรงจูงใจของคุณคือ “ฉันอยากให้คนอื่นอธิบาย ______ ให้ฉันฟัง” "
"คุณก็อาจจะโอเค"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"หากคุณทำเครื่องหมายโพสต์นี้ว่าไม่เหมาะสม โพสต์นั้นจะถูกซ่อนสำหรับผู้ใช้ส่วนใหญ่ เฉพาะ\n"
"           ผู้ใช้ที่มีคะแนน Karma สูงสามารถเห็นโพสต์ที่ไม่เหมาะสมเพื่อกลั่นกรอง\n"
"            ได้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "รูปภาพ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Insert tags related to your question."
msgstr "แทรกแท็กที่เกี่ยวข้องกับคำถามของคุณ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "เป็นรายการโปรด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "ตอบแล้ว"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "ชีวประวัติของผู้เขียนมองเห็นได้จากโพสต์ของเขาหรือไม่"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to modify someone else's vote."
msgstr "ไม่อนุญาตให้แก้ไขการลงคะแนนของผู้อื่น"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr "ไม่อนุญาตให้ลงคะแนนสำหรับโพสต์ของตัวเอง"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Karma Error"
msgstr "ข้อผิดพลาด Karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr "Karma เพิ่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr "สิทธิเกี่ยวข้องกับ Karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma เพื่อปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma เป็นความคิดเห็น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "ใช้ Karma เพื่อแปลงความคิดเห็นเป็นคำตอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Karma เพื่อแก้ไข"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma เพื่อยกเลิกการเชื่อมโยง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__write_date_desc
msgid "Last Updated"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr "วันที่กิจกรรมสุดท้าย"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "เหลือ 10 คำตอบกับ 10 หรือมากกว่า"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr "ไฟล์แนบหลัก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Mark as Best Answer"
msgstr "ทำเครื่องหมายว่าเป็นคำตอบที่ดีที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "ทำเครื่องหมายว่าเป็นการละเมิด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "ทำเครื่องหมายว่าเป็นแสปม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "ส่งข้อความผิดพลาด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "โมเดอเรทโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation"
msgstr "การกลั่นกรอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Moderation tools"
msgstr "เครื่องมือกลั่นกรอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "More"
msgstr "เพิ่มเติม"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "More over:"
msgstr "นอกจากนี้:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "ได้รับการโหวตมากที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr "ได้รับคำตอบมากที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr "ได้รับการโหวตมากที่สุด"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr "ย้ายคำถามนี้ไปที่ด้านบนสุดของรายการโดยการแชร์บนเครื่อข่ายสังคม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Favourites</b>"
msgstr "<b>รายการโปรด</b>ของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Questions</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Favourites"
msgstr "รายการโปรดของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "โหวตของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "My profile"
msgstr "โปรไฟล์ของฉัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Name"
msgstr "ชื่อ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr "โหวตด้านลบ"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "คำตอบใหม่"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "New Forum"
msgstr "ใหม่ฟอรัม"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "คำถามใหม่"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Newest"
msgstr "ใหม่สุด"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "คำตอบที่น่าสนใจ"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_answer
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "คำตอบที่น่าสนใจ (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "คำถามน่าสนใจ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "No activities yet!"
msgstr "ยังไม่มีกิจกรรม!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "No favourite questions in this forum (yet).<br/>"
msgstr "ไม่มีคำถามโปรดในฟอรั่มนี้<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No flagged posts"
msgstr "ไม่มีโพสต์ที่ตั้งสถานะ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "ยังไม่มีฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No post to be validated"
msgstr "ไม่มีโพสต์ที่จะตรวจสอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "No tags"
msgstr "ไม่มีแท็ก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "No vote given by you yet!"
msgstr "ยังไม่มีการลงคะแนนจากคุณ!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "ลิงก์ไม่ติดตาม"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "คำถามที่โดดเด่น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "จำนวนกิจกรรม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "จำนวนของโพสต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
msgid "Number of Views"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
msgid "Number of answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "จำนวนข้อผิดพลาด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "จำนวนโพสต์ที่ตั้งสถานะ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "จำนวนโพสต์ที่รอการตรวจสอบ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Offensive"
msgstr "ก้าวร้าว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Offensive Post"
msgstr "โพสต์ที่ล่วงละเมิด"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "On average,"
msgstr "โดยเฉลี่ย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Only one answer per question is allowed"
msgstr "อนุญาตเพียงหนึ่งคำตอบต่อหนึ่งคำถามเท่านั้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "ตัวเลือก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Order"
msgstr "Order"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Order by"
msgstr "สั่งโดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Our forums"
msgstr "ฟอรัมของเรา"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.challenge.line,name:website_forum.line_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "แรงกดดันจากเพื่อน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "People"
msgstr "ประชาชน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "เนื้อหาธรรมดา"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr "โปรดรอให้ผู้ดูแลตรวจสอบโพสต์ก่อนหน้าของคุณก่อนดำเนินการต่อ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "คำถามยอดนิยม"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_popular_question
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "คำถามยอดนิยม (150)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_notable_question
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "คำถามยอดนิยม (250)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_famous_question
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "คำถามยอดนิยม (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr "โหวตด้านบวก"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "ลงบัญชี"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr "โพสต์คำตอบ"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "โพสต์เหตุผลปิด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "Post Count"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "โพสต์โหวต"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "โพสต์คำถามของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Post comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr "โพสต์:"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "โพสต์ 10 ความคิดเห็น"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "โพสต์ 100 ความคิดเห็น"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr "ไม่สามารถโพสต์คำตอบ [ลบ] หรือ [ปิด] ในคำถามได้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
msgid "Posts"
msgstr "โพสต์"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.challenge.line,name:website_forum.line_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "บัณฑิต"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your answer here."
msgstr "ใส่คำตอบของคุณตรงนี้"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your question here."
msgstr "ใส่คำถามของคุณที่นี้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question"
msgstr "คำถาม"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr "แก้ไขคำถามแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question by"
msgstr "คำถามโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "คำถามที่ถูกโหวตไม่เห็นด้วย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "ไม่พบคำถาม!"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "คำถามที่ตั้งไว้เป็นที่ชื่นชอบโดยผู้ใช้ 1 ราย"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "คำถามที่ถูกตั้งไว้เป็นที่ชื่นชอบโดยผู้ใช้ 25 ราย"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "คำถามที่ถูกตั้งไว้เป็นที่ชื่นชอบโดยผู้ใช้ 5 ราย"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Question should not be empty."
msgstr "คำถามต้องไม่เว้นว่าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "คำถามถูกโหวตเห็นด้วย"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "คำถามถูกโหวตเห็นด้วย 15 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "คำถามถูกโหวตเห็นด้วย 4 ครั้ง"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "คำถามถูกโหวตเห็นด้วย 6 ครั้ง"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Questions"
msgstr "คำถาม"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Questions and Answers"
msgstr "คำถามและคำตอบ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"โหมดคำถาม: อนุญาติเพียงคำตอบเดียว\n"
"โหมดการสนทนา: อนุญาตให้ตอบได้หลายคำตอบ"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "อันดับ"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Re: %s"
msgstr "ตอบกลับ: %s"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Read the guidelines to know how to gain karma."
msgstr "อ่านแนวปฏิบัติเพื่อทราบวิธีการได้รับ Karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Read: #{question.name}"
msgstr "อ่าน: #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "เหตุผล"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "ประเภทเหตุผล"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr "เหตุผล:"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "ได้รับการโหวตเห็นด้วยอย่างน้อย 3 ครั้งสำหรับการตอบเป็นครั้งแรก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "ปฏิเสธ"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr "สมัคร"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "เกี่ยวข้อง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr "การคำนวรที่เกี่ยวข้อง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Reopen"
msgstr "เปิดอีกครั้ง"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Reply should not be empty."
msgstr "การตอบกลับต้องไม่เว้นว่าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "ตอบคำถามของตัวเอง"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr "Restrict publishing to this website."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the forum."
msgstr "กลับไปที่ฟอรั่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Return to the question list."
msgstr "กลับไปที่รายการคำถาม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "รีวิวโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimized"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save changes"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.challenge.line,name:website_forum.line_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "นักวิชาการ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Search Tips"
msgstr "ค้นหาเคล็ดลับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr "ต้นหาในโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "ค้นหา..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "พารามิเตอร์ที่เกี่ยวข้องอันดับสอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "See"
msgstr "ดู"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "ดูโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "ดูคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "เลือกทั้งหมด"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Select this menu item to create a new forum."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.challenge.line,name:website_forum.line_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "ผู้เรียนรู้ด้วยตัวเอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"แชร์เนื้อหานี้เพื่อเพิ่มโอกาสในการแสดงจุดเด่นต่าง ๆ "
"บนหน้าแรกและดึงดูดผู้เยี่ยมชมมากขึ้น"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "แชร์ตัวเลือก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Show"
msgstr "แสดง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Show Tags Starting By"
msgstr "แสดงแท็กเริ่มต้นโดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Sign in"
msgstr "ลงชื่อเข้าใช้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Solved"
msgstr "แก้แล้ว"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr "ขออภัย คุณต้องเข้าสู่ระบบเพื่อดำเนินการ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr "ขออภัย คุณต้องเข้าสู่ระบบเพื่อตั้งค่าสถานะโพสต์"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to vote"
msgstr "ขออภัย คุณต้องเข้าสู่ระบบเพื่อโหวต"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr "ขออภัย ผู้ใช้ที่ไม่ระบุชื่อไม่สามารถเลือกคำตอบที่ถูกต้องได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "ขออภัย คำถามนี้ไม่มีอยู่แล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"Sorry, we could not find any <b>%s</b> result <b>\n"
"                        %s</b> %s%s%s."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr "ขออภัย คุณไม่สามารถลงคะแนนสำหรับโพสต์ของคุณเองได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "แสปมโพสต์ทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "สถานะ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "คำถามแจ่ม ๆ "

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "นักเรียน"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.challenge.line,name:website_forum.line_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "ผู้สนับสนุน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "ป้ายกำกับ"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists !"
msgstr "ชื่อป้ายกำกับมีอยู่แล้ว"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Tags"
msgstr "ป้ายกำกับ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Tags I Follow"
msgstr "แท็กที่ฉันติดตาม"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.challenge.line,name:website_forum.line_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "นักอนุกรมวิธาน"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.challenge.line,name:website_forum.line_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "ครู"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""
"ฟิลด์เทคนิคที่อนุญาตให้กระแทกคำถาม การเขียนในฟิลด์นี้จะทำให้เกิดการเขียนใน "
"write_date และทำให้โพสต์สะดุด ขณะนี้ไม่รองรับการเขียนโดยตรงใน write_date "
"และฟิลด์นี้เป็นวิธีแก้ปัญหาชั่วคราว"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "Thanks for posting!"
msgstr "ขอบคุณสำหรับโพสต์!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"เป้าหมายของไซต์นี้คือการสร้างฐานความรู้ที่เกี่ยวข้อง "
"ซึ่งจะตอบคำถามที่เกี่ยวกับ Odoo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "คำถามนี้ถูกปิดแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"ดังนั้น คำถามและคำตอบสามารถแก้ไขได้เหมือนหน้า Wiki "
"โดยผู้ใช้ที่มีประสบการณ์ของไซต์นี้ "
"เพื่อปรับปรุงคุณภาพโดยรวมของเนื้อหาและความรู้ต่าง ๆ  สิทธิพิเศษต่าง ๆ "
"จะได้รับตามระดับ Karma ของผู้ใช้ "
"คุณจะสามารถทำได้เช่นเดียวกันเมื่อกรรมของคุณสูงพอ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers."
msgstr ""
"ชุมชนนี้มีไว้สำหรับผู้ใช้ พาร์ทเนอร์ และนักพฒนาโปรแกรม "
"ที่เป็นมืออาชีพและกระตือรือร้น"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"ชุมชนนี้มีไว้สำหรับผู้ใช้ พาร์ทเนอร์ "
"และนักพัฒนาโปรแกรมที่เป็นมืออาชีพและกระตือรือร้น คุณสามารถถามคำถามเกี่ยวกับ:"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and services.\n"
"                                        <br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""
"ชุมชนนี้มีไว้สำหรับมืออาชีพและผู้ที่ชื่นชอบสินค้าและบริการของเรา\n"
"                                        <br>แบ่งปันและหารือเกี่ยวกับเนื้อหาที่ดีที่สุดและแนวคิดทางการตลาดใหม่ ๆ สร้างโปรไฟล์ทางวิชาชีพของคุณและกลายเป็นนักการตลาดที่ดีขึ้นไปด้วยกัน"

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"ชุมชนนี้มีไว้สำหรับมืออาชีพและผู้ที่ชื่นชอบผลิตภัณฑ์และบริการของเรา "
"แบ่งปันและหารือเกี่ยวกับเนื้อหาที่ดีที่สุดและแนวคิดทางการตลาดใหม่ ๆ "
"สร้างโปรไฟล์มืออาชีพของคุณและกลายเป็นนักการตลาดที่ดีขึ้นด้วยกัน"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"สูตรนี้ใช้เพื่อจัดเรียงตามความเกี่ยวข้อง ตัวแปร 'โหวต' "
"หมายถึงจำนวนโหวตสำหรับโพสต์ และ 'วัน' คือจำนวนวันนับตั้งแต่สร้างโพสต์"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "ฟอรั่มนี้ถูกเก็บถาวรแล้ว"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post can not be flagged"
msgstr "โพสต์นี้ไม่สามารถตั้งค่าสถานะได้"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post is already flagged"
msgstr "โพสต์นี้ถูกตั้งค่าสถานะแล้ว"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and it's not published yet.<br/>\n"
"                Do you want <b>Accept</b> or <b>Reject</b> this post ?"
msgstr ""
"โพสต์นี้อยู่ในระหว่างรอการตรวจสอบและยังไม่ได้เผยแพร่<br/>\n"
"                คุณต้องการ <b>ยอมรับ</b> หรือ <b>ปฏิเสธ</b> โพสต์นี้?"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "ชื่อเรื่องต้องไม่เว้นว่าง"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Title should not be empty."
msgstr "ชื่อเรื่องไม่ควรถูกเว้นว่าง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "ถึง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "To Validate"
msgstr "เพื่อตรวจสอบ"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"เพื่อป้องกันไม่ให้คำถามของคุณถูกตั้งค่าสถานะและอาจถูกลบ "
"ให้หลีกเลี่ยงการถามคำถามที่มีความเป็นความคิดเห็นส่วนตัวซึ่ง ..."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr "สถานะสลับรายการโปรด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Toolbar with button groups"
msgstr "แถบเครื่องมือที่มีกลุ่มปุ่ม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Topics"
msgstr "หัวข้อ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "คะแนนโหวตทั้งหมด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Trending"
msgstr "กำลังมาแรง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Try searching for one or two words"
msgstr "ลองค้นหาสักหนึ่งหรือสองคำ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr "ยกเลิกการตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Undelete"
msgstr "ยกเลิกการลบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr "ยกเลิกการเชื่อมโยงความคิดเห็นทั้งหมด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr "ยกเลิกการเชื่อมโยงกับความคิดเห็นของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Unmark as Best Answer"
msgstr "ยกเลิกการทำเครื่องหมายเป็นคำตอบที่ดีที่สุด"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread_counter
msgid "Unread Messages Counter"
msgstr "จำนวนข้อความที่ยังไม่ได้อ่าน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unsolved"
msgstr "ยังไม่แก้"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Update on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "อัปเดตโดย"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr "โหวตเห็นด้วย"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_student
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "คำถามถูกโหวตเห็นด้วย (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_question
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "คำถามถูกโหวตเห็นด้วย (15)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_question
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "คำถามถูกโหวตเห็นด้วย (4)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_question
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "คำถามที่ถูกโหวตเห็นด้วย (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise question title"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "ผู้ใช้"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "ผู้ใช้งาน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "ตรวจสอบคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr "มุมมอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr "มุมมอง"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
msgid "Views Count"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "โหวต"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists !"
msgstr "โหวตมีอยู่แล้ว !"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "โหวต"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "กำลังรอการยืนยัน"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "รอการตรวจสอบ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr "รอโพสต์"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__category__forum
msgid "Website / Forum"
msgstr "เว็บไซต์ / ฟอรัม"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "ข้อความจากเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารเว็บไซต์"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Website meta description"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta keywords"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Website meta title"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Website opengraph image"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "ข้อความต้อนรับ"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "ยินดีต้อนรับ!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"เมื่อมีการโหวตคำถามหรือคำตอบ ผู้ใช้ที่โพสต์จะได้รับคะแนนบางส่วน ซึ่งเรียกว่า"
" \"คะแนนกรรม\" ประเด็นเหล่านี้เป็นตัววัดความเชื่อถือของชุมชนที่มีต่อเขา/เธอ "
"งานการกลั่นกรองต่างๆ จะค่อยๆ มอบหมายให้กับผู้ใช้ตามจุดเหล่านั้น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You already have a pending post"
msgstr "คุณมีโพสต์ที่รอดำเนินการอยู่"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "You cannot create recursive forum posts."
msgstr "คุณไม่สามารถสร้างโพสต์ในฟอรั่มแบบเรียกซ้ำได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "คุณไม่สามารถโพสต์คำตอบที่ว่างเปล่าได้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You have no questions in this forum (yet)."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "คุณต้องมีคะแนน Karma เพียงพอที่จะแก้ไขแท็ก"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"คุณควรถามคำถามที่ใช้งานได้จริงและตอบได้ โดยอิงจากปัญหาจริงที่คุณเผชิญ "
"คำถามปลายเปิดช่างพูดลดทอนประโยชน์ของเว็บไซต์นี้และผลักคำถามอื่นๆ "
"ออกจากหน้าแรก"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You're not following any topic in this forum (yet).<br/>"
msgstr "คุณยังไม่ได้ติดตามหัวข้อใด ๆ ในฟอรั่มนี้<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "คำตอบของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr "การตอบกลับของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "รายการโปรดปรานของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Closed]"
msgstr "[ปิด]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Deleted]"
msgstr "[ลบ]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Offensive]"
msgstr "[ล่วงละเมิด]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "accept any answer"
msgstr "ยอมรับคำตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "activity date"
msgstr "วันที่กิจกรรม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr "โดย"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "close any posts"
msgstr "ปิดโพสต์"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "contains offensive or malicious remarks"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any comment"
msgstr "ลบความคิดเห็น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any question or answer"
msgstr "ลบคำถามหรือคำตอบใด ๆ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete own comment"
msgstr "ลบความคิดเห็นของตัวเอง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "downvote"
msgstr "โหวตไม่เห็นด้วย"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "duplicate post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "edit any post, view offensive flags"
msgstr "แก้ไขโพสต์ใด ๆ ดูการตั้งค่าสถานะที่ไม่เหมาะสม"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "ทุกคำตอบมีความถูกต้องเท่าเทียมกัน: “คุณชอบอะไร ______?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "flag offensive, close own questions"
msgstr "ตั้งค่าสถานะโพสต์เป็นการล่วงละเมิด และปิดคำถาม"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "เนื่องจาก:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"ได้รับการโพสต์และต้องการการการตรวจสอบจากคุณ คลิกที่นี้เพื่อเข้าสู่คำถาม:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "ได้รับการโพสต์ คลิกที่นี่เพื่อเข้าสู่โพสต์:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "ได้รับการโพสต์ คลิกที่นี้เพื่อเข้าสู่คำถาม:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "here"
msgstr "ที่นี่"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"กำหนดค่าหรือปรับแต่ง Odoo ให้ตรงกับความต้องการทางธุรกิจเฉพาะได้อย่างไร"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to develop modules for your own need,"
msgstr "พัฒนาโมดูลตามความต้องการของคุณได้อย่างไร"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to install Odoo on a specific infrastructure,"
msgstr "วิธีการติดตั้ง Odoo บนโครงสร้างพื้นฐานเฉพาะ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"ถ้า\n"
"        คำตอบของคุณถูกเลือกให้เป็นคำตอบที่ถูกต้อง ดูว่าคุณสามารถทำอย่างไรกับคะแนน Karma "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your favourites"
msgstr "ในรายการโปรดของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your followed list"
msgstr "ในรายการที่ติดตามของคุณ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your questions"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "inappropriate and unacceptable statements"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "insert text link, upload files"
msgstr "แทรกลิงก์ข้อความ อัปโหลดไฟล์"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "insulting and offensive language"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "เป็นการพูดจาโผงผางปลอมตัวเป็นคำถาม: “______ ห่วย จริงไหม?”"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "karma is required to perform this action. "
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "karma points"
msgstr "คะแนน karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "matching \""
msgstr "ตรงกัน \""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most answered"
msgstr "ถูกตอบมากที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most voted"
msgstr "ถูกโหวตมากที่สุด"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "newest"
msgstr "ใหม่ล่าสุด"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "not a real post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "not relevant or out dated"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "off-topic or not relevant"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "บน"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"บนเครือข่ายทางสังคมได้รับคำตอบภายใน\n"
"        5 ชั่วโมง คำถามที่แชร์ในสองช่องทางโซเชียลเน็ตเวิร์กมี"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "post"
msgstr "โพสต์"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "racist and hate speech"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "solved"
msgstr "แก้แล้ว"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "spam or advertising"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "specific questions about Odoo service offers, etc."
msgstr "คำถามเฉพาะเกี่ยวกับข้อเสนอและบริการต่าง ๆ ของ Odoo และอื่น ๆ "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "tag"
msgstr "แท็ก"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr "ไม่มีปัญหาจริงที่ต้องแก้ไข: \"ฉันอยากรู้ว่าคนอื่นรู้สึกเหมือนฉันไหม\""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "threatening language"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "to partecipate"
msgstr "เพื่อเข้าร่วม"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "too localized"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "too subjective and argumentative"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "trending"
msgstr "กำลังมาแรง"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unanswered"
msgstr "ไม่ได้ตอบ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unsolved"
msgstr "ยังไม่แก้"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "upvote, add comments"
msgstr "โหวตออนไลน์และเพิ่มความคิดเห็น"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "using the"
msgstr "ใช้"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "violent language"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "w-100"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"เรากำลังถูกถามคำถามปลายเปิดและสมมติขึ้นว่า \"จะเกิดอะไรขึ้นถ้า ______ "
"เกิดขึ้น\""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"วิธีที่ดีที่สุดในการใช้ Odoo สำหรับความต้องการทางธุรกิจแบบเฉพาะคืออะไร"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"คำตอบของคุณมีให้พร้อมกับคำถาม และคุณคาดหวังคำตอบเพิ่มเติม: “ฉันใช้ ______ "
"สำหรับ ______ คุณล่ะใช้อะไร”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "your biography can be seen as tooltip"
msgstr "ชีวประวัติของคุณสามารถมองเห็นเป็นทูลทิป"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "| Flagged"
msgstr "| ตั้งค่าสถานะ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "圾 Text"
msgstr "圾 ข้อความ"
