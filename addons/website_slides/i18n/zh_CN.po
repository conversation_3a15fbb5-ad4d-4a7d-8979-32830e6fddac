# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> Gu <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# fausthuang, 2019
# <AUTHOR> <EMAIL>, 2019
# bower <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# bf2549c5415a9287249cba2b8a5823c7, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# Martin Trigaux, 2020
# <AUTHOR> <EMAIL>, 2020
# nle_odoo, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: Jeffery CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "# Completed Slides"
msgstr "# 完成的幻灯片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
msgid "# Views"
msgstr "# 访问"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "# 公众访问"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "# 网站访问"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "${user.name} shared a ${object.slide_type} with you!"
msgstr "${user.name} 向你分享了 ${object.slide_type}!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "(empty)"
msgstr "(空白)"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 主要方法学"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "50%"
msgstr "50%"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(空白)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>排序</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>未分类</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name or 'participant'}<br/><br/>\n"
"        You have been invited to join a new course: ${object.channel_id.name}.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"${(object.channel_id.website_url) | safe}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Click here to start the course.\n"
"            </a>\n"
"        </div>\n"
"        Enjoy this exclusive content !\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        亲爱的 ${object.partner_id.name or 'participant'}<br/><br/>\n"
"       您已被邀请加入新课程: ${object.channel_id.name}.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"${(object.channel_id.website_url) | safe}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"               点击此处开始课程.\n"
"            </a>\n"
"        </div>\n"
"        享受这个独家内容 !\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        ${user.name} shared the ${object.slide_type} <strong>${object.name}</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${(object.website_url + '?fullscreen=1') if ctx['fullscreen'] else object.website_url | safe}\">\n"
"                                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide.slide/${object.id}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${(object.website_url + '?fullscreen=1') if ctx['fullscreen'] else object.website_url | safe}\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong>${object.name}</strong></a>\n"
"                        </div>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        你好<br/><br/>\n"
"                        ${user.name} 分享了 ${object.slide_type} <strong>${object.name}</strong> 给你!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${(object.website_url + '?fullscreen=1') if ctx['fullscreen'] else object.website_url | safe}\">\n"
"                                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide.slide/${object.id}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${(object.website_url + '?fullscreen=1') if ctx['fullscreen'] else object.website_url | safe}\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">浏览 <strong>${object.name}</strong></a>\n"
"                        </div>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong>${object.channel_id.name}</strong> you are following:<br/><br/>\n"
"                        <center><strong>${object.name}</strong></center>\n"
"                        % if object.image_1024\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${object.website_url}\">\n"
"                                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide.slide/${object.id}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        % endif\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${object.website_url}\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        你好<br/><br/>\n"
"                       课程中有一些新内容 <strong>${object.channel_id.name}</strong>你正系关注:<br/><br/>\n"
"                        <center><strong>${object.name}</strong></center>\n"
"                        % if object.image_1024\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${object.website_url}\">\n"
"                                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide.slide/${object.id}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        % endif\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"${object.website_url}\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">查看内容</a>\n"
"                        </div>\n"
"                        享受这个独家内容!\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> 统计"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">课程</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">上一页</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>加载中...</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload mr-1\"/>Upload new content"
msgstr "<i class=\"fa fa-cloud-upload mr-1\"/>上传新内容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-code\"/> Embed"
msgstr "<i class=\"fa fa-code\"/> 嵌入"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments-o\"/> Comments ("
msgstr "<i class=\"fa fa-comments-o\"/> 备注 ("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">全屏</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-envelope\"/> Email"
msgstr "<i class=\"fa fa-envelope\"/> 电子邮件"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope-o\"/> Send Email"
msgstr "<i class=\"fa fa-envelope-o\"/> 发送邮件"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser mr-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser mr-1\"/>清除筛选"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> 清除筛选"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> 这份文件是私有的。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/> 测验"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>测验"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o mr-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o mr-1\"/><span>添加章节</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap mr-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap mr-1\"/>所有课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home d-md-none\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Back to course</span>"
msgstr ""
"<i class=\"fa fa-home d-md-none\"/><span class=\"d-none d-md-inline-block "
"ml-1\">回到课程</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid ""
"<i class=\"fa fa-home\"/>\n"
"                                            Course"
msgstr ""
"<i class=\"fa fa-home\"/>\n"
"                                            课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> 关于"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/> 课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    The social sharing module will be unlocked when a moderator will allow your publication."
msgstr ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    当管理员允许您的出版时，社交共享模块将被解锁。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-pencil\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Write a review</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/><span class=\"d-none d-md-inline-block "
"ml-1\">写评论</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-"
"block\">添加内容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus mr-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus mr-1\"/><span>添加内容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> 分享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">分享</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-square fa-fw\"/> Share"
msgstr "<i class=\"fa fa-share-square fa-fw\"/> 分享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-share-square\"/> Share"
msgstr "<i class=\"fa fa-share-square\"/> 分享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out d-md-none\"/><span class=\"d-none d-md-inline-"
"block ml-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out d-md-none\"/><span class=\"d-none d-md-inline-"
"block ml-1\">退出全屏</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" title=\"Likes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" "
"title=\"Likes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz
msgid "<span aria-hidden=\"true\">&amp;times;</span>"
msgstr "<span aria-hidden=\"true\">&amp;times;</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-danger\">Unpublished</span>"
msgstr "<span class=\"badge badge-danger\">未发布</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-info\">Free preview</span>"
msgstr "<span class=\"badge badge-info\">免费预览</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> 已完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/> "
"已完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">下一个</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Create a Google Project and Get a Key"
msgstr ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    创建一个Google 项目以及获取密钥"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid "<span class=\"fa fa-clipboard\"> Copy Text</span>"
msgstr "<span class=\"fa fa-clipboard\"> 复制文字</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"font-weight-bold text-muted mr-2\">Current rank:</span>"
msgstr "<span class=\"font-weight-bold text-muted mr-2\">当前排名：</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"font-weight-normal\">Last update:</span>"
msgstr "<span class=\"font-weight-normal\">最后更新：</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid ""
"<span class=\"form-text text-muted d-block w-100\">Send presentation through"
" email</span>"
msgstr "<span class=\"form-text text-muted d-block w-100\">通过电子邮件发送简报</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid ""
"<span class=\"form-text text-muted\">Use permanent link to share in social "
"media</span>"
msgstr "<span class=\"form-text text-muted\">使用永久链接分享社交媒</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span class=\"ml-2\">Hours</span>"
msgstr "<span class=\"ml-2\">小时</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">幻灯片</span>\n"
"  <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">课程</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">课程内容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted font-weight-bold mr-3\">Rating</span>"
msgstr "<span class=\"text-muted font-weight-bold mr-3\">评分</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Attendees</span>"
msgstr "<span class=\"text-muted\">与会者</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Contents</span>"
msgstr "<span class=\"text-muted\">内容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span name=\"done_members_count_label\" class=\"text-muted\">Finished</span>"
msgstr "<span name=\"done_members_count_label\" class=\"text-muted\">完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span name=\"members_done_count_label\" class=\"o_stat_text\">Finished</span>"
msgstr "<span name=\"members_done_count_label\" class=\"o_stat_text\">完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span name=\"total_slides_label\" class=\"o_stat_text\">Contents</span>"
msgstr "<span name=\"total_slides_label\" class=\"o_stat_text\">内容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> 小时</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<span>&amp;times;</span>"
msgstr "<span>&amp;times;</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>经验</span>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#, python-format
msgid "<strong>Thank you!</strong> Mail has been sent."
msgstr "<strong>谢谢！</strong> 邮件已发送。"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "从古至今的强大的森林"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "A 水果"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "与Harry Potted的一次小谈话"

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr "很多漂亮的文件：树木、木材、花园。一个参考的金矿。"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "一把铁锹"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid ""
"A slide is either filled with a document url or HTML content. Not both."
msgstr "幻灯片填上文档URL或HTML内容而不是两者。"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "一个勺子"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "诀窍总结：怎么做和知道什么。"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr "诀窍总结：怎么做知道什么。本课程关于园艺的所有基础知识。"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr "诀窍总结：什么是主要的树木类别，如何区分它们。"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "一张桌子"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "标签必须唯一!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "一颗蔬菜"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "API密钥"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "About us"
msgstr "关于我们"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "访问组"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "访问权限"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "需要行动"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Actions"
msgstr "操作"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "启用"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "添加备注"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "新增内容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "添加评论"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "添加章节"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid "Add a new lesson"
msgstr "添加新课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#, python-format
msgid "Add a section"
msgstr "添加章节"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add existing contacts..."
msgstr "添加现有联系人..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid ""
"Add quizzes at the end of your lessons to evaluate what your students "
"understood."
msgstr "在课程结束时添加测验，以评估学生的理解。"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "高级"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "所有课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "All completed classes and earned karma will be lost."
msgstr "您将失去所有已完成的课程和赚取的经验。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "All members of the channel."
msgstr "频道的全部成员"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "所有问题都必须回答！"

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "你需要知道的关于家具创作的所有信息。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "允许预览"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Rating"
msgstr "允许评级"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "允许给课程评分"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Allow review on Course"
msgstr "允许对课程进行评论"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "允许说明"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Already installing \"%s\"."
msgstr "已经安装 \"%s\"."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Amazing!"
msgstr "了不起!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "还有香蕉"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "答案"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Answer for a slide question"
msgstr "回答幻灯片问题"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__category
msgid "Appears in"
msgstr "出现在"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
msgid ""
"Applied directly as ACLs. Allow to hide channels and their content for non "
"members."
msgstr "直接应用为ACL。 允许隐藏非成员的频道及其内容。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive"
msgstr "存档"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive Slide"
msgstr "存档幻灯片"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "已归档"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to archive this slide ?"
msgstr "您确定要存档此幻灯片吗？"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attachment"
msgstr "附件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "附件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "尝试平均"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "尝试计数"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Attendees"
msgstr "参加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_done_count
msgid "Attendees Done Count"
msgstr "参加者完成位数"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "Attendees count"
msgstr "参加者数量"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Attendees of %s"
msgstr "与会者的 %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__author_id
msgid "Author"
msgstr "作者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "自动组队"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "基础的"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "家具创作的基础知识"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "园艺基础"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "可评论的"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "可以发布"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "可以评论"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "可以上传"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "可以投票"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "卡朋特"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "一个很棒的主标题"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "类别"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "类别"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "认证"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
msgid "Certifications"
msgstr "认证"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.challenge.line,name:website_slides.badge_data_certification_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "认证知识"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Channel"
msgstr "频道"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "频道/合作伙伴（会员）"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "频道邀请向导"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel Member"
msgstr "频道成员"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "频道类型"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__completed
msgid "Channel validated, even if slides / lessons are added once done."
msgstr "即使已完成幻灯片/课程，也会验证频道。"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course tags"
msgstr "频道/课程标签"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "渠道"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "小费表"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check answers"
msgstr "检查答案"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check your answers"
msgstr "检查一下你的答案"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a Cover Image"
msgstr "选择封面图片"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a PDF or an Image"
msgstr "选择PDF或图像"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood !"
msgstr "选择你的木材 !"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "清除筛选"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr "单击右上角的“新建”以编写您的第一个课程。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz
#, python-format
msgid "Close"
msgstr "关闭"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
msgid "Color Index"
msgstr "颜色索引"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "色彩"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "注释"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "附言"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.challenge.line,name:website_slides.badge_data_karma_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "社区英雄"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "公司课程计数"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_3_0
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "比较木材品种的硬度"

#. module: website_slides
#: model:gamification.badge,description:website_slides.badge_data_course
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
msgid "Complete a course"
msgstr "完成的课程"

#. module: website_slides
#: model:gamification.badge,description:website_slides.badge_data_profile
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
msgid "Complete your profile"
msgstr "填写个人资料"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
msgid "Completed"
msgstr "已完成"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "完成课程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "完成"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "完成时间"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "撰写邮件"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
msgid "Condition to enroll: everyone, on invite, on payment (sale bridge)."
msgstr "注册条件：每人，邀请，付款（销售桥）。"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "配置"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "恭喜你，你已经达到了最近的排名！"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Contact"
msgstr "联系人"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Contact all the members of a course via mass mailing"
msgstr "通过群发邮件联系课程的所有成员"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Contact the website administrator"
msgstr "联系网站管理员"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Contact us"
msgstr "联系我们"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Contact website administrator."
msgstr "联系网站管理员"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__datas
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "内容"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Content Preview"
msgstr "内容预览"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "内容测验问题"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "内容标签"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "内容标题"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "内容"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Continue"
msgstr "继续"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Copy Link"
msgstr "复制链接"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available.\n"
"Here is the received response: %s"
msgstr ""
"无法从网址获取数据。文档或访问权限不可用。\n"
"这是已收到的回复：%s"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""
"无法从网址获得数据。文档或者访问权限不存在:\n"
"%s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "课程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "课程计数"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "课程标签"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "课程标签组"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "课程标签组"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_tags
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "课程标签"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Course Title"
msgstr "课程名称"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "课程结束"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "课程排名"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
msgid "Course type"
msgstr "课程类型"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model:website.menu,name:website_slides.website_menu_slides
msgid "Courses"
msgstr "课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create"
msgstr "创建"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let the members help each others"
msgstr "创建一个社区，让成员互相帮助"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "Create a course"
msgstr "创建课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Create new %s '%s'"
msgstr "创建新 %s '%s'"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "创建者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "创建时间"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
msgid "Creation Date"
msgstr "创建日期"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of type 'Web Page'."
msgstr "“网页”类型幻灯片的自定义HTML内容。"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "DIY家具"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__category
msgid "Define the visibility of the challenge through menus"
msgstr "通过菜单定义挑战的可见性"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Delete"
msgstr "刪除"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "说明"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article ?"
msgstr "你读了整篇文章吗？"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Discard"
msgstr "丢弃"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Discover more"
msgstr "了解更多"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislikes"
msgstr "不喜欢"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "显示"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__sequence
msgid "Display order"
msgstr "现实顺序"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees ?"
msgstr "你用柠檬树做横梁吗？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams ?"
msgstr "你用梁子做柠檬吗？"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Do you really want to leave the course?"
msgstr "你真的想离开课程吗？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name ?"
msgstr "你认为哈利波特是个好名字吗？"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Do you want to install the \"%s\" app?"
msgstr "你想安装 \"%s\"应用程序吗？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly ?"
msgstr "你想正确回答吗？"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "文档"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_id
msgid "Document ID"
msgstr "文档ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "Document URL"
msgstr "文档网址"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
#, python-format
msgid "Documentation"
msgstr "文档"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
msgid "Documents"
msgstr "文档"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "适合养狗"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Don't have an account ?"
msgstr "没有账户"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "完成"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Done !"
msgstr "完成!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "完成计数"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "下载"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "下载内容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Duration"
msgstr "时长"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "编辑"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
msgid "Email"
msgstr "EMail"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid "Email template to send slide publication through email"
msgstr "透过EMail发送已发布的幻灯片之EMail模板"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_template_id
msgid "Email template used when sharing a slide"
msgstr "分享幻灯片时使用的邮件模板"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "嵌入代码"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embedcount_ids
msgid "Embed Count"
msgstr "嵌入个数"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr "嵌入你的网页"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "嵌入幻灯片观看个数"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "End course"
msgstr "结束课程"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "能源效率事实"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "注册信息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
msgid "Enroll Policy"
msgstr "注册政策"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Estimated slide completion time"
msgstr "预计幻灯片完成时间"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Evaluate and certificate your students."
msgstr "评估并认证您的学生。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate your students and certify them"
msgstr "评估您的学生并对其进行认证"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "练习"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "External Links"
msgstr "外部链接"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_link
msgid "External URL for a particular slide"
msgstr "外部链接对特定幻灯片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__link_ids
msgid "External URL for this slide"
msgstr "此幻灯片的外部链接"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "External sources"
msgstr "外部来源"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Failed to install \"%s\"."
msgstr "无法安装 \"%s\"."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Featured Content"
msgstr "特色内容"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr "文件太大。文件大小不能超过 25 MB"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "Filter &amp; order"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "完成课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First attempt"
msgstr "第一次尝试"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "First attempt reward"
msgstr "第一次尝试奖励"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "First slide"
msgstr "第一张幻灯片"

#. module: website_slides
#: code:addons/website_slides/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
#, python-format
msgid "Followed Courses"
msgstr "关注的课程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_channel_ids
msgid "Followers (Channels)"
msgstr "关注者(频道)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "论坛"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth and more attempt"
msgstr "第四次和更多的尝试"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Free preview"
msgstr "免费预览"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__email_from
msgid "From"
msgstr "来自"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "从一块木头到一个功能齐全的家具，一步步来。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Fullscreen"
msgstr "全屏"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "家具设计师"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "家具技术规格"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "游戏化挑战"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "园丁"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "园艺:诀窍"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Generate revenues thanks to your courses"
msgstr "通过你的课程创造收入"

#. module: website_slides
#: model:gamification.badge,description:website_slides.badge_data_certification
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
msgid "Get a certification"
msgstr "获得认证"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.challenge.line,name:website_slides.badge_data_register_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "开始"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Go Back"
msgstr "反回"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_footer
msgid "Go meet them"
msgstr "去满足他们"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "谷歌文档密钥"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Google Drive API 密钥"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "图的内容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "群组"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "分组"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr "允许用户组在文档课程上发布内容。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "组序列"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "HTML内容"

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on !"
msgstr "握手！"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr "以下是如何获得你所品尝过的最甜的草莓的方法!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "首页"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "家庭园艺"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "如何种植和收获最好的草莓 - 基础知识"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr "如何种植和收获最好的草莓｜园艺技巧和窍门"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How to create your own piece of furniture"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "如何找到优质木材"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted tree"
msgstr "如何种植一棵盆栽树"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr "如何通过在悬挂的塑料瓶中植树来进行墙面装饰。"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "如何做"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ID"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"If checked it allows members to either:\n"
" * like content and post comments on documentation course;\n"
" * post comment and review on training course;"
msgstr ""
"如果勾选此项，成员可以选择::\n"
" * 讚好文档课程的内容和评论;\n"
" * 发表评论和注解培训课程;"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr "如果你正在寻找技术规格，请看看这个文件。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
msgid "Image"
msgstr "图像"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "图像 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "图像128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "图像 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "图像 512"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Image File"
msgstr "图象档案"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__infographic
msgid "Infographic"
msgstr "信息图"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "信息图"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Install"
msgstr "安装"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Installing \"%s\"."
msgstr "安装 \"%s\"."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "有趣的事实"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting Tree Facts"
msgstr "有趣的树木事实"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting informations about home gardening. Keep it close !"
msgstr ""

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "中级"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"服务器内部错误，请稍后再尝试或者联系管理员。\n"
"这里是错误信息：%s"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Introduction"
msgstr "介绍"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr "错误的文件类型。请选择pfd文件或者图像"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "邀请"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed
msgid "Is Completed"
msgstr "已完成"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Member"
msgstr "是会员"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "已发布"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "是一个类别"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "是正确的答案"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr "吉姆和托德为克尼希特苗圃和景观设计公司的一位客户种植一棵盆栽树。由业主Leif Knecht讲述。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/channel_management.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "Join Course"
msgstr "加入课程"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "只是一些基本的能源效率事实。"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "只是一些基本的有趣的树木事实。"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "只是一些基本的树木信息图。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma Rules"
msgstr "贡献值规则"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr "贡献值需要在本课程的幻灯片上添加评论"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "贡献值需要在课程上添加评论"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr "贡献值需要讚好/負評这门课程的幻灯片。"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.challenge.line,name:website_slides.badge_data_profile_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "认识你自己"

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"根据你的应用，知道使用哪种木材是很重要的。在本课程中，你\n"
"将学习木材特性的基本知识。"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr "了解木材特性是一项要求，以便知道在特定情况下使用哪种木材。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_question____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag____last_update
msgid "Last Modified on"
msgstr "最后更改日"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "最近更新"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "更新时间"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Last slide"
msgstr "最后一个幻灯片"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Published"
msgstr "最新发布"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "最新成就"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "排行榜"

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr "学习如何照顾你喜爱的树木。了解何时种植，如何管理盆栽树木，..."

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn of to identify quality wood in order to create solid furnitures."
msgstr ""

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening !"
msgstr "学习园艺的基本知识 !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Leave the course"
msgstr "离开课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "课程导航"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_slide_vote
msgid "Lesson voted"
msgstr "课程投票"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "课程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Likes"
msgstr "喜爱"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__link
msgid "Link"
msgstr "链接"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "邮件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_main_attachment_id
msgid "Main Attachment"
msgstr "附件"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "主要树种分类"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_footer
msgid "Meet The Heros Who Transformed Their Company!"
msgstr "认识改造公司的英雄们！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Member"
msgstr "会员"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "会员"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Members Information"
msgstr "会员信息"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Members Only"
msgstr "成员限定"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "成员的观点"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "这些组的成员将自动添加为频道成员。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "菜单项"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Menu entry"
msgstr "菜单项"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "说明注册过程的消息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "消息"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "方法"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "强大的胡萝卜"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr "伟大的森林不是在几个星期内出现的。了解时间是如何使我们的森林变得强大和神秘的。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__mime_type
msgid "Mime-type"
msgstr "类型"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Minutes"
msgstr "分钟"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "流动子导航"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "更多信息"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "浏览最多的"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "得到最多投票的"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "最受欢迎的课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "我的课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "我的课程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Name"
msgstr "名称"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "导航"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New ${object.slide_type} published on ${object.channel_id.name}"
msgstr "在${object.channel_id.name}发布的新的${object.slide_type}"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "New Certification"
msgstr "新认证"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Email"
msgstr "新内容电子邮件"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "New Course"
msgstr "新课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "最新"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "最新课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Next"
msgstr "下一页"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "下一个排名："

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Next slide"
msgstr "下一幻灯片"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
msgid "No"
msgstr "否"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "尚未创建课程。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "No achievements currently :("
msgstr "目前没有成就:("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed course yet"
msgstr "尚未完成课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "找不到使用您的搜索内容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "找不到符合您搜索条件的课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "找不到符合您搜索条件的课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No followed course yet"
msgstr "尚未關注课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "目前没有排行榜:("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "没有介绍。"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Not enough karma to comment"
msgstr "没有足够的贡献值進行评论"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Not enough karma to review"
msgstr "没有足够的贡献值進行审查"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "动作个数"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "文档数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Infographics"
msgstr "信息图数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_presentation
msgid "Number of Presentations"
msgstr "介绍数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "提问的数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "视频数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_webpage
msgid "Number of Webpages"
msgstr "网页数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "评论数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要操作消息数量"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "递送错误消息数量"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "问题数量"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Odoo • Image and Text"
msgstr "Odoo • 图像和文字"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "应邀"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "选项"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "业务伙伴"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"People already took this quiz. To keep course progression it should not be "
"deleted."
msgstr "人们已经参加了这个测验。为了保持课程进度，不应删除它。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Pick a course"
msgstr "选择一个课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please"
msgstr "请"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to join this course"
msgstr "请 <a href=\"/web/login?redirect=%s\">登录</a> 参加此课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to vote this lesson"
msgstr "请 <a href=\"/web/login?redirect=%s\">登录</a> 投票这一课"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to join "
"this course"
msgstr "请 <a href=\"/web/signup?redirect=%s\">建账户</a> 加入此课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to vote "
"this lesson"
msgstr "请 <a href=\"/web/signup?redirect=%s\">创建账户</a> 投票这一课"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr "请输入有效的YouTube或谷歌文档的URL"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr "请输入有效的YouTube或谷歌文档网址"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "请填写这一栏"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.challenge.line,name:website_slides.badge_data_course_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "超级用户"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__presentation
#, python-format
msgid "Presentation"
msgstr "演示"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "展示已发布"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_presentation
msgid "Presentations"
msgstr "展示"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Previous slide"
msgstr "前一幻灯片"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Private Course"
msgstr "私人课程"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Public"
msgstr "公共"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "公众浏览"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Publish"
msgstr "发布"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "发布日期"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "已发布"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "发布日期"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr "出版仅限于负责培训课程或出版商组成员的文档课程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
msgid "Question"
msgstr "疑问"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" can only have one good answer"
msgstr "问题 \"%s\" 只能有一个好的答案"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" has no valid answer, please set one"
msgstr "问题 \"%s\" 没有有效的答案，请设置一个"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must at least have one good answer"
msgstr "问题 \"%s\" 必须至少有一个好的答案"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "问题名称"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "问题"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#, python-format
msgid "Quiz"
msgstr "测验"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "测试尝试计数"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Quizzes"
msgstr "测验"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Rank Course"
msgstr "课程排名"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Rating"
msgstr "点评"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
msgid "Rating Average"
msgstr "平均评级"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "平均评分（星级）"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "最新反馈评级"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "最新图像评级"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "最新值评级"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "点评数"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Rating of %s"
msgstr "等级 %s"

#. module: website_slides
#: model:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "达到2000经验值"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "达到新的记录"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
msgid "Read 2000 XP"
msgstr "达到2000经验值"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "点评的理由"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "收件人"

#. module: website_slides
#: model:gamification.badge,description:website_slides.badge_data_register
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
msgid "Register to the platform"
msgstr "注册到平台"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "相关的"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Remove"
msgstr "移除"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "报表"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
msgid "Responsible"
msgstr "负责人"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr "限制发布到本网站。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Retry"
msgstr "重试"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.private_profile
msgid "Return to the course."
msgstr "返回课程。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#, python-format
msgid "Review"
msgstr "审查"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_reviews
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "评论"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward for every attempt after the third try"
msgstr "第三次尝试后，对每一次尝试进行奖励"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Rewards"
msgstr "奖励"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO优化"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Save"
msgstr "保存"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Save as Draft"
msgstr "存为草稿"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search"
msgstr "搜索"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr "搜索内容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "搜索课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "搜索的内容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second attempt"
msgstr "第二次尝试"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Second attempt reward"
msgstr "第二次尝试奖励"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#, python-format
msgid "Section"
msgstr "章节"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Section Subtitle"
msgstr "章节子标题"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Section name"
msgstr "章节名称"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "安全令牌"

#. module: website_slides
#: code:addons/website_slides/models/res_users.py:0
#, python-format
msgid "See our eLearning"
msgstr "查看我们的电子学习"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr "选择页面开始"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "在电子商务销售"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "发送"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Send Email"
msgstr "发送EMail"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
msgid "Sequence"
msgstr "序号"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Set Done"
msgstr "设置完成"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "设置"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Share"
msgstr "共享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "分享频道"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
#, python-format
msgid "Share Link"
msgstr "分享链接"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_template_id
msgid "Share Template"
msgstr "分享模板"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Share by mail"
msgstr "通过邮件分享"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
#, python-format
msgid "Share on Facebook"
msgstr "分享至 Facebook"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
#, python-format
msgid "Share on LinkedIn"
msgstr "分享至 LinkedIn"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
#, python-format
msgid "Share on Social Networks"
msgstr "分享到社交网络"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
#, python-format
msgid "Share on Twitter"
msgstr "分享至 Twitter"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
msgid "Short Description"
msgstr "简要说明"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge !"
msgstr "展示你新掌握的知识 !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign Up !"
msgstr "注册 ！"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in"
msgstr "登录"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in and join the course to take the quiz!"
msgstr "登录并加入课程即可参加测验！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here. Time to start"
" a course."
msgstr "熟练并产生影响！ 你的商业生涯从这里开始。 是时候开始上课了。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
msgid "Slide"
msgstr "幻灯片"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "幻灯片/合作伙伴装饰为 多对多m2m"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
msgid "Slide Channel"
msgstr "幻灯片频道"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "幻灯片标签"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "幻灯片用户数据"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
msgid "Slide channel"
msgstr "幻灯片频道"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#, python-format
msgid "Slide image"
msgstr "幻灯片图片"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr "提交所有好答案时，必须将带问题的幻灯片标记为已完成"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
msgid "Slides"
msgstr "幻灯片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "幻灯片和类别"

#. module: website_slides
#: model:slide.channel,description:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "这么多惊人的认证。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "排序"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/channel_management.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "Start Course"
msgstr "开始课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/channel_management.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "Start Course Channel"
msgstr "开始课程频道"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Start with the customer – find out what they want and give it to them."
msgstr "从客户开始 - 找出他们想要的东西并将其给他们。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today !"
msgstr "立即开始您的在线课程！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Statistics"
msgstr "统计信息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "主题"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "主题..."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Subscribe"
msgstr "订阅"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Subscribe to get notified when a new content is added."
msgstr "订阅以在添加新内容时收到通知。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "订阅者信息"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "当前登录用户的订户信息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "订阅者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "订阅者信息"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Succeed and gain karma"
msgstr "成功并获得贡献值"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
msgid "Tag"
msgstr "标签"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#, python-format
msgid "Tags"
msgstr "标签"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "标签..."

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "照顾树木"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "测试"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "测试你的知识"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_5_3
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge !"
msgstr "测试你的知识 !"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr "何人都可以访问该课程：用户无需加入频道即可访问课程内容。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr "将根据文档网址和属性（例如演示文稿的高度和宽度以及文档）自动设置文档类型。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__completion_time
msgid "The estimated completion time for this slide"
msgstr "此幻灯片的估计完成时间"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external sign up in configuration."
msgstr "以下收件人没有用户账户：%s。 您应该为它们创建用户账户或允许在配置中进行外部注册。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__channel_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "通过网站访问文档的完整网址。"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "理论"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "There are no quizzes"
msgstr "没有测验"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel_report
msgid "There are no ratings for these courses at the moment"
msgstr "这些课程目前还没有评级"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "验证此测试时出错。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "第三方网站网址"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third attempt"
msgstr "第三次尝试"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Third attempt reward"
msgstr "第三次尝试奖励"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "This course is private."
msgstr "这门课程是不公开的。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "这个测验已经做完了, 重新开始是不可能的。"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This video already exists in this channel on the following slide: %s"
msgstr "此视频已存在于以下幻灯片中的此频道中： %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__name
#, python-format
msgid "Title"
msgstr "标题"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "切换导航"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "工具"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "工具和方法"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "完成本课程你需要的工具。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Total"
msgstr "合计"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "所有幻灯片"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Total Views"
msgstr "总浏览"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "培训"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Training (with progress)"
msgstr "培训（进度）"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "Tree Infographic"
msgstr "树木信息图"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "Tree planting in hanging bottles on wall"
msgstr "在墙上的吊瓶中植树"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "树"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "树木、木材和花园"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
#, python-format
msgid "Type"
msgstr "类型"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "无法发布邮件，请配置发件人的EMail地址。"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#, python-format
msgid "Uncategorized"
msgstr "未归类"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "令人难忘的工具"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Unknown document"
msgstr "未知文档"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Unknown error"
msgstr "未知错误"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Unpublished"
msgstr "未发布"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息计数器"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Unsubscribe"
msgstr "退订"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Upload"
msgstr "上传"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "上传组"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Upload Image"
msgstr "上传图像"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Upload Presentation"
msgstr "上传演示"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Upload a document"
msgstr "上传文档"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "上传者"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading Image..."
msgstr "上传图像中..."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading document ..."
msgstr "上传文档中..."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Use template"
msgstr "使用模版"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "用于分类和过滤显示的频道/课程"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "用于装饰看板"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "用户投票"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "Users"
msgstr "用户"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__video
#, python-format
msgid "Video"
msgstr "视频"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "视频"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "查看"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "查看全部"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "查看课程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "View fullscreen"
msgstr "查看全屏"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Views"
msgstr "视图"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "查看 •"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
msgid "Visibility"
msgstr "可见性"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "在当前网站显示"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "访问"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "投票"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "投票"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Votes and comments are disabled for this course"
msgstr "本课程禁止投票和评论"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "等待验证"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Watch Time"
msgstr "观看时间"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "观察大师的工作情况"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say !"
msgstr "我们和Harry Potted聊了一会儿，他肯定有有趣的事情要讲!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__webpage
#, python-format
msgid "Web Page"
msgstr "Web页"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_webpage
msgid "Webpages"
msgstr "网页"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "网站"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__category__slides
msgid "Website / Slides"
msgstr "网站/幻灯片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "网站网址"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "网站原说明"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "网站meta关键词"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "网站标题meta元素"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "网站opengraph图像"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry ?"
msgstr "草莓是什么？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants ?"
msgstr "为植物挖洞的最佳工具是什么？"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again ?"
msgstr "问题是什么来着？"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "木头"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "用蒸汽箱进行木材折弯"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "木材特性"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "与木材打交道"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Write here a short description of your first course"
msgstr "在这里写下你第一个课程的简短描述"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr "写一或两段话来描述你的产品或服务。<br>要取得成功，你的内容需要对你的读者有用。</br>"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr "写一到两段话，描述你的产品、服务或一个特定的功能。<br>要取得成功，你的内容需要对你的读者有用。</br>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "XP"
msgstr "经验值"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Yes"
msgstr "是"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "You can not upload password protected file."
msgstr "你无法上传密码保护的文件。"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr "如果您不是其成员，则无法将幻灯片标记为已完成。"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr "如果您不是其成员之一，则无法将幻灯片标记为已查看。"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members."
msgstr "如果您不是其成员之一，则无法将幻灯片测验标记为已完成。"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot upload on this channel."
msgstr "您无法在此频道上传。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have access to this lesson"
msgstr "您无权访问本课程"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have enough karma to vote"
msgstr "你没有足够的贡献值去投票"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You gained"
msgstr "你获得了"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "You have already joined this channel"
msgstr "您已加入此频道"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You have already voted for this lesson"
msgstr "您已经投票给这一课"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to join ${object.channel_id.name}"
msgstr "您已被邀请加入 ${object.channel_id.name}"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "You must be logged to submit the quiz."
msgstr "您必须登录才能提交测验。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You must be member of this course to vote"
msgstr "您必须是本课程的成员才能投票"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "你不会相信这些关于胡萝卜的事实。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "You're enrolled"
msgstr "你已经注册了"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "你的等级"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "你的角色"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Link"
msgstr "Youtube链接"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Video URL"
msgstr "YouTube视频网址"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_id
msgid "Youtube or Google Document ID"
msgstr "YouTube或谷歌文档ID"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "Youtube or Google Document URL"
msgstr "YouTube或谷歌文档的网址"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "达到"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "bg-200"
msgstr "bg-200"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "浏览路径"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "by email."
msgstr "通过电子邮件。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. How to grow your business with Odoo?"
msgstr "例如: 如何通过Odoo拓展业务？"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr "例如: 在本视频中，我们将向您介绍Odoo如何帮助您拓展业务。 最后，我们将为测试您的知识。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. https://www.odoo.com"
msgstr "例如：https://www.odoo.com"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "在线学习"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "电子教学课程"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "电子教学课程概览"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "login"
msgstr "登录"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "mr-1"
msgstr "mr-1"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "reviews"
msgstr "评论"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "步骤"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "to enroll."
msgstr "报名参加。"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "to share this"
msgstr "分享这个"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "经验值"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ml-1\">Uncategorized</span>"
msgstr "└<span class=\"ml-1\">Uncategorized</span>"
