# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition_stock
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:21+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON><PERSON>, 2020\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__move_dest_id
msgid "Downstream Move"
msgstr "Downstream bevægelse"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Minimum lager regel"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__picking_type_id
msgid "Operation Type"
msgstr "Operation type"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_order
msgid "Purchase Order"
msgstr "Indkøbsordre"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Indkøbs rekvisition"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr "Indkøbsrekvisitions linie"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__requisition_line_ids
msgid "Requisition Line"
msgstr "Indkøbs linje"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytning"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Lager regel"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__warehouse_id
msgid "Warehouse"
msgstr "Lagerstyring"
