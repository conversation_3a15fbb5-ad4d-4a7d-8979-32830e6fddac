# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# Anna, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 09:01+0000\n"
"PO-Revision-Date: 2019-12-05 12:40+0000\n"
"Last-Translator: Anna, 2023\n"
"Language-Team: Estonian (https://www.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr "The lock date for advisors is irreversible and can't be removed."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""
"The new lock date for advisors must be set after the previous lock date."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Uus maksu lukustamise kuupäev peaks olema määratud peale eelmist lukustamise"
" kuupäeva. "

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "Lukustamise kuupäev on pöördumatu ja seda ei saa eemaldada."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"Te ei saa määrata nõuandjatele rangemaid tingimusi kui kasutajatele. Palun "
"veenduge, et nõuandjatele on lukustamise kuupäev seatud varasemaks kui "
"kasutajatele. "

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" tax lock date is not set after the last day of the previous month."
msgstr ""
