# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 09:01+0000\n"
"PO-Revision-Date: 2019-12-05 12:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for advisors is irreversible and can't be removed."
msgstr ""
"Tanggal kunci untuk penasihat tidak dapat dibatalkan dan tidak dapat "
"dihapus."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"The new lock date for advisors must be set after the previous lock date."
msgstr ""
"Tanggal kunci baru untuk penasihat harus ditetapkan setelah tanggal kunci "
"sebelumnya."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr ""

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot define stricter conditions on advisors than on users. Please make"
" sure that the lock date on advisor is set before the lock date for users."
msgstr ""
"Anda tidak dapat menentukan kondisi yang lebih ketat pada penasihat daripada"
" pada pengguna. Pastikan tanggal kunci pada penasihat disetel sebelum "
"tanggal kunci untuk pengguna."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" lock date for advisors is not set after the last day of the previous month."
msgstr ""
"Anda tidak dapat mengunci periode yang belum selesai. Harap pastikan bahwa "
"tanggal kunci untuk penasehat tidak ditetapkan setelah hari terakhir bulan "
"sebelumnya."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that is not finished yet. Please make sure that the"
" tax lock date is not set after the last day of the previous month."
msgstr ""
