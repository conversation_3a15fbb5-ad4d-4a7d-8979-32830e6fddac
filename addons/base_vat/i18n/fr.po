# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: Cécile Collart <<EMAIL>>, 2019\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Les valeurs définies ici sont"
" spécifiques à l'entreprise.\" aria-label=\"Les valeurs définies ici sont "
"spécifiques à l'entreprise.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
msgid "<span class=\"o_vat_label\">VAT</span>"
msgstr "<span class=\"o_vat_label\">TVA</span>"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""
"Si cette case est cochée, vous ne pourrez pas enregistrer un contact si son "
"numéro de TVA ne peut pas être vérifié par le service européen VIES."

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:188
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] does not seem to be valid. \n"
"Note: the expected format is %s"
msgstr ""
"Le numéro de TVA [%s] pour le partenaire [%s] semble invalide.\n"
"Remarque : le format attendu est %s"

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:187
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] either failed the VIES VAT validation "
"check or did not respect the expected format %s."
msgstr ""
"Le numéro de TVA [%s] pour le partenaire [%s]  n'est pas valide d'après "
"VIES, ou bien ne respecte pas le format attendu %s."

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.company_form_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_short_form
msgid "VAT"
msgstr "TVA"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Vérifier les numéros TVA"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Vérifier les numéros TVA en utilisant le service européen VIES"
