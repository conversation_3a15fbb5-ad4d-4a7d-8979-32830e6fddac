# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# UAB "Draugi<PERSON><PERSON> sprendimai" <<EMAIL>>, 2019
# <PERSON>as <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>lvi<PERSON> <<EMAIL>>, 2019
# Audr<PERSON> Palenskis <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# Linas Vers<PERSON> <<EMAIL>>, 2019
# A<PERSON>as Vaitekunas <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 12:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: Jonas Zinkevicius <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "${object.event_id.name}: ${object.get_date_range_str()}"
msgstr "${object.event_id.name}: ${object.get_date_range_str()}"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: event
#: model:ir.actions.report,print_report_name:event.report_event_registration_badge
msgid ""
"'Registration Event - %s' % (object.name or 'Attendee').replace('/','')"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "( <i class=\"fa fa-clock-o\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid ""
"<b><i class=\"fa fa-clock-o\"/>\n"
"                                        To</b>"
msgstr ""
"<b><i class=\"fa fa-clock-o\"/>\n"
"                                        Į</b>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<div>\n"
"    Dear ${object.name},<br/>\n"
"    Thank you for your inquiry.<br/>\n"
"    Here is your badge for the event ${object.event_id.name}.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,<br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>"
msgstr ""
"<div>\n"
"    Gerb. ${object.name},<br/>\n"
"    Dėkojame jums už užklausą.<br/>\n"
"    Štai jūsų ženklelis renginiui ${object.event_id.name}.<br/>\n"
"    Jei turite klausimų, praneškite mums.\n"
"    <br/><br/>\n"
"    Ačiū,<br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle mr-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-level-down\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Attended the Event\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel Registration\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "<i>to</i>"
msgstr "<i>iki</i>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span attrs=\"{'invisible': [('seats_availability', '=', 'unlimited')]}\" class=\"oe_read_only\">\n"
"                                        to\n"
"                                    </span>"
msgstr ""
"<span attrs=\"{'invisible': [('seats_availability', '=', 'unlimited')]}\" class=\"oe_read_only\">\n"
"                                        į\n"
"                                    </span>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge badge-secondary o_wevent_badge float-"
"right\">SPEAKER</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid ""
"<span class=\"o_form_label\">Warning: This Event has not reached its Minimum"
" Registration Limit. Are you sure you want to confirm it?</span>"
msgstr ""
"<span class=\"o_form_label\">Įspėjimas: šis renginys nepasiekė savo "
"minimalaus registracijos limito. Ar tikrai norite jį patvirtinti?</span>"

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"% set date_begin = format_datetime(object.event_id.date_begin, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"% set date_end = format_datetime(object.event_id.date_end, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"${'/logo.png?company=%s' % object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${'%s' % object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello ${object.name or ''},<br/>\n"
"                        We are happy to confirm your registration to the event\n"
"                        % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        for attendee ${object.name}.\n"
"                    </div>\n"
"                    % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a><br/>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </td>\n"
"                                <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <div>${object.event_id.address_id.name}</div>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <div>${object.event_id.address_id.street}</div>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <div>${object.event_id.address_id.street2}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <div>\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </div>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <div>${object.event_id.address_id.country_id.name}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.organizer_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                        <div>Please contact the organizer:</div>\n"
"                        <ul>\n"
"                            <li>${object.event_id.organizer_id.name}</li>\n"
"                            % if object.event_id.organizer_id.email\n"
"                                <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                            % endif\n"
"                            % if object.event_id.organizer_id.phone\n"
"                                <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                            % endif\n"
"                        </ul>\n"
"                    </div>\n"
"                </td></tr>\n"
"                % endif\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text=${object.event_id.name}&amp;dates=${date_begin}/${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                                <a href=\"/event/${slug(object.event_id)}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title=${object.event_id.name}&amp;in_loc=${location}&amp;st=${format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}&amp;et=${format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                                    <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.address_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                                <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"                % endif\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"% if object.company_id\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" href=\"${object.company_id.website}\" style=\"color: #875A7B;\">${object.company_id.name}</a>\n"
"        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"        <br/>\n"
"        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"        % endif\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"% endif\n"
"</table>\n"
"            "
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"% set date_begin = format_datetime(object.event_id.date_begin, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"% set date_end = format_datetime(object.event_id.date_end, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"${'/logo.png?company=%s' % object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${'%s' % object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello ${object.name},<br/>\n"
"                        We are excited to remind you that the event\n"
"                        % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        is starting <strong>${object.get_date_range_str()}</strong>.\n"
"                    </div>\n"
"                    % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a><br/>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </td>\n"
"                                <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <div>${object.event_id.address_id.name}</div>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <div>${object.event_id.address_id.street}</div>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <div>${object.event_id.address_id.street2}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <div>\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </div>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <div>${object.event_id.address_id.country_id.name}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.organizer_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                        <div>Please contact the organizer:</div>\n"
"                        <ul>\n"
"                            <li>${object.event_id.organizer_id.name}</li>\n"
"                            % if object.event_id.organizer_id.email\n"
"                                <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                            % endif\n"
"                            % if object.event_id.organizer_id.phone\n"
"                                <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                            % endif\n"
"                        </ul>\n"
"                    </div>\n"
"                </td></tr>\n"
"                % endif\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text=${object.event_id.name}&amp;dates=${date_begin}/${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                                <a href=\"/event/${slug(object.event_id)}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title=${object.event_id.name}&amp;in_loc=${location}&amp;st=${format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}&amp;et=${format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                                    <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.address_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                                <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"                % endif\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"% if object.company_id\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" href=\"${object.company_id.website}\" style=\"color: #875A7B;\">${object.company_id.name}</a>\n"
"        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"        <br/>\n"
"        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"        % endif\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"% endif\n"
"</table>\n"
"            "
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "Reikia veiksmo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
msgid "Active"
msgstr "Aktyvus"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "Veiklos"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos Išimties Dekoravimas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""
"Į savo svetainę pridėkite navigacijos meniu su planu, sekimu, pasiūlymo "
"forma ir pan."

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
msgid "Add a new attendee"
msgstr "Pridėti naują dalyvį"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Add a new event"
msgstr "Pridėti naują renginį"

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "Administratorius"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "Po kiekvienos registracijos"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "Po renginio"

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr "Įvykis, kurio paskelbimas buvo atšauktas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Archived"
msgstr "Archyvuotas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"Are you sure you want to cancel this event? All the linked attendees will be"
" cancelled as well."
msgstr ""
"Ar tikrai norite atšaukti šį renginį? Visi susieti dalyviai taip pat bus "
"atšaukti."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "Lankomumas"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "Dalyvavo"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "Dalyvavimo data"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Attended the Event"
msgstr "Dalyvavo renginyje"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Dalyvis"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
msgid "Attendee Name"
msgstr "Dalyvio vardas"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "Dalyviai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__auto_confirm
msgid "Autoconfirm Registrations"
msgstr "Automatiškai patvirtinamos registracijos"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__auto_confirm
msgid "Automatically Confirm Registrations"
msgstr "Automatiškai patvirtinti registracijas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_mail_schedule
msgid "Automatically Send Emails"
msgstr "Automatiškai siųsti el. laiškus"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
msgid "Available Seats"
msgstr "Laisvos vietos"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_back
msgid "Badge Back"
msgstr "Ženklelio galinė pusė"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_front
msgid "Badge Front"
msgstr "Ženklelio priekinė pusė"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_innerleft
msgid "Badge Inner Left"
msgstr "Ženklelio vidus kairėje"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_innerright
msgid "Badge Inner Right"
msgstr "Ženklelio vidus dešinėje"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_barcode
msgid "Barcode"
msgstr "Brūkšninis kodas"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "Prieš renginį"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr ""

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Cancel"
msgstr "Atšaukti"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Cancel Event"
msgstr "Atšaukti renginį"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel Registration"
msgstr "Atšaukti registraciją"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__cancel
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "Atšauktas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
msgid "Category"
msgstr "Kategorija"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "Komunikacija"

#. module: event
#: model_terms:event.event,description:event.event_2
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
msgid "Company"
msgstr "Įmonė"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "Compose Email"
msgstr "Rašyti el. laišką"

#. module: event
#: model:event.type,name:event.event_type_2
msgid "Conference"
msgstr "Konferencija"

#. module: event
#: model_terms:event.event,description:event.event_2
#: model:event.event,name:event.event_2
msgid "Conference for Architects"
msgstr "Konferencija architektams"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "Konfigūracija"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Confirm"
msgstr "Patvirtinti"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Confirm Anyway"
msgstr "Vis tiek patvirtinti"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Confirm Event"
msgstr "Patvirtinti renginį"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm Registration"
msgstr "Patvirtinti registraciją"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__confirm
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Confirmed"
msgstr "Patvirtinti"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Confirmed attendees"
msgstr "Patvirtinti dalyviai"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Confirmed events"
msgstr "Patvirtinti renginiai"

#. module: event
#: model:ir.model,name:event.model_res_partner
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
msgid "Contact"
msgstr "Kontaktai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "Valstybė"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__create_uid
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__create_date
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "Customer"
msgstr "Klientas"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "Customer Email"
msgstr "Kliento el. paštas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "Data"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "Dienos"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Delete"
msgstr "Trinti"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
msgid "Description"
msgstr "Aprašymas"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr "Dizaino mugė Los Andžele"

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__display_name
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "Atvaizdavimo tvarka"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__done
msgid "Done"
msgstr "Atlikta"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Dropdown menu"
msgstr "Išsiskleidžiantis meniu"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""
"Šios konferencijos metu mūsų komanda plačiai apžvelgs mūsų verslo programas."
" Sužinosite visus jų naudojimo privalumus."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__email
msgid "Email"
msgstr "El. paštas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__template_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_id
msgid "Email Template"
msgstr "El. laiško šablonas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "Pabaigos data"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr "Aptikta pabaigos data"

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "Renginys"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "Automatiniai renginio laiškai"

#. module: event
#: model:ir.actions.report,name:event.report_event_event_badge
msgid "Event Badge"
msgstr "Renginio ženklelis"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Categories"
msgstr "Renginio kategorijos"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Category"
msgstr "Renginio kategorija"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_confirm
#: model:ir.model,name:event.model_event_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Event Confirmation"
msgstr "Renginio patvirtinimas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "Renginio pabaigos data"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "Renginio informacija"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_logo
msgid "Event Logo"
msgstr "Renginio logotipas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "Renginio laiškų planuoklis"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "Renginio laiškų planuokliai"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "Renginio pavadinimas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "Renginio organizavimas"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "Renginio registracija"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"Event Scheduler for:\n"
"                              - Event: %s (%s)\n"
"                              - Scheduled: %s\n"
"                              - Template: %s (%s)\n"
"\n"
"                            Failed with error:\n"
"                              - %s\n"
"\n"
"                            You receive this email because you are:\n"
"                              - the organizer of the event,\n"
"                              - or the responsible of the event,\n"
"                              - or the last writer of the template."
msgstr ""
"Renginių planuoklis:\n"
"                              - Renginys: %s (%s)\n"
"                              - Suplanuotas: %s\n"
"                              - Šablonas: %s (%s)\n"
"\n"
"                            Nepavyko dėl klaidos:\n"
"                              - %s\n"
"\n"
"                            Gavote šį laišką, nes jūs esate\n"
"                              - renginio organizatorius,\n"
"                              - atsakingas už renginį,\n"
"                              - paskutinis redagavęs šabloną."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "Renginio pradžios data"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "Renginio tipas"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
#: model:ir.cron,cron_name:event.event_mail_scheduler
#: model:ir.cron,name:event.event_mail_scheduler
msgid "Event: Mail Scheduler"
msgstr "Renginys: pašto planuoklis"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
#: model:ir.ui.menu,name:event.event_event_menu_pivot_report
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "Renginiai"

#. module: event
#: model:ir.actions.act_window,name:event.event_event_action_pivot
msgid "Events Analysis"
msgstr "Renginio analizė"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "Renginių pašto planuokliai"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Events and registrations will automatically be confirmed\n"
"                                            upon creation, easing the flow for simple events."
msgstr ""
"Renginiai ir registracijos bus automatiškai patvirtintos\n"
"po sukūrimo, kad palengvintų paprastų įvykių procesus."

#. module: event
#: model:ir.model.fields,help:event.field_event_type__auto_confirm
msgid ""
"Events and registrations will automatically be confirmed upon creation, "
"easing the flow for simple events."
msgstr ""
"Renginiai ir registracijos bus automatiškai patvirtintos po sukūrimo, kad "
"palengvintų paprastų renginių procesus."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Events in New state"
msgstr "Renginiai naujojoje būsenoje"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Exhibition"
msgstr "Paroda"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr "Numatoma"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr "Planuojami dalyviai"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Finish Event"
msgstr "Pabaigti renginį"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_channel_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_channel_ids
msgid "Followers (Channels)"
msgstr "Sekėjai (kanalai)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "For any additional information, please contact us at"
msgstr "Jei norite papildomos informacijos, prašome su mumis susisiekti"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""
"Kiekvienam renginiui galite nustatyti rezervuojamu vietų maksimumą (dalyvių "
"kiekį), viršijus šį skaičių, registracijos nebus priimamos."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_min
msgid ""
"For each event you can define a minimum reserved seats (number of "
"attendees), if it does not reach the mentioned registrations the event can "
"not be confirmed (keep 0 to ignore this rule)"
msgstr ""
"Kiekvienam renginiui galite nustatyti rezervuojamu vietų minimumą (dalyvių "
"kiekį), jei šis registracijų skaičius nebus pasiektas, renginio nebus galima"
" patvirtinti (palikite \"0\", jei norite nepaisyti šios taisyklės)."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr "Pagrindinių programų funkcinis procesas;"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "Būsimos veiklos"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr ""

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr "Dalyvavę šioje konferencijoje turėtų galėti:"

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "Valandos"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__id
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
msgid "ID"
msgstr "ID"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_event__message_unread
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__state
msgid ""
"If event is created, the status is 'Draft'. If event is confirmed for the "
"particular dates the status is set to 'Confirmed'. If the event is over, the"
" status is set to 'Done'. If event is cancelled the status is set to "
"'Cancelled'."
msgstr ""
"Jei renginys yra sukurtas, jo būsena yra \"juodraštis\". Jei renginys yra "
"patvirtintas konkrečiomis datomis, jo būsena nustatoma kaip "
"\"patvirtintas\". Jei renginys baigiasi, jo būsena nustatoma kaip "
"\"Atliktas\". Jei renginys yra atšauktas, jo būsena yra nustatoma kaip "
"\"atšauktas\"."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"If you wish to make a presentation, please send your topic proposal as soon "
"as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany (dot) "
"com. The presentations should be, for example, a presentation of a community"
" module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English."
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "Nedelsiant"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "Intervalas"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "Įvadas, CRM, pardavimų valdymas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__default_registration_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""
"Kai pasirenkate šį renginį, tai parinks šią numatytąją maksimalią reikšmę"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__default_registration_min
msgid "It will select this default minimum value when you choose this event"
msgstr ""
"Kai pasirenkate šį renginį, tai parinks šią numatytąją minimalią reikšmę"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__color
msgid "Kanban Color Index"
msgstr "Kanban spalvos indeksas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm____last_update
#: model:ir.model.fields,field_description:event.field_event_event____last_update
#: model:ir.model.fields,field_description:event.field_event_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_type____last_update
#: model:ir.model.fields,field_description:event.field_event_type_mail____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__write_uid
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__write_date
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "Vėluojančios veiklos"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__seats_availability__limited
msgid "Limited"
msgstr "Ribotas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "Ribotos vietos"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Location"
msgstr "Vieta"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "Logo"
msgstr "Logotipas"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
msgid "Mail"
msgstr "Paštas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "Registracija paštu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "Pašto planas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "Pašto planuoklis"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "Pašto planuokliai"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Laiškų planavimas įvykių kategorijai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "Laiškas išsiųstas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_sent
msgid "Mail Sent on Event"
msgstr "Laiškas išsiųstas renginiui"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_main_attachment_id
#: model:ir.model.fields,field_description:event.field_event_registration__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage &amp; publish a schedule with tracks"
msgstr "Valdykite ir skelbkite planus su sekimu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_availability
msgid "Maximum Attendees"
msgstr "Maks. dalyvių"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
msgid "Maximum Attendees Number"
msgstr "Maksimalus dalyvių skaičius"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_registration_max
msgid "Maximum Registrations"
msgstr "Maks. registracijų"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid ""
"Maximum attendees number should be greater than minimum attendees number."
msgstr "Maksimalus dalyvių skaičius turėtų būti didesnis nei minimalus."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_min
msgid "Minimum Attendees"
msgstr "Min. dalyvių"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_registration_min
msgid "Minimum Registrations"
msgstr "Min. registracijų"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__mobile
msgid "Mobile"
msgstr "Mobilus"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "Mėnesiai"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Mano renginiai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "No more available seats."
msgstr "Daugiau galimų vietų nėra."

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "No more seats available for this event."
msgstr "Daugiau galimų vietų šiam renginiui nėra."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_expected
msgid "Number of Expected Attendees"
msgstr "Planuojamas dalyvių skaičius"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Participants"
msgstr "Dalyvių skaičius"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: event
#: model:ir.model.fields,help:event.field_res_partner__event_count
#: model:ir.model.fields,help:event.field_res_users__event_count
msgid "Number of events the partner has participated."
msgstr "Kiekis renginių, kuriuose dalyvavo partneris."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_unread_counter
#: model:ir.model.fields,help:event.field_event_registration__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "Tikslai"

#. module: event
#: model:event.type,name:event.event_type_data_online
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Online"
msgstr "Prisijungęs"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_online
#: model:ir.model.fields,field_description:event.field_event_type__is_online
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Online Event"
msgstr "Renginys internetu"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Online Events"
msgstr "Nuotoliniai renginiai"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr "Bilietai internetu"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Online events like webinars do not require a specific location\n"
"                                            and are hosted online."
msgstr ""
"Renginiai internetu, tokie kaip vebinarai, nereikalauja konkrečios\n"
" vietos ir vyksta internete."

#. module: event
#: model:ir.model.fields,help:event.field_event_type__is_online
msgid ""
"Online events like webinars do not require a specific location and are "
"hosted online."
msgstr ""
"Renginiai internetu, tokie kaip vebinarai, nereikalauja konkrečios vietos ir"
" vyksta internete."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
msgid "Organizer"
msgstr "Organizatorius"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "Dalyvis"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Partneris"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__phone
msgid "Phone"
msgstr "Telefonas"

#. module: event
#: model:event.type,name:event.event_type_data_physical
msgid "Physical Event"
msgstr "Fizinis renginys"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr "Pardavimo taškas (POS), įvadas į ataskaitų personalizavimą."

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "Programa"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr "Projekto valdymas, žmogiškieji ištekliai, kontraktų valdymas."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr "Pirkimas, Pardavimų ir pirkimų valdymas, Finansai."

#. module: event
#: model:ir.model.fields,help:event.field_event_registration__origin
msgid ""
"Reference of the document that created the registration, for example a sales"
" order"
msgstr ""
"Dokumento, kuris sukūrė registraciją, numeris, pavyzdžiui, pardavimo "
"užsakymas."

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Register with this event"
msgstr "Registruotis su šiuo renginiu"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr "Registracija"

#. module: event
#: model:ir.actions.report,name:event.report_event_registration_badge
msgid "Registration Badge"
msgstr "Registracijos ženklelis"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_open
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "Registracijos data"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "Registracijos ID"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Registracijos pašto planuoklis"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "Registracijos laiškai"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_questions
msgid "Registration Survey"
msgstr "Registracijos apklausa"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "Registracijos paštas"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "Ataskaitos"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Reserved Seats"
msgstr "Rezervuotos vietos"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Responsible"
msgstr "Atsakingas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS pristatymo klaida"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Scan badges to confirm attendances"
msgstr "Nuskenuoti ženklelius dalyvavimo patvirtinimui"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "Planavimas ir sekimas"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events efficiently:\n"
"                track registrations and participations, automate the confirmation emails, sell tickets, etc."
msgstr ""
"Planuokite ir organizuokite savo renginius efektyviai:\n"
"sekite registracijas ir dalyvavimą, automatizuokite patvirtinimo laiškus, parduokite bilietus ir t.t."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Scheduled Sent Mail"
msgstr "Suplanuotas išsiųstas laiškas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "Planuojamas laikas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "Parduokite bilietus savo svetainėje"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "Parduoti bilietus su pardavimo užsakymais"

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Seminar"
msgstr "Seminaras"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "Siųsti"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "Siųsti el. paštu"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__done
msgid "Sent"
msgstr "Išsiųsta"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__sequence
msgid "Sequence"
msgstr "Seka"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Set To Draft"
msgstr "Nustatyti kaip juodraštį"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr "Nustatyti kaip nepatvirtintą"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "Nustatymai"

#. module: event
#: model:event.type,name:event.event_type_3
msgid "Show"
msgstr "Rodyti"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Rodyti visus įrašus, kurių sekančio veiksmo data yra ankstesnė nei šiandiena"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__origin
msgid "Source Document"
msgstr "Šaltinio dokumentas"

#. module: event
#: model:event.type,name:event.event_type_5
msgid "Sport"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "Pradžios data"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr "Pradžios data nustatyta"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__state
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Būsena"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "The closing date cannot be earlier than the beginning date."
msgstr "Uždarymo data negali būti ankstesnė nei pradžios data."

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid ""
"There are already attendees who attended this event. Please reset it to "
"draft if you want to cancel this event."
msgstr ""
"Jau yra dalyvių, kurie dalyvauja šiame renginyje. Jei norite atšaukti šį "
"renginį, atstatykite jį į juodraštį."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__template_id
#: model:ir.model.fields,help:event.field_event_type_mail__template_id
msgid ""
"This field contains the template of the mail that will be automatically sent"
msgstr "Šiame lauke yra laiško, kuris bus automatiškai išsiųstas, šablonas."

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
msgid "Tickets"
msgstr "Bilietai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "Laiko juosta"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "Šiandienos veiklos"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "Suma"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "Sekimas ir planas"

#. module: event
#: model:event.type,name:event.event_type_4
msgid "Training"
msgstr "Mokymai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "Iššaukimas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr "Iššaukimas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__twitter_hashtag
#: model:ir.model.fields,field_description:event.field_event_type__default_hashtag
msgid "Twitter Hashtag"
msgstr "Twitter žyma"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__draft
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "Nepatvirtintas"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr "Nepatvirtintos vietų rezervacijos"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "Suprasti įvairius modulius;"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "Vienetas"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__seats_availability__unlimited
msgid "Unlimited"
msgstr "Neribotai"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "Artimiausi renginiai nuo šiandien"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr "Ateinantis/vykstantis"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_hashtag
msgid "Use Default Hashtag"
msgstr "Naudoti numatytąją žymą"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_timezone
msgid "Use Default Timezone"
msgstr "Naudoti numatytąją laiko zoną"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Vartotojas"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Visibility"
msgstr "Matomumas"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr "ĮSPĖJIMAS: Renginio planuoklio klaida renginiui: %s"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr "Sandėlio valdymas, gamyba (MRP) ir pardavimai, importas/exportas."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "Interneto svetainės žinutės"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "Svetainės komunikacijos istorija"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "Savaitės"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "You must wait the event confirmation before doing this action."
msgstr "Prieš atliekant šį veiksmą, turite sulaukti renginio patvirtinimo."

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "You must wait the event starting day before doing this action."
msgstr "Prieš atliekant šį veiksmą, turite sulaukti renginio pradžios dienos."

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for ${object.event_id.name}"
msgstr "Jūsų ženklelis, skirtas ${object.event_id.name}"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at ${object.event_id.name}"
msgstr "Jūsų registracija į ${object.event_id.name}"

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid "badge_of_${(object.event_id.name or '').replace('/','_')}"
msgstr "badge_of_${(object.event_id.name or '').replace('/','_')}"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<EMAIL>"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "in %d days"
msgstr "per %d dienų"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "next month"
msgstr "kitą mėnesį"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "next week"
msgstr "kitą savaitę"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "on "
msgstr " "

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "today"
msgstr "šiandien"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "tomorrow"
msgstr "rytoj"
