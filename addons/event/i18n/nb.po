# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 12:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: <PERSON> (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "${object.event_id.name}: ${object.get_date_range_str()}"
msgstr "${object.event_id.name}: ${object.get_date_range_str()}"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopi)"

#. module: event
#: model:ir.actions.report,print_report_name:event.report_event_registration_badge
msgid ""
"'Registration Event - %s' % (object.name or 'Attendee').replace('/','')"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "( <i class=\"fa fa-clock-o\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid ""
"<b><i class=\"fa fa-clock-o\"/>\n"
"                                        To</b>"
msgstr ""
"<b><i class=\"fa fa-clock-o\"/>\n"
"                                        Til</b>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<div>\n"
"    Dear ${object.name},<br/>\n"
"    Thank you for your inquiry.<br/>\n"
"    Here is your badge for the event ${object.event_id.name}.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,<br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle mr-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-level-down\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Attended the Event\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel Registration\"/>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "<i>to</i>"
msgstr "<i>til</i>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span attrs=\"{'invisible': [('seats_availability', '=', 'unlimited')]}\" class=\"oe_read_only\">\n"
"                                        to\n"
"                                    </span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge badge-secondary o_wevent_badge float-"
"right\">SPEAKER</span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid ""
"<span class=\"o_form_label\">Warning: This Event has not reached its Minimum"
" Registration Limit. Are you sure you want to confirm it?</span>"
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"% set date_begin = format_datetime(object.event_id.date_begin, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"% set date_end = format_datetime(object.event_id.date_end, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"${'/logo.png?company=%s' % object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${'%s' % object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello ${object.name or ''},<br/>\n"
"                        We are happy to confirm your registration to the event\n"
"                        % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        for attendee ${object.name}.\n"
"                    </div>\n"
"                    % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a><br/>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </td>\n"
"                                <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <div>${object.event_id.address_id.name}</div>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <div>${object.event_id.address_id.street}</div>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <div>${object.event_id.address_id.street2}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <div>\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </div>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <div>${object.event_id.address_id.country_id.name}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.organizer_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                        <div>Please contact the organizer:</div>\n"
"                        <ul>\n"
"                            <li>${object.event_id.organizer_id.name}</li>\n"
"                            % if object.event_id.organizer_id.email\n"
"                                <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                            % endif\n"
"                            % if object.event_id.organizer_id.phone\n"
"                                <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                            % endif\n"
"                        </ul>\n"
"                    </div>\n"
"                </td></tr>\n"
"                % endif\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text=${object.event_id.name}&amp;dates=${date_begin}/${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                                <a href=\"/event/${slug(object.event_id)}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title=${object.event_id.name}&amp;in_loc=${location}&amp;st=${format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}&amp;et=${format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                                    <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.address_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                                <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"                % endif\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"% if object.company_id\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" href=\"${object.company_id.website}\" style=\"color: #875A7B;\">${object.company_id.name}</a>\n"
"        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"        <br/>\n"
"        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"        % endif\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"% endif\n"
"</table>\n"
"            "
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"% set date_begin = format_datetime(object.event_id.date_begin, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"% set date_end = format_datetime(object.event_id.date_end, tz='UTC', dt_format=\"yyyyMMdd'T'HHmmss'Z'\")\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"${'/logo.png?company=%s' % object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${'%s' % object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello ${object.name},<br/>\n"
"                        We are excited to remind you that the event\n"
"                        % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        is starting <strong>${object.get_date_range_str()}</strong>.\n"
"                    </div>\n"
"                    % if 'website_published' in object.event_id and object.event_id.website_published:\n"
"                    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a><br/>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </td>\n"
"                                <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <div>${object.event_id.address_id.name}</div>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <div>${object.event_id.address_id.street}</div>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <div>${object.event_id.address_id.street2}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <div>\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </div>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <div>${object.event_id.address_id.country_id.name}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.organizer_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                        <div>Please contact the organizer:</div>\n"
"                        <ul>\n"
"                            <li>${object.event_id.organizer_id.name}</li>\n"
"                            % if object.event_id.organizer_id.email\n"
"                                <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                            % endif\n"
"                            % if object.event_id.organizer_id.phone\n"
"                                <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                            % endif\n"
"                        </ul>\n"
"                    </div>\n"
"                </td></tr>\n"
"                % endif\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text=${object.event_id.name}&amp;dates=${date_begin}/${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                                <a href=\"/event/${slug(object.event_id)}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title=${object.event_id.name}&amp;in_loc=${location}&amp;st=${format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}&amp;et=${format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                                    <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.address_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                                <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"                % endif\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"% if object.company_id\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" href=\"${object.company_id.website}\" style=\"color: #875A7B;\">${object.company_id.name}</a>\n"
"        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"        <br/>\n"
"        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"        % endif\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"% endif\n"
"</table>\n"
"            "
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevd"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
msgid "Active"
msgstr "Aktiv"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorering for Aktivitetsunntak"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "Aktivitetsstatus"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
msgid "Add a new attendee"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Add a new event"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "Administrator"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "Etter hver registrering"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "Etter arrangementet"

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Archived"
msgstr "Arkivert"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"Are you sure you want to cancel this event? All the linked attendees will be"
" cancelled as well."
msgstr ""
"Er du sikker på at du vil avlyse dette arrangementet? Det vil også "
"kansellere alle tilknyttede deltakere."

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "Oppmøte"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "Deltok"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "Deltatt dato"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Attended the Event"
msgstr "Deltok på arrangementet."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Deltaker"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
msgid "Attendee Name"
msgstr "Deltakernavn"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "Deltakere"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__auto_confirm
msgid "Autoconfirm Registrations"
msgstr "Godkjenn registreringer automatisk"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__auto_confirm
msgid "Automatically Confirm Registrations"
msgstr "Bekreft registreringer automatisk"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_mail_schedule
msgid "Automatically Send Emails"
msgstr "Send e-post automatisk"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
msgid "Available Seats"
msgstr "Tilgjengelige plasser"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_back
msgid "Badge Back"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_front
msgid "Badge Front"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_innerleft
msgid "Badge Inner Left"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_innerright
msgid "Badge Inner Right"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_barcode
msgid "Barcode"
msgstr "Strekkode"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "Før arrangementet"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr ""

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Cancel"
msgstr "Avbryt"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Cancel Event"
msgstr "Avlys arrangement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel Registration"
msgstr "Avbryt registrering"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__cancel
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "Kansellert"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
msgid "Category"
msgstr "Kategori"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "Kommunikasjon"

#. module: event
#: model_terms:event.event,description:event.event_2
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
msgid "Company"
msgstr "Firma"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "Compose Email"
msgstr "Skriv e-postmelding"

#. module: event
#: model:event.type,name:event.event_type_2
msgid "Conference"
msgstr "Konferanse"

#. module: event
#: model_terms:event.event,description:event.event_2
#: model:event.event,name:event.event_2
msgid "Conference for Architects"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr "Konferanser, workshops og opplæring vil organiseres i 6 rom:"

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurasjonsinnstillinger"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Confirm"
msgstr "Bekreft"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Confirm Anyway"
msgstr "Bekreft likevel"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Confirm Event"
msgstr "Bekreft arrangement"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm Registration"
msgstr "Bekreft registrering"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__confirm
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Confirmed"
msgstr "Bekreftet"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Confirmed attendees"
msgstr "Bekreftede deltakere"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Confirmed events"
msgstr "Bekreftede arrangementer"

#. module: event
#: model:ir.model,name:event.model_res_partner
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "Land"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__create_uid
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__create_date
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "Customer"
msgstr "Kunde"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "Customer Email"
msgstr "Kunde-e-post"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "Dato"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "Dager"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Delete"
msgstr "Slett"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
msgid "Description"
msgstr "Beskrivelse"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr ""

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__display_name
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "Visningsrekkefølge"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__done
msgid "Done"
msgstr "Fullført"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Dropdown menu"
msgstr "Nedtrekksmeny"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__email
msgid "Email"
msgstr "E-post"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__template_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_id
msgid "Email Template"
msgstr "E-postmal"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "Avslutningsdato"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr ""

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "Arrangement"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "Arrangement Automatisk Utsendelse"

#. module: event
#: model:ir.actions.report,name:event.report_event_event_badge
msgid "Event Badge"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Categories"
msgstr "Arrangementskategorier"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Category"
msgstr "Arrangementskategori"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_confirm
#: model:ir.model,name:event.model_event_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Event Confirmation"
msgstr "Arrangementsbekreftelse"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "Sluttdato for arrangementet"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "Arrangementsinformasjon"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_logo
msgid "Event Logo"
msgstr "Arrangementslogo"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "Arrangementsnavn"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "Arrangementsorganisering"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "Registrering til arrangement"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"Event Scheduler for:\n"
"                              - Event: %s (%s)\n"
"                              - Scheduled: %s\n"
"                              - Template: %s (%s)\n"
"\n"
"                            Failed with error:\n"
"                              - %s\n"
"\n"
"                            You receive this email because you are:\n"
"                              - the organizer of the event,\n"
"                              - or the responsible of the event,\n"
"                              - or the last writer of the template."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "Startdato for arrangement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "Arrangementstype"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
#: model:ir.cron,cron_name:event.event_mail_scheduler
#: model:ir.cron,name:event.event_mail_scheduler
msgid "Event: Mail Scheduler"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
#: model:ir.ui.menu,name:event.event_event_menu_pivot_report
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "Arrangementer"

#. module: event
#: model:ir.actions.act_window,name:event.event_event_action_pivot
msgid "Events Analysis"
msgstr "Arrangementsanalyse"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Events and registrations will automatically be confirmed\n"
"                                            upon creation, easing the flow for simple events."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__auto_confirm
msgid ""
"Events and registrations will automatically be confirmed upon creation, "
"easing the flow for simple events."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Events in New state"
msgstr "Hendelser i ny stat."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Exhibition"
msgstr "Utstilling"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr "Forventet"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr "Forventede deltakere"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Finish Event"
msgstr "Avslutt arrangement"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_channel_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_channel_ids
msgid "Followers (Channels)"
msgstr "Følgere (kanaler)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "For any additional information, please contact us at"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_min
msgid ""
"For each event you can define a minimum reserved seats (number of "
"attendees), if it does not reach the mentioned registrations the event can "
"not be confirmed (keep 0 to ignore this rule)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "Fremtidige aktiviteter"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr ""

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Grupper etter"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr "Etter å ha vært på denne konferansen, skal deltakerne kunne:"

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "Timer"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__id
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
msgid "ID"
msgstr "ID"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for å indikere aktivitetsunntak."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_event__message_unread
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis huket av, vil nye meldinger kreve din oppmerksomhet."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis huket av, har enkelte meldinger leveringsfeil."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__state
msgid ""
"If event is created, the status is 'Draft'. If event is confirmed for the "
"particular dates the status is set to 'Confirmed'. If the event is over, the"
" status is set to 'Done'. If event is cancelled the status is set to "
"'Cancelled'."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"If you wish to make a presentation, please send your topic proposal as soon "
"as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany (dot) "
"com. The presentations should be, for example, a presentation of a community"
" module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English."
msgstr ""

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "Umiddelbart"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "Intervall"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "Introduksjon, CRM, salgsadministrasjon"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__default_registration_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__default_registration_min
msgid "It will select this default minimum value when you choose this event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__color
msgid "Kanban Color Index"
msgstr "Fargeindeks for Kanban"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm____last_update
#: model:ir.model.fields,field_description:event.field_event_event____last_update
#: model:ir.model.fields,field_description:event.field_event_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_type____last_update
#: model:ir.model.fields,field_description:event.field_event_type_mail____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__write_uid
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__write_date
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "Forsinkede aktiviteter"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__seats_availability__limited
msgid "Limited"
msgstr "Begrenset"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "Begrenset antall plasser"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Location"
msgstr "Lokasjon"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "Logo"
msgstr "Logo"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
msgid "Mail"
msgstr "E-post"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "E-postregistrering"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "E-postplan"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "E-postplanlegger"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "E-postplanleggere"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Mail Planlegging på Arrangementkategori"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "E-post sendt"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_sent
msgid "Mail Sent on Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_main_attachment_id
#: model:ir.model.fields,field_description:event.field_event_registration__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hovedvedlegg"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage &amp; publish a schedule with tracks"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_availability
msgid "Maximum Attendees"
msgstr "Maksimalt antall deltakere"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
msgid "Maximum Attendees Number"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_registration_max
msgid "Maximum Registrations"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid ""
"Maximum attendees number should be greater than minimum attendees number."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "Melding for feil ved levering"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_min
msgid "Minimum Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_registration_min
msgid "Minimum Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__mobile
msgid "Mobile"
msgstr "Mobil"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "Måneder"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Arrangementene mine"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Neste aktivitetsfrist"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "Oppsummering av neste aktivitet"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "Neste aktivitetstype"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "No more available seats."
msgstr "Ingen ledige plasser igjen."

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "No more seats available for this event."
msgstr "Ingen flere plasser igjen på dette arrangementet."

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_expected
msgid "Number of Expected Attendees"
msgstr "Forventet antall deltakere"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Participants"
msgstr "Antall deltakere"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: event
#: model:ir.model.fields,help:event.field_res_partner__event_count
#: model:ir.model.fields,help:event.field_res_users__event_count
msgid "Number of events the partner has participated."
msgstr "Antall arrangementer partneren har deltatt på."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antall meldinger som krever handling"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_unread_counter
#: model:ir.model.fields,help:event.field_event_registration__message_unread_counter
msgid "Number of unread messages"
msgstr "Antall uleste meldinger"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "Mål"

#. module: event
#: model:event.type,name:event.event_type_data_online
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Online"
msgstr "På nett"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_online
#: model:ir.model.fields,field_description:event.field_event_type__is_online
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Online Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Online Events"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Online events like webinars do not require a specific location\n"
"                                            and are hosted online."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__is_online
msgid ""
"Online events like webinars do not require a specific location and are "
"hosted online."
msgstr ""
"Nettarrangementer som webinarer krever ikke et definert geografisk sted."

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
msgid "Organizer"
msgstr "Arrangør"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "Deltaker"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Partner"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__phone
msgid "Phone"
msgstr "Telefon"

#. module: event
#: model:event.type,name:event.event_type_data_physical
msgid "Physical Event"
msgstr "Fysisk arrangement"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "Program"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_registration__origin
msgid ""
"Reference of the document that created the registration, for example a sales"
" order"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Register with this event"
msgstr "Registrer deg på arrangementet"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr "Registrering"

#. module: event
#: model:ir.actions.report,name:event.report_event_registration_badge
msgid "Registration Badge"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_open
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "Registreringsdato"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "Registrerings-ID"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Påmelding Mail Planlegger"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "Registreringse-post"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_questions
msgid "Registration Survey"
msgstr "Registreringsundersøkelse"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "Registrerings-e-post"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "Rapportering"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Reserved Seats"
msgstr "Reserverte plasser"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Responsible"
msgstr "Ansvarlig"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruker"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Scan badges to confirm attendances"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "Program og spor"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events efficiently:\n"
"                track registrations and participations, automate the confirmation emails, sell tickets, etc."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Scheduled Sent Mail"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "Planlagt tid"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "Selg billetter på nettstedet ditt"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "Selg billetter med salgsordrer"

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Seminar"
msgstr "Seminar"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "Send"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "Send med e-post"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__done
msgid "Sent"
msgstr "Sendt"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Set To Draft"
msgstr "Sett som utkast"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr "Sett som ubekreftet"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "Innstillinger"

#. module: event
#: model:event.type,name:event.event_type_3
msgid "Show"
msgstr "Vis"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr "Vis alle poster som har neste handlingsdato før dagen i dag"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__origin
msgid "Source Document"
msgstr "Kildedokument"

#. module: event
#: model:event.type,name:event.event_type_5
msgid "Sport"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "Startdato"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__state
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Status"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basert på aktiviteter\n"
"Utgått: Fristen er allerede passert\n"
"I dag: Aktiviteten skal gjøres i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "The closing date cannot be earlier than the beginning date."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid ""
"There are already attendees who attended this event. Please reset it to "
"draft if you want to cancel this event."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__template_id
#: model:ir.model.fields,help:event.field_event_type_mail__template_id
msgid ""
"This field contains the template of the mail that will be automatically sent"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
msgid "Tickets"
msgstr "Billetter"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "Tidssone"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "Total"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "Spor og program"

#. module: event
#: model:event.type,name:event.event_type_4
msgid "Training"
msgstr "Opplæring"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "Utløser"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__twitter_hashtag
#: model:ir.model.fields,field_description:event.field_event_type__default_hashtag
msgid "Twitter Hashtag"
msgstr "Twitter-hashtag"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type unntaks-aktivitet på posten."

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__state__draft
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "Ubekreftet"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr "Ubekreftede plassreservasjoner"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "Forstå de ulike modulene;"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "Enhet"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__seats_availability__unlimited
msgid "Unlimited"
msgstr "Ubegrenset"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unread Messages"
msgstr "Uleste meldinger"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Teller for uleste meldinger"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "Kommende arrangementer fra i dag"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_hashtag
msgid "Use Default Hashtag"
msgstr "Bruk standard-hashtag"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_timezone
msgid "Use Default Timezone"
msgstr "Bruk standard-tidssone"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Bruker"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Visibility"
msgstr "Synlighet"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "Historikk for kommunikasjon på nettsted"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "Uker"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "You must wait the event confirmation before doing this action."
msgstr "Du kan ikke gjøre dette før arrangementet er bekreftet."

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "You must wait the event starting day before doing this action."
msgstr "Du kan ikke gjøre dette før arrangementets startdato."

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for ${object.event_id.name}"
msgstr "Din medalje for ${object.event_id.name}"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at ${object.event_id.name}"
msgstr "Din registrering på ${object.event_id.name}"

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid "badge_of_${(object.event_id.name or '').replace('/','_')}"
msgstr "badge_of_${(object.event_id.name or '').replace('/','_')}"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "in %d days"
msgstr "om %d dag(er)"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "next month"
msgstr "neste måned"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "next week"
msgstr "neste uke"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "on "
msgstr "på"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "today"
msgstr "i dag"

#. module: event
#: code:addons/event/models/event.py:0
#, python-format
msgid "tomorrow"
msgstr "i morgen"
