# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "(Available seats:"
msgstr "(Places disponibles: "

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "<i class=\"fa fa-ban mr-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban mr-2\"/>Épuisé"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "<i class=\"fa fa-check mr-2\"/>Registered"
msgstr "<i class=\"fa fa-check mr-2\"/>Inscrit"

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""
"Un élément de liste de prix avec une quantité min. positive ne peut pas être"
" appliquée à ce produit d'événement."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""
"Un élément de liste de prix avec une quantité min. positive ne sera pas "
"appliquée aux produits de billets d'événement."

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Billets d'événement"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Free"
msgstr "Gratuit"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_complete_inherit_website_event_sale
msgid "N/A"
msgstr "N/D"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.index
msgid "Only"
msgstr "Uniquement"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Règle des listes des prix"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product"
msgstr "Article"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Register"
msgstr "S'inscrire"

#. module: website_event_sale
#: code:addons/website_event_sale/controllers/main.py:0
#, python-format
msgid "Registration"
msgstr "Inscription"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.index
msgid "Remaining"
msgstr "Restant"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Bon de commande"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de bons de commande"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Sales end on"
msgstr "Fin des ventes le"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.index
msgid "Sold Out"
msgstr "Complet"

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr ""
"Désolé, les billets %(ticket)s pour l'événement %(event)s sont tous vendus."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Désolé, seulement %(remaining_seats)d places sont encore disponibles pour le"
" billet %(ticket)s pour l'événement %(event)s."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "Le billet ne correspond pas avec ce produit."

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_complete_inherit_website_event_sale
msgid "Ticket"
msgstr "Ticket"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid ""
"Tickets\n"
"                    <span class=\"close\">×</span>"
msgstr ""
"Tickets\n"
"                    <span class=\"close\">×</span>"

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Avertissement"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Site Web"
