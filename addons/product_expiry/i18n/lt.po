# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_expiry
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> V<PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-09 12:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>au<PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Expiration Alert</span>"
msgstr ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Galiojimo įspėjimas</span>"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days</span>"
msgstr "<span>dienos</span>"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__alert_date
msgid "Alert Date"
msgstr "Perspėjimo data"

#. module: product_expiry
#: model:mail.activity.type,name:product_expiry.mail_activity_type_alert_date_reached
msgid "Alert Date Reached"
msgstr "Įspėjimo data pasiekta"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_date
msgid "Best before Date"
msgstr "Geriausias iki"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""
"Data, naudojama pasibaigusio galiojimo partijų ir serijinių numerių "
"nustatymui, naudojant filtrą \"Besibaigiančio galiojimo perspėjimai\"."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Datos"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__life_date
msgid "End of Life Date"
msgstr "Galiojimo pabaigos data"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration Alerts"
msgstr "Galiojimo įspėjimai"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_reminded
msgid "Expiry has been reminded"
msgstr "Galiojimo laikas jau buvo primintas"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Partijos / serijinis numeris"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template__alert_time
msgid ""
"Number of days before an alert should be raised on the lot/serial number."
msgstr ""
"Dienų skaičius, kuriam likus turėtų būti skelbiamas perspėjimas partijai / "
"serijiniam numeriui."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__life_time
#: model:ir.model.fields,help:product_expiry.field_product_template__life_time
msgid ""
"Number of days before the goods may become dangerous and must not be "
"consumed. It will be computed on the lot/serial number."
msgstr ""
"Dienų skaičius, per kurį prekės gali tapti pavojingos ir nebeturėtų būti "
"vartojamos. Tai bus skaičiuojama pagal partijos / serijinį numerį."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template__removal_time
msgid ""
"Number of days before the goods should be removed from the stock. It will be"
" computed on the lot/serial number."
msgstr ""
"Dienų skaičius, per kurį prekės turėtų būti pašalintos iš atsargų. Tai bus "
"skaičiuojama pagal partijos / serijinį numerį."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_time
#: model:ir.model.fields,help:product_expiry.field_product_template__use_time
msgid ""
"Number of days before the goods starts deteriorating, without being "
"dangerous yet. It will be computed using the lot/serial number."
msgstr ""
"Dienų skaičius, per kurį prekės pradeda gesti, dar netapdamos pavojingomis. "
"Tai bus skaičiuojama naudojant partijos / serijinį numerį."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_procurement_group
msgid "Procurement Group"
msgstr "Planinio užsakymo grupė"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__alert_time
msgid "Product Alert Time"
msgstr "Produkto perspėjimo laikas"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "Product Expiry Alert"
msgstr "Produkto galiojimo pabaigos perspėjimas"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__life_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__life_time
msgid "Product Life Time"
msgstr "Produkto gyvavimo laikas"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__removal_time
msgid "Product Removal Time"
msgstr "Produkto pašalinimo laikas"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Produkto šablonas"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_time
msgid "Product Use Time"
msgstr "Produkto vartojimo laikas"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Kiekių grupės"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__removal_date
msgid "Removal Date"
msgstr "Pašalinimo data"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "The Alert Date has been reached."
msgstr "Perspėjimo data buvo pasiekta."

#. module: product_expiry
#: code:addons/product_expiry/models/production_lot.py:0
#, python-format
msgid "The alert date has been reached for this lot/serial number"
msgstr "Šiam partijos/serijiniam numeriui buvo pasiekta įspėjimo data"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__life_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""
"Tai yra data, kuriai atėjus, produktai su šiuo serijiniu numeriu gali tapti "
"pavojingais ir nebeturėtų būti vartojami."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock. This date will be used in FEFO removal strategy."
msgstr ""
"Tai data, kurią šiuo serijos numeriu pažymėtos prekės turi būti pašalintos "
"iš atsargų. Ši data naudojama tik FEFO pašalinimo strategijoje."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
"Tai yra data, kuriai atėjus, produktai su šiuo serijiniu numeriu pradeda "
"gesti, bet dar netampa pavojingais."
