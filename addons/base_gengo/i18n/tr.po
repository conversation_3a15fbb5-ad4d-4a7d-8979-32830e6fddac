# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_gengo
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# Tu<PERSON>y Hatıl <<EMAIL>>, 2020
# Ertu<PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-11-22 08:19+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid ""
"<span class=\"o_form_label\">Gengo Public or Private keys are wrong or "
"missing.</span>"
msgstr ""
"<span class=\"o_form_label\">Gengo Public veya Private keyleri yanlış yada "
"eksik.</span>"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid ""
"<span class=\"o_form_label\">Note: If the translation state is 'In "
"Progress', it means that the translation has to be approved to be uploaded "
"in this system. You are supposed to do that directly by using your Gengo "
"Account</span>"
msgstr ""
"<span class=\"o_form_label\">Not: Çeviri durumu 'İşleniyor' durumundaysa, "
"çevirinin sisteme yüklenebilmesi için onaylanması gerekiyor anlamındadır. "
"Doğrudan Gengo Hesabınızı kullanarak bunu yapmanız gerekiyor.</span>"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add Gengo login Private Key..."
msgstr "Gengo girişi Özel Anahtarını ekle..."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add Gengo login Public Key..."
msgstr "Gengo girişi Genel Anahtarını ekle..."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add your comments here for translator...."
msgstr "Çevirmen için buraya yorum ekleyin...."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Auto Approve Translation"
msgstr "Çeviri Otomatik Onaylansın mı?"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_auto_approve
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_auto_approve
msgid "Auto Approve Translation ?"
msgstr "Çeviri Otomatik Onaylansın mı?"

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_base_gengo_translations
msgid "Base Gengo Translations"
msgstr "Temel Gengo Çevirisi"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__base_gengo_translations__sync_type__both
msgid "Both"
msgstr "İkisi de"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Cancel"
msgstr "İptal"

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company__gengo_sandbox
#: model:ir.model.fields,help:base_gengo.field_res_config_settings__gengo_sandbox
msgid ""
"Check this box if you're using the sandbox mode of Gengo, mainly used for "
"testing purpose."
msgstr ""
"Gengo sanbox modunu kullanıyorsanız bu kutuyu işaretleyin, genelde deneme "
"amacıyla kullanılır."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Click here to Configure Gengo Parameters"
msgstr "Gengo Parametrelerini Konfigüre etmek için buraya tıklayın."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Comment"
msgstr "Yorum"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_comment
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_comment
msgid "Comments"
msgstr "Yorumlar"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation__gengo_comment
msgid "Comments & Activity Linked to Gengo"
msgstr "Gengo'ya bağlı yorumlar ve aktiviteler"

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigürasyon Ayarları"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid "Gengo Comments & Activity..."
msgstr "Gengo Açıklamaları & Etkinlikleri..."

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation__order_id
msgid "Gengo Order ID"
msgstr "Gengo Sipariş Kimliği..."

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_private_key
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_private_key
msgid "Gengo Private Key"
msgstr "Gengo Özel Anahtarı"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_public_key
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_public_key
msgid "Gengo Public Key"
msgstr "Gengo Genel Anahtarı"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Gengo Request Form"
msgstr "Gengo İstek Formu"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid "Gengo Translation Service"
msgstr "Gengo Çeviri Hizmeti"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation__gengo_translation
msgid "Gengo Translation Service Level"
msgstr "Genge Çeviri Hizmet Düzeyi"

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"Gengo `Public Key` or `Private Key` are missing. Enter your Gengo "
"authentication parameters under `Settings > Companies > Gengo Parameters`."
msgstr ""
"Gengo 'Genel Anahtar\" ya da 'Özel Anahtar' eksiktir. "
"'Ayarlar>Şirketler>Gengo Parametreleri' menüsünden Gengo kimlik doğrulama "
"parametrelerinizi girin."

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"Gengo connection failed with this message:\n"
"``%s``"
msgstr ""
"Gengo bağlantısı bu mesaj ile hata verdi:\n"
"``%s``"

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"Gengo library not installed. Contact your system administrator to use it."
msgstr ""
"Gengo Kütüphanesi yüklü değil. Kullanmak için sistem yöneticinize başvurun."

#. module: base_gengo
#: model:ir.actions.act_window,name:base_gengo.action_wizard_base_gengo_translations
#: model:ir.ui.menu,name:base_gengo.menu_action_wizard_base_gengo_translations
msgid "Gengo: Manual Request of Translation"
msgstr "Gengo: Elle Çeviri İsteği"

#. module: base_gengo
#: model:ir.actions.server,name:base_gengo.gengo_sync_send_request_scheduler_ir_actions_server
#: model:ir.cron,cron_name:base_gengo.gengo_sync_send_request_scheduler
#: model:ir.cron,name:base_gengo.gengo_sync_send_request_scheduler
msgid "Gengo: Sync translation (Request)"
msgstr "Gengo: Senkronize çeviri (Gerekli)"

#. module: base_gengo
#: model:ir.actions.server,name:base_gengo.gengo_sync_receive_request_scheduler_ir_actions_server
#: model:ir.cron,cron_name:base_gengo.gengo_sync_receive_request_scheduler
#: model:ir.cron,name:base_gengo.gengo_sync_receive_request_scheduler
msgid "Gengo: Sync translation (Response)"
msgstr "Gengo: Senkronize çeviri (Yanıt)"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__id
msgid "ID"
msgstr "ID"

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company__gengo_auto_approve
#: model:ir.model.fields,help:base_gengo.field_res_config_settings__gengo_auto_approve
msgid "Jobs are Automatically Approved by Gengo."
msgstr "İşler Gengo tarafından otomatik olarak onaylanıyor"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__lang_id
msgid "Language"
msgstr "Dil"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__sync_limit
msgid "No. of terms to sync"
msgstr "Eşlenecek terim sayısı"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Private Key"
msgstr "Özel Anahtar"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__pro
msgid "Pro"
msgstr "Pro"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Public Key"
msgstr "Genel Anahtar"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__base_gengo_translations__sync_type__receive
msgid "Receive Translation"
msgstr "Çeviri Al"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_sandbox
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_sandbox
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Sandbox Mode"
msgstr "Sandbox Modu"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Send"
msgstr "Gönder"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__base_gengo_translations__sync_type__send
msgid "Send New Terms"
msgstr "Yeni Şartları Gönder"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__standard
msgid "Standard"
msgstr "Standart"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__sync_type
msgid "Sync Type"
msgstr "Eşleme Türü"

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"The number of terms to sync should be between 1 to 200 to work with Gengo "
"translation services."
msgstr ""
"Gengo çeviri hizmetleriyle çalışabilmesi için senkronize edilecek terim "
"sayısı 1 den 200 e kadar olmalı."

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__authorized_credentials
msgid "The private and public keys are valid"
msgstr "Özel ya da genel keyler doğrulandı."

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company__gengo_comment
#: model:ir.model.fields,help:base_gengo.field_res_config_settings__gengo_comment
msgid ""
"This comment will be automatically be enclosed in each an every request sent"
" to Gengo"
msgstr ""
"Bu açıklama Gengo'ya gönderilen her bir ve her isteğin içine otomatik olarak"
" konulacaktır"

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid "This language is not supported by the Gengo translation services."
msgstr "Bu dil Gengo tercüme servisi tarafından desteklenmiyor."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_translation_search
msgid "To Approve In Gengo"
msgstr "Gengo'da Onaylanacak"

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_ir_translation
msgid "Translation"
msgstr "Çeviri"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__machine
msgid "Translation By Machine"
msgstr "Makine Tarafından Çeviri"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__ultra
msgid "Ultra"
msgstr "Ultra"

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_ir_translation__gengo_translation
msgid ""
"You can select here the service level you want for an automatic translation "
"using Gengo."
msgstr ""
"Genge otomatik çeviri servisi için istediğiniz hizmet düzeyini buradan "
"seçebilirsiniz"
