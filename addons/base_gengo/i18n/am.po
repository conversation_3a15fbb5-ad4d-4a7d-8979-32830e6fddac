# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_gengo
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Amharic (https://www.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add Gengo login Private Key..."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add Gengo login Public Key..."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add your comments here for translator...."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Auto Approve Translation"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company_gengo_auto_approve
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings_gengo_auto_approve
msgid "Auto Approve Translation ?"
msgstr ""

#. module: base_gengo
#: selection:base.gengo.translations,sync_type:0
msgid "Both"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Cancel"
msgstr "መሰረዝ"

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company_gengo_sandbox
#: model:ir.model.fields,help:base_gengo.field_res_config_settings_gengo_sandbox
msgid ""
"Check this box if you're using the sandbox mode of Gengo, mainly used for "
"testing purpose."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Click here to Configure Gengo Parameters"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Comment"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company_gengo_comment
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings_gengo_comment
msgid "Comments"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation_gengo_comment
msgid "Comments & Activity Linked to Gengo"
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_res_company
msgid "Companies"
msgstr "ድርጅት"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_create_uid
msgid "Created by"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_create_date
msgid "Created on"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_display_name
msgid "Display Name"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid "Gengo Comments & Activity..."
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation_order_id
msgid "Gengo Order ID"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company_gengo_private_key
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings_gengo_private_key
msgid "Gengo Private Key"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company_gengo_public_key
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings_gengo_public_key
msgid "Gengo Public Key"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Gengo Public or Private keys are wrong or missing."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Gengo Request Form"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid "Gengo Translation Service"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation_gengo_translation
msgid "Gengo Translation Service Level"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:84
#, python-format
msgid ""
"Gengo `Public Key` or `Private Key` are missing. Enter your Gengo "
"authentication parameters under `Settings > Companies > Gengo Parameters`."
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:95
#, python-format
msgid ""
"Gengo connection failed with this message:\n"
"``%s``"
msgstr ""

#. module: base_gengo
#: model:ir.actions.act_window,name:base_gengo.action_wizard_base_gengo_translations
#: model:ir.ui.menu,name:base_gengo.menu_action_wizard_base_gengo_translations
msgid "Gengo: Manual Request of Translation"
msgstr ""

#. module: base_gengo
#: model:ir.actions.server,name:base_gengo.gengo_sync_send_request_scheduler_ir_actions_server
#: model:ir.cron,cron_name:base_gengo.gengo_sync_send_request_scheduler
#: model:ir.cron,name:base_gengo.gengo_sync_send_request_scheduler
msgid "Gengo: Sync translation (Request)"
msgstr ""

#. module: base_gengo
#: model:ir.actions.server,name:base_gengo.gengo_sync_receive_request_scheduler_ir_actions_server
#: model:ir.cron,cron_name:base_gengo.gengo_sync_receive_request_scheduler
#: model:ir.cron,name:base_gengo.gengo_sync_receive_request_scheduler
msgid "Gengo: Sync translation (Response)"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_id
msgid "ID"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company_gengo_auto_approve
#: model:ir.model.fields,help:base_gengo.field_res_config_settings_gengo_auto_approve
msgid "Jobs are Automatically Approved by Gengo."
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_lang_id
msgid "Language"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_write_date
msgid "Last Updated on"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_sync_limit
msgid "No. of terms to sync"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid ""
"Note: If the translation state is 'In Progress', it means that the "
"translation has to be approved to be uploaded in this system. You are "
"supposed to do that directly by using your Gengo Account"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Private Key"
msgstr ""

#. module: base_gengo
#: selection:ir.translation,gengo_translation:0
msgid "Pro"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Public Key"
msgstr ""

#. module: base_gengo
#: selection:base.gengo.translations,sync_type:0
msgid "Receive Translation"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company_gengo_sandbox
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings_gengo_sandbox
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Sandbox Mode"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Send"
msgstr ""

#. module: base_gengo
#: selection:base.gengo.translations,sync_type:0
msgid "Send New Terms"
msgstr ""

#. module: base_gengo
#: selection:ir.translation,gengo_translation:0
msgid "Standard"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_sync_type
msgid "Sync Type"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:114
#, python-format
msgid ""
"The number of terms to sync should be between 1 to 200 to work with Gengo "
"translation services."
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations_authorized_credentials
msgid "The private and public keys are valid"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company_gengo_comment
#: model:ir.model.fields,help:base_gengo.field_res_config_settings_gengo_comment
msgid ""
"This comment will be automatically be enclosed in each an every request sent"
" to Gengo"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:109
#, python-format
msgid "This language is not supported by the Gengo translation services."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_translation_search
msgid "To Approve In Gengo"
msgstr ""

#. module: base_gengo
#: selection:ir.translation,gengo_translation:0
msgid "Translation By Machine"
msgstr ""

#. module: base_gengo
#: selection:ir.translation,gengo_translation:0
msgid "Ultra"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_ir_translation_gengo_translation
msgid ""
"You can select here the service level you want for an automatic translation "
"using Gengo."
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_base_gengo_translations
msgid "base.gengo.translations"
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_ir_translation
msgid "ir.translation"
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_res_config_settings
msgid "res.config.settings"
msgstr ""
