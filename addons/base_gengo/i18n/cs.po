# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_gengo
# 
# Translators:
# <PERSON>, 2019
# <PERSON> <jan.ho<PERSON><PERSON><PERSON>@centrum.cz>, 2019
# trendspotter, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-11-22 08:19+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: trendspotter, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid ""
"<span class=\"o_form_label\">Gengo Public or Private keys are wrong or "
"missing.</span>"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid ""
"<span class=\"o_form_label\">Note: If the translation state is 'In "
"Progress', it means that the translation has to be approved to be uploaded "
"in this system. You are supposed to do that directly by using your Gengo "
"Account</span>"
msgstr ""
"<span class=\"o_form_label\">Poznámka: Pokud je stav překladu 'In Progress',"
" znamená to, že překlad musí být schválen k nahrání do tohoto systému. Měli "
"byste tak učinit přímo pomocí svého účtu Gengo</span>"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add Gengo login Private Key..."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add Gengo login Public Key..."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Add your comments here for translator...."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Auto Approve Translation"
msgstr "Automatické schválení překladu"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_auto_approve
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_auto_approve
msgid "Auto Approve Translation ?"
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_base_gengo_translations
msgid "Base Gengo Translations"
msgstr "Základní Gengo překlady"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__base_gengo_translations__sync_type__both
msgid "Both"
msgstr "Zájemce/Příležitost"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Cancel"
msgstr "Zrušit"

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company__gengo_sandbox
#: model:ir.model.fields,help:base_gengo.field_res_config_settings__gengo_sandbox
msgid ""
"Check this box if you're using the sandbox mode of Gengo, mainly used for "
"testing purpose."
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Click here to Configure Gengo Parameters"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Comment"
msgstr "Komentář"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_comment
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_comment
msgid "Comments"
msgstr "Komentáře"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation__gengo_comment
msgid "Comments & Activity Linked to Gengo"
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid "Gengo Comments & Activity..."
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation__order_id
msgid "Gengo Order ID"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_private_key
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_private_key
msgid "Gengo Private Key"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_public_key
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_public_key
msgid "Gengo Public Key"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Gengo Request Form"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_ir_translation_inherit_base_gengo_form
msgid "Gengo Translation Service"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_ir_translation__gengo_translation
msgid "Gengo Translation Service Level"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"Gengo `Public Key` or `Private Key` are missing. Enter your Gengo "
"authentication parameters under `Settings > Companies > Gengo Parameters`."
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"Gengo connection failed with this message:\n"
"``%s``"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"Gengo library not installed. Contact your system administrator to use it."
msgstr ""

#. module: base_gengo
#: model:ir.actions.act_window,name:base_gengo.action_wizard_base_gengo_translations
#: model:ir.ui.menu,name:base_gengo.menu_action_wizard_base_gengo_translations
msgid "Gengo: Manual Request of Translation"
msgstr ""

#. module: base_gengo
#: model:ir.actions.server,name:base_gengo.gengo_sync_send_request_scheduler_ir_actions_server
#: model:ir.cron,cron_name:base_gengo.gengo_sync_send_request_scheduler
#: model:ir.cron,name:base_gengo.gengo_sync_send_request_scheduler
msgid "Gengo: Sync translation (Request)"
msgstr ""

#. module: base_gengo
#: model:ir.actions.server,name:base_gengo.gengo_sync_receive_request_scheduler_ir_actions_server
#: model:ir.cron,cron_name:base_gengo.gengo_sync_receive_request_scheduler
#: model:ir.cron,name:base_gengo.gengo_sync_receive_request_scheduler
msgid "Gengo: Sync translation (Response)"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__id
msgid "ID"
msgstr "ID"

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company__gengo_auto_approve
#: model:ir.model.fields,help:base_gengo.field_res_config_settings__gengo_auto_approve
msgid "Jobs are Automatically Approved by Gengo."
msgstr "Práce jsou automaticky schváleny společností Gengo."

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__lang_id
msgid "Language"
msgstr "Jazyk"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__sync_limit
msgid "No. of terms to sync"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Private Key"
msgstr "Privátní klíč"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__pro
msgid "Pro"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Public Key"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__base_gengo_translations__sync_type__receive
msgid "Receive Translation"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_res_company__gengo_sandbox
#: model:ir.model.fields,field_description:base_gengo.field_res_config_settings__gengo_sandbox
#: model_terms:ir.ui.view,arch_db:base_gengo.res_config_settings_view_form
msgid "Sandbox Mode"
msgstr ""

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.base_gengo_translation_wizard_from
msgid "Send"
msgstr "Odeslat"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__base_gengo_translations__sync_type__send
msgid "Send New Terms"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__standard
msgid "Standard"
msgstr "Standardní"

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__sync_type
msgid "Sync Type"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid ""
"The number of terms to sync should be between 1 to 200 to work with Gengo "
"translation services."
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,field_description:base_gengo.field_base_gengo_translations__authorized_credentials
msgid "The private and public keys are valid"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_res_company__gengo_comment
#: model:ir.model.fields,help:base_gengo.field_res_config_settings__gengo_comment
msgid ""
"This comment will be automatically be enclosed in each an every request sent"
" to Gengo"
msgstr ""

#. module: base_gengo
#: code:addons/base_gengo/wizard/base_gengo_translations.py:0
#, python-format
msgid "This language is not supported by the Gengo translation services."
msgstr "Tento jazyk není podporován překladatelskými službami Gengo."

#. module: base_gengo
#: model_terms:ir.ui.view,arch_db:base_gengo.view_translation_search
msgid "To Approve In Gengo"
msgstr ""

#. module: base_gengo
#: model:ir.model,name:base_gengo.model_ir_translation
msgid "Translation"
msgstr "Překlad"

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__machine
msgid "Translation By Machine"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields.selection,name:base_gengo.selection__ir_translation__gengo_translation__ultra
msgid "Ultra"
msgstr ""

#. module: base_gengo
#: model:ir.model.fields,help:base_gengo.field_ir_translation__gengo_translation
msgid ""
"You can select here the service level you want for an automatic translation "
"using Gengo."
msgstr ""
