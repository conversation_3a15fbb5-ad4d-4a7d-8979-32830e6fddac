# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# <PERSON>VET <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <will<PERSON>.<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# C<PERSON>cile Collart <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 12:34+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "${object.event_id.name} - Reminder"
msgstr "${object.event_id.name} - Rappel"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "${object.event_id.name}: Date updated"
msgstr "${object.event_id.name}: Date modifiée"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid ""
"%s at %s To\n"
" %s at %s (%s)"
msgstr ""
"Du %s à %s jusqu'au\n"
"%s à %s (%s)"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "%s at (%s To %s) (%s)"
msgstr "le %s de %s à %s (%s)"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "%s has accepted invitation"
msgstr "%s a accepté l'invitation"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "%s has declined invitation"
msgstr "%s a refusé l'invitation"

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <!--\n"
"        In a recurring event case, the object.event_id is always the first event\n"
"        This makes the event date (and a lot of other information) incorrect\n"
"    -->\n"
"    % set event_id = ctx.get('force_event_id') or object.event_id\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        This is a reminder for the below event :\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${event_id.get_interval('dayname', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${event_id.get_interval('day', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${event_id.get_interval('month', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not event_id.allday and event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <!--\n"
"        In a recurring event case, the object.event_id is always the first event\n"
"        This makes the event date (and a lot of other information) incorrect\n"
"    -->\n"
"    % set event_id = ctx.get('force_event_id') or object.event_id\n"
"    <p>\n"
"        Bonjour ${object.common_name},<br/><br/>\n"
"        Ceci est un rappel pour l'événement ci-dessous  :\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accepter</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Refuser</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Voir</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${event_id.get_interval('dayname', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${event_id.get_interval('day', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${event_id.get_interval('month', tz=object.partner_id.tz if not event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not event_id.allday and event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Détails de l'événement</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Lieu: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">Voir la carte</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Durée: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Participants\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">Vous</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Merci,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        ${object.event_id.user_id.partner_id.name} invited you for the ${object.event_id.name} meeting of ${object.event_id.user_id.company_id.name}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Bonjour ${object.common_name},<br/><br/>\n"
"        ${object.event_id.user_id.partner_id.name} vous invite à la réunion ${object.event_id.name} de ${object.event_id.user_id.company_id.name}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accepter</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Refuser</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Voir</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Détails de l'évènement</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Lieu: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">Voir la carte</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Durée: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Participants\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">Vous</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Merci,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Hello ${object.common_name},<br/><br/>\n"
"        The date of the meeting has been updated. The meeting ${object.event_id.name} created by ${object.event_id.user_id.partner_id.name} is now scheduled for ${object.event_id.get_display_time_tz(tz=object.partner_id.tz)}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Location: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">View Map</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Duration: ${('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Attendees\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: ${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    % set colors = {'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00',  'declined': 'red'}\n"
"    <p>\n"
"        Bonjour ${object.common_name},<br/><br/>\n"
"        La date de la réunion a été mise à jour. La réunion ${object.event_id.name} créée par ${object.event_id.user_id.partner_id.name} est maintenant prévue pour ${object.event_id.get_display_time_tz(tz=object.partner_id.tz)}.\n"
"    </p>\n"
"    <div style=\"text-align: center; margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/calendar/meeting/accept?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accepter</a>\n"
"        <a href=\"/calendar/meeting/decline?db=${'dbname' in ctx and ctx['dbname'] or '' }&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Refuser</a>\n"
"        <a href=\"/calendar/meeting/view?db=${'dbname' in ctx and ctx['dbname'] or ''}&amp;token=${object.access_token}&amp;action=${'action_id' in ctx and ctx['action_id'] or ''}&amp;id=${object.event_id.id}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Voir</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('dayname', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                ${object.event_id.get_interval('day', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                ${object.event_id.get_interval('month', tz=object.partner_id.tz if not object.event_id.allday else None)}\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                ${not object.event_id.allday and object.event_id.get_interval('time', tz=object.partner_id.tz) or ''}\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Détails de l'évènement</strong></p>\n"
"            <ul>\n"
"                % if object.event_id.location:\n"
"                    <li>Lieu: ${object.event_id.location}\n"
"                        (<a target=\"_blank\" href=\"http://maps.google.com/maps?oi=map&amp;q=${object.event_id.location}\">Voir la carte</a>)\n"
"                    </li>\n"
"                % endif\n"
"                % if object.event_id.description :\n"
"                    <li>Description: ${object.event_id.description}</li>\n"
"                % endif\n"
"                % if not object.event_id.allday and object.event_id.duration\n"
"                    <li>Durée: ${('%dH%02d' % (object.event_id.duration,(object.event_id.duration*60)%60))}</li>\n"
"                % endif\n"
"                <li>Participants\n"
"                <ul>\n"
"                % for attendee in object.event_id.attendee_ids:\n"
"                    <li>\n"
"                        <div style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: ${colors[attendee.state] or 'white'};\"> </div>\n"
"                        % if attendee.common_name != object.common_name:\n"
"                            <span style=\"margin-left:5px\">${attendee.common_name}</span>\n"
"                        % else:\n"
"                            <span style=\"margin-left:5px\">Vous</span>\n"
"                        % endif\n"
"                    </li>\n"
"                % endfor\n"
"                </ul></li>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Merci,\n"
"    <br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span> heures</span>"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Accept"
msgstr "Accepter"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__accepted
msgid "Accepted"
msgstr "Accepté"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action to Perform"
msgstr "Action à effectuer"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Les actions peuvent déclencher des comportements spécifiques, tels que "
"l’ouverture de la vue du calendrier ou automatiquement marquer comme fait "
"lorsqu’un document est téléchargé."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__active
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
msgid "Active"
msgstr "Active"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "Activité"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "Type d'activité"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "Toute la journée"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "AllDay , %s"
msgstr "Toute la journée , %s"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_contacts_user_id_partner_id_unique
msgid "An user cannot have twice the same contact."
msgstr "Le même contact ne peut pas figurer deux fois chez un utilisateur."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "Archivé"

#. module: calendar
#: model:ir.model,name:calendar.model_ir_attachment
msgid "Attachment"
msgstr "Pièce jointe"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "Compte des pièces jointes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_attendee
msgid "Attendee"
msgstr "Participant"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_status
msgid "Attendee Status"
msgstr "État du participant"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Attendees"
msgstr "Participants"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Availability"
msgstr "Disponibilité"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#, python-format
msgid "Busy"
msgstr "Occupé(e)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
msgid "By day"
msgstr "Par jour"

#. module: calendar
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
msgid "Calendar"
msgstr "Calendrier"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "Alarme du calendrier"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Informations des participants au calendrier"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_contacts
msgid "Calendar Contacts"
msgstr "Calendrier des contacts"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Calendrier de l'événement"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "Invitation calendrier"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "Calendrier de réunion"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
#: model:ir.cron,cron_name:calendar.ir_cron_scheduler_alarm
#: model:ir.cron,name:calendar.ir_cron_scheduler_alarm
msgid "Calendar: Event Reminder"
msgstr "Calendrier : rappel pour un événement"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Click here to update only this instance and not all recurrences."
msgstr ""
"Cliquer ici pour modifier seulement cet événement, mais pas les autres "
"occurrences."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "Nom"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__state__open
msgid "Confirmed"
msgstr "Confirmé"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Contact"
msgstr "Contact"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
msgid "Created on"
msgstr "Créé le"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_start
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Date"
msgstr "Date"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
msgid "Date of month"
msgstr "Date dans le mois"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "Jour du mois"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
msgid "Day of month"
msgstr "Jour du mois"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
msgid "Days"
msgstr "Jours"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Decline"
msgstr "Refuser"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__declined
msgid "Declined"
msgstr "Refusé"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
msgid "Description"
msgstr "Description"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/notification_calendar.xml:0
#, python-format
msgid "Details"
msgstr "Détails"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
msgid "Display Name"
msgstr "Afficher Nom"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Document"
msgstr "Document"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "Référence du document"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "Modèle de document"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "Nom de modèle de document"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "Durée"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
#: model:ir.model.fields,help:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "Durée en minutes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "Email"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "Email - 3 heures"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "Email - 6 heures"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__email
msgid "Email of Invited Person"
msgstr "Email des invités"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__partner_id
msgid "Employee"
msgstr "Employé"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "Date de fin"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_datetime
msgid "End Datetime"
msgstr "Date de fin"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
msgid "End date"
msgstr "Date de fin"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending at"
msgstr "Heure de fin"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "Rappel d'événement"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "Gestionnaire de rappel d'événements"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "Type d'événement réunion"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "Heure de l'événement"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
msgid "Everyone"
msgstr "Tout le monde"

#. module: calendar
#: code:addons/calendar/models/mail_activity.py:0
#, python-format
msgid "Feedback: "
msgstr "Commentaires :"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__5
msgid "Fifth"
msgstr "Cinquième"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
msgid "First"
msgstr "Premier"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "D'abord, vous devez indiquer la date de l'invitation."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_channel_ids
msgid "Followers (Channels)"
msgstr "Abonnés (Canaux)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
msgid "Fourth"
msgstr "Quatrième"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Free"
msgstr "Original"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Free/Busy"
msgstr "Libre/Occupé"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fr
msgid "Fri"
msgstr "Ven."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__fr
msgid "Friday"
msgstr "Vendredi"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "Regrouper par"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "Group by date is not supported, use the calendar view instead."
msgstr ""
"Le regroupement par date n'est pas supporté, utilisez la vue calendrier."

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "Heures"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
msgid "ID"
msgstr "ID"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Si le champ actif est décoché, il vous permet de désactiver l'alarme de cet "
"événement sans la supprimer."

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "Invitation"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
msgid "Invitation Token"
msgstr "Jeton d'invitation"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "Détails de l'invitation"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "Invitation pour"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to ${object.event_id.name}"
msgstr "Invitation à ${object.event_id.name}"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "Invitations"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Assistant d'invitation"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "L'événement est-il mis en avant"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
msgid "Last"
msgstr "Dernier"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm_manager____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "Dernière notification marquée comme lue sur base du Calendrier"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
msgid "Let the event automatically repeat at that interval"
msgstr "Laisser l'événement se répéter automatiquement à chaque intervalle"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "Lieu"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__location
msgid "Location of Event"
msgstr "Emplacement de l'événement"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "Logo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_contacts__user_id
msgid "Me"
msgstr "Moi"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "Rendez-vous"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "Meeting '%s' starts '%s' and ends '%s'"
msgstr "L’événement '%s' débute le '%s' et se fini le '%s'"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Meeting Details"
msgstr "Détails de l'évènement"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "Titre de l'évènement"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "Types de réunion"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "Réunion liée"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.actions.act_window,name:calendar.action_calendar_event_notify
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Meetings"
msgstr "Rendez-vous"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_message
msgid "Message"
msgstr "Message"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "Messages"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "Minutes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Misc"
msgstr "Divers"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mo
msgid "Mon"
msgstr "Lun."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__mo
msgid "Monday"
msgstr "Lundi"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
msgid "Months"
msgstr "Mois"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "Mes rendez-vous"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
msgid "Name"
msgstr "Nom"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__needsaction
msgid "Needs Action"
msgstr "Nécessite une action"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "No, je n'y vais pas"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "Notification"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "Notification - 1 jour"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "Notification - 1 heure"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "Notification - 15 minutes"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "Notification - 2 heures"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "Notification - 30 minutes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
msgid "Number of repetitions"
msgstr "Nombre de répétitions"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/notification_calendar.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
msgid "Only internal users"
msgstr "Utilisateurs internes seulement"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
msgid "Only me"
msgstr "Moi seulement"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "Ouvrir le calendrier"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "Option"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Options"
msgstr "Options"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Owner"
msgstr "Propriétaire"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "Participant"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "Données du partenaire lié à l'utilisateur"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "Please select a proper day of the month."
msgstr "Sélectionner un jour correct du mois."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Privacy"
msgstr "Confidentialité"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "Récurrence"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "Fin de la récurrence"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
msgid "Recurrent"
msgstr "Récurrent"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrent_id
msgid "Recurrent ID"
msgstr "ID récurrent"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrent_id_date
msgid "Recurrent ID date"
msgstr "Id. de la date récurrente"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrency
msgid "Recurrent Meeting"
msgstr "Rendez-vous récurrents"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "Règle de récurrence"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "Rappeler avant"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
msgid "Reminders"
msgstr "Rappels"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Repeat"
msgstr "Répéter"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat Every"
msgstr "Répéter tous les"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__final_date
msgid "Repeat Until"
msgstr "Répéter jusqu'au"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "Répéter chaque (Jour/Semaine/Mois/Année)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "Répéter x fois"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "Responsable"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sa
msgid "Sat"
msgstr "Sam."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__sa
msgid "Saturday"
msgstr "Samedi"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "Schedule a new meeting"
msgstr "Planifier une nouvelle réunion"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "Rechercher dans les rendez-vous"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
msgid "Second"
msgstr "Seconde"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "Selectionnez les participants..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send mail"
msgstr "Envoyer e-mail"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show Time as"
msgstr "Afficher l'heure comme"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/notification_calendar.xml:0
#, python-format
msgid "Snooze"
msgstr "Reporter"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "Démarrer"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "Date de début"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_datetime
msgid "Start DateTime"
msgstr "Date de début"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr ""
"Date de début d'événement, sans heure pour les événements durant toute la "
"journée"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Starting at"
msgstr "Heure de début"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
#: model:ir.model.fields,field_description:calendar.field_calendar_event__state
msgid "Status"
msgstr "État"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__state
msgid "Status of the attendee's participation"
msgstr "Statut de participation des invités"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "Status :"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "Arrêter"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr ""
"Date de fin d'événement, sans heure pour les événements durant toute la "
"journée"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "Sujet"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__su
msgid "Sun"
msgstr "Dim."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__su
msgid "Sunday"
msgstr "Dimanche"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists !"
msgstr "Ce nom d'étiquette existe déjà !"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "Étiquettes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr "Le"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/mail_activity.js:0
#, python-format
msgid ""
"The activity is linked to a meeting. Deleting it will remove the meeting as "
"well. Do you want to proceed ?"
msgstr ""
"L'activité est associée à une réunion. Si vous la supprimez, cela supprimera"
" également la réunion. Voulez-vous continuer ?"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"Le calendrier est partagé entre les employés et intégré complètement à\n"
" d'autres applications comme celles des congés ou des opportunités\n"
" d'affaires."

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr ""
"Les date et heure de fin ne peuvent pas être antérieure aux date et heure de"
" début."

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr "La date de fin ne peut pas être plus tôt que la date de début."

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "The interval cannot be negative."
msgstr "L'intervale ne peut pas être négatif"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "The number of repetitions  cannot be negative."
msgstr "Le nombre de répétitions ne peut pas être négatif"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
msgid "Third"
msgstr "Troisième"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "This event is linked to a recurrence...<br/>"
msgstr "Cet événement est associé à une récurrence...<br/>"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__th
msgid "Thu"
msgstr "Jeu."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__th
msgid "Thursday"
msgstr "Jeudi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
msgid "Timezone"
msgstr "Fuseau horaire"

#. module: calendar
#: code:addons/calendar/models/res_users.py:0
#, python-format
msgid "Today's Meetings"
msgstr "Réunion aujourd'hui"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tu
msgid "Tue"
msgstr "Mar."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__tu
msgid "Tuesday"
msgstr "Mardi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "Type"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__tentative
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Uncertain"
msgstr "Incertain"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__state__draft
msgid "Unconfirmed"
msgstr "Non confirmé"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "Unité"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "Jusqu'à"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Update only this instance"
msgstr "Modifier cette instance uniquement"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__we
msgid "Wed"
msgstr "Mer."

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__week_list__we
msgid "Wednesday"
msgstr "Mercredi"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__week_list
msgid "Weekday"
msgstr "Jour de la semaine"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
msgid "Weeks"
msgstr "Semaines"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
msgid "Years"
msgstr "Années"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "Oui je le fais"

#. module: calendar
#: code:addons/calendar/models/calendar.py:0
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "Vous ne pouvez indiquer un participant en double."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "ex: Repas de travail"
