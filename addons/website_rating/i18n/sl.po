# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_rating
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Slovenian (https://www.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"#{widget.options['force_submit_url'] ? widget.options['force_submit_url'] : "
"'/mail/chatter_post'}"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#, python-format
msgid "Add a review"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#, python-format
msgid "Average"
msgstr "Povprečna"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Cancel"
msgstr "Prekliči"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#, python-format
msgid "Close"
msgstr "Zaključi"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Comment"
msgstr "Komentar"

#. module: website_rating
#: model:ir.model.fields,field_description:website_rating.field_rating_rating__publisher_id
msgid "Commented by"
msgstr ""

#. module: website_rating
#: model:ir.model.fields,field_description:website_rating.field_rating_rating__publisher_datetime
msgid "Commented on"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Delete"
msgstr "Izbriši"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#, python-format
msgid "Details"
msgstr "Podrobnosti"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#, python-format
msgid "Half a star"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/portal_composer.js:0
#, python-format
msgid "I don't like it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/portal_composer.js:0
#, python-format
msgid "I hate it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/portal_composer.js:0
#, python-format
msgid "I like it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/portal_composer.js:0
#, python-format
msgid "I love it"
msgstr ""

#. module: website_rating
#: code:addons/website_rating/controllers/portal_rating.py:0
#, python-format
msgid "Invalid rating"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/portal_composer.js:0
#, python-format
msgid "It's okay"
msgstr ""

#. module: website_rating
#: model:ir.model,name:website_rating.model_mail_message
msgid "Message"
msgstr "Sporočilo"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#, python-format
msgid "Modify your review"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#, python-format
msgid "One star"
msgstr ""

#. module: website_rating
#: code:addons/website_rating/models/rating.py:0
#, python-format
msgid "Only the publisher of the website can change the rating comment"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Post comment"
msgstr ""

#. module: website_rating
#: model:ir.model.fields,field_description:website_rating.field_rating_rating__website_published
msgid "Published"
msgstr "Objavljeno"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Published on"
msgstr ""

#. module: website_rating
#: model:ir.model.fields,field_description:website_rating.field_rating_rating__publisher_comment
msgid "Publisher Comment"
msgstr ""

#. module: website_rating
#: model_terms:ir.ui.view,arch_db:website_rating.rating_rating_view_form
msgid "Publisher comment"
msgstr ""

#. module: website_rating
#: model:ir.model,name:website_rating.model_rating_rating
msgid "Rating"
msgstr "Ocena"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#, python-format
msgid "Remove Selection"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Update comment"
msgstr ""

#. module: website_rating
#: model:ir.model.fields,help:website_rating.field_rating_rating__website_published
msgid "Visible on the website as a comment"
msgstr "Vidno na spletni strani kot komentar"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/portal_chatter.js:0
#, python-format
msgid "Write your comment"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#: code:addons/website_rating/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "avatar"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_rating_composer.xml:0
#, python-format
msgid "ratings)"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/portal_tools.xml:0
#, python-format
msgid "stars"
msgstr ""
