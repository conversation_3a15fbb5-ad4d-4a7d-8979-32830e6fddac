<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_journal_form_inherit_l10n_in" model="ir.ui.view">
        <field name="name">account.journal.form.inherit.l10n.in</field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.view_account_journal_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='column_posting']" position="attributes">
                <attribute name="attrs">{'invisible': [('type', 'not in', ['bank', 'cash', 'sale', 'purchase'])]}</attribute>
            </xpath>
            <xpath expr="//field[@name='loss_account_id']" position="after">
                <field name="l10n_in_import_export" attrs="{'invisible': [('type', 'not in', ['sale', 'purchase'])]}"/>
            </xpath>
            <field name="company_id" position="after">
                <field name="l10n_in_gstin_partner_id" context="{'show_vat':True}" options='{"no_create": True,"always_reload": True}'/>
            </field>
        </field>
    </record>
</odoo>
