# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: German (https://www.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_uid
msgid "Created by"
msgstr "Erstell<PERSON> von"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "Google Service"
msgstr "Google Service"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__id
msgid "ID"
msgstr "ID"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service____last_update
msgid "Last Modified on"
msgstr "Letzte Änderung am"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: google_account
#: code:addons/google_account/models/google_service.py:172
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""
"Methode nicht unterstützt [%s] nicht in [GET, POST, PUT, PATCH or DELETE]!"

#. module: google_account
#: code:addons/google_account/models/google_service.py:120
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""
"Während der Generierung des Tokens ist ein Fehler aufgetreten. Eventuell ist"
" Ihr Autorisierungscode ungültig."

#. module: google_account
#: code:addons/google_account/models/google_service.py:56
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""
"Während der Generierung des Tokens ist ein Fehler aufgetreten. Eventuell ist"
" Ihr Autorisierungscode ungültig oder bereits abgelaufen."

#. module: google_account
#: code:addons/google_account/models/google_service.py:150
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""
"Während der Generierung des Tokens ist ein Fehler aufgetreten. Eventuell ist"
" Ihr Autorisierungscode ungültig oder bereits abgelaufen [%s]"

#. module: google_account
#: code:addons/google_account/models/google_service.py:193
#, python-format
msgid "Something went wrong with your request to google"
msgstr "Etwas ging schief mit Ihrer Anfrage an Google"

#. module: google_account
#: code:addons/google_account/models/google_service.py:131
#, python-format
msgid "The account for the Google service '%s' is not configured."
msgstr "Der Benutzer für den Google Service '%s' ist nicht konfiguriert."
