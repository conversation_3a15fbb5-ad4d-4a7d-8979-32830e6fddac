# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: Ryanto The <<EMAIL>>, 2019\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "Google Service"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__id
msgid "ID"
msgstr "ID"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service____last_update
msgid "Last Modified on"
msgstr "Terakhir Diubah Pada"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: google_account
#: code:addons/google_account/models/google_service.py:172
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:120
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:56
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:150
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:193
#, python-format
msgid "Something went wrong with your request to google"
msgstr "Terjadi kesalahan pada permintaan layanan ke google"

#. module: google_account
#: code:addons/google_account/models/google_service.py:131
#, python-format
msgid "The account for the Google service '%s' is not configured."
msgstr ""
