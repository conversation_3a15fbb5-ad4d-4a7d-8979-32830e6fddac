# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON><PERSON> <baskhuu<PERSON><EMAIL>>, 2019
# <PERSON>, 2019
# Khis<PERSON><PERSON><PERSON> Ganbold <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:11+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: tserendavaa tsogtoo <<EMAIL>>, 2019\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr ""

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 3.12"
msgstr "$ 3.12"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 4.40"
msgstr "$ 4.40"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 4.50"
msgstr "$ 4.50"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 8.50"
msgstr "$ 8.50"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"pos-change_amount\">$ 0.86</span>"
msgstr "<span class=\"pos-change_amount\">$ 0.86</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"pos-change_title\">Change</span>"
msgstr "<span class=\"pos-change_title\">Хариулт</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"total-amount-formatting\">TOTAL</span>"
msgstr "<span class=\"total-amount-formatting\">НИЙТ</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span id=\"total-amount\" class=\"pos_total-amount\">$ 469.14</span>"
msgstr "<span id=\"total-amount\" class=\"pos_total-amount\">$ 469.14</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span>$ 470.00</span>"
msgstr "<span>$ 470.00</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span>Cash (USD):</span>"
msgstr "<span>Бэлнээр (USD):</span>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Давхрын нэр: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr "<strong>Борлуулалтын цэг: </strong>"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_image
msgid ""
"A background image used to display a floor layout in the point of sale "
"interface"
msgstr ""
"Борлуулалтын цэгийн интерфэйсд давхрын зохион байгуулалтыг үзүүлэхэд дэвсгэр"
" зургийг ашигладаг"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Рестораны давхар нь үйлчлүүлэгч нар үйлчлүүлдэг газрыг хэлнэ, энд та "
"ширээнүүдийг тодорхойлж, байрлуулах боломжтой."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Идэвхитэй"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Add"
msgstr "Нэмэх"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/notes.js:0
#, python-format
msgid "Add Note"
msgstr "Тэмдэглэл нэмэх"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Рестораны давхар шинээр нэмэх"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr "Рестораны захиалгын принтер шинээр нэмэх"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Add button"
msgstr "Товч нэмэх"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add notes to orderlines"
msgstr "Захиалгын мөрөнд тэмдэглэл нэмэх"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom notes on Orderlines."
msgstr "Захиалгын мөр дээр тусгай тэмдэглэлийг зөвшөөрөх."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Allow to print bill before payment"
msgstr "Төлбөр төлөхөөс өмнө тооцоог хэвлэхийг зөвшөөрөх"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Төлбөр хийхээс өмнө тухайн тооцоог хэвлэхийг зөвшөөрнө."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "Ширээний дотоод тодорхойлолт"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr "Принтерийн дотоод тодорхойлолт"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__name
msgid "An internal identification of the restaurant floor"
msgstr "Рестораны давхрын дотоод тодорхойлолт"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Харагдац"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Are you sure ?"
msgstr "Та итгэлтэй байна уу?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "Back"
msgstr "Буцах"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Back to floor"
msgstr "Давхар луу буцах"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Дэвсгэр өнгө"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Дэвсгэр зураг"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bacon
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "Bill"
msgstr "Нэхэмжлэл"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Тооцоо хэвлэх"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Тооцоог хуваах"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Blue"
msgstr "Цэнхэр"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr "ЦУЦЛАГДСАН"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Changes could not be saved"
msgstr "Өөрчлөлтийг хадгалах боломжгүй"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_cheeseburger
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chicken
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Close"
msgstr "Хаах"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_club
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.coke
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Кока-Кола"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Өнгө"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Delete"
msgstr "Устгах"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Desk Organizer"
msgstr "Ширээний тавиур"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Уух зүйлс"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Duplicate"
msgstr "Хуулбарлан үүсгэх"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""
"Захиалгын принтер бүр нь IoT хайрцаг / техник хэрэгслийг тодорхойлдог IP хаягтай\n"
"Принтерийг олж болох прокси, барааны ангиллын жагсаалт.\n"
"               Захиалгын принтер нь зөвхөн категорийн аль нэг бүтээгдэхүүнд хамаарах бүтээгдэхүүний шинэчлэлтийг хэвлэх болно."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Edit"
msgstr "Засах"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Борлуулалтын цэг дээр тооцоо хуваахыг идэвхжүүлнэ."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Давхар"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Давхарын нэр"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Давхарын Төлөвлөлтүүд"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Floors"
msgstr "Давхарууд"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_funghi
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Green"
msgstr "Ногоон"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Grey"
msgstr "Саарал"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "Зочид"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Guests ?"
msgstr "Зочид ?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Guests:"
msgstr "Зочид:"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Өндөр"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Хэвтээ Байрлал"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Хэрвээ буруу бол, ширээ нь идэвхгүй болж борлуулалтын цэг дээр боломжгүй "
"байна"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Баар/Ресторан эсэх"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Led Lamp"
msgstr "Led гэрэл"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Light grey"
msgstr "Цайвар саарал"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_maki
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_salmon
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_temaki
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Manage table orders"
msgstr "Хүснэгтийн захиалгуудыг удирдах"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_margherita
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.minute_maid
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Monitor Stand"
msgstr "Дэлгэцийн суурь"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_mozza
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr "ШИНЭ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr "Тэмдэглэл"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/notes.xml:0
#: code:addons/pos_restaurant/static/src/xml/notes.xml:0
#, python-format
msgid "Note"
msgstr "Тэмдэглэл"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Note added by the waiter."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/printbill.js:0
#, python-format
msgid "Nothing to Print"
msgstr "Хэвлэх зүйл алга"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Number of Seats ?"
msgstr "Суудлын тоо ?"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Odoo Logo"
msgstr "Odoo лого"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "Ok"
msgstr "Тийм"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Orange"
msgstr "Оранж"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "Order"
msgstr "Захиалга"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "Захиалгын принтер"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "Захиалгын Хэвлэгч"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"Зөөгч захиалгыг шинэчлэх үед ресторанууд болон барууд гал тогоо/бар дахь "
"захиалгын шинэчлэлүүдийг хэвлэхэд Захиалгын Принтерүүдийг ашигладаг."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Orderline Notes"
msgstr "Захиалгын мөрийн тэмдэглэлүүд"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "POS хэвлэгч"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "PRO FORMA"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_4formaggi
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bolo
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "Payment"
msgstr "Төлбөр"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "Борлуулалтын цэг"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Борлуулалтын цэгийн тохиргоо"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "ПОС захиалгын мөр"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "ПОС захиалга"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Price"
msgstr "Үнэ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "Print"
msgstr "Хэвлэх"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "Гал тогоо, бааранд захиалга хэвлэх гэх мэт."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Хэвлэгдсэн барааны ангилал"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr "Хэвлэгчийн нэр"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Printers"
msgstr "Хэвлэгчүүд"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Proxy IP Хаяг"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Purple"
msgstr "Нил ягаан"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Quantity"
msgstr "Тоо хэмжээ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Red"
msgstr "Улаан"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr "Хассан ширээг буцаах боломжгүй"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Rename"
msgstr "Нэр өөрчлөх"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Рестораны давхар"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Рестораны давхарууд"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr "Рестораны Захиалгын Хэвлэгчүүд"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr "Рестораны принтер"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Рестораны ширээ"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Дугуй"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Round Shape"
msgstr "Дугуй хэлбэртэй"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chirashi
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "Суудлууд"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Хэлбэр"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_skip
msgid "Skip line when sending ticket to kitchen printers."
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_tuna
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "Split"
msgstr "Хуваах"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Split total or order lines"
msgstr "Нийт эсвэл захиалгын мөрийг хуваах"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Талбай"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Square Shape"
msgstr "Дөрвөлжин хэлбэр"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr "Хүснэгт"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Table Management"
msgstr "Хүснэгтийн менежмент"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Ширээний нэр"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Table Name ?"
msgstr "Ширээний нэр ?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Хүснэгт"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "Хэвлэгчийн тоног төхөөрөмжийн проксийн IP хаяг эсвэл хостын нэр"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "Энэ захиалгаар үйлчлүүлсэн үйлчлүүлэгчдийн хэмжээ."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid ""
"The background color of the floor layout, (must be specified in a html-"
"compatible format)"
msgstr ""
"Давхарын зохион байгуулалтын дэвсгэр өнгө, (html-д тохирох форматаар "
"тодорхойлсон байх ёстой)"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "Энэ ширээн дээр үйлчлүүлсэн үйлчлүүлэгчдийн тооны анхны утга."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__table_ids
msgid "The list of tables in this floor"
msgstr "Энэ давхар дахь ширээнүүдийн жагсаалт"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "Энэхүү борлуулалтын цэгээр үйлчилдэг рестораны давхарууд."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "Энэ захиалгыг хүргэсэн ширээ"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr "Үнэн зөв CSS 'дэвсгэр' утгатай ширээний өнгө"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "Ширээний өндөр, цэгээр"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr "Ширээний зүүн талаас төв хүртэлх ширээний хэвтээ байрлал, цэгээр"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr "Ширээний дээд хэсгээс төв хүртэлх ширээний босоо байрлал, цэгээр"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "Ширээний өргөн, цэгээр"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/printbill.js:0
#, python-format
msgid "There are no order lines"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "This floor has no tables yet, use the"
msgstr "Энэ давхарт одоогоор ширээ алга, дараахийг ашиглана уу"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Tint"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Transfer"
msgstr "Шилжүүлгэ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Turquoise"
msgstr "Оюу"

#. module: pos_restaurant
#: model:product.product,uom_name:pos_restaurant.coke
#: model:product.product,uom_name:pos_restaurant.minute_maid
#: model:product.product,uom_name:pos_restaurant.pos_food_4formaggi
#: model:product.product,uom_name:pos_restaurant.pos_food_bacon
#: model:product.product,uom_name:pos_restaurant.pos_food_bolo
#: model:product.product,uom_name:pos_restaurant.pos_food_cheeseburger
#: model:product.product,uom_name:pos_restaurant.pos_food_chicken
#: model:product.product,uom_name:pos_restaurant.pos_food_chirashi
#: model:product.product,uom_name:pos_restaurant.pos_food_club
#: model:product.product,uom_name:pos_restaurant.pos_food_funghi
#: model:product.product,uom_name:pos_restaurant.pos_food_maki
#: model:product.product,uom_name:pos_restaurant.pos_food_margherita
#: model:product.product,uom_name:pos_restaurant.pos_food_mozza
#: model:product.product,uom_name:pos_restaurant.pos_food_salmon
#: model:product.product,uom_name:pos_restaurant.pos_food_temaki
#: model:product.product,uom_name:pos_restaurant.pos_food_tuna
#: model:product.product,uom_name:pos_restaurant.pos_food_vege
#: model:product.product,uom_name:pos_restaurant.water
#: model:product.template,uom_name:pos_restaurant.coke_product_template
#: model:product.template,uom_name:pos_restaurant.minute_maid_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_4formaggi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bacon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bolo_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_cheeseburger_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chicken_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chirashi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_club_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_funghi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_maki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_margherita_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_mozza_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_salmon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_temaki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_tuna_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_vege_product_template
#: model:product.template,uom_name:pos_restaurant.water_product_template
msgid "Units"
msgstr "Нэгж"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__sequence
msgid "Used to sort Floors"
msgstr "Давхруудыг ангилахад хэрэглэнэ"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_vege
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Босоо Байрлал"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.water
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Ус"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Whiteboard Pen"
msgstr "Цагаан самбарын үзэг"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Өргөн"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "With a"
msgstr "дээр нь"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Yellow"
msgstr "Шар"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid ""
"You must be connected to the internet to save your changes.\n"
"\n"
"Changes made to previously synced orders will get lost at the next sync.\n"
"Orders that where not synced before will be synced next time you open and close the same table."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "at"
msgstr "at"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "at table"
msgstr "ширээ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr "шинэ ширээ үүсгэхэд засварлах багажийн мөр дахь товч."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "discount"
msgstr "хөнгөлөлт"
