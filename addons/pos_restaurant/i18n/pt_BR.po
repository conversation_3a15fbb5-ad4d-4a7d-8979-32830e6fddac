# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:11+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: Éder Brito <<EMAIL>>, 2021\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "Use uma impressora conectada à IoT Box"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 3.12"
msgstr "R$ 3,12"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 4.40"
msgstr "R$ 4,40"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 4.50"
msgstr "R$ 4,50"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 8.50"
msgstr "R$ 8,50"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"Para bares e restaurantes\" "
"role=\"img\" aria-label=\"Para bares e restaurantes\"/>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"pos-change_amount\">$ 0.86</span>"
msgstr "<span class=\"pos-change_amount\">R$ 0,86</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"pos-change_title\">Change</span>"
msgstr "<span class=\"pos-change_title\">Troco</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"total-amount-formatting\">TOTAL</span>"
msgstr "<span class=\"total-amount-formatting\">TOTAL</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span id=\"total-amount\" class=\"pos_total-amount\">$ 469.14</span>"
msgstr "<span id=\"total-amount\" class=\"pos_total-amount\">R$ 469,14</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span>$ 470.00</span>"
msgstr "<span>R$ 470,00</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span>Cash (USD):</span>"
msgstr "<span>Dinheiro (BRL):</span>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Nome do Andar: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr "<strong>Ponto de Venda: </strong>"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_image
msgid ""
"A background image used to display a floor layout in the point of sale "
"interface"
msgstr ""
"Uma imagem de fundo usado para exibir um layout de chão no ponto de "
"interface de venda"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Ativo"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Add"
msgstr "Adicionar"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/notes.js:0
#, python-format
msgid "Add Note"
msgstr "Adicionar Nota"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Add button"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add notes to orderlines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom notes on Orderlines."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Allow to print bill before payment"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "Uma identificação interna de uma mesa"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr "Uma identificação interna da impressora"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__name
msgid "An internal identification of the restaurant floor"
msgstr "Uma identificação interna do piso do restaurante"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Aparência"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Are you sure ?"
msgstr "Tem certeza?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "Back"
msgstr "Voltar"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Back to floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Cor de Fundo"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Imagem De Fundo"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bacon
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "Bill"
msgstr "Conta"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Impressão de Conta"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Dividir Conta "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Blue"
msgstr "Azul"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr "Cancelado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Changes could not be saved"
msgstr "Alterações não puderam ser salvas"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_cheeseburger
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chicken
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Close"
msgstr "Fechar"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_club
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.coke
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Cor"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Criado em"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Delete"
msgstr "Excluir"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Desk Organizer"
msgstr "Organizador de mesa"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Duplicate"
msgstr "Duplicar"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Piso"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Nome do Piso"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Planta do Piso"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Floors"
msgstr ""

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Comida"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_funghi
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Green"
msgstr "Verde"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Grey"
msgstr "Cinza"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "Convidados"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Guests ?"
msgstr "Convidados ?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Guests:"
msgstr "Convidados:"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Altura"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Posição Horizontal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "É um Bar ou Restaurant"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Led Lamp"
msgstr "Lâmpada Led"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Light grey"
msgstr "Cinza claro"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_maki
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_salmon
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_temaki
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Manage table orders"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_margherita
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.minute_maid
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr ""

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Monitor Stand"
msgstr "Suporte para monitor"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_mozza
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr "Novo"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr "NOTA"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/notes.xml:0
#: code:addons/pos_restaurant/static/src/xml/notes.xml:0
#, python-format
msgid "Note"
msgstr "Nota"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Note added by the waiter."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/printbill.js:0
#, python-format
msgid "Nothing to Print"
msgstr "Nada para Imprimir"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Number of Seats ?"
msgstr "Número de assentos ?"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Odoo Logo"
msgstr "Logo do Odoo"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Orange"
msgstr "Laranjado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "Order"
msgstr "Pedido"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "Impressoras de Pedido"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Orderline Notes"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "Impressora PDV"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "PRO FORMA"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_4formaggi
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bolo
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "Payment"
msgstr "Pagamento"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "Ponto de Venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuração do Ponto de Vendas"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Linhas de pedido de Ponto de Venda"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pedidos do Ponto de Vendas"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Price"
msgstr "Preço"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:0
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Print orders at the kitchen, at the bar, etc."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Categorias de produto impresso"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr "Nome da Impressora"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Printers"
msgstr "Impressoras"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Endereço IP do Proxy"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Purple"
msgstr "Roxo"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Quantity"
msgstr "Quantidade"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Red"
msgstr "Vermelho"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Rename"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Piso do Restaurante"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Pisos do Restaurante"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr "Impressoras de Pedido do Restaurante"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Mesa do Restaurante"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Round Shape"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chirashi
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "Assentos"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Seqüência"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Forma"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_skip
msgid "Skip line when sending ticket to kitchen printers."
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_tuna
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "Split"
msgstr "Dividir"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Split total or order lines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Quadrado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Square Shape"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr "Mesa"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Table Management"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Nome da Mesa"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid "Table Name ?"
msgstr "Nome da Mesa ?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Mesas"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "O endereço IP ou nome do host do proxy de hardware da impressora"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid ""
"The background color of the floor layout, (must be specified in a html-"
"compatible format)"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "O número padrão de clientes servidos nesta mesa."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__table_ids
msgid "The list of tables in this floor"
msgstr "A lista de mesas neste piso"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/printbill.js:0
#, python-format
msgid "There are no order lines"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "This floor has no tables yet, use the"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Tint"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Transfer"
msgstr "Transferir"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Turquoise"
msgstr ""

#. module: pos_restaurant
#: model:product.product,uom_name:pos_restaurant.coke
#: model:product.product,uom_name:pos_restaurant.minute_maid
#: model:product.product,uom_name:pos_restaurant.pos_food_4formaggi
#: model:product.product,uom_name:pos_restaurant.pos_food_bacon
#: model:product.product,uom_name:pos_restaurant.pos_food_bolo
#: model:product.product,uom_name:pos_restaurant.pos_food_cheeseburger
#: model:product.product,uom_name:pos_restaurant.pos_food_chicken
#: model:product.product,uom_name:pos_restaurant.pos_food_chirashi
#: model:product.product,uom_name:pos_restaurant.pos_food_club
#: model:product.product,uom_name:pos_restaurant.pos_food_funghi
#: model:product.product,uom_name:pos_restaurant.pos_food_maki
#: model:product.product,uom_name:pos_restaurant.pos_food_margherita
#: model:product.product,uom_name:pos_restaurant.pos_food_mozza
#: model:product.product,uom_name:pos_restaurant.pos_food_salmon
#: model:product.product,uom_name:pos_restaurant.pos_food_temaki
#: model:product.product,uom_name:pos_restaurant.pos_food_tuna
#: model:product.product,uom_name:pos_restaurant.pos_food_vege
#: model:product.product,uom_name:pos_restaurant.water
#: model:product.template,uom_name:pos_restaurant.coke_product_template
#: model:product.template,uom_name:pos_restaurant.minute_maid_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_4formaggi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bacon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bolo_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_cheeseburger_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chicken_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chirashi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_club_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_funghi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_maki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_margherita_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_mozza_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_salmon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_temaki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_tuna_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_vege_product_template
#: model:product.template,uom_name:pos_restaurant.water_product_template
msgid "Units"
msgstr "Unidades"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__sequence
msgid "Used to sort Floors"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_vege
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr "Vegetariano"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Posição Vertical"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.water
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Água"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Whiteboard Pen"
msgstr "Caneta do quadro branco"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Largura"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "With a"
msgstr "Com um(a)"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "Yellow"
msgstr "Amarelo"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:0
#, python-format
msgid ""
"You must be connected to the internet to save your changes.\n"
"\n"
"Changes made to previously synced orders will get lost at the next sync.\n"
"Orders that where not synced before will be synced next time you open and close the same table."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "at"
msgstr "em"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "at table"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:0
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:0
#, python-format
msgid "discount"
msgstr "desconto"
