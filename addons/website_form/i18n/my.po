# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_form
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:13+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: Hein Myat Phone <<EMAIL>>, 2019\n"
"Language-Team: Burmese (https://www.transifex.com/odoo/teams/41243/my/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: my\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "&amp;times;"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Add a custom field"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Add an existing field"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Cancel"
msgstr "ဖကြျသိမျးသညျ"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Change Form Parameters"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Checkbox"
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Date"
msgstr "နေ့စွဲ"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Date &amp; Time"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Decimal Number"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Email"
msgstr "အီးမေးလ်"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid "Email To"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Error"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "File Upload"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Hidden"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_label
msgid "Label for form action"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Long Text"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Metadata"
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model
msgid "Models"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Multiple Checkboxes"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Number"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Option 1"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Option 2"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Option 3"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Other Information:"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Phone Number"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Please fill in the form correctly."
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Radio Buttons"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Required"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Save"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Selection"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form
msgid "Send"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Subject"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Success"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_res_config_settings__website_form_enable_metadata
#: model:ir.model.fields,field_description:website_form.field_website__website_form_enable_metadata
msgid "Technical data on contact form"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Text"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "Thanks!"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.res_config_settings_view_form
msgid "Track metadata (IP, User Agent, ...) on your Website Forms"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "We will get back to you shortly."
msgstr ""

#. module: website_form
#: model:ir.model,name:website_form.model_website
msgid "Website"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.ir_model_view
msgid "Website Forms"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_res_config_settings__website_form_enable_metadata
#: model:ir.model.fields,help:website_form.field_website__website_form_enable_metadata
msgid "You can choose to log technical data like IP, User Agent ,..."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "You can't duplicate an item which refers to an actual record."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Your Company"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Your Name"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Your Question"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "Your message has been sent successfully."
msgstr ""
