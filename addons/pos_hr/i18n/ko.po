# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2020\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"
msgstr "<span class=\"o_form_label oe_edit_only\">허용된 사원 </span>"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "계산원"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/chrome.js:0
#: code:addons/pos_hr/static/src/js/screens.js:0
#, python-format
msgid "Change Cashier"
msgstr "계산원 변경"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Close session"
msgstr "세션 닫기"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "직원"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "직원 : %s - PoS 설정 : %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
msgid "Employees with access"
msgstr "접근 권한이 있는 직원"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr "비워두면, 모든 직원들이 PoS 세션에 로그인 할 수 있습니다"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Incorrect Password"
msgstr "잘못된 비밀번호"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/chrome.js:0
#, python-format
msgid "Lock"
msgstr "잠금"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Log in to"
msgstr "로그인"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr "PoS 앱에 대한 관리자 액세스 권한이 있는 사용자만 주문시 제품 가격을 수정할 수 있습니다."

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Password ?"
msgstr "비밀번호 ?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr "금전 등록기를 사용하는 사람. 구제자, 학생 또는 임시 직원 일 수 있습니다."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "점포판매시스템 환경설정"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "점포판매시스템 주문"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "점포판매시스템 주문 보고서"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Price Control"
msgstr "가격 관리"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Restrict price modification to managers"
msgstr "관리자에 대한 가격 수정 제한"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Scan your badge"
msgstr "배치 검색"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Select Cashier"
msgstr "계산원 선택"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Select User"
msgstr "사용자 선택"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr "활성화 중인 PoS 세션을 사용하는 사원은 삭제할 수가 없으니, 먼저 세션(들)을 닫아주세요: \n"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "or"
msgstr "또는"
