# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"
msgstr "<span class=\"o_form_label oe_edit_only\">Povolení zamestnanci </span>"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "Pokladník"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/chrome.js:0
#: code:addons/pos_hr/static/src/js/screens.js:0
#, python-format
msgid "Change Cashier"
msgstr "Zmeniť pokladníka"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Close session"
msgstr "Uzavretie relácie"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "Zamestnanec"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "Zamestnanec: %s - PoS Config(s): %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
msgid "Employees with access"
msgstr "Zamestnanci s prístupom"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr ""
"Ak zostanú prázdne, všetci zamestnanci sa môžu prihlásiť do relácie PoS"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Incorrect Password"
msgstr "Nesprávne heslo"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/chrome.js:0
#, python-format
msgid "Lock"
msgstr "Zamknúť"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Log in to"
msgstr "Prihlásiť sa"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Iba užívatelia s právami manažéra POS môžu meniť ceny produktov na "
"objednávkach."

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Password ?"
msgstr "Heslo ?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Osoba ktorá používa pokladnicu. Môže to by výpomoc, študent alebo dočasný "
"zamestnanec."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Konfigurácia miesta predaja"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "Objednávky miesta predaja"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Report objednávok miesta predaja"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Price Control"
msgstr "Kontrola ceny"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Restrict price modification to managers"
msgstr "Obmedziť zmenu ceny pre manažérov"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Zosnímaj kartu"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Select Cashier"
msgstr "Výber pokladníka"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Select User"
msgstr "Vybrať používateľa"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"Nemôžete vymazať zamestnanca, ktorý môže byť použitý v aktívnej relácii PoS,"
" najskôr zatvorte reláciu:\n"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "or"
msgstr "alebo"
