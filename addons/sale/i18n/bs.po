# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <kriz<PERSON><PERSON>.<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "# Linija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "# naloga za prodaju"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
#: model:mail.template,report_name:sale.mail_template_sale_confirmation
msgid "${(object.name or '').replace('/','_')}"
msgstr "${(object.name or '').replace('/','_')}"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
msgid ""
"${object.company_id.name} ${(object.get_portal_last_transaction().state == "
"'pending') and 'Pending Order' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} ${(object.get_portal_last_transaction().state == "
"'pending') and 'Nepotvrđena narudžba' or 'Narudžba'} (Ref ${object.name or "
"'n/a' })"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Quotation' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Predračun' or 'Narudžba'} (Ref ${object.name or 'n/a' })"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content_totals_table
msgid "&amp;nbsp;<span>on</span>&amp;nbsp;"
msgstr "&amp;nbsp;<span>na</span>&amp;nbsp;"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "&amp;times;"
msgstr "&amp;puta;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'PREDRAČUN - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Predračun - %s' % (object.name)) or "
"'Narudžba - %s' % (object.name)"

#. module: sale
#: model:product.product,description_sale:sale.product_product_4e
#: model:product.product,description_sale:sale.product_product_4f
#: model:product.template,description_sale:sale.product_product_4e_product_template
msgid "160x80cm, with large legs."
msgstr "160x80cm, s velikim nogama."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        % set transaction = object.get_portal_last_transaction()\n"
"        Your order <strong>${object.name}</strong> amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"        % if object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized')) :\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        % elif transaction and transaction.state == 'pending' :\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            % if object.reference:\n"
"                Your payment reference is <strong>${object.reference}</strong>.\n"
"            % endif\n"
"        % endif\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/><br/>\n"
"    </p>\n"
"% if object.website_id:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td width=\"18%\"><strong>Products</strong></td>\n"
"                <td/>\n"
"                <td><strong>Quantity</strong></td>\n"
"                <td width=\"10%\" align=\"center\"><strong>Price</strong></td>\n"
"            </tr>\n"
"        </table>\n"
"        % for line in object.order_line:\n"
"            % if not line.is_delivery and line.display_type in ['line_section', 'line_note']:\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <tr style=\"${loop.cycle('background-color: #f2f2f2', 'background-color: #ffffff')}\">\n"
"                        <td colspan=\"4\">\n"
"                            % if line.display_type == 'line_section':\n"
"                                <strong>${line.name}</strong>\n"
"                            % elif line.display_type == 'line_note':\n"
"                                <i>${line.name}</i>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            % elif not line.is_delivery\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <tr style=\"${loop.cycle('background-color: #f2f2f2', 'background-color: #ffffff')}\">\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img src=\"/web/image/product.product/${line.product_id.id}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\">${line.product_id.name}</td>\n"
"                        <td>${line.product_uom_qty}</td>\n"
"                        % if object.user_id.has_group('account.group_show_line_subtotals_tax_excluded'):\n"
"                            <td align=\"right\">${format_amount(line.price_reduce_taxexcl, object.currency_id)}</td>\n"
"                        % endif\n"
"                        % if object.user_id.has_group('account.group_show_line_subtotals_tax_included'):\n"
"                            <td align=\"right\">${format_amount(line.price_reduce_taxinc, object.currency_id)}</td>\n"
"                        % endif\n"
"                    </tr>\n"
"                </table>\n"
"            % endif\n"
"        % endfor\n"
"    </div>\n"
"    % if object.carrier_id:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Delivery:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\">${format_amount(object.amount_delivery, object.currency_id)}</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\">${format_amount(object.amount_untaxed, object.currency_id)}</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % else:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\">${format_amount(object.amount_untaxed, object.currency_id)}</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % endif\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Taxes:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\">${format_amount(object.amount_tax, object.currency_id)}</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Total:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\">${format_amount(object.amount_total, object.currency_id)}</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % if object.partner_invoice_id:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <strong>Bill to:</strong>\n"
"                    ${object.partner_invoice_id.street or ''}\n"
"                    ${object.partner_invoice_id.city or ''}\n"
"                    ${object.partner_invoice_id.state_id.name or ''}\n"
"                    ${object.partner_invoice_id.zip or ''}\n"
"                    ${object.partner_invoice_id.country_id.name or ''}\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Payment Method:</strong>\n"
"                    % if transaction.payment_token_id:\n"
"                        ${transaction.payment_token_id.name}\n"
"                    % else:\n"
"                        ${transaction.acquirer_id.name}\n"
"                    % endif\n"
"                     (${format_amount(transaction.amount, object.currency_id)})\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % endif\n"
"    % if object.partner_shipping_id and not object.only_services:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <strong>Ship to:</strong>\n"
"                    ${object.partner_shipping_id.street or ''}\n"
"                    ${object.partner_shipping_id.city or ''}\n"
"                    ${object.partner_shipping_id.state_id.name or ''}\n"
"                    ${object.partner_shipping_id.zip or ''}\n"
"                    ${object.partner_shipping_id.country_id.name or ''}\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        % if object.carrier_id:\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Shipping Method:</strong>\n"
"                    ${object.carrier_id.name}\n"
"                    % if object.carrier_id.fixed_price == 0.0:\n"
"                        (Free)\n"
"                    % else:\n"
"                        (${format_amount(object.carrier_id.fixed_price, object.currency_id)})\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        % endif\n"
"    </div>\n"
"    % endif\n"
"% endif\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Pozdrav,\n"
"        <br/><br/>\n"
"        % set transaction = object.get_portal_last_transaction()\n"
"        Vaša narudžba <strong>${object.name}</strong> u iznosu do <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"        % if object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized')) :\n"
"            je potvrđena.<br/>\n"
"            Zahvaljujemo vam na povjerenju!\n"
"        % elif transaction and transaction.state == 'pending' :\n"
"            je na čekanju. Bit će potvrđena kada zaprimimo uplatu.\n"
"            % if object.reference:\n"
"                Vaša referenca uplate je <strong>${object.reference}</strong>.\n"
"            % endif\n"
"        % endif\n"
"        <br/><br/>\n"
"        Ako imate bilo kakvih pitanja ili nejasnoća, slobodno nas kontaktirajte.\n"
"        <br/><br/>\n"
"    </p>\n"
"% if object.website_id:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td width=\"18%\"><strong>Proizvodi</strong></td>\n"
"                <td/>\n"
"                <td><strong>Količina</strong></td>\n"
"                <td width=\"10%\" align=\"center\"><strong>Cijena</strong></td>\n"
"            </tr>\n"
"        </table>\n"
"        % for line in object.order_line:\n"
"            % if not line.is_delivery and line.display_type in ['line_section', 'line_note']:\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <tr style=\"${loop.cycle('background-color: #f2f2f2', 'background-color: #ffffff')}\">\n"
"                        <td colspan=\"4\">\n"
"                            % if line.display_type == 'line_section':\n"
"                                <strong>${line.name}</strong>\n"
"                            % elif line.display_type == 'line_note':\n"
"                                <i>${line.name}</i>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            % elif not line.is_delivery\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <tr style=\"${loop.cycle('background-color: #f2f2f2', 'background-color: #ffffff')}\">\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img src=\"/web/image/product.product/${line.product_id.id}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\">${line.product_id.name}</td>\n"
"                        <td>${line.product_uom_qty}</td>\n"
"                        % if object.user_id.has_group('account.group_show_line_subtotals_tax_excluded'):\n"
"                            <td align=\"right\">${format_amount(line.price_reduce_taxexcl, object.currency_id)}</td>\n"
"                        % endif\n"
"                        % if object.user_id.has_group('account.group_show_line_subtotals_tax_included'):\n"
"                            <td align=\"right\">${format_amount(line.price_reduce_taxinc, object.currency_id)}</td>\n"
"                        % endif\n"
"                    </tr>\n"
"                </table>\n"
"            % endif\n"
"        % endfor\n"
"    </div>\n"
"    % if object.carrier_id:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Isporuka:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\">${format_amount(object.amount_delivery, object.currency_id)}</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Podzbir:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\">${format_amount(object.amount_untaxed, object.currency_id)}</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % else:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Podzbir:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\">${format_amount(object.amount_untaxed, object.currency_id)}</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % endif\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Porez:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\">${format_amount(object.amount_tax, object.currency_id)}</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Ukupno:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\">${format_amount(object.amount_total, object.currency_id)}</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % if object.partner_invoice_id:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <strong>Fakturisati na:</strong>\n"
"                    ${object.partner_invoice_id.street or ''}\n"
"                    ${object.partner_invoice_id.city or ''}\n"
"                    ${object.partner_invoice_id.state_id.name or ''}\n"
"                    ${object.partner_invoice_id.zip or ''}\n"
"                    ${object.partner_invoice_id.country_id.name or ''}\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Način plaćanja:</strong>\n"
"                    % if transaction.payment_token_id:\n"
"                        ${transaction.payment_token_id.name}\n"
"                    % else:\n"
"                        ${transaction.acquirer_id.name}\n"
"                    % endif\n"
"                     (${format_amount(transaction.amount, object.currency_id)})\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    % endif\n"
"    % if object.partner_shipping_id and not object.only_services:\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <strong>Poslati na adresu:</strong>\n"
"                    ${object.partner_shipping_id.street or ''}\n"
"                    ${object.partner_shipping_id.city or ''}\n"
"                    ${object.partner_shipping_id.state_id.name or ''}\n"
"                    ${object.partner_shipping_id.zip or ''}\n"
"                    ${object.partner_shipping_id.country_id.name or ''}\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        % if object.carrier_id:\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Način slanja:</strong>\n"
"                    ${object.carrier_id.name}\n"
"                    % if object.carrier_id.fixed_price == 0.0:\n"
"                        (besplatno)\n"
"                    % else:\n"
"                        (${format_amount(object.carrier_id.fixed_price, object.currency_id)})\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        % endif\n"
"    </div>\n"
"    % endif\n"
"% endif\n"
"</div>"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        % set doc_name = 'quotation' if object.state in ('draft', 'sent') else 'order'\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        % if ctx.get('proforma'):\n"
"            Pro forma invoice for ${doc_name} <strong>${object.name}</strong>\n"
"            % if object.origin:\n"
"                (with reference: ${object.origin} )\n"
"            % endif\n"
"            amounting in <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong> is available.\n"
"        % else:\n"
"            ${doc_name} <strong>${object.name}</strong>\n"
"            % if object.origin:\n"
"                (with reference: ${object.origin} )\n"
"            % endif\n"
"            amounting in <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong> is ready for review.\n"
"        % endif\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        % set doc_name = 'quotation' if object.state in ('draft', 'sent') else 'order'\n"
"        Pozdrav,\n"
"        <br/><br/>\n"
"        Vaš\n"
"        % if ctx.get('proforma'):\n"
"            Predračun za ${doc_name} <strong>${object.name}</strong>\n"
"            % if object.origin:\n"
"                (with reference: ${object.origin} )\n"
"            % endif\n"
"            u iznosu od <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong> je dostupan.\n"
"        % else:\n"
"            ${doc_name} <strong>${object.name}</strong>\n"
"            % if object.origin:\n"
"                (with reference: ${object.origin} )\n"
"            % endif\n"
"            u iznosu od <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong> je spreman za provjeru.\n"
"        % endif\n"
"        <br/><br/>\n"
"        Ako imate bilo kakvih pitanja, slobodno nas kontaktirajte.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-clock-o\" aria-label=\"Dates\" title=\"Dates\"/>"
msgstr "<i class=\"fa fa-clock-o\" aria-label=\"Dates\" title=\"Dates\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""
"<i class=\"fa fa-comment\"/> Kontaktirajte nas da biste dobili novu ponudu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> Utisak"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/> Pošalji poruku"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Preuzimanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/>Završeno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/> <b>Plaćeno</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Čeka se plaćanje</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Isteklo"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> Otkazano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Štampaj"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Odbi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">This offer expires in</b></small>"
msgstr "<small><b class=\"text-muted\">Ova ponuda ističe za</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">Vaš avans</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Nalog prodaje #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">Potvrda emailom</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Down Payments</span>"
msgstr "<span class=\"o_form_label\">Unaprijed plaćanja</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                                <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">Kupac</span>\n"
"                                <span class=\"o_stat_text\">Pregled</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Prodano</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Iznos</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Ukupna cijena</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr "<span>Prihvatio u ime:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By paying this proposal, I agree to the following terms:</span>"
msgstr "<span>Plaćanjem ove ponude, slažem se sa sljedećim uslovima:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr ""
"<span>Potpisivanjem ove ponude, slažem se sa sljedećim uslovima:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>Pop.%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr "<span>Za iznos od:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Pro-Forma Invoice # </span>"
msgstr "<span>Predračun # </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Porezi</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr "<span>sa uslovima plaćanja:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Fakture</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Shipping Address:</strong>"
msgstr "<strong class=\"d-block mb-1\">Adresa dostave:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Podzbir</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr "<strong class=\"text-muted\">Prodavač</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Expiration Date:</strong>"
msgstr "<strong>Važi do:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration:</strong>"
msgstr "<strong>Rok važenja:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Zabilješka fiskalne pozicije:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Datum narudžbe:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Quotation Date:</strong>"
msgstr "<strong>Datum ponude:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Prodavač:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_document_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>Adresa dostave:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Potpis</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content_totals_table
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Podukupno</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>Hvala Vam!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>Ova ponuda je istekla!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr "<strong>Ova ponuda je otkazana.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content_totals_table
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Vaša referenca:</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "Potvrđeni nalozi prodaje zahtjevaju datum potvrde."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "A journal must be specified of the acquirer %s."
msgstr "Dnevnik mora biti određen od strane sakupljača %s."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "A payment acquirer is required to create a transaction."
msgstr "Sakupljač plaćanja je neophodan da se napravi transakcija."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for invoicing,"
"         according to their invoicing policy (based on ordered or delivered "
"quantity)."
msgstr ""
"Izdana je standardna faktura sa svim stavkama narudžbe i spremna za "
"fakturisanje,         u skladu sa svojom politikom fakturisanja (zasnovana "
"na naručenim ili isporučenim količinama)."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"A transaction can't be linked to sales orders having different currencies."
msgstr ""
"Transakcija se ne može povezati sa nalozima za prodaju koje imaju različite "
"valute."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"A transaction can't be linked to sales orders having different partners."
msgstr ""
"Transakcija ne može biti povezana sa nalozima za prodaju koje imaju "
"različite partnere."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"A typical example is the pre-paid hours of service,\n"
"                when you want to sell extra hours because the initial\n"
"                hours have already been used."
msgstr ""
"Tipični primjer je pretplata sati izvršene usluge,\n"
"                kada želite platiti dodatne sate jer su početni\n"
"                sati već iskorišteni."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Upozorenje se može postaviti na proizvod ili kupca (Prodaja)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Mogućnost odabira tipa pakovanja u nalozima za prodaju i forsiranje količine"
" koja je umnožak broja jedinica po jednom pakovanju."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "Prihvati &amp; plati"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Accept &amp; Pay Quotation"
msgstr "Prihvati &amp; plati narudžbu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "Prihvati &amp; potpiši"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Accept &amp; Sign Quotation"
msgstr "Prihvati &amp; potpiši narudžbu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "Upozorenje pristupa"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Zavisno od konfiguracije proizvoda, isporučene količine mogu biti automatski izračunate na osnovu mehanizama :\n"
"  - Ručno: količine se ručno unose na stavke\n"
"  - Analitički iz troškova: količina je zbir količina na proknjiženim troškovima\n"
"  - Vremenski listovi: količina je suma sati utrošena na stavkama zadataka povezanih sa ovom prodajnom stavkom\n"
"  - Kretanja zaliha: zaliha dolazi sa potvrđenih prikupljanja\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Broj računa"

#. module: sale
#: model:ir.model,name:sale.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Programčić sravnjenja računa"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "Konto za depozite"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Oznaka izuzetka aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "Dodaj napomenu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "Dodaj proizvod"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "Dodaj sekciju"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "Dodaj više varijanti na narudžbu iz pregleda"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "Adrese u prodajnim narudžbama"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:sale.field_product_template_attribute_value__is_custom
msgid "Allow users to input custom values for this attribute value"
msgstr ""
"Omogućava korisnicima da unesu proizvoljne vrijednosti za ovu vrijednost "
"atributa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "Omogućava vam da pošaljete predračun svojim kupcima"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Omogućava vam da pošaljete predračun."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Sinhr Amazon"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Amount"
msgstr "Iznos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Iznos prije popusta"

#. module: sale
#: code:addons/sale/models/payment.py:0
#, python-format
msgid "Amount Mismatch (%s)"
msgstr "Nepodudaranje iznosa (%s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "Iznos sa ponude na račun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "Analitika iz troškova"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitička stavka"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Analitičke oznake"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Retci analitike"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Primijeni ručne popuste na stavke prodajnog naloga ili prikaži popuste "
"izračunate sa cjenovnika (opcija za aktiviranje u postavkama cjenovnika)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Jeste li sigurni da želite obrisati ovu potvrđenu transakciju? Ova akcija se"
" ne može više vratiti."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "Na nabavnu cijenu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Broj priloga"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__custom_product_template_attribute_value_id
msgid "Attribute Value"
msgstr "Vrijednost atributa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Potvrđene transakcije"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Automatska faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Automatic email sent after the customer has signed or paid online"
msgstr "Automatski mail poslan nakon što je kupac potpisao ili platio online"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Naziv banke"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "Zasnovano na ID kupca"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "Zasnovano"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Blokiranje poruke"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"Povećati vašu prodaju sa dvije vrste programa rabata: promocije i kuponi. "
"Posebni uslovi se mogu postaviti (proizvodi, kupci, najmanji iznos za "
"prodaju, period). Nagrade mogu biti rabati (% ili iznos) ili besplatni "
"proizvodi."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "Može uređivati proizvod"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Otkaži"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Otkazan"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Uhvati transakciju"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "Kategorija"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__use_quotations
msgid ""
"Check this box if you send quotations to your customers rather than "
"confirming orders straight away."
msgstr ""
"Označite ovu kućicu ako šaljete ponude vašim kupcima umjesto da odmah "
"potvrđujete njihove narudžbe."

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_payment_acquirer_wizard
msgid "Choose how to confirm quotations"
msgstr "Izaberite kako da potvrđujete ponude"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Choose your default customer payment method."
msgstr "Odaberite vaš defisani metod plaćanja kupca."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Click here to add some products or services to your quotation."
msgstr ""
"Kliknite ovdje za dodavanje nekih proizvoda ili usluga na vašu ponudu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Click to define an invoicing target"
msgstr "Kliknite ovdje za definisanje cilja fakturisanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Zatvori"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__closed
msgid "Closed"
msgstr "Zatvoreno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_value__html_color
#: model:ir.model.fields.selection,name:sale.selection__product_attribute__display_type__color
msgid "Color"
msgstr "Boja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_acquirer__so_reference_type
msgid "Communication"
msgstr "Komunikacija"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Kompanija"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Izračunaj troškove otpreme i isporuči sa DHL-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Izračunaj troškove otpreme i isporuči sa Easypost-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Izračunaj troškove otpreme i isporuči sa FedEx-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Izračunaj troškove otpreme i isporuči sa UPS-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Izračunaj troškove otpreme i isporuči sa USPS-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Izračunaj troškove otpreme i isporuči sa bpost-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Izračunaj troškove otpreme na narudžbama"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "Postavke konfiguracije"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Konfiguracija"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Potvrdi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__confirmation_template_id
msgid "Confirmation Email"
msgstr "Email potvrđivanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "Konektori"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "Kontakt"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konverzija između jedinica mjera može samo da se pojavi ako pripadaju istoj "
"kategoriji. Konverzija se izvršava na osnovu omjera."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr "Kuponi & promocije"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Create Date"
msgstr "Kreiraj datum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoice"
msgstr "Kreiraj fakturu"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Kreirajte novi proizvod"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Create a new quotation, the first step of a new sale!"
msgstr "Kreiraj novu ponudu, prvi korak nove prodaje!"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoice"
msgstr "Kreiraj i pogledaj fakturu"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoices"
msgstr "Kreiraj Fakturu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
msgid "Creation Date"
msgstr "Datum kreiranja"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"Datum kreiranja nacrta/poslanog naloga prodaje, datum konfirmacije na "
"potvrđenim ponudama."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "Kreditna kartica (preko Stripe)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Kursna lista"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
#: model:product.template.attribute.value,name:sale.product_4_attribute_1_value_3
msgid "Custom"
msgstr "Prilagođeno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__custom_value
msgid "Custom Value"
msgstr "Proizvoljna vrijednost"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Proizvoljne vrijednosti"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Proizvoljne instrukcije plaćanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Kupac"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Korisnički račun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr "Adrese kupca"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "Država kupca"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "Pokrajina kupca"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "Djelatnost kupca"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "URL portala kupca"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Referenca kupca"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "Potpis kupca"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "Porezi kupca"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Kupci"

#. module: sale
#: model:product.product,name:sale.product_product_4e
#: model:product.product,name:sale.product_product_4f
#: model:product.template,name:sale.product_product_4e_product_template
msgid "Customizable Desk (CONFIG)"
msgstr "Podesivi panel (CONFIG)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize"
msgstr "Prilagodi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize the look of your quotations."
msgstr "Prilagodi izgled vaših ponuda."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express konektor"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Datum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__date_order
msgid "Date Order"
msgstr "Datum narudžbe"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_on
msgid "Date of the signature."
msgstr "Datum potpisa."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__create_date
msgid "Date on which sales order is created."
msgstr "Datum kreiranja prodajne narudžbe"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Datum:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "Odbitak avansnog plaćanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default Limit:"
msgstr "Početna granica:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr "Zadani rok važenja ponude"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr "Zadani rok važenja ponude (dana)"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr "Zadani proizvod koji se koristi za avansne uplate"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "Otpremljeni sadržaj putem emaila"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "Dostavljeno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr "Isporučeno lično"

#. module: sale
#: code:addons/sale/models/sale.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
#, python-format
msgid "Delivered Quantity"
msgstr "Isporučena količina"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Isporučene količine"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Adresa dostave"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "Datum dostave"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__partner_shipping_id
msgid "Delivery address for current invoice."
msgstr "Adresa isporuke za trenutnu fakturu."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Datum dostave koji možete obećati kupcu, izračunato na najkraće vrijeme "
"isporuke stavki narudžbe u slučaju prodaje usluga. U slučaju isporuke, "
"politika isporuke naručenih stavki će se uzeti u obzir bilo da se koristi "
"najkraće ili najduže vrijeme isporuke stavki na narudžbi."

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "Proizvod za depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Opis"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr "Digitalni sadržaj"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "Popust%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr "Popust %"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "Iznos popusta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__display_name
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma__display_name
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute__display_type
#: model:ir.model.fields,field_description:sale.field_product_attribute_value__display_type
#: model:ir.model.fields,field_description:sale.field_product_template_attribute_value__display_type
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "Prikaži vrstu"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__done
msgid "Done"
msgstr "Gotovo"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment"
msgstr "Predujam"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "Iznos predujma"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount(Fixed)"
msgstr "Iznos predujma (fiksni)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "Proizvod predujma"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment: %s"
msgstr "Predujam: %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "Predujam (fiksni iznos)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "Predujam (procenat)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment of %s%%"
msgstr "Predujam za %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"Predujmovi se prave kada pravite fakture iz naloga za prodaju. Oni se ne "
"kopiraju kada se dupliciraju nalozi za prodaju."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Download"
msgstr "Preuzimanje"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
msgid "Draft Quotation"
msgstr "Predračun u pripremi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost Konektor"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Uredi konfiguraciju"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "E-Mail"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__template_id
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Email Template"
msgstr "Predložak email-a"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__confirmation_template_id
msgid "Email sent to the customer once the order is paid."
msgstr "Email poslan kupcu nakon što je ponuda plaćena."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Očekivani datum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "Očekivano:"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""
"Troškovi i računi dobavljača se mogu prefakturisati kupcima. Sa ovom "
"opcijom, potvrđeni troškovi se mogu prefakturisati kupcu bilo po nastalom "
"trošku ili po prodajnoj cijeni."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "Rok trajanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Prošireni filteri"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "External Link"
msgstr "Vanjski link"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Dodatne vrijednosti"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Extra line with %s "
msgstr "Dodatna stavka sa %s "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx Konektor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"Fiskalne pozicije se koriste da se usklade porezi i konta za određene kupce "
"ili prodajne naloge/fakture. Zadana vrijednost dolazi od kupca."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr ""
"Zabranjene vrijednosti za neračunovodstvene prodajne stavke na narudžbi"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Besplatna registracija"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Iz ovog izvještaja, možete dobiti pregled iznosa koji je fakturisan vašem "
"kupcu. Alat za pretragu se također može koristiti za prilagođavanje vaših "
"izvještaja fakturisanja i slično, podesiti ovu analizu vašim potrebama."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "U potpunosti fakturisano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Generiše link za plaćanje prodanog"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "Generiši link za plaćanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "Generiše fakturu automatski kada je potvrđeno plaćanje online"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "Primi upozorenja na narudžbama za proizvode ili kupce"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Dopusti popuste na stavkama prodajnog naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Bruto težina"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr "HTML index boje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "Ima predujme"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_attribute_value__html_color
#: model:ir.model.fields,help:sale.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color if the attribute type is 'Color'."
msgstr ""
"Ovdje možete postaviti određeni HTML indeks boje (npr. #ff0000) da prikaže "
"boju ako je tip atributa 'Boja'."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "History"
msgstr "Istorija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__id
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma__id
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "Znak"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona koja označava izuzetnu aktivnost."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
#: model:ir.model.fields,help:sale.field_sale_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ukoliko je označeno, neke poruke će imati grešku isporuke."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"Ako je prodaja zaključana, više ne možete mijenjati. Ipak, još uvijek možete"
" fakturisati ili isporučiti."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid ""
"If we store your payment information on our server, subscription payments "
"will be made automatically."
msgstr ""
"Ako držimo vaše informacije o plaćanju na našem serveru, plaćanje pretplate "
"će biti izvršeno automatski."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr "Ako promijenite cjenovnik, samo novododane stavke će biti izmjenjene."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "Uvoz narudžbi od Amazona i sinhr isporuka"

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Uvoz šablona za proizvode"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "uklj. porez)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "Konto prihoda"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid order."
msgstr "Nevaljana narudžba."

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "Nevaljan datum potpisa."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Invalid token found! Token acquirer %s != %s"
msgstr "Pronađen nevaljan token! Dostavljač tokena %s != %s"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Invalid token found! Token partner %s != %s"
msgstr "Pronađen nevaljan token! Token partnera %s != %s"

#. module: sale
#: code:addons/sale/models/account_invoice.py:0
#, python-format
msgid "Invoice %s paid"
msgstr "Faktura %s plaćena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Adresa fakture"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Faktura potvrđena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "Brojač faktura"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Faktura kreirana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Stavke fakture"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Fakturiši prodajnu narudžbu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
msgid "Invoice Status"
msgstr "Status fakture"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Prihod po fakturama za trenutni mjesec. Ovo je iznos koji je prodajni kanal "
"fakturisao ovog mjeseca. Koristi se za izračun odnosa napretka trenutnog i "
"ciljnog prihoda u pregledu kanban."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "Faktura za dostavljeno"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "Faktura za naručeno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Fakturisano"

#. module: sale
#: code:addons/sale/models/sale.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
#, python-format
msgid "Invoiced Quantity"
msgstr "Fakturisana količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Fakturisano ovaj mjesec"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Fakture"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Analiza faktura"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Statistika faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."
msgstr ""
"Fakture će biti kreirane u pripremi tako da ih možete\n"
"pregledati prije validacije."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_invoice_policy
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address:"
msgstr "Adresa za slanje fakture:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Pravilo fakturisanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "Cilj fakturisanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address:"
msgstr "Adresa za isporuku i slanje fakture:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "Je predujam"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:sale.field_product_template_attribute_value__is_custom
msgid "Is custom value"
msgstr "Je proizvoljna vrijednost"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "Je trošak"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is expired"
msgstr "Je isteklo"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr "Je tačno ako stavke narudžbe dolaze iz troškova ili računa dobavljača"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"Zabranjeno je mijenjati sljedeća polja u zaključanoj narudžbi:\n"
"%s"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "It is not allowed to confirm an order in the following states: %s"
msgstr "Nije dopušteno odobravati narudžbu u sljedećim statusima: %s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entries"
msgstr "Dnevnički zapisi"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "Stavka dnevnika"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__just_done
msgid "Just done"
msgstr "Samo što je urađeno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value____last_update
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales____last_update
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma____last_update
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:sale.field_sale_report____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Vrijeme vođenja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Neka se kupci prijave kako bi vidjeli njihove dokumente"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"Let's create a new quotation.<br/><i>Note that colored buttons usually point"
" to the next logical actions.</i>"
msgstr ""
"Napravimo novu ponudu. <br/><i> Obratite pažnju da obojeni dugmići obično "
"pokazuju na sljedeće logičke akcije.</i>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Lock"
msgstr "Zaključaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__done
msgid "Locked"
msgstr "Zaključano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Looks great!"
msgstr "Izgleda odlično!"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Ručno"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Ručno postavi količine na narudžbu"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Marže"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "Marketing"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with a sale order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "Medijum"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Greška u isporuci poruke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Poruka za prodajnu narudžbu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Upozorenje za stavku prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Metoda za ažuriranje isporučenih količina"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Moje narudžbe"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Moje stavke prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__name
msgid "Name"
msgstr "Naziv:"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_by
msgid "Name of the person that signed the SO."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0 code:addons/sale/models/sale.py:0
#: code:addons/sale/models/sale.py:0 code:addons/sale/models/sale.py:0
#, python-format
msgid "New"
msgstr "Novi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Novi predračun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Ne"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Bez Poruka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__not_done
msgid "Not done"
msgstr "Nije urađeno"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Zabilješka"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Nema ništa za fakturisanje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr "Broj dana između potvrde narudžbe i isporuke proizvoda kupcu."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Broj grešaka"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Broj poruka sa greškom isporuke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Na poziv"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Once your quotation is ready, you can save, print or send it by email."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online Payment"
msgstr "Online plaćanje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online Signature"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__digital_signature
msgid "Online signature"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Samo vrijednost cijelog broja bi trebala biti validna."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Only draft orders can be marked as sent directly."
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Open Sales app to send your first quotation in a few clicks."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Narudžba #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#, python-format
msgid "Order Date"
msgstr "Datum narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Stavke naloga"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Order Number"
msgstr "Broj narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__name
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Referenca naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Status narudžbe"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Order signed by %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Order to Invoice"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Ordered Quantity"
msgstr "Naručena količina"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Naručene količine"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Narudžbe"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Orders to Invoice"
msgstr "Narudžbe za fakturisati"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Narudžbe za naknadno fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "Ostale informacije"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr ""

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__partner_id
msgid "Partner"
msgstr "Partner"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__country_id
msgid "Partner Country"
msgstr "Zemlja partnera"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Pay & Confirm"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay &amp; Confirm"
msgstr ""

#. module: sale
#: code:addons/sale/models/payment.py:0
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#, python-format
msgid "Pay Now"
msgstr "Plati Sad"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment acquirer"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Sticaoc plaćanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Acquirers"
msgstr "Sticaoc plaćanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Metoda plaćanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Uslovi plaćanja"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Payment method"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "Pristupni URL portala"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce
msgid "Price Reduce"
msgstr "Umanjena cijena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Umanjena cijena bez PDV-a"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Umanjena cijana s PDV"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_subtotal
msgid "Price Subtotal"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Cijenovnik"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cjenovnik"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Pricing"
msgstr "Cijene"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Print"
msgstr "Štampaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Pro-forma fakture"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Proizvod"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr "Atribut proizvoda"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__categ_id
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_qty
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_tmpl_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: sale
#: model:ir.model,name:sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
msgid "Product Variant"
msgstr "Varijante proizvoda"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Proizvodi"

#. module: sale
#: model:ir.model,name:sale.model_report_sale_report_saleproforma
msgid "Proforma Report"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Isporučena kol."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Fakturisana kol."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Kol. za fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Količine za fakturisanje sa prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "Količina"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "Količina:"

#. module: sale
#: code:addons/sale/models/sale.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#, python-format
msgid "Quotation"
msgstr "Predračun"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Predračun #"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "Ponuda / Nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Quotation Layout"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Number"
msgstr "Broj predračuna"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Predračun poslan"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid "Quotation Validity is required and must be greater than 0."
msgstr ""

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Predračun potvrđen"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Predračun poslan"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Quotation viewed by customer %s"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.model.fields,field_description:sale.field_crm_team__use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr "Predračuni"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations &amp; Orders"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Analiza predračuna"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Predračuni i Prodaja"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_attribute__display_type__radio
msgid "Radio"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Expenses"
msgstr "Re-fakturiši troškove"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Reference"
msgstr "Referenca"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request."
msgstr "Referenca na dokument koji je generisao ovu prodajnu narudžbu."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__remaining_validity_days
msgid "Remaining Days Before Expiration"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Reporting"
msgstr "Izvještavanje"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online payment to confirm orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Requested date is too soon."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid ""
"Revenue target for the current month (untaxed total of confirmed invoices)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Greška pri isporuci SMSa"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/account_reconciliation.js:0
#: code:addons/sale/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/account_reconciliation.js:0
#, python-format
msgid "Sale Orders"
msgstr "Prodajne narudžbe"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_acquirer_onboarding_wizard
msgid "Sale Payment acquire onboarding wizard"
msgstr ""

#. module: sale
#: model:utm.source,name:sale.utm_source_sale_order_0
msgid "Sale Promotion 1"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Prodaja"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Avansna faktura prodaje"

#. module: sale
#: code:addons/sale/models/sales_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Sales Analysis"
msgstr "Analiza prodaje"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Izvještaj analize prodaje"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__done
msgid "Sales Done"
msgstr "Prodaja završena"

#. module: sale
#: code:addons/sale/models/sale.py:0 model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Sales Order"
msgstr "Prodajna narudžba"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Prodajna narudžba potvrđena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "Stavka prodajnih naloga"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Stavka prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Stavke Prodajnog naloga"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Stavke prodajne narudžbe spremne za fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Stavke prodajne narudžbe vezane za moju prodajnu narudžbu"

#. module: sale
#: code:addons/sale/models/payment.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
#, python-format
msgid "Sales Order(s)"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Prodajni nalozi"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Prodajni tim"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Prodajni timovi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_report_all_channels_sales
msgid "Sales by Channel (All in One)"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:0
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Prodaja: ukupno bez PDVa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Prodavač"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Order Line"
msgstr ""

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Sample Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Pretraži prodajne narudžbe"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Odjel"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Sigurnosni token"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_attribute__display_type__select
msgid "Select"
msgstr "Odaberi"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select product attributes and optional products from the sales order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select specific invoice and delivery addresses"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Odabirom opcije \"Upozorenje\" obavijestit ćete korisnika porukom, odabirom "
"opcije \"Blokiranje poruke\" učinit ćete izuzetak s porukom i blokirati tok."
" Poruku treba napisati u slijedećem polju."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Prodaja i nabavka proizvoda u različitim jedinicama mjere"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send a quotation to test the customer portal."
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_sample_quotation
msgid "Send a sample quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Pošalji email-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send sample"
msgstr "Pošalji urnek"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Sales tab)."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set a default validity on your quotations"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Set an invoicing target: "
msgstr "Postavi cilj fakturisanja:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Set payments"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Postavi na predračun"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Postavke"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Podijeli"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr "Prevoznik"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Shipping Costs"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Sign &amp; Pay Quotation"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Potpis"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signature
msgid "Signature received through the portal."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "Prodano u zadnjih 365 dana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "Izvor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Izvorni dokument"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Stage"
msgstr "Faza"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_order_confirmation_state
msgid "State of the onboarding confirmation order step"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_sample_quotation_state
msgid "State of the onboarding sample quotation step"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_quotation_onboarding_state
msgid "State of the sale onboarding panel"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na osnovu aktivnosti\n"
"Kašnjenje: Rok je već prošao\n"
"Danas: Datum aktivnosti je današnji\n"
"Planirano: Buduće aktivnosti."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Podukupno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Tax Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_by_group
msgid "Tax amount by group"
msgstr "Iznos poreza po grupama"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Porezi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "Upotreba PDV-a za depozite"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Tehničko polje u svrhu UX "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Termini i Uslovi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Pravila i Uslovi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "Termini i uslovi..."

#. module: sale
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is cancelled. You "
"cannot register an expense on a cancelled Sales Order."
msgstr ""

#. module: sale
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is currently locked. "
"You cannot register an expense on a locked Sales Order. Please create a new "
"SO linked to this Analytic Account."
msgstr ""

#. module: sale
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s must be validated "
"before registering expenses."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "Analitički konto povezan sa prodajnom narudžbom."

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"The delivery date is sooner than the expected date.You may be unable to "
"honor the delivery date."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_attribute__display_type
#: model:ir.model.fields,help:sale.field_product_attribute_value__display_type
#: model:ir.model.fields,help:sale.field_product_template_attribute_value__display_type
msgid "The display type used in the Product Configurator."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance, taxes excluded."
msgstr ""

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"The following products cannot be restricted to the company %s because they have already been used in quotations or sales orders in another company:\n"
"%s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The order is not in a state requiring customer signature."
msgstr ""

#. module: sale
#: code:addons/sale/models/payment.py:0
#, python-format
msgid ""
"The order was not confirmed despite response from the acquirer (%s): order "
"total is %r but acquirer replied with %r."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "The ordered quantity has been updated."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance, taxes excluded."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate 1 applicable at the date of"
" the order"
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "Iznos predujma mora biti pozitivan."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There are"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0 code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"There is no invoiceable line. If a product has a Delivered quantities "
"invoicing policy, please make sure that a quantity has been delivered."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Ovaj izvještaj radi analizu vaših ponuda i prodajnih narudžbi. Analiza "
"provjerava prihode od prodaje i sortira ih prema različitim kriterijima "
"grupisanja (prodavač, partner, proizvod, itd.) Koristite ovaj izvještaj za "
"izvođenje analize na prodajama koje još nisu fakturisane. Ako želite "
"analizirati prihod, trebali bi koristiti Izvještaj analize faktura u "
"računovodstvenoj aplikaciji."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "Za fakturisati"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_total
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Ukupno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Total Price"
msgstr "Ukupna Cijena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Excluded"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "Ukupan uračunati porez"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Usluga praćenja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Transakcije"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "Type Name"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Vrsta izuzetne aktivnosti u evidenciji."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "Unit Price:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: sale
#: model:product.product,uom_name:sale.advance_product_0
#: model:product.product,uom_name:sale.product_product_4e
#: model:product.product,uom_name:sale.product_product_4f
#: model:product.template,uom_name:sale.advance_product_0_product_template
#: model:product.template,uom_name:sale.product_product_4e_product_template
msgid "Units"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Jedinice mere"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Unlock"
msgstr "Otključaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Neoporezivi iznos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Ukupno neoporezovano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "JM"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"Upsell <a href='#' data-oe-model='%s' data-oe-id='%d'>%s</a> for customer <a"
" href='#' data-oe-model='%s' data-oe-id='%s'>%s</a>"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Prilika naknadnog fakturisanja"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Use the breadcrumbs to <b>go back to preceeding screens</b>."
msgstr ""
"Koristite breadcumb-ove da bi ste se  <b>vratili na prethodni ekran</b>."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Use this menu to access quotations, sales orders and customers."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Važi do"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "View Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Volumen"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
msgid "Warning"
msgstr "Upozorenje"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "Warning for %s"
msgstr "Upozorenje za %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Upozorenje na prodajnoj narudžbi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "Upozorenje kod Prodaje ovog Proizvoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Istorija komunikacije web sajta "

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"Write the name of your customer to create one on the fly, or select an "
"existing one."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Wrong value entered!"
msgstr "Unesena je pogrešna vrijednost!"

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"You can not remove an order line once the sales order is confirmed.\n"
"You should rather set the quantity to 0."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"                or check every order and invoice them one by one."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_acquirer__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"You can upsell an order when you have delivered more quantities\n"
"                than initially ordered, with an invoicing policy based on\n"
"                ordered quantities."
msgstr ""

#. module: sale
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                    whether it's a storable product, a consumable or a service."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid ""
"Your quotation contains products from company %s whereas your quotation belongs to company %s. \n"
" Please change the company of your quotation or remove the products from other companies (%s)."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost Povezivač"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "zatvori"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "day"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "Dani"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "matching the communication of the bank statement line"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:0
#, python-format
msgid "sale order"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__amount_by_group
msgid "type: [(name, amount, base, formated amount, formated base)]"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "uninvoiced sales orders"
msgstr ""
