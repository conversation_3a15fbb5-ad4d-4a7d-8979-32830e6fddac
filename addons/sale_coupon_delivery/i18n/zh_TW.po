# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_coupon_delivery
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:14+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2019\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_coupon_delivery
#: model:ir.model.fields,help:sale_coupon_delivery.field_sale_coupon_program__reward_type
#: model:ir.model.fields,help:sale_coupon_delivery.field_sale_coupon_reward__reward_type
msgid ""
"Discount - Reward will be provided as discount.\n"
"Free Product - Free product will be provide as reward \n"
"Free Shipping - Free shipping will be provided as reward (Need delivery module)"
msgstr ""
"折扣獎勵將提供折扣。\n"
"\n"
"免費的產品-免費產品將提供獎勵\n"
"\n"
"免費送貨，免費送貨將提供獎勵（需要交付模組）"

#. module: sale_coupon_delivery
#: code:addons/sale_coupon_delivery/models/sale_order.py:0
#, python-format
msgid "Discount: "
msgstr "折扣: "

#. module: sale_coupon_delivery
#: code:addons/sale_coupon_delivery/models/sale_coupon_reward.py:0
#: model:ir.model.fields.selection,name:sale_coupon_delivery.selection__sale_coupon_reward__reward_type__free_shipping
#, python-format
msgid "Free Shipping"
msgstr "免費送貨"

#. module: sale_coupon_delivery
#: model:ir.model.fields,field_description:sale_coupon_delivery.field_sale_coupon_program__reward_type
#: model:ir.model.fields,field_description:sale_coupon_delivery.field_sale_coupon_reward__reward_type
msgid "Reward Type"
msgstr "獎勵類型"

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_coupon_program
msgid "Sales Coupon Program"
msgstr "銷售優惠計劃"

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_coupon_reward
msgid "Sales Coupon Reward"
msgstr "銷售券獎勵"

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_order
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "銷售訂單明細"

#. module: sale_coupon_delivery
#: code:addons/sale_coupon_delivery/models/sale_coupon.py:0
#: code:addons/sale_coupon_delivery/models/sale_coupon_program.py:0
#, python-format
msgid "The shipping costs are not in the order lines."
msgstr "運費不在訂單行中。"
