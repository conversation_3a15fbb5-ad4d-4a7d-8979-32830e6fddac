# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead_enrich
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:21+0000\n"
"PO-Revision-Date: 2019-09-09 12:33+0000\n"
"Last-Translator: <PERSON>, 2020\n"
"Language-Team: <PERSON> (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "(Time Now)"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "<b>Phone :</b>"
msgstr "<b>Telefon :</b>"

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "<b>Timezone : </b>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done as no email address was provided.</span>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "<span>Lead enriched based on email address</span>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.actions.server,name:crm_iap_lead_enrich.ir_cron_lead_enrichment_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_lead_enrich.ir_cron_lead_enrichment
#: model:ir.cron,name:crm_iap_lead_enrich.ir_cron_lead_enrichment
msgid "CRM: enrich leads (IAP)"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model,name:crm_iap_lead_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurasjonsinnstillinger"

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_iap_enrich_api__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: crm_iap_lead_enrich
#: model:ir.actions.server,name:crm_iap_lead_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.crm_lead_view_form
msgid "Enrich"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.crm_lead_view_form
msgid "Enrich this lead with company data based on the email address"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.crm_lead_view_form
msgid "Enrich this opportunity with company data based on the email address"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model,name:crm_iap_lead_enrich.model_ir_model_fields
msgid "Fields"
msgstr "Felter"

#. module: crm_iap_lead_enrich
#: model:ir.model,name:crm_iap_lead_enrich.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "IAP account"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_iap_enrich_api__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_iap_enrich_api____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment based on email address"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "Lead enriched based on email address"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model,name:crm_iap_lead_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/salgsmulighet"

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_crm_lead__reveal_id
msgid "Reveal ID"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "Technology Used :"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,help:crm_iap_lead_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "Your balance for Lead Enrichment is insufficient. Please go to your"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "to buy credits."
msgstr ""
