<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.activity_items">
        <div class="o_thread_date_separator o_border_dashed" data-toggle="collapse" data-target="#o_chatter_planned_activities">
            <a role="button" class="o_thread_date btn">
                <i class="fa fa-fw fa-caret-down"/>
                Planned activities
                <small class="o_chatter_planned_activities_summary ml8">
                    <span class="badge rounded-circle badge-danger"><t t-esc="nbOverdueActivities"/></span>
                    <span class="badge rounded-circle badge-warning"><t t-esc="nbTodayActivities"/></span>
                    <span class="badge rounded-circle badge-success"><t t-esc="nbPlannedActivities"/></span>
                </small>
            </a>
        </div>
        <div id="o_chatter_planned_activities" class="collapse show">
            <t t-foreach="activities" t-as="activity">
                <div class="o_thread_message" style="margin-bottom: 10px">
                    <div class="o_thread_message_sidebar">
                        <div class="o_avatar_stack">
                            <img t-attf-src="/web/image#{activity.user_id[0] >= 0 ? ('/res.users/' + activity.user_id[0] + '/image_128') : ''}" class="o_thread_message_avatar rounded-circle mb8" t-att-title="activity.user_id[1]" t-att-alt="activity.user_id[1]"/>
                            <i t-att-class="'o_avatar_icon fa ' + activity.icon + ' bg-' + (activity.state == 'planned'? 'success' : (activity.state == 'today'? 'warning' : 'danger')) + '-full'"
                               t-att-title="activity.activity_type_id[1]"/>
                        </div>
                    </div>
                    <div class="o_thread_message_core">
                        <div class="o_mail_info text-muted">
                            <strong><span t-attf-class="o_activity_date o_activity_color_#{activity.state}"><t t-esc="activity.label_delay" /></span></strong>:
                            <strong t-if="activity.summary"> &#8220;<t t-esc="activity.summary"/>&#8221;</strong>
                            <strong t-if="!activity.summary"> <t t-esc="activity.activity_type_id[1]" /></strong>
                            <em> for </em>
                            <t t-esc="activity.user_id[1]" />
                            <a class="btn btn-link btn-info text-muted collapsed o_activity_info ml4" role="button" data-toggle="collapse" t-attf-data-target="#o_chatter_activity_info_#{activity.id}">
                                <i class="fa fa-info-circle" role="img" aria-label="Info" title="Info"></i>
                            </a>
                            <div class="o_thread_message_collapse collapse" t-attf-id="o_chatter_activity_info_#{activity.id}">
                                <dl class="dl-horizontal">
                                    <dt>Activity type</dt>
                                    <dd class="mb8">
                                        <t t-esc="activity.activity_type_id[1]"/>
                                    </dd>
                                    <dt>Created on</dt>
                                    <dd class="mb8">
                                        <t t-esc="activity.create_date.format(datetimeFormat)"/>
                                        by
                                        <img t-attf-src="/web/image#{activity.create_uid[0] >= 0 ? ('/res.users/' + activity.create_uid[0] + '/image_128') : ''}"
                                            height="18" width="18"
                                            class="o_object_fit_cover rounded-circle mr4"
                                            t-att-title="activity.create_uid[1]"
                                            t-att-alt="activity.create_uid[1]"/>
                                        <b><t t-esc="activity.create_uid[1]"/></b>
                                    </dd>
                                    <dt>Assigned to</dt>
                                    <dd class="mb8">
                                        <img t-attf-src="/web/image#{activity.user_id[0] >= 0 ? ('/res.users/' + activity.user_id[0] + '/image_128') : ''}" height="18" width="18" class="o_object_fit_cover rounded-circle mr4" t-att-title="activity.user_id[1]" t-att-alt="activity.user_id[1]"/>
                                        <b><t t-esc="activity.user_id[1]"/></b>
                                        <em>, due on </em><span t-attf-class="o_activity_color_#{activity.state}"><t t-esc="activity.date_deadline.format(dateFormat)"/></span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div t-if="activity.note" t-attf-class="o_thread_message_#{activity.activity_decoration ? activity.activity_decoration : 'note'} #{activity.activity_decoration ? 'alert alert-' + activity.activity_decoration : ''}">
                            <t t-raw="activity.note"/>
                        </div>
                        <t t-if="activity.mail_template_ids &amp;&amp; activity.mail_template_ids.length &gt; 0">
                            <div class="mt16" t-att-data-activity-id="activity.id" t-att-data-previous-activity-type-id="activity.activity_type_id[0]">
                                <t t-foreach="activity.mail_template_ids" t-as="mail_template">
                                    <div>
                                        <i class="fa fa-envelope-o" aria-label="Mail" title="Mail" role="img"></i>
                                        <span t-esc="mail_template.name"/>:
                                        <span class="o_activity_template_preview" t-att-data-template-id="mail_template.id">Preview</span>
                                        <span class="text-muted">or</span>
                                        <span class="o_activity_template_send" t-att-data-template-id="mail_template.id">Send Now</span>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <div class="o_thread_message_tools btn-group">
                            <t t-call="mail.activity_thread_message_tools"/>
                        </div>
                    </div>
                </div>
            </t>
        </div>
    </t>
    <t t-name="mail.activity_thread_message_tools">
        <div t-if="activity.can_write" class="o_thread_message_tools btn-group">
            <span t-if="activity.activity_category !== 'upload_file'" class="o_mark_as_done" data-toggle="popover" t-att-data-activity-id="activity.id" t-att-data-force-next-activity="activity.force_next" t-att-data-previous-activity-type-id="activity.activity_type_id[0]">
                <a role="button" href="#" class="btn btn-link btn-success text-muted o_activity_link mr8">
                <i class="fa fa-check"/> Mark Done </a>
            </span>
            <span t-if="activity.activity_category === 'upload_file'" class="o_mark_as_done_upload_file" t-att-data-activity-id="activity.id" t-att-data-force-next-activity="activity.force_next" t-att-data-previous-activity-type-id="activity.activity_type_id[0]" t-att-data-fileupload-id="activity.fileuploadID">
                <a role="button" href="#" class="btn btn-link btn-success text-muted o_activity_link mr8">
                <i class="fa fa-upload"/> Upload Document </a>
            </span>
            <span t-if="activity.activity_category === 'upload_file'" class="d-none">
                <t t-call="HiddenInputFile">
                    <t t-set="fileupload_id" t-value="activity.fileuploadID"/>
                    <t t-set="fileupload_action" t-translation="off">/web/binary/upload_attachment</t>
                    <input type="hidden" name="model" t-att-value="activity.res_model"/>
                    <input type="hidden" name="id" t-att-value="activity.res_id"/>
                </t>
            </span>
            <a role="button" href="#" class="btn btn-link btn-secondary text-muted o_edit_activity o_activity_link" t-att-data-activity-id="activity.id">
                <i class="fa fa-pencil"/> Edit
            </a>
            <a role="button" href="#" class="btn btn-link btn-danger text-muted o_unlink_activity o_activity_link" t-att-data-activity-id="activity.id">
                <i class="fa fa-times"/> Cancel
            </a>
        </div>
    </t>
    <t t-name="mail.activity_feedback_form">
        <div>
            <textarea class="form-control" rows="3" id="activity_feedback" placeholder="Write Feedback"/>
            <div class="mt8">
                <t t-if="!force_next">
                    <button type="button" class="btn btn-sm btn-primary o_activity_popover_done_next" t-att-data-previous-activity-type-id="previous_activity_type_id">
                        Done &amp; Schedule Next</button>
                    <button type="button" class="btn btn-sm btn-primary o_activity_popover_done">
                        Done</button>
                </t>
                <t t-else="">
                    <button type="button" class="btn btn-sm btn-primary o_activity_popover_done_next">
                        Done &amp; Launch Next</button>
                </t>
                <button type="button" class="btn btn-sm btn-link o_activity_popover_discard">
                    Discard</button>
            </div>
        </div>
    </t>
</templates>
