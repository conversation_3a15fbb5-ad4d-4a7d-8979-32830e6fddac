<?xml version="1.0" ?>
<odoo>
    <data>

        <record id="res_partner_view_form_inherit_mail" model="ir.ui.view">
            <field name="name">res.partner.view.form.inherit.mail</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='email']" position="replace">
                    <field name="is_blacklisted" invisible="1"/>
                    <label for="email" class="oe_inline"/>
                    <div class="o_row o_row_readonly">
                        <i class="fa fa-ban" style="color: red;" role="img" title="This email is blacklisted for mass mailing"
                            aria-label="Blacklisted" attrs="{'invisible': [('is_blacklisted', '=', False)]}" groups="base.group_user"></i>
                        <field name="email" widget="email" context="{'gravatar_image': True}" attrs="{'required': [('user_ids','!=', [])]}" />
                    </div>
                </xpath>
                <xpath expr="//sheet" position="after">
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </xpath>
            </field>
        </record>

       <record id="res_partner_view_search_inherit_mail" model="ir.ui.view">
            <field name="name">res.partner.view.search.inherit.mail</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_res_partner_filter"/>
            <field name="arch" type="xml">
                    <filter name="inactive" position="after">
                        <filter invisible="1" string="Late Activities" name="activities_overdue"
                                domain="[('activity_ids.date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                                help="Show all records which has next action date is before today"/>
                        <filter invisible="1" string="Today Activities" name="activities_today"
                                domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                        <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                                domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                        <separator/>
                    </filter>
            </field>
        </record>

        <record id="res_partner_view_activity" model="ir.ui.view">
            <field name="name">res.partner.activity</field>
            <field name="model">res.partner</field>
            <field name="arch" type="xml">
                <activity string="Contacts">
                    <field name="id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img t-att-src="activity_image('res.partner', 'image_128', record.id.raw_value)" role="img" t-att-title="record.id.value" t-att-alt="record.id.value"/>
                            <div>
                                <field name="name" display="full"/>
                                <field name="parent_id" muted="1" display="full"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <!--Definition of an email template with an empty body that will be used in partner mailing. Used to give a
            basis for email recipients, name and to ease the definition of a further elaborated template. -->
        <record id="email_template_partner" model="mail.template">
            <field name="name">Partner Mass Mail</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="partner_to">${object.id}</field>
            <field name="user_signature" eval="True"/>
            <field name="auto_delete" eval="True"/>
            <field name="lang">${object.lang}</field>
        </record>

        <!-- Add mail-defined activity view to standard action on partners to manage activities related to partner model -->
        <record id="base.action_partner_form" model="ir.actions.act_window">
            <field name="view_mode">kanban,tree,form,activity</field>
        </record>
        <record id="base.action_partner_customer_form" model="ir.actions.act_window">
            <field name="view_mode">kanban,tree,form,activity</field>
        </record>
        <record id="base.action_partner_supplier_form" model="ir.actions.act_window">
            <field name="view_mode">kanban,tree,form,activity</field>
        </record>

        <!--  Replace the default mass-mailing wizard in base with the composition wizard -->
        <act_window name="Send email"
                res_model="mail.compose.message"
                binding_model="res.partner"
                binding_views="list"
                view_mode="form"
                target="new"
                id="action_partner_mass_mail"
                context="{
                            'default_composition_mode': 'mass_mail',
                            'default_partner_to': '${object.id or \'\'}',
                            'default_use_template': True,
                            'default_template_id': ref('email_template_partner'),
                        }"/>

        <record id="email_template_partner" model="mail.template">
            <field name="ref_ir_act_window" ref="action_partner_mass_mail"/>
        </record>

    </data>
</odoo>
