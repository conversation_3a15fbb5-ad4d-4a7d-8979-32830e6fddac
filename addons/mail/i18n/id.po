# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# R<PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# I<PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# Dedi <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# whenwesober, 2020
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Abe Manyo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:11+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr "Saluran ini bersifat pribadi. Orang harus diundang untuk bergabung."

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr ""
"%(user_name)s mengundang Anda untuk mengikuti %(document)s dokumen: "
"%(title)s"

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s mengundang Anda untuk mengikuti dokumen baru."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#, python-format
msgid "%d Messages"
msgstr "%d Pesan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "terlambat %d hari"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid ""
"%s \n"
"(inactive)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (salinan)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s dan %s sedang mengetik..."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s dibuat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s sedang mengetik..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s dan lainnya sedang mengetik..."

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%s: %s assigned to you"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "&nbsp;("
msgstr "&nbsp;("

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(awalnya ditugaskan untuk"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid ", due on"
msgstr ", batas tenggat waktu pada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Tampilkan pesan yang lama --------"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "0 Akan Datang"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "0 Terlambat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "0 Hari Ini"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b>.to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>\n"
"            Type <b>:shortcut</b> to insert canned responses in your message.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">%(author)s invited %(new_partner)s to <a "
"href=\"#\" class=\"o_channel_redirect\" data-oe-"
"id=\"%(channel_id)s\">#%(channel_name)s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">%(author)s mengundang %(new_partner)s ke "
"<a href=\"#\" class=\"o_channel_redirect\" data-oe-"
"id=\"%(channel_id)s\">#%(channel_name)s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">membuat <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">joined <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">bergabung di <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">left <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">meninggalkan <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_short
msgid ""
"<i class=\"fa fa-ban\" style=\"color: red;\" role=\"img\" title=\"This email"
" is blacklisted for mass mailing\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': [('is_blacklisted', '=', False)]}\" "
"groups=\"base.group_user\"/>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p> Create a private channel.</p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Chat dengan rekan kerja</b> secara aktual menggunakan pesan "
"langsung.</p><p><i>Kamu perlu mengundang pengguna lain dari aplikasi "
"Settings terlebih dahulu.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Tulis pesan</b> kepada anggota saluran ini.</p> <p>Anda dapat "
"memanggil seseorang dengan <i>'@'</i> atau menyambungkan saluran lain dengan"
" <i>'#'</i>. Mulai pesan Anda dengan <i>'/'</i> untuk melihat perintah yang "
"dapat dilakukan.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Saluran memudahkan Anda mengelola informasi dari berbagai topik dan "
"grup.</p> <p>Coba <b>buat saluran Anda</b> (mis. penjualan, pemasaran, "
"produk XYZ, ngopi setelah kerja, dll).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/create_mode_document_thread.js:0
#, python-format
msgid "<p>Creating a new record...</p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    Jika Anda ingin mengirim untuk semua data yang cocok dengan kriteria pencarian Anda, centang kotak ini :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    Jika Anda ingin mengirim hanya untuk data yang Anda pilih, hapus centang kotak ini :\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">Kegiatan</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Tambah</span>\n"
"                                    <span class=\"o_stat_text\">Tindakan Konteks</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Hapus</span>\n"
"                                    <span class=\"o_stat_text\">Tindakan Konteks</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>\n"
"                                    Semua data yang cocok dengan kriteria pencarian Anda akan dikirimkan email,\n"
"                                    bukan hanya yang dipilih pada tampilan daftar.\n"
"                                </strong><br/>\n"
"                                Email akan dikirimkan untuk semua data yang dipilih di daftar.<br/>\n"
"                                Mengonfirmasi wisaya ini mungkin akan memerlukan beberapa menit dan memblok browser Anda."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Hanya data yang dicentang pada tampilan daftar yang akan digunakan.</strong><br/>\n"
"                                Email akan dikirimkan ke semua data yang dipilih di tampilan daftar."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Saran Aktivitas</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Kamus Python yang akan dievaluasi untuk memberikan nilai baku ketika membuat"
" catatan baru untuk alias ini."

#. module: mail
#: code:addons/mail/models/ir_actions.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Kode singkat adalah tombol pintas untuk keyboard. Misalnya, jika Anda "
"mengetik #gm, maka akan diubah menjadi \"Good Morning\"."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Accept"
msgstr "Terima"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Accept selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Accept |"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__moderation_status__accepted
msgid "Accepted"
msgstr "Diterima"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Kelompok Akses"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "Tindakan yang Akan DIlakukan"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Jendela Tindakan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action to Perform"
msgstr "Tindakan untuk Dilakukan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Tindakan dapat memicu perilaku spesifik seperti membuka tampilan kalender "
"atau secara otomatis ditandai sebagai selesai saat dokumen diunggah"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Langsung aktif bila disetujui"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Aktif"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Domain aktif"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model:mail.message.subtype,name:mail.mt_activities
#, python-format
msgid "Activities"
msgstr "Aktivitas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "Aktivitas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Aktivitas Mixin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Jenis Aktivitas"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Jenis Aktivitas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Jenis aktivitas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Add"
msgstr "Tambahkan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Add Attachments"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__channel_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__channel_ids
#, python-format
msgid "Add Channels"
msgstr "Tambah Saluran"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Tambah Pengikut"

#. module: mail
#: code:addons/mail/models/ir_actions.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr "Tambah Pengikut hanya dapat dilakukan pada model pesan thread"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "Add Sign"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,field_description:mail.field_mail_template__user_signature
msgid "Add Signature"
msgstr "Tambah Tanda Tangan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Add a channel"
msgstr "Tambah saluran"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address in the blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "Tambah lampiran"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add channels to notify..."
msgstr "Tambah saluran yang akan diberitahu..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Tambah kontak yang akan diberitahu..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Add this email address to white list of people"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Kontak Tambahan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Lanjutan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Pengaturan Lebih Lanjut"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "Peringatan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model:ir.model.fields,field_description:mail.field_res_users__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,field_description:mail.field_res_users__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Kontak Keamanan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Domain Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Nama Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Domain alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Model Alias"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Alias"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "All"
msgstr "Semua"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Allowed Emails"
msgstr "Email yang Diizinkan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_moderation__status__allow
msgid "Always Allow"
msgstr "Selalu Mengizinkan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Always Allow |"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/mail_failure.js:0
#, python-format
msgid "An error occurred when sending an email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/static/src/js/models/messages/message.js:0
#, python-format
msgid "Anonymous"
msgstr "Anonymous"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_message__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"Balasan tidak masuk di thread diskusi dokumen sumber. Ini akan mempengaruhi "
"id pesan yang dihasilkan."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Terapkan untuk"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "Apply"
msgstr "Terapkan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures. You won't be "
"able to re-send these mails later!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Ditugaskan untuk"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Lampirkan berkas"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
msgid "Attachment"
msgstr "Lampiran"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#: model:ir.model.fields,field_description:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Lampiran"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Lampiran terkait dengan dokumen melalui model / res_id dan dengan pesan "
"melalui kolom ini."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Rekanan Terotentikasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Penulis"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Author Signature (mass mail only)"
msgstr "Tanda Tangan Penulis (hanya untuk email massal)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Penulis pesan. Jika tidak diatur, email_from dapat diisi dengan alamat email"
" yang tidak cocok dengan rekanan manapun."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar penulis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Kelompok Berwenang"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Hapus Otomatis"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Subskripsi Otomatis"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Subskripsi otomatis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify
msgid "Automatic notification"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Ban"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Ban List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Ban this email address"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Banned Emails"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Dasar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Be careful with channels following internal notifications"
msgstr "Hati-hati dengan saluran yang mengikuti notifikasi internal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Body"
msgstr "Badan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "Kembali"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Kembali"

#. module: mail
#: code:addons/mail/models/mail_cc_mixin.py:0
#, python-format
msgid "CC Email"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Panggilan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Batalkan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Batalkan Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Dibatalkan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Dibatalkan"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Tanggapan Singkat / Kode Singkat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr "Canned Responses"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Cc penerima email"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Cc penerima email (dapat menggunakan placeholder)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall
msgid "Catchall Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Changed"
msgstr "Diubah"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Changed Field"
msgstr "Kolom yang Diubah"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Saluran"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_message_ids
msgid "Channel Message"
msgstr "Pesan Saluran"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_moderation_menu
msgid "Channel Moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Tipe Saluran"

#. module: mail
#: model:ir.model,name:mail.model_mail_moderation
msgid "Channel black/white list"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Channel settings"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Saluran"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Saluran/Rekanan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Chat"
msgstr "Chat"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
msgid "Chat Discussion"
msgstr "Diskusi Chat"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Kode Singkat Chat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__out_of_office_message
msgid "Chat Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Pesan Anak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose a language:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose an example"
msgstr "Pilih salah satu contoh"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Close"
msgstr "Tutup"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:0
#, python-format
msgid "Close chat window"
msgstr "Tutup jendela obrolan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "Ditutup"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Cc alamat penerima dipisahkan dengan koma"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Id rekanan penerima dipisahkan dengan koma"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Id rekanan penerima dipisahkan dengan koma (dapat menggunakan placeholder)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Alamat penerima yang dipisahkan dengan koma"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_to
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Alamat penerima dipisahkan dengan koma (dapat menggunakan placeholder)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Komentar"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Buat Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Mode Komposisi"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Config Settings"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Konfigurasi jenis aktivitas Anda"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Selamat, kotak pesan Anda kosong"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Selamat, kotak pesan Anda kosong!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Kontak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Konten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Konten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Status Tampilan Percakapan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "Percakapan dikecilkan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Conversations"
msgstr "Percakapan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Jumlah email kembali untuk kontak ini"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "Buat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Create %s (Private)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Create %s (Public)"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create a new %(document)s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create a new %(document)s by sending an email to %(email_link)s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Dibuat Oleh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#, python-format
msgid "Created on"
msgstr "Dibuat pada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Tanggal Pembuatan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Pengguna ini memiliki notifikasi berbintang yang terkait dengan pesan ini"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__is_moderator
msgid "Current user is a moderator of the channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_res_partner_needaction_rel_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Tanggal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Dates"
msgstr "Tanggal"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Hari"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Batas waktu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Kepada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Tipe Dekorasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Baku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_description
msgid "Default Description"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_next_type_id
msgid "Default Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Nilai Baku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Nilai Baku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Penerima baku"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Penerima baku untuk data ini:\n"
"- rekanan (gunakan id rekanan atau kolom partner_id) ATAU\n"
"- email (gunakan email_from atau kolom email)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Hapus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Hapus Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Hapus Salinan Pesan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr "Hapus email terkirim (hanya untuk email massal)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Pengiriman gagal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Deskripsi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Deskripsi yang akan ditambahkan dalam pesan yang dikirim untuk sub-tipe ini."
" Jika kosong, nama akan ditambahkan sebagai gantinya."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Direct Messages"
msgstr "Pesan Langsung"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Discard"
msgstr "Abaikan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Discard selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Discard |"
msgstr ""

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "Diskusi"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "Saluran Diskusi"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Diskusi"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_moderation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Tampilkan pilihan pada dokumen terkait untuk membuka wisaya komposisi dengan"
" contoh ini"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "Nama tampilan dari dokumen terkait."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Jangan simpan salinan email di riwentri komunikasi dokumen (hanya untuk "
"email massal)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chatter.js:0
#, python-format
msgid "Do you really want to delete %s?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Dokumen"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Pengikut Dokumen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "Model Dokumen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Nama Dokumen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Document not downloadable"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "Selesai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Selesai & Jadwalkan Yang Berikutnya"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "Unduh"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Drag Files Here"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Dropdown menu - Followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Batas Waktu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "Batas waktu dalam %d hari lagi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Pembuat Placeholder Dinamik"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Edit"
msgstr "Sunting"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "Edit Subscription of "
msgstr "Sunting Subskripsi dari"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Sunting Subskripsi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_moderation__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: model:mail.activity.type,name:mail.mail_activity_data_email
msgid "Email"
msgstr "Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
msgid "Email Address"
msgstr "Alamat Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_address_mixin
msgid "Email Address Mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "Email Alias"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Alias Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Konfigurasi Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Email Preview"
msgstr "Pratinjau Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Pencarian email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
msgid "Email Status"
msgstr "Status Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Template Email"

#. module: mail
#: model:ir.model,name:mail.model_email_template_preview
msgid "Email Template Preview"
msgstr "Pratinjau Contoh Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_template
msgid "Email Templates"
msgstr "Contoh Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Thread email"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__alias_id
msgid ""
"Email address internally associated with this user. Incoming emails will "
"appear in the user's notifications."
msgstr ""
"Alamat email internal yang terkait dengan pengguna ini. Email masuk akan "
"muncul dalam notifikasi pengguna."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Alamat email pengirim. Kolom ini digunakan ketika tidak ada rekanan cocok "
"yang ditemukan dan menggantikan kolom author_id di kotak chat."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__bounce
msgid "Email address rejected by destination"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to redirect replies..."
msgstr "Alamat email untuk mengarahkan balasan..."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted means that the recipient won't receive "
"mass mailing anymore."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Wisaya komposisi email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Pesan email"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Email"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Emojis"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/attachment_box.js:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Eror!"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Kesalahan ketika komunikasi dengan server garansi penerbit."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""
"Eror tanpa pengecualian. Mungkin karena mengirim email tanpa penerima."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_channel_res_model_id_uniq
msgid "Error, a channel cannot follow twice the same object."
msgstr "Eror, sebuah saluran tidak dapat mengikuti objek yang sama dua kali."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Eror, seorang rekanan tidak dapat mengikuti objek yang sama dua kali."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_partner_xor_channel
msgid ""
"Error: A follower must be either a partner or a channel (but not both)."
msgstr ""
"Eror: Seorang pengikut haruslah rekanan atau saluran (tapi tidak kedua-"
"duanya)."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "Semua Orang"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Pengecualian"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Penyaring Tingkat Lanjut..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Pesan Gagal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Gagal"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Failed to render template %r using values %r"
msgstr "Gagal untuk render contoh %r menggunakan nilai %r"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Alasan Gagal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Alasan gagal. Ini biasanya pengecualian yang disampaikan oleh server email, "
"dan disimpan untuk membantu debug masalah yang berkaitan dengan pesan."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Difavoritkan Oleh"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr "Masukan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
msgid "Field"
msgstr "Kolom"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Kolom \"Thread Pesan\" tidak dapat diganti menjadi \"False\"."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Deskripsi Kolom"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Tipe Kolom"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Kolom yang digunakan untuk mengaitkan model ke sub-model ketika menggunakan "
"subskripsi otomatis pada dokumen terkait. Kolom ini digunakan untuk "
"menghitung getattr(related_document.relation_field)."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Kolom"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Ekspresi placeholder akhir, akan disalin pada kolom contoh yang diinginkan."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "Terlipat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Follow"
msgstr "Ikuti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "Pengikut"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pengikut (Saluran)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Formulir Pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Followers of"
msgstr "Pengikut"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Hanya untuk pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Following"
msgstr "Mengikuti"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Following invites are invalid as user groups do not match: %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_from
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
msgid "From"
msgstr "Dari"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Full composer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "Akan Datang"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Aktivitas Akan Datang"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Gateway"
msgstr "Gateway"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Ke panel konfigurasi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Dikelompokkan Menurut"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Kelompokkan menurut...."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Kelompok"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines_msg
msgid "Guidelines"
msgstr "Pedoman"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Guidelines of channel %s"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Ditangani melalui email"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Ditangani di Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Ada Panggilan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Ada kesalahan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Header"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Hello"
msgstr "Halo"

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "Hello,"
msgstr "Halo,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Tersembunyi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Menyembunyikan sub-tipe dalam pilihan pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "History"
msgstr "Riwayat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_thread__id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID dari catatan induk alias (contoh: proyek yang menahan pembuatan alias "
"tugas)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "ID dari sumber diikuti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Idle"
msgstr "Senggang"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""
"Jika dicentang, rekanan akan menerima email yang memberikan peringatan bahwa"
" mereka telah ditambahkan sebagai pengikut dokumen."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,help:mail.field_mail_template__user_signature
msgid ""
"If checked, the user's signature will be appended to the text version of the"
" message"
msgstr ""
"Jika dicentang, tanda tangan pengguna akan ditambahkan ke versi teks dari "
"pesan"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible."
msgstr ""
"Jika diatur, pengelola antrian akan mengirim email setelah tanggal tersebut."
" Jika kosong, email akan dikirimkan secepatnya."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Jinja2 placeholders may be used."
msgstr ""
"Jika diatur, pengelola antrian akan mengirim email setelah tanggal tersebut."
" Jika kosong, email akan dikirimkan secepatnya. Placeholder Jinja2 dapat "
"digunakan."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Jika Anda mengatur email domain penangkap-semua yang dialihkan ke server "
"Odoo, masukkan nama domain di sini."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid ""
"If you remove a follower, he won't be notified of any email or discussion on"
" this document. Do you really want to remove %s?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#, python-format
msgid "Image"
msgstr "Gambar"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Alias Tidak Aktif"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Kotak Pesan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Info"
msgstr "Informasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model_id
msgid "Initial model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "Pesan thread awal."

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Integrasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Khusus Internal"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__recipient
msgid "Invalid email address"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Perintahnya salah, harus sesuai dengan definisi pada kamus python misalnya. "
"\"{'kolom':'nilai'}\""

#. module: mail
#: code:addons/mail/models/mail_address_mixin.py:0
#: code:addons/mail/models/mail_address_mixin.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr ""

#. module: mail
#: code:addons/mail/controllers/main.py:0
#, python-format
msgid "Invalid token in route %s"
msgstr "Token tidak sah pada rute %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "Invitation"
msgstr "Undangan"

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "Invitation to follow %s: %s"
msgstr "Undangan untuk mengikuti %s: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Invite"
msgstr "Undang"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "Invite Follower"
msgstr "Undang Pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Invite people"
msgstr "Undang orang-orang"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Invite people to #%s"
msgstr "Undang orang-orang ke #%s"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Wisaya undangan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "Khusus untuk pengikut yang diundang"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Allowed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Banned"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification
msgid "Is Notification"
msgstr "Notifikasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Dibaca"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_subscribed
msgid "Is Subscribed"
msgstr "Disubskripsi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is a member"
msgstr "Anggota"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__is_moderator
msgid "Is moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "Dipermanenkan di antarmuka"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Gabung"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Bergabung dengan salah satu kelompok"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Bahasa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_moderation____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract____last_update
msgid "Last Modified on"
msgstr "Terakhir Diubah Pada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Terakhir Aktif"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "Terlambat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Aktivitas Terakhir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Layout"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Leave"
msgstr "Keluar"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Leave this channel"
msgstr "Keluar dari saluran ini"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr "Daftar cc dari email masuk"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__channel_ids
msgid ""
"List of channels that will be added as listeners of the current document."
msgstr "Daftar saluran yang akan ditambahkan sebagai pendengar dokumen ini."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr "Daftar rekanan yang akan ditambahkan sebagai pengikut dokumen ini."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "List users in the current channel"
msgstr "Tampilkan daftar pengguna di saluran ini"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__channel_id
msgid "Listener"
msgstr "Pendengar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
msgid "Listeners"
msgstr "Pendengar"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Pendengar dari Saluran"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "Memuat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading older messages..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "Memuat..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Log"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Log a note. Followers will not be notified."
msgstr "Log sebuah catatan. Pengikut tidak akan diberikan notifikasi."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Log catatan..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Log Aktivitas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Log Catatan Internal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Log note"
msgstr "Log catatan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Log or schedule an activity"
msgstr "Log atau jadwalkan aktivitas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_id
#, python-format
msgid "Mail"
msgstr "Surat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Tipe Aktivitas Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Mail Body"
msgstr "Tubuh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Formulir Saluran Pesan"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Mail Delivery Failed"
msgstr "Pengiriman Pesan Gagal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Thread Pesan"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Nilai Lacak Pesan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Pesan telah dibuat untuk memberitahu orang-orang mengenai mail.message yang "
"ada"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "Pesan: Pengelola Antrian Email"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_notify_channel_moderators_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_notify_channel_moderators
#: model:ir.cron,name:mail.ir_cron_mail_notify_channel_moderators
msgid "Mail: Notify channel moderators"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Kotak pesan tidak tersedia - %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Manage Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "Tandai Selesai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Mark all as read"
msgstr "Tandai semua sebagai sudah dibaca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Mark all read"
msgstr "Tandai semua sebagai sudah dibaca"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Tandai sebagai Selesai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Tandai sebagai Sudah Dibaca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Tandai sebagai Todo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "Tandai sebagai selesai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Mark as todo"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
msgid "Mass Mail Blacklist"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Rapat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Anggota"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Anggota dari kelompok tersebut akan secara otomatis ditambahkan sebagai "
"pengikut. Perhatikan bahwa mereka akan mampu mengelola subskripsi mereka "
"secara manual jika diperlukan."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/base_partner_merge.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Gabung dengan rekanan berikut:"

#. module: mail
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Message"
msgstr "Pesan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID Pesan"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Notifikasi Pesan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Nama Catatan Pesan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Tipe Pesan"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Message are pending moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Penerima pesan (email)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Referensi pesan, seperti pengidentifikasi pesan sebelumnya"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Message sent in \""
msgstr "Pesan dikirim di \""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid Message instance"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Pesan subtipe memberikan sejenis yang lebih tepat pada pesan, terutama untuk"
" sistem pemberitahuan. Sebagai contoh, dapat pemberitahuan terkait rekor "
"baru (New), atau perubahan tahap dalam proses (tahap perubahan). Pesan "
"subtipe biarkan justru tune pemberitahuan pengguna ingin menerima di "
"dinding."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipe pesan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Pesan subtipe diikuti, berarti subtipe yang akan mendorong ke pengguna "
"dinding."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Jenis pesan: email untuk pesan email, pemberitahuan untuk pesan sistem, "
"komentar untuk pesan lain seperti balasan pengguna"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Pengenal unik pesan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Pesan-Id"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Pesan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Cari pesan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""
"Pesan dapat ditandai <b>bintang</b> untuk mengingatkan Anda agar "
"memeriksanya lagi belakangan."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Pesan dengan subtipe internal akan terlihat hanya oleh karyawan, yaitu "
"anggota dari kelompok base_user"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mailbox.js:0
#, python-format
msgid "Missing domain for mailbox with ID '%s'"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Model dari sumber yang diikuti"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Model yang berlaku untuk subtipe. Jika False, subtipe ini berlaku untuk "
"semua model."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Model"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#, python-format
msgid "Moderate Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation
msgid "Moderate this channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderator_id
#: model:ir.model.fields,field_description:mail.field_mail_message__moderator_id
msgid "Moderated By"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_ids
msgid "Moderated Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_channel_ids
msgid "Moderated channels"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Moderated channels must have moderators."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_count
msgid "Moderated emails count"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_moderation_action
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Moderation"
msgstr "Moderasi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_tree
msgid "Moderation Lists"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Moderation Queue"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderation_status
#: model:ir.model.fields,field_description:mail.field_mail_message__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_counter
msgid "Moderation count"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_moderator
msgid "Moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderator_ids
msgid "Moderators"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Moderators must have an email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Moderators should be members of the channel they moderate."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Module Uninstal"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Bulan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Name"
msgstr "Nama"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "Nama perolehan dari dokumen terkait."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__report_name
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Nama yang digunakan untuk berkas laporan yang dibuat (dapat memakai placeholder)\n"
"Ekstensi tidak perlu dimasukkan dan akan diambil dari tipe laporan."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Butuh Tindakan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__need_moderation
#: model:ir.model.fields,field_description:mail.field_mail_message__need_moderation
msgid "Need moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
msgid "Needaction Recipient"
msgstr "Penerima Butuh Tindakan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "Saluran Baru"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "New Message"
msgstr "Pesan Baru"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Nilai Karakter Baru"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Nilai Datetime Baru"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Nilai Desimal Baru"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Nilai Bilangan Bulat Baru"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Nilai Mata Uang Baru"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Nilai Teks Baru"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "New message"
msgstr "Pesan baru"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "New messages"
msgstr "Pesan baru"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Pesan baru muncul di sini."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "New people"
msgstr "Orang baru"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_guidelines
msgid ""
"Newcomers on this moderated channel will automatically receive the "
"guidelines."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "Next"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Aktivitas Berikutnya"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Aktivitas berikutnya yang tersedia"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "No activities planned."
msgstr "Tidak ada aktivitas terjadwal."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "No conversation yet..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "No data to display"
msgstr "Tidak data yang bisa ditampilkan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "No follower"
msgstr "Tidak ada pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No history messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No matches found"
msgstr "Tidak ada hasil cocok yang ditemukan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "No message available"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No message matches your search. Try to change your search filters."
msgstr ""
"Tidak ada pesan yang cocok dengan pencarian Anda. Coba ganti penyaring "
"pencarian Anda."

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No starred messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_message__no_auto_thread
msgid "No threading for answers"
msgstr "Tidak ada threading untuk jawaban"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "Tidak Ada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "Catatan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Note by"
msgstr "Catatan oleh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Notifikasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify_msg
msgid "Notification message"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
msgid "Notifications"
msgstr "Notifikasi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Pengikut notifikasi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Buat notifikasi untuk pengikut dokumen (khusus email massal)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Offline"
msgstr "Luring"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Nilai Karakter Lama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Nilai Datetime Lama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Nilai Desimal Lama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Nilai Bilangan Bulat Lama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Nilai Mata Uang Lama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Nilai Teks Lama"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Jika pesan telah ditandai bintang, Anda dapat kembali dan meninjaunya kapan "
"saja di sini."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "One follower"
msgstr "Satu pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Online"
msgstr "Daring"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only an administrator or a moderator can send guidelines to channel members!"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Hanya model kustom yang dapat dimodifikasi."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Only mailing lists can be moderated."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "Terbuka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Open Channel in Discuss to moderate"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Buka Dokumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Buka Dokumen Induk"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Open chat"
msgstr "Buka chat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#, python-format
msgid "Open document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#, python-format
msgid "Open in Discuss"
msgstr "Buka di Diskusi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID Opsional dari berkas (catatan) di mana semua pesan masuk akan "
"dilampirkan, bahkan jika mereka tidak menjawabnya. Jika diatur, hal ini akan"
" menonaktifkan pembuatan catatan baru sepenuhnya."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Server opsional untuk email keluar. Jika tidak diatur, server dengan "
"prioritas yang lebih tinggi akan digunakan."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_template
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Laporan opsional untuk dicetak dan dilampirkan"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. "
"${object.partner_id.lang}."
msgstr ""
"Terjemahan bahasa opsional (kode ISO) yang dipilih ketika mengirim email. "
"Jika tidak diatur, versi Bahasa Inggris yang akan digunakan. Isinya "
"seharusnya berupa ekspresi placeholder yang menyediakan bahasa yang sesuai, "
"mis: ${object.partner_id.lang}."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Nilai optional yang digunakan jika kolom target kosong"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/dm_chat.js:0
#, python-format
msgid "Out of office until %s"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Keluar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Server Email Keluar"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Email Keluar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Server email keluar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Terlambat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Kesampingkan email penulis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Pemilik"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr "File PDF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Induk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Pesan Induk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Model Induk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Catatan Induk ID Berkas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Model induk dengan alias. Model yang memiliki referensi alias ini tidak "
"selalu berarti model yang diberikan oleh alias_model_id (contoh: proyek "
"(parent_model) dan tugas (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Subtipe induk, digunakan untuk subskripsi otomatis. Kolom ini tidak "
"dinamakan dengan benar. Contoh pada proyek, parent_id dari subtipe proyek "
"mengacu pada subtipe yang terkait dengan tugas."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
msgid "Partner"
msgstr "Rekanan"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Profil Rekanan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additionnal information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Rekanan yang Butuh Tindakan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Pending moderation messages appear here."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_moderation__status__ban
msgid "Permanent Ban"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid "Permanently delete this email after sending it, to save space"
msgstr "Hapus email ini setelah mengirimnya, untuk menghemat ruang"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Ekspresi Placeholder"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Sudah Direncanakan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Planned activities"
msgstr "Aktivitas sudah direncanakan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:0
#, python-format
msgid "Please complete customer's informations"
msgstr "Silahkan lengkapi informasi pelanggan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Silakan hubungi kami alih-alih menggunakan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Please find below the guidelines of the"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Please wait"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Please wait..."
msgstr "Mohon menunggu..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:0
#, python-format
msgid "Please, wait while the file is uploading."
msgstr "Harap tunggu sementara berkas diunggah."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,help:mail.field_res_users__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Kebijakan untuk mengirimkan pesan di dokumen menggunakan mailgateway.\n"
"- semua orang: setiap orang dapat mengirim\n"
"- rekanan: hanya rekanan yang diijinkan\n"
"- pengikut: hanya pengikut dokumen terkait\n"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Disajikan oleh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Aktivitas Sebelumnya"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preferred reply address"
msgstr "Alamat email balasan"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid "Preferred response address (placeholders may be used here)"
msgstr "Alamat email tanggapan (placeholder dapat digunakan di sini)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Pratinjau"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Preview of"
msgstr "Pratinjau dari"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "Sebelum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Tipe Aktivitas Sebelumnya"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "Cetak"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Privasi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Private channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Public Channels"
msgstr "Saluran Publik"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Kontrak Garansi Penerbit"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "Penerbit: Perbarui Notifikasi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Quick search..."
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "Balas:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#, python-format
msgid "Read less"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#: code:addons/mail/static/src/xml/thread_window.xml:0
#, python-format
msgid "Read more"
msgstr "Lihat lebih lanjut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Ready"
msgstr "Siap"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Siap Dikirim"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Diterima"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Received by Everyone"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Received by:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Penerima"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Recipients"
msgstr "Penerima"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Tipe Aktivitas yang Disarankan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__next_type_ids
msgid "Recommended Next Activities"
msgstr "Tipe Aktivitas yang Disarankan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Rekam ID Thread"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referensi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Salam,"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Reject"
msgstr "Tolak"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Reject selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reject |"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__moderation_status__rejected
msgid "Rejected"
msgstr "Ditolak"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "ID Dokumen Terkait"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Model Dokumen Terkait"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Nama Model Dokumen Terkait"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Pesan Terkait"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Rekanan terkait"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Kolom relasi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Remove message with explanation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Remove message without explanation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Hapus tindakan konteks untuk menggunakan contoh ini pada dokumen terkait"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Hapus pengikut ini"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Rename"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Rename conversation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Balas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Alamat email balasan. Pengaturan reply_to mengabaikan pembuatan thread "
"otomatis."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply-To"
msgstr "Balas-Ke"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_name
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Nama Berkas Laporan"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Ulangi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Konten rich-text"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Rich-text/Pesan HTML"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr "Rotasi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP Server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Penjual"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__res_id
msgid "Sample Document"
msgstr "Contoh Dokumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "Simpan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Simpan sebagai template baru"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Simpan sebagai contoh baru"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:0
#, python-format
msgid "Say something"
msgstr "Katakan sesuatu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Jadwal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Schedule Activity"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Jadwalkan aktivitas"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Jadwalkan aktivitas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
msgid "Scheduled Date"
msgstr "Tanggal Terjadwal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Tanggal Pengiriman yang Direncanakan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Cari Alias"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Cari Kelompok"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Search Moderation List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "Cari: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Seen by:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Select All"
msgstr "Pilih Semua"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Select all messages to moderate"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Pilih kolom target dari model dokumen terkait.\n"
"Jika berupa kolom relasi, Anda dapat memilih kolom target pada tujuan relasi."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
msgid "Selected group of users"
msgstr "Kelompok pengguna yang dipilih"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:0
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Kirim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "Kirim Email"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Kirim Email (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Kirim Sekarang"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Send a message"
msgstr "Kirim pesan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Send by mail"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "Kirim email"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Send explanation to author"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Send guidelines"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Send message"
msgstr "Kirim pesan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__email_send
msgid "Send messages by email"
msgstr "Kirim pesan melalui email"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_from
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Alamat pengirim (placeholder dapat digunakan di sini). Jika tidak diatur, "
"nilai baku yang dipakai adalah email alias penulis jika ada, atau alamat "
"email."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:0
#, python-format
msgid "Sending Error"
msgstr "Eror Pengiriman"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Sends messages by email"
msgstr "Kirim pesan melalui email"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Terkirim"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Penomoran"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Tindakan Server"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Kode Singkat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Jalan Pintas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Show"
msgstr "Tampilkan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan tindakan berikut sebelum hari ini"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Show an helper message"
msgstr "Tampilkan pesan penolong"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Tindakan sidebar"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Tindakan sidebar untuk membuat contoh ini tersedia pada data model dokumen "
"terkait"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_id
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Spesifikasikan model jika aktivitas seharusnya spesifik untuk model tertentu"
" dan tidak tersedia untuk pengelolaan aktivitas di model lain."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Membintangi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
msgid "State"
msgstr "Status"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_moderation__status
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Status"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Sub-kolom"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Sub-model"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields,field_description:mail.field_email_template_preview__subject
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#, python-format
msgid "Subject"
msgstr "Subjek"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__subject
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Judul (placeholder dapat digunakan di sini)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Judul..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Subject:"
msgstr "Judul:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Substitusi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Subtipe"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Subtipe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "Ringkasan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "Notifikasi sistem"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr "Kolom teknis untuk keperluan UX"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model_id
msgid ""
"Technical field to keep trace of the model at the beginning of the edition "
"for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.wizard_email_template_preview
msgid "Template Preview"
msgstr "Pratinjau Contoh"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__preview_lang
msgid "Template Preview Language"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Contoh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "Thank you!"
msgstr "Terima Kasih!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "The"

#. module: mail
#: code:addons/mail/models/ir_actions.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_moderation_channel_email_uniq
msgid "The email address must be unique per channel !"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "Kode html yang di-\"escape\" menggantikan jalan pintas"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Model (Jenis Dokumen Odoo) di mana alias ini dikaitkan. Email masuk yang "
"tidak menjawab catatan yang sudah ada akan menyebabkan pembuatan rekor baru "
"model ini (misalnya tugas proyek)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Nama email alias, misalnya 'pekerjaan' jika Anda ingin menangkap email untuk"
" <<EMAIL>>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Pemilik catatan yang dibuat setelah menerima email pada alias ini. Jika "
"kolom ini belum ditetapkan, sistem akan berusaha untuk menemukan pemilik "
"yang tepat berdasarkan pengirim (dari) alamat, atau akan menggunakan akun "
"Administrator jika tidak ada pengguna sistem yang ditemukan untuk alamat "
"tersebut."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "The partner can not join this channel"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Operasi yang diminta tidak dapat diselesaikan karena batasan keamanan. Hubungi administrator sistem Anda.\n"
"\n"
"(Jenis dokumen: %s, Operasi: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr "Jalan pintas yang harus digantikan pada Pesan Chat"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_id
#: model:ir.model.fields,help:mail.field_mail_template__model_id
msgid "The type of document this template can be used with"
msgstr "Tipe dokumen yang dapat digunakan dengan contoh ini"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Ini"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_address_mixin__email_normalized
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""
"Kelompok ini keliatan juga oleh yang bukan anggota. Kelompok yang tidak "
"kelihatan dapat menambah anggota dengan tombol undang."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Thread"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
msgid "To"
msgstr "Kepada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Kepada (Email)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Kepada (Rekanan)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Todo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/thread_window.xml:0
#, python-format
msgid "To:"
msgstr "Kepada:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "Hari Ini"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Aktivitas Hari Ini"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "Besok"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Topik yang dibahas dalam kelompok ini..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Nilai yang dilacak disimpan pada model yang terpisah. Kolom ini memungkinkan"
" Anda untuk merekonstruksi ulang pelacakan dan membuat statistik untuk "
"model."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Tracking"
msgstr "Pelacakan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Nilai Pelacakan"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Nilai Pelacakan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Nilai Pelacakan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__force_next
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__force_next
msgid "Trigger Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Jenis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Tidak dapat terhubung ke Server SMTP"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:0
#, python-format
msgid "Undefined"
msgstr "Belum Terdefinisi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Unfollow"
msgstr "Batal Ikuti"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr "Sayangnya alias email ini sudah digunakan, silahkan gunakan yang lain"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Unit"
msgstr "Satuan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
#, python-format
msgid "Unread messages"
msgstr "Pesan yang belum dibaca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unselect All"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unselect all messages to moderate"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unstar all"
msgstr "Hapus semua bintang"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unstar all messages"
msgstr "Hapus semua bintang pesan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Unsubscribe"
msgstr "Berhenti Subskripsi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "Unsubscribed"
msgstr "Berhenti Subskripsi"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Tipe laporan yang tidak didukung %s ditemukan."

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Unsupported search filter on moderation status"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "Unggah Dokumen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Uploaded"
msgstr "Berhasil diunggah"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Uploading"
msgstr "Mengunggah"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:0
#, python-format
msgid "Uploading error"
msgstr "Unggah eror"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Gunakan domain aktif"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Gunakan template"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use your own email servers"
msgstr "Gunakan server email Anda sendiri"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Digunakan untuk mengurutkan subtipe."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "User"
msgstr "Pengguna"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "User is offline"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "User is online"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread_window.xml:0
#, python-format
msgid "User name"
msgstr "Nama pengguna"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Pengguna:"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Pengguna"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %s %s and you."
msgstr "Pengguna pada saluran ini: %s %s dan Anda."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Menggunakan server email Anda sendiri diperlukan untuk mengirim/menerima "
"email pada versi Komunitas dan Enterprise. Pengguna daring sudah disediakan "
"server email yang siap dipakai (@mycompany.odoo.com)."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Tampilan"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"View \"mail.mail_channel_send_guidelines\" was not found. No email has been "
"sent. Please contact an administrator to fix this issue."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Tampilan %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Tipe Tampilan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "View all the attachments of the current record"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr "Viewer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Warning"
msgstr "Peringatan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Minggu"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Ketika kolom relasi dipilih sebagai kolom pertama, kolom ini memungkinkan "
"Anda untuk memilih kolom target dari model dokumen tujuan (sub-model)."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Ketika kolom relasi dipilih sebagai kolom pertama, kolom ini menampilkan "
"model dokumen dari kolom relasi."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Apakah pesan adalah catatan internal (mode komentar saja)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr "Apakah model ini mendukung pesan dan notifikasi."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Siapa yang dapat mengikuti aktivitas kelompok?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Tulis Masukan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Write something..."
msgstr "Tulis sesuatu..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:0
#, python-format
msgid "Yesterday"
msgstr "Kemarin"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You added <b>%s</b> to the conversation."
msgstr "Anda menambahkan <b>%s</b> ke percakapan."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "Anda sendirian pada saluran ini."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You are going to ban: %s. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You are going to discard %s messages. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You are going to discard 1 message. Do you confirm the action?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid ""
"You are going to send the guidelines to all the subscribers. Do you confirm "
"the action?"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "Anda dalam percakapan pribadi dengan <b>@%s</b>."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "Anda berada di saluran <b>#%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/attachment_box.js:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "Anda tidak diizinkan untuk mengunggah lampiran di sini."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to "
"unsubscribe?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You can not remove this partner from this channel"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You can not write on the record of other users"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You can not write on this field"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"Anda tidak dapat membuat pengguna baru dari sini.\n"
" Untuk membuat pengguna baru silakan ke panel konfigurasi."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Anda tidak dapat menghapus kelompok tersebut, karena kelompok Seluruh "
"Perusahaan diperlukan oleh modul lainnya."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You do not have the rights to modify fields related to moderation on one of "
"the channels you are modifying."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Anda telah ditugaskan ke"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "You have been invited to: %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "You have no message to moderate"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/user_menu.xml:0
#, python-format
msgid "You have set a chat message."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Anda dapat melampirkan berkas pada contoh ini, yang akan ditambahkan ke "
"semua email yang dibuat melalui contoh ini"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "You unpinned your conversation with <b>%s</b>."
msgstr "Anda menghapus \"pin\" percakapan Anda dengan <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "You unsubscribed from <b>%s</b>."
msgstr "Anda membatalkan subskripsi dari <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "You:"
msgstr "Anda:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:0
#, python-format
msgid "Your message has not been sent."
msgstr "Pesan Anda belum dikirim."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Your message is pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Your message was rejected by moderator."
msgstr ""

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr "Zoom In"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Zoom Out"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after validation date"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %s: %s"
msgstr "alias %s: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "pertemuan dewan pengurus"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "oleh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "channel."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "created"
msgstr "dibuat"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "hari"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "document"
msgstr "dokumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "selesai"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "mis. Diskusi Proposal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "followers"
msgstr "pengikut"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "for"
msgstr "untuk"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "from"
msgstr "dari"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "from:"
msgstr "dari:"

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "umum"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been"
msgstr "telah"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "konfigurasi alias salah"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "konfigurasi alias salah (data referensi tidak diketahui)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "less"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "model %s tidak menerima pembuatan dokumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "modified"
msgstr "dimodifikasi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "bulan"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "more"
msgstr "Babe I'm gonna leaves you"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "mycompany.odoo.com"
msgstr "mycompany.odoo.com"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr "sekarang"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "pada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "atau"

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr "rd"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:0
#, python-format
msgid "read less"
msgstr "tutup selengkapnya"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:0
#, python-format
msgid "read more"
msgstr "selengkapnya"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "record:"
msgstr "data:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "reply to missing document (%s,%s), fall back on document creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr "dibatasi khusus untuk anggota saluran"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "restricted to followers"
msgstr "dibatasi khusus untuk pengikut"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "restricted to known authors"
msgstr "dibatasi khusus untuk penulis yang dikenal"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "penjualan"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "this document"
msgstr "dokumen ini"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "eror tidak diketahui"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "model target tidak diketahui %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/attachment_box.js:0
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:0
#, python-format
msgid "unnamed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr ""
