# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cash_rounding
# 
# Translators:
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<PERSON>.<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-01-15 14:05+0000\n"
"PO-Revision-Date: 2020-03-12 07:02+0000\n"
"Last-Translator: <PERSON> <<PERSON>.<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "帳戶現金捨入"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "現金捨入"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.res_config_view_form_inherit_pos_cash_rounding
msgid "Cash Roundings"
msgstr "現金捨入"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "現金捨入"

#. module: pos_cash_rounding
#: code:addons/pos_cash_rounding/models/pos_config.py:0
#, python-format
msgid "Cash rounding strategy must be: 'Add a rounding line'"
msgstr "現金捨入策略必須是: '增加捨入明細'"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_config_view_form_inherit_cash_rounding
msgid "Define the smallest coinage of the currency used to pay"
msgstr "定義用於支付的貨幣的最小額硬幣"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.res_config_view_form_inherit_pos_cash_rounding
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "定義用於通過現金支付的貨幣的最小硬幣"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_account_cash_rounding__loss_account_id
msgid "Loss Account"
msgstr "損失科目"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "僅對現金使用四捨五入"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.res_config_view_form_inherit_pos_cash_rounding
msgid "Payments"
msgstr "付款"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS配置"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS訂單"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_pos_session
msgid "Point of Sale Session"
msgstr "POS前台"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_rounding_form_view_inherited
msgid "Profit Account"
msgstr "利潤科目"

#. module: pos_cash_rounding
#. openerp-web
#: code:addons/pos_cash_rounding/static/src/xml/pos.xml:0
#, python-format
msgid "ROUNDED"
msgstr ""

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_config_view_form_inherit_cash_rounding
msgid "Rounding Method"
msgstr "捨入方法"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only support the \"add a rounding line\" rounding "
"strategy."
msgstr ""

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_order_view_form_inherit_cash_rounding
msgid "Total Paid (with rounding)"
msgstr "支付總額(四捨五入)"

#. module: pos_cash_rounding
#: code:addons/pos_cash_rounding/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr "當使用的 pos營業點已經打開時，您不能更改現金捨入設定."
