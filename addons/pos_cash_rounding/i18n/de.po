# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cash_rounding
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-01-15 14:05+0000\n"
"PO-Revision-Date: 2020-03-12 07:02+0000\n"
"Last-Translator: philku79 <<EMAIL>>, 2020\n"
"Language-Team: German (https://www.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Bargeldrundung"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "Bargeld runden"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.res_config_view_form_inherit_pos_cash_rounding
msgid "Cash Roundings"
msgstr "Bargeld Rundungen"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "Bargeld runden"

#. module: pos_cash_rounding
#: code:addons/pos_cash_rounding/models/pos_config.py:0
#, python-format
msgid "Cash rounding strategy must be: 'Add a rounding line'"
msgstr "Bargeld Rundungsstrategie muss sein: \"Rundungszeile hinzufügen\""

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_config_view_form_inherit_cash_rounding
msgid "Define the smallest coinage of the currency used to pay"
msgstr ""
"Definieren Sie die kleinste Münzeinheit der Währung, die zur Bezahlung "
"genutzt wird."

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.res_config_view_form_inherit_pos_cash_rounding
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""
"Definieren Sie die kleinste Münzeinheit der Währung, die zur Bezahlung "
"genutzt wird."

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_account_cash_rounding__loss_account_id
msgid "Loss Account"
msgstr "Verlustkonto"

#. module: pos_cash_rounding
#: model:ir.model.fields,field_description:pos_cash_rounding.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Rundung nur bei Bargeld anwenden"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.res_config_view_form_inherit_pos_cash_rounding
msgid "Payments"
msgstr "Zahlungen"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Point Of Sale Konfiguration"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_pos_order
msgid "Point of Sale Orders"
msgstr "Point Of Sale Verkäufe"

#. module: pos_cash_rounding
#: model:ir.model,name:pos_cash_rounding.model_pos_session
msgid "Point of Sale Session"
msgstr "Point of Sale Sitzung"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_rounding_form_view_inherited
msgid "Profit Account"
msgstr "Erlöskonto"

#. module: pos_cash_rounding
#. openerp-web
#: code:addons/pos_cash_rounding/static/src/xml/pos.xml:0
#, python-format
msgid "ROUNDED"
msgstr "GERUNDET"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_config_view_form_inherit_cash_rounding
msgid "Rounding Method"
msgstr "Rundungsverfahren"

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only support the \"add a rounding line\" rounding "
"strategy."
msgstr ""
"Das Modul Point of Sale unterstützt ausschließlich \"Rundungszeile "
"hinzufügen\" als Rundungs-Strategie."

#. module: pos_cash_rounding
#: model_terms:ir.ui.view,arch_db:pos_cash_rounding.pos_order_view_form_inherit_cash_rounding
msgid "Total Paid (with rounding)"
msgstr "Bezahlte Gesamtsumme (inkl. Rundung)"

#. module: pos_cash_rounding
#: code:addons/pos_cash_rounding/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"Es ist nicht gestattet, die Bargeld-Rundung zu ändern, während eine Sitzung "
"des Point of Sale geöffnet ist."
