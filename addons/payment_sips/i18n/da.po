# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON><PERSON>, 2020\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; flere ordre fundet"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; ingen ordre fundet"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Currency not supported by Wordline"
msgstr "Valuta ikke understøttet af Wordline"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr "Forkert betalingsindløser"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "Brugerflade version"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "Sælger ID"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betalingsindløser"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransaktion"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr "Produktions URL"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "Udbyder"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr "Hemmelig nøgle"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Sips: received data for reference %s"
msgstr "Sips: Modtaget data for reference %s"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr "Test URL"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr "Bruges udelukkende til produktion"
