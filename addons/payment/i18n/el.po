# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids_nbr
msgid "# of Invoices"
msgstr "# των τιμολογιών "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__html_3ds
msgid "3D Secure HTML"
msgstr "3D Secure HTML"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Επιστροφή στον Λογαριασμό μου"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid ""
"<i class=\"fa fa-check text-muted\" title=\"This payment method has not been"
" verified by our system.\" role=\"img\" aria-label=\"Not verified\"/>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid ""
"<i class=\"fa fa-check text-success\" title=\"This payment method is "
"verified by our system.\" role=\"img\" aria-label=\"Ok\"/>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "<i class=\"fa fa-home\" role=\"img\" aria-label=\"Home\" title=\"Home\"/>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-lock\"/> Pay"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-plus-circle\"/> Add new card"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/> Διαγραφή"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "<p>This card is currently linked to the following records:<p/>"
msgstr "<p>Αυτή η κάρτα είναι συνδεδεμένη με τις παρακάτω εγγραφές: </p>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<small class=\"text-muted\">(Some fees may apply)</small>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span class=\"fa fa-arrow-right\"> Get my Stripe keys</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span class=\"fa fa-arrow-right\"> How to configure your PayPal "
"account</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Credit Cards</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A journal must be specified of the acquirer %s."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A payment acquirer is required to create a transaction."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction already exists."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "A transaction %s with %s initiated using %s credit card."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "A transaction %s with %s initiated."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A transaction can't be linked to invoices having different currencies."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "A transaction can't be linked to invoices having different partners."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "Διακριτικό Πρόσβασης"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "Λογαριασμός"

#. module: payment
#: model:ir.model,name:payment.model_account_chart_template
msgid "Account Chart Template"
msgstr "Πρότυπο γραφήματος λογαριασμού"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Account Holder:"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Αριθμός Λογαριασμού"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Account Number:"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Acquirer"
msgstr "Αποδέκτης"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "Λογαριασμός Αποδέκτη / Acquirer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
msgid "Acquirer Ref."
msgstr "Αναφ. Αποδέκτη"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "Αναφορά Αποδέκτη"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "Αποδέκτες"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Acquirers list"
msgstr "Λίστα Αποδεκτών"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Activate"
msgstr "Ενεργοποίηση"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "Προσθήκη Επιπλέον Χρεώσεων"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Address"
msgstr "Διεύθυνση"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__save_token__always
msgid "Always"
msgstr "Πάντα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Ποσό"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "An error occured during the processing of this payment"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "An error occured during the processing of this payment."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Εφαρμογή"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_view_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__authorize_implemented
msgid "Authorize Mechanism Supported"
msgstr "Υποστηρίζεται Μηχανισμός Εξουσιοδότησης"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Εγκεκριμένη"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Availability"
msgstr "Διαθεσιμότητα"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "Τράπεζα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Όνομα Τράπεζας"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank:"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Document ID"
msgstr "Ταυτότητα Εγγράφου Επανάκλησης"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "Μοντέλο Εγγράφου Επανάκλησης"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "Hash επανάκλησης"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "Μέθοδος επανάκλησης"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Ακύρωση"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Cancel Message"
msgstr "Μήνυμα Ακύρωσης"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Ακυρώθηκε"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot setup the payment"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Cannot setup the payment."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "Χειροκίνητη Καταγραφή Ποσού"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Capture Transaction"
msgstr "Καταγραφή Συναλλαγής"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid "Capture the amount from Odoo, when the delivery is completed."
msgstr "Καταγραφή του ποσού από το Odoo, όταν ολοκληρωθεί η παράδοση."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Choose your default customer payment method."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "City"
msgstr "Πόλη"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "Κλείσιμο"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "Χρώμα"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Configure"
msgstr "Παραμετροποίηση"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "Επιβεβαίωση Διαγραφής"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "Αντίστοιχο αρθρώμα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Count Payment Token"
msgstr "Πλήθος Διακριτικών Πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Χώρες"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Country"
msgstr "Χώρα"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "Create a new payment transaction"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.payment_token_action
msgid "Create a saved payment data"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Credentials"
msgstr "Διαπιστευτήρια"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ingenico
msgid "Credit Card (powered by Ingenico)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payu
msgid "Credit Card (powered by PayUmoney)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit Card (powered by Stripe)"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Νόμισμα"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__manual
msgid "Custom Payment Form"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Πελάτης"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Περιγραφή"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Determine the display order"
msgstr "Καθορισμός σειράς εμφάνισης"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Disabled"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Dismiss"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "Ολοκληρωμένο Μήνυμα"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Προσχέδιο"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "E-mail"
msgstr "E-mail"

#. module: payment
#: model:account.payment.method,name:payment.account_payment_method_electronic_in
msgid "Electronic"
msgstr "Ηλεκτρονικά"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "Email"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Σφάλμα"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Error: "
msgstr "Σφάλμα:"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Fee"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Fees"
msgstr "Προμήθεια"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_implemented
msgid "Fees Computation Supported"
msgstr "Υποστήριξη Υπολογισμού Προμήθειας"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "Fees amount; set by the system because depends on the acquirer"
msgstr ""
"Ποσό προμήθειας. Ορίζεται από το σύστημα επειδή εξαρτάται από τον αποδέκτη"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "Field used to store error and/or validation messages for information"
msgstr ""
"Το πεδίο χρησιμοποιείται για να αποθηκεύει τα μηνύματα λάθους ή / και "
"επικύρωσης για πληροφόρηση"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Σταθερή εγχώρια προμήθεια"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Σταθερή διεθνής προμήθεια"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__form
msgid "Form"
msgstr "Φόρμα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__view_template_id
msgid "Form Button Template"
msgstr "Πρότυπο Φόρμας με Κουμπιά"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__form_save
msgid "Form with tokenization"
msgstr "Φόρμα με Διακριτικό"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "Από"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_processed
msgid "Has the payment been post processed"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "Μήνυμα Βοήθειας"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "How the acquirer is displayed to the customers."
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "Κωδικός"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Εικόνα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "Εικόνα που εμφανίζεται στη φόρμα πληρωμής"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test\n"
"             payment interface. This mode is advised when setting up the\n"
"             acquirer. Watch out, test and production modes require\n"
"             different credentials."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ingenico
msgid "Ingenico"
msgstr "Ingenico"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Install"
msgstr "Εγκατάσταση"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "Κατάσταση Εγκατάστασης"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Installed"
msgstr "Εγκατεστημένο"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "Internal reference of the TX"
msgstr "Εσωτερική αναφορά του TX"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "Invalid token found! Token acquirer %s != %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:0
#, python-format
msgid "Invalid token found! Token partner %s != %s"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Invoice(s)"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Τιμολόγια"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entries"
msgstr "Ημερολογιακές Καταχωρήσεις"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "Journal where the successful transactions will be posted"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Γλώσσα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__save_token__ask
msgid "Let the customer decide"
msgstr "Αφήστε τον πελάτη να αποφασίσει"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "List of Acquirers supporting this payment icon."
msgstr ""
"Λίστα των αποδεκτών πληρωμής που υποστηρίζουν αυτό το εικονίδιο πληρωμής."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Manage Payment Methods"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Manage your payment methods"
msgstr "Διαχειριστείτε τις δικές σας μεθόδους πληρωμής"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Χειροκίνητα"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Message"
msgstr "Μήνυμα"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "Message displayed if payment is authorized."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "Message displayed to explain and help the payment process."
msgstr ""
"Μήνυμα που εμφανίζεται για να επεξηγήσει και να βοηθήσει στην διαδικασία "
"πληρωμής."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid "Message displayed, if order is cancel during the payment process."
msgstr ""
"Το μήνυμα που εμφανίζεται, όταν ακυρωθεί η παραγγελία κατά τη διαδικασία "
"πληρωμής."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"Message displayed, if order is done successfully after having done the "
"payment process."
msgstr ""
"Το μήνυμα που εμφανίζεται, όταν η παραγγελία ολοκληρωθεί επιτυχώς μετά την "
"ολοκλήρωση της διαδικασίας πληρωμής."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid ""
"Message displayed, if order is in pending state after having done the "
"payment process."
msgstr ""
"Το μήνυμα που εμφανίζεται, όταν η παραγγελία είναι σε κατάσταση αναμονής "
"μετά την ολοκλήρωση της διαδικασία πληρωμής."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Messages"
msgstr "Μηνύματα"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Μέθοδος"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Missing partner reference when trying to create a new payment token"
msgstr ""
"Δεν υπάρχει αναφορά συνεργάτη κατά την προσπάθεια δημιουργίας ενός νέου "
"διακριτικού πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Name"
msgstr "Περιγραφή"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "Name of the payment token"
msgstr "Όνομα του διακριτικού πληρωμής"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__save_token__none
msgid "Never"
msgstr "Ποτέ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "No payment acquirer found."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "No payment method selected"
msgstr "Δεν έχει επιλεγεί μέθοδος πληρωμής"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Λάβετε υπόψη ότι τα διακριτικά από τους αποδέκτες πληρωμής ορίζονται μόνο σε"
" εξουσιοδοτημένες συναλλαγές (σε αντίθεση με την καταγραφή του ποσού) δεν "
"είναι διαθέσιμα."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__payment_flow
msgid ""
"Note: Subscriptions does not take this field in account, it uses server to "
"server by default."
msgstr ""
"Σημείωση: Οι συνδρομές δεν συμπεριλαμβάνουν αυτό το πεδίο, χρησιμοποιείται "
"από προεπιλογή στην διαδικασία διακομιστή-σε-διακομιστή."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Ok"
msgstr "ΟΚ"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Only transactions having the capture status can be captured."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Only transactions having the capture status can be voided."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Άλλο"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
msgid "Partner"
msgstr "Συναλλασόμενος"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Όνομα Συνεργάτη"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
msgid "PayPal"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payu
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Payment"
msgstr "Πληρωμή"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Acquirer"
msgstr "Αποδέκτης Πληρωμής"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.acquirer_list
msgid "Payment Acquirers"
msgstr "Αποδέκτες Πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_flow
msgid "Payment Flow"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Followup"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Form"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Payment Icon"
msgstr "Εικονίδιο Πληρωμής"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "Εικονίδια Πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Ημερολόγιο Πληρωμών"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Payment Method"
msgstr "Μέθοδος Πληρωμής"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Payment Methods"
msgstr "Μέθοδοι Πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_token_id
msgid "Payment Token"
msgstr "Διακριτικό Πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_tree_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_view_search
msgid "Payment Tokens"
msgstr "Διακριτικά Πληρωμής"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
msgid "Payment Transaction"
msgstr "Συναλλαγή Πληρωμής"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.actions.act_window,name:payment.action_payment_tx_ids
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
#: model_terms:ir.ui.view,arch_db:payment.transaction_list
msgid "Payment Transactions"
msgstr "Συναλλαγές Πληρωμής"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__payment_flow__s2s
msgid "Payment from Odoo"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Payment method set!"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
msgid "Payments"
msgstr "Συναλλαγές"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Payments received"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr ""

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "Εκρεμμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "Μήνυμα σε Αναμονή"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Τηλέφωνο"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Please configure a payment acquirer."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please make a payment to:"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Please select a payment method."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Please select the option to add a new payment method."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr ""

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "Post process payment transactions"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Επεξεργάστηκε από"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Provider"
msgstr "Πάροχος"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "Αιτία:"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__payment_flow__form
msgid "Redirection to the acquirer website"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Σχετικό"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "Reference of the TX as stored in the acquirer database"
msgstr "Αναφορά του ΤΧ όπως είναι αποθηκευμένη στη βάση δεδομένων αγοραστή"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "Κωδικός σχετικού εγγράφου"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Μοντέλο Σχετικού Εγγράφου"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Required fields not filled: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__return_url
msgid "Return URL after payment"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__registration_view_template_id
msgid "S2S Form Template"
msgstr "S2S Πρότυπο φόρμας"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__save_token
msgid "Save Cards"
msgstr "Αποθήκευση Καρτών"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Save my payment data"
msgstr "Αποθήκευση των δεδομένων πληρωμής μου"

#. module: payment
#: model:ir.actions.act_window,name:payment.payment_token_action
#: model:ir.ui.menu,name:payment.payment_token_menu
msgid "Saved Payment Data"
msgstr "Αποθηκευμένα Δεδομένα Πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved payment token"
msgstr "Αποθηκευμένο διακριτικό πληρωμής"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__token_implemented
msgid "Saving Card Data supported"
msgstr "Υποστηρίζεται η αποθήκευση δεδομένων καρτών "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Σφάλμα Διακομιστή"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__server2server
msgid "Server To Server"
msgstr "Διακομιστής προς διακομιστή"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Server error"
msgstr "Σφάλμα διακομιστή"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Server error:"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Set payments"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__short_name
msgid "Short name"
msgstr "Σύντομο όνομα"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
msgid "Status"
msgstr "Κατάσταση"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Δημοσιεύσιμο Κλειδί Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Κρυφό Κλειδί Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "Υποστηριζόμενα Εικονίδια Πληρωμής"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__registration_view_template_id
msgid "Template for method registration"
msgstr "Πρότυπο για την μέθοδο καταχώρισης"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Test Mode"
msgstr "Λειτουργία Δοκιμής"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The %s payment acquirers are not allowed to manual capture mode!"
msgstr ""
"Οι %s αποδέκτες πληρωμών δεν επιτρέπονται να πραγματοποιήσουν χειροκίνητη "
"καταγραφή!"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "The SEPA QR Code informations are not set correctly."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The customer has selected %s to pay this document."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has been authorized. Waiting for "
"capture..."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has been cancelled with the following "
"message: %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The transaction %s with %s for %s has been cancelled."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has been confirmed. The related payment is"
" posted: %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"The transaction %s with %s for %s has return failed with the following error"
" message: %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The transaction %s with %s for %s is pending."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid ""
"This Transaction was automatically processed & refunded in order to validate"
" a new credit card."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
msgid ""
"This field holds the image used for this payment icon, limited to "
"1024x1024px"
msgstr ""
"Αυτό το πεδίο αποθηκεύει την εικόνα που χρησιμοποιείται για αυτό το "
"εικονίδιο πληρωμής, με μέγιστο μέγεθος τα 1024x1024 εικονοστοιχεία."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"This payment gateway is available for selected countries. If none is "
"selected it is available for all countries."
msgstr ""
"Αυτή η πύλη πληρωμής είναι διαθέσιμη για τις επιλεγμένες χώρες. Εάν δεν έχει"
" επιλεγεί καμία χώρα, θα είναι διαθέσιμη για όλες τις χώρες."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__view_template_id
msgid ""
"This template renders the acquirer button with all necessary values.\n"
"It is rendered with qWeb with the following evaluation context:\n"
"tx_url: transaction URL to post the form\n"
"acquirer: payment.acquirer browse record\n"
"user: current user browse record\n"
"reference: the transaction reference number\n"
"currency: the transaction currency browse record\n"
"amount: the transaction amount, a float\n"
"partner: the buyer partner browse record, not necessarily set\n"
"partner_values: specific values about the buyer, for example coming from a shipping form\n"
"tx_values: transaction values\n"
"context: the current context dictionary"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
msgid "Transactions"
msgstr "Συναλλαγές"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__check_validity
msgid ""
"Trigger a transaction of 1 currency unit and its refund to check the validity of new credit cards entered in the customer portal.\n"
"        Without this check, the validity will be verified at the very first transaction."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__type
msgid "Type"
msgstr "Τύπος"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Upgrade"
msgstr "Αναβάθμιση"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__qr_code
msgid "Use SEPA QR Code"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__date
msgid "Validation Date"
msgstr "Ημερομηνία Επιβεβαίωσης"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__type__validation
msgid "Validation of the bank card"
msgstr "Επικύρωση της τραπεζικής κάρτας"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Μεταβλητή εγχώρια προμήθεια (σε ποσοστό)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Μεταβλητή διεθνής προμήθεια (σε ποσοστό)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "Επαληθευμένο"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__check_validity
msgid "Verify Card Validity"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Void Transaction"
msgstr "Άκυρη συναλλαγή"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Waiting for payment confirmation..."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Warning!"
msgstr "Προειδοποίηση"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to delete your payment method at the moment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to redirect you to the payment form."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "We're unable to process your payment."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "Έμβασμα"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "You have to set a journal for your payment acquirer %s."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ingenico
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "ZIP"
msgstr "ΤΚ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "Τ.Κ."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "and more"
msgstr "και περισσότερα"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "e.g. Your credit card details are wrong. Please verify."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr ""
