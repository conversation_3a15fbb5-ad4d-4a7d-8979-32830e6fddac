# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_support
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-18 09:47+0000\n"
"PO-Revision-Date: 2019-08-26 09:11+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Croatian (https://www.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:0
#, python-format
msgid " (offline)"
msgstr ""

#. module: im_support
#: model:ir.model,name:im_support.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmjeravanje"

#. module: im_support
#: model_terms:ir.ui.view,arch_db:im_support.support_qunit_suite
msgid "IM Support Tests"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:0
#, python-format
msgid ""
"None of our operators are available. <a href='https://www.odoo.com/help' "
"target='_blank'>Submit a ticket</a> to ask your question now."
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/systray_messaging_menu.js:0
#: code:addons/im_support/static/src/js/systray_messaging_menu.js:0
#, python-format
msgid "Odoo Live Support"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/xml/discuss.xml:0
#, python-format
msgid "Odoo Support"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:0
#, python-format
msgid "Support"
msgstr "Podrška"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/mail_manager.js:0
#, python-format
msgid "The Support server can't be reached."
msgstr ""
