# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * im_support
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:48
#, python-format
msgid " (offline)"
msgstr ""

#. module: im_support
#: model:ir.model,name:im_support.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: im_support
#: model_terms:ir.ui.view,arch_db:im_support.support_qunit_suite
msgid "IM Support Tests"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:187
#, python-format
msgid ""
"None of our operators are available. <a href='https://www.odoo.com/help' "
"target='_blank'>Submit a ticket</a> to ask your question now."
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/xml/discuss.xml:10
#, python-format
msgid "Odoo Support"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:41
#: code:addons/im_support/static/src/js/systray_messaging_menu.js:30
#, python-format
msgid "Support"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/mail_manager.js:153
#, python-format
msgid "The Support server can't be reached."
msgstr ""
