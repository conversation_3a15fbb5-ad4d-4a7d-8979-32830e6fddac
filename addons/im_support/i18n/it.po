# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_support
# 
# Translators:
# <PERSON>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-18 09:47+0000\n"
"PO-Revision-Date: 2019-08-26 09:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:0
#, python-format
msgid " (offline)"
msgstr " (fuori linea)"

#. module: im_support
#: model:ir.model,name:im_support.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: im_support
#: model_terms:ir.ui.view,arch_db:im_support.support_qunit_suite
msgid "IM Support Tests"
msgstr ""

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:0
#, python-format
msgid ""
"None of our operators are available. <a href='https://www.odoo.com/help' "
"target='_blank'>Submit a ticket</a> to ask your question now."
msgstr ""
"Nessun operatore disponibile. <a href='https://www.odoo.com/help' "
"target='_blank'>Apri un ticket</a> per effettuare subito una domanda."

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/systray_messaging_menu.js:0
#: code:addons/im_support/static/src/js/systray_messaging_menu.js:0
#, python-format
msgid "Odoo Live Support"
msgstr "Supporto in linea Odoo"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/xml/discuss.xml:0
#, python-format
msgid "Odoo Support"
msgstr "Supporto Odoo"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/support_channel.js:0
#, python-format
msgid "Support"
msgstr "Supporto"

#. module: im_support
#. openerp-web
#: code:addons/im_support/static/src/js/mail_manager.js:0
#, python-format
msgid "The Support server can't be reached."
msgstr "Impossibile raggiungere il server per il supporto."
