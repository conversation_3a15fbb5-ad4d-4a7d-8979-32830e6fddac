# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pad
# 
# Translators:
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://www.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "API Key"
msgstr "API 金鑰"

#. module: pad
#: model:ir.model,name:pad.model_res_company
msgid "Companies"
msgstr "公司"

#. module: pad
#: model:ir.model,name:pad.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: pad
#: model:ir.model.fields,help:pad.field_res_company__pad_key
#: model:ir.model.fields,help:pad.field_res_config_settings__pad_key
msgid "Etherpad lite api key."
msgstr "Etherpad lite 的 api key。"

#. module: pad
#: model:ir.model.fields,help:pad.field_res_company__pad_server
#: model:ir.model.fields,help:pad.field_res_config_settings__pad_server
msgid "Etherpad lite server. Example: beta.primarypad.com"
msgstr "Etherpad lite伺服器。例如 beta.primarypad.com"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common__id
msgid "ID"
msgstr "ID"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:149
#, python-format
msgid "Loading"
msgstr "正在載入"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_company__pad_key
msgid "Pad Api Key"
msgstr "Pad Api 密鑰"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_config_settings__pad_key
msgid "Pad Api Key *"
msgstr "Pad Api 密鑰 *"

#. module: pad
#: model:ir.model,name:pad.model_pad_common
msgid "Pad Common"
msgstr "Pad 公用"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_company__pad_server
msgid "Pad Server"
msgstr "Pad 伺服器"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_config_settings__pad_server
msgid "Pad Server *"
msgstr "Pad 伺服器 *"

#. module: pad
#: code:addons/pad/models/pad.py:58
#, python-format
msgid ""
"Pad creation failed, either there is a problem with your pad server URL or "
"with your connection."
msgstr "Pad的創建失敗了，估您的Pad 網址有問題或者您的連接有問題。"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/xml/pad.xml:6
#, python-format
msgid ""
"Please enter your Etherpad credentials through the menu 'Settings > General "
"Settings'."
msgstr "請通過選單「設定 > 常規設定」輸入 Etherpad 憑據。"

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "Server"
msgstr "伺服器"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/xml/pad.xml:14
#, python-format
msgid "Switch pad"
msgstr "pad切換"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:168
#, python-format
msgid "This pad will be initialized on first edit"
msgstr "此Pad將在第一個編輯時初始化"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:162
#, python-format
msgid "Unable to load pad"
msgstr "不能上載至Pad"

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "e.g. beta.primarypad.com"
msgstr "例如：beta.primarypad.com"
