# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* uom
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>kra<PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# hoxhe <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <Mohammad.al<PERSON>@outlook.com>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey\">\n"
"                                    e.g: 1*(reference unit)=ratio*(this unit)\n"
"                                </span>"
msgstr ""

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey\">\n"
"                                    e.g: 1*(this unit)=ratio*(reference unit)\n"
"                                </span>"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__active
msgid "Active"
msgstr "نشط"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid "Add a new unit of measure"
msgstr "إضافة وحدة قياس جديدة "

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid "Add a new unit of measure category"
msgstr "إضافة فئة وحدة قياس جديدة "

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Archived"
msgstr "مؤرشف"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor_inv
msgid "Bigger Ratio"
msgstr "نسبة أكبر"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__bigger
msgid "Bigger than the reference Unit of Measure"
msgstr "أكبر من وحدة القياس المرجعية"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__category_id
msgid "Category"
msgstr "فئة"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"لا يمكن التحويل بين وحدات القياس إلا إذا كانت تنتمي لنفس الفئة. سيتم إجراء "
"التحويل بناءً على النسب."

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: uom
#: model:uom.uom,name:uom.product_uom_day
msgid "Days"
msgstr "الأيام"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_category__measure_type__length
msgid "Default Length"
msgstr ""

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_category__measure_type__unit
msgid "Default Units"
msgstr ""

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_category__measure_type__volume
msgid "Default Volume"
msgstr ""

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_category__measure_type__weight
msgid "Default Weight"
msgstr ""

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_category__measure_type__working_time
msgid "Default Working Time"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__display_name
#: model:ir.model.fields,field_description:uom.field_uom_uom__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: uom
#: model:uom.uom,name:uom.product_uom_dozen
msgid "Dozens"
msgstr "درزينات "

#. module: uom
#: model:uom.uom,name:uom.product_uom_hour
msgid "Hours"
msgstr "الساعات"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category: 1 * (this unit) = ratio * (reference unit)"
msgstr ""
"بكم مرة تعد وحدة القياس هذه أكبر من وحدة القياس المرجعية في هذه الفئة: 1 * "
"(هذه الوحدة) = النسبة * (الوحدة المرجعية) "

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category: 1 * (reference unit) = ratio * (this unit)"
msgstr ""
"بكم مرة تعد وحدة القياس هذه أكبر أو أصغر مقارنة بوحدة القياس المرجعية في هذه"
" الفئة: 1 * (هذه الوحدة) = النسبة * (الوحدة المرجعية) "

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__id
#: model:ir.model.fields,field_description:uom.field_uom_uom__id
msgid "ID"
msgstr "المُعرف"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category____last_update
#: model:ir.model.fields,field_description:uom.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: uom
#: model:uom.category,name:uom.uom_categ_length
msgid "Length / Distance"
msgstr "طول / المسافة"

#. module: uom
#: model:uom.uom,name:uom.product_uom_litre
msgid "Liters"
msgstr ""

#. module: uom
#: model:res.groups,name:uom.group_uom
msgid "Manage Multiple Units of Measure"
msgstr "إدارة وحدات متعددة للقياس"

#. module: uom
#: model:ir.model,name:uom.model_uom_uom
msgid "Product Unit of Measure"
msgstr "وحدة قياس المنتج"

#. module: uom
#: model:ir.model,name:uom.model_uom_category
msgid "Product UoM Categories"
msgstr "فئات وحدات قياس المنتج"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor
msgid "Ratio"
msgstr "النسبة"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__reference
msgid "Reference Unit of Measure for this category"
msgstr "وحدة قياس المرجع لهذه الفئة "

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__rounding
msgid "Rounding Precision"
msgstr "دقة التقريب"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Search UOM"
msgstr "البحث عن وحدة القياس "

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__smaller
msgid "Smaller than the reference Unit of Measure"
msgstr "أصغر من وحدة القياس المرجعية"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""
"ستكون الكمية المحتسبة من أضعاف هذه القيمة. استخدم 1.0 لوحدة قياس لا يمكن "
"تقسيمها بعد الآن، كقطعة. "

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_gt_zero
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr "لا يمكن أن يكون معدل التحويل لوحدة قياس 0! "

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_reference_is_one
msgid "The reference unit must have a conversion factor equal to 1."
msgstr "يجب أن يكون للوحدة المرجعية معامل تحويل يساوي 1. "

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_rounding_gt_zero
msgid "The rounding precision must be strictly positive."
msgstr "يجب أن تكون دقة التقريب قيمة موجبة. "

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The unit of measure %s defined on the order line doesn't belong to the same "
"category than the unit of measure %s defined on the product. Please correct "
"the unit of measure defined on the order line or on the product, they should"
" belong to the same category."
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__uom_type
msgid "Type"
msgstr "النوع"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__measure_type
msgid "Type of Measure"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__measure_type
msgid "Type of measurement category"
msgstr ""

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__active
msgid ""
"Uncheck the active field to disable a unit of measure without deleting it."
msgstr "قم بإلغاء تحديد الحقل النشط لتعطيل وحدة قياس دون حذفها. "

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_unit
msgid "Unit"
msgstr "الوحدة"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__name
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__name
msgid "Unit of Measure Category"
msgstr "قسم وحدة القيس"

#. module: uom
#: model:uom.uom,name:uom.product_uom_unit
msgid "Units"
msgstr "الوحدات"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_form_action
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_tree_view
msgid "Units of Measure"
msgstr "وحدات القياس"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "فئات وحدات القياس "

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_tree_view
msgid "Units of Measure categories"
msgstr "فئات وحدات القياس"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"            converted between each others. For example, in the category\n"
"            <i>'Time'</i>, you will have the following units of measure:\n"
"            Hours, Days."
msgstr ""
"يمكن تحويل وحدات القياس التي تنتمي إلى \n"
"            نفس الفئة بين بعضها. على سبيل المثال، في الفئة\n"
"            <i>'الوقت'</i>، ستكون لديك وحدات القياس التالية:\n"
"             ساعات، أيام. "

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"UoM category %s should have a reference unit of measure. If you just created"
" a new category, please record the 'reference' unit first."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should only have one reference unit of measure."
msgstr "يجب أن يكون لفئة وحدة القياس %s وحدة قياس مرجعية واحدة فقط. "

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_vol
msgid "Volume"
msgstr "الحجم"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_kgm
msgid "Weight"
msgstr "الوزن"

#. module: uom
#: model:uom.category,name:uom.uom_categ_wtime
msgid "Working Time"
msgstr "فترة العمل"

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_category_uom_category_unique_type
msgid "You can have only one category per measurement type."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "You cannot delete this UoM Category as it is used by the system."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"You cannot delete this UoM as it is used by the system. You should rather "
"archive it."
msgstr ""

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"            Measure within the same category."
msgstr ""
"عليك تحديد معدل تحويل بين عدة وحدات\n"
"            قياس ضمن نفس الفئة. "

#. module: uom
#: model:uom.uom,name:uom.product_uom_cm
msgid "cm"
msgstr "سم"

#. module: uom
#: model:uom.uom,name:uom.product_uom_floz
msgid "fl oz"
msgstr "أونصة سائلة"

#. module: uom
#: model:uom.uom,name:uom.product_uom_foot
msgid "foot(ft)"
msgstr "قدم(ق)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_gal
msgid "gals"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_inch
msgid "inches"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_kgm
msgid "kg"
msgstr "كجم"

#. module: uom
#: model:uom.uom,name:uom.product_uom_km
msgid "km"
msgstr "كم"

#. module: uom
#: model:uom.uom,name:uom.product_uom_lb
msgid "lbs"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_mile
msgid "miles"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_oz
msgid "ozs"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_qt
msgid "qt"
msgstr "كوارت"
