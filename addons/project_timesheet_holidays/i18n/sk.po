# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <jar<PERSON>.<PERSON><PERSON>@ekoenergo.sk>, 2019
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytický riadok"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
msgid "Analytic Lines"
msgstr "Analytické riadky"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#, python-format
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off. If you don't want a timesheet, you should leave the internal "
"project and task empty."
msgstr ""
"Interný projekt aj úloha sú potrebné na vygenerovanie pracovného výkazu pre "
"voľné dni. Ak si neželáte pracovný výkaz, mali by ste nechať interný projekt"
" a úlohu prázdne."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenie konfigurácie"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_company__leave_timesheet_project_id
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""
"Predvolená hodnota projektu pre pracovný výkaz generovaný z typu voľných "
"dní."

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheet"
msgstr "Generovanie pracovného výkazu"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"Ak je toto políčko začiarknuté, pri potvrdení pracovného voľna sa vygeneruje"
" pracovný výkaz projekte dovolenka."

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_project_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_project_id
#, python-format
msgid "Internal Project"
msgstr "Interný projekt"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__holiday_id
msgid "Leave Request"
msgstr "Žiadosť o voľno"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Projekt"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Úloha"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
msgid "Task for timesheet"
msgstr "Úloha pre pracovný výkaz"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr "Interný projekt spoločnosti by mal byť v tejto spoločnosti."

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
msgid ""
"The project will contain the timesheet generated when a time off is "
"validated."
msgstr ""
"Projekt bude obsahovať pracovný výkaz vygenerovaný pri overení voľných dní."

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
#, python-format
msgid "Time Off"
msgstr "Voľné dni"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "Voľné dni úloha"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "Typ voľných dní"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheet"
msgstr "Časový rozvrh"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot delete timesheet lines attached to a leaves. Please cancel the "
"leaves instead."
msgstr ""
"Nemožno odstrániť riadky pracovného výkazu pripojené k listom. Namiesto toho"
" zrušte listy."
