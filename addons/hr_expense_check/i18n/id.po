# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_check
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# Ikh<PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: Ikh<PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Indonesian (https://www.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__check_amount_in_words
msgid "Amount in Words"
msgstr "Jumlah dalam kata"

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__check_number
msgid "Check Number"
msgstr "Nomor cek"

#. module: hr_expense_check
#: model:ir.model.fields,help:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "Centang opsi jika cek Anda yang belum dicetak tidak bernomor."

#. module: hr_expense_check
#: model:ir.model,name:hr_expense_check.model_hr_expense_sheet_register_payment_wizard
msgid "Expense Register Payment Wizard"
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__check_manual_sequencing
msgid "Manual Numbering"
msgstr "Penomoran manual"

#. module: hr_expense_check
#: model:ir.model.fields,help:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__check_number
msgid ""
"Number of the check corresponding to this payment. If your pre-printed check"
" are not already numbered, you can manage the numbering in the journal "
"configuration page."
msgstr ""
"Nomor cek yang berkenaan dengan pembayaran ini. Jika cek Anda yang belum "
"dicetak belum bernomor, Anda dapat mengatur penomoran pada halaman "
"pengaturan jurnal."

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__payment_method_code_2
msgid "Payment Method Code 2"
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,help:hr_expense_check.field_hr_expense_sheet_register_payment_wizard__payment_method_code_2
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
"Kolom teknis yang digunakan untuk menyesuaikan antarmuka untuk tipe "
"pembayaran yang dipilih."
