# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_twitter
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# j<PERSON><PERSON> jensen <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: lhmflexerp <<EMAIL>>, 2019\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                Vis mig hvordan jeg indhenter Twitter API-nøglen og Twitter API-hemmeligheden"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Twitter Roller</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Twitter Roller</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Callback URL: </strong>Leave blank"
msgstr "<strong>Callback URL: </strong>Efterlad tom"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr "<strong>Beskrivelse: </strong> Odoo Twitter-integration"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr "<strong>Navn: </strong> Odoo Twitter-integration"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Website: </strong>"
msgstr "<strong>Website: </strong>"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr "API nøgle"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr "API-hemmelighed"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Accept terms of use and click on the Create your Twitter application button "
"at the bottom"
msgstr ""
"Accepter brugsbetingelser og klik på knappen opret dit Twitterprogram "
"nederst"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:16
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"Godkendelsesakkreditiver mangler eller er ukorrekte. Måske er "
"skærmnavnstweet beskyttede."

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Copy/Paste Consumer Key (API Key) and Consumer Secret (API Secret) keys "
"below."
msgstr ""
"Kopier/indsæt tasterne forbrugernøgle (API-nøgle) og forbrugerhemmelighed "
"(API-hemmelighed) nedenfor."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Create a new Twitter application on"
msgstr "Opret en ny Twitter ansøgning på"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr "Favoritter fra"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "Hent favoritter fra dette skærmnavn"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:48
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "HTTP-fejl: Der er en fejl i konfigurationen"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr "Sådan konfigurerer du Twitter API-adgang"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "ID"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:59
#, python-format
msgid "Internet connection refused"
msgstr "Internetforbindelse nægtet"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:61
#: code:addons/website_twitter/models/res_config_settings.py:62
#, python-format
msgid "Please double-check your Twitter API Key and Secret!"
msgstr "Kontroller venligst din Twitter API-nøgle og hemmelighed igen!"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:27
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"Angiv venligst et Twitterskærmnavn til at indlæse favoritter fra, under "
"indstillinger for hjemmesiden (det behøver ikke at være dit)."

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:23
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr ""
"Angiv venligst Twitter API-nøglen og -hemmeligheden under indstillinger for "
"hjemmesiden."

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:21
#, python-format
msgid "Reload"
msgstr "Opdater"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:18
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""
"Anmodning kan ikke betjenes, da begrænsningen for programmet er opbrugt for "
"ressourcen."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr "Skærmnavn"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"Skærmnavn på Twitterkontoen hvorfra du ønsker at indlæse favoritter. Navnet "
"skal ikke matche API-nøglen/-hemmeligheden."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Switch to the API Keys tab: <br/>"
msgstr "Skift til API-nøglens faneblad: <br/>"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:21
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"Twitterserveren er oppe, men overbelastet med forespørgsler. Prøv igen "
"senere."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:22
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"Twitterserverne er oppe, men forespørgslen kunne ikke betjenes på grund af "
"en fejl med vores stak. Prøv igen senere."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:17
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed."
" Please check your Twitter API Key and Secret."
msgstr ""
"Forespørgslen er forstået, men er blevet nægtet eller adgang er ikke "
"tilladt. Kontroller venligst din Twitter API-nøgle og -hemmelighed."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:15
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"Forespørgslen var ugyldig eller kan ikke betjenes på anden måde. "
"Forespørgsler uden godkendelse anses for at være ugyldige og vil give dette "
"svar."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:14
#, python-format
msgid "There was no new data to return."
msgstr "Der var ingen nye data at returnere."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr "Tweet ID"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr "Tweets"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr "Twitter API-akkreditiver"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr "Twitter API-nøgle"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr "Twitter API Secret"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr "Twitter API-nøgle"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr "Twitter API-nøgle kan hentes fra https://apps.twitter.com/"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr "Twitter API secret"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr "Twitter API-hemmelighed kan hentes fra https://apps.twitter.com/"

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:40
#, python-format
msgid "Twitter Configuration"
msgstr "Twitter Konfiguration"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:62
#, python-format
msgid "Twitter authorization error!"
msgstr "Twittergodkendelsesfejl!"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:20
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter er nede eller ved at blive opgraderet."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:19
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"Twitter er nede. Prøv igen senere. Du kan overveje at anmelde "
"problemstillingen på Twitterfora for at få hjælp."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr "Tvitterserver-uri"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr "Twitterguide"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:37
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more"
" or choose a different screen name."
msgstr ""
"Twitterbrugeren @%(username)s har mindre end 12 favorittweets. Tilføj "
"venligst flere eller vælg et andet skærmnavn."

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:6
#, python-format
msgid "Twitter's user"
msgstr "Twitterbruger"

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
#: model:ir.cron,cron_name:website_twitter.ir_cron_twitter_actions
#: model:ir.cron,name:website_twitter.ir_cron_twitter_actions
msgid "Twitter: Fetch new favorites"
msgstr "Twitter: Hent nye favoritter"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:58
#: code:addons/website_twitter/models/res_config_settings.py:59
#, python-format
msgid "We failed to reach a twitter server."
msgstr "Vi kunne ikke nå en Twitterserver."

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "Hjemmeside"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr "Twitters hjemmeside"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://apps.twitter.com/app/new"
msgstr "https://apps.twitter.com/app/new"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "with the following values:"
msgstr "med de følgende værdier:"
