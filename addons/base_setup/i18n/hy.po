# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_setup
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-30 09:24+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Armenian (http://www.transifex.com/odoo/odoo-9/language/hy/)\n"
"Language: hy\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_portal
msgid "Activate the customer portal"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_share
msgid "Allow documents sharing"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_group_multi_currency
msgid "Allow multi currencies"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_base_import
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
#: model_terms:ir.ui.view,arch_db:base_setup.view_sale_config_settings
msgid "Apply"
msgstr "Ավելացնել"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_google_drive
msgid "Attach Google documents to any record"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Authentication"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
#: model_terms:ir.ui.view,arch_db:base_setup.view_sale_config_settings
msgid "Cancel"
msgstr "Հրաժարվել"

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Client"
msgstr ""

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_sale_config
#: model_terms:ir.ui.view,arch_db:base_setup.view_sale_config_settings
msgid "Configure Sales"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Configure outgoing email servers"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Configure your company data"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_create_uid
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_create_uid
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings_create_uid
msgid "Created by"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_create_date
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_create_date
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings_create_date
msgid "Created on"
msgstr ""

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Customer"
msgstr "Հաճախորդ"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_display_name
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_display_name
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings_display_name
msgid "Display Name"
msgstr ""

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Donor"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Email"
msgstr "էլ.փոստ"

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "General Settings"
msgstr "Հիմանակն լարքեր"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_module_portal
msgid "Give your customers access to their documents."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Google Calendar"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Google Drive"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Google Integration"
msgstr ""

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Guest"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_partner
msgid "How do you call a Customer"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_id
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_id
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings_id
msgid "ID"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Import / Export"
msgstr "Ներբեռնում / Արտահանում"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Inter company"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings___last_update
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology___last_update
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_write_uid
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_write_uid
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings_write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_write_date
#: model:ir.model.fields,field_description:base_setup.field_base_setup_terminology_write_date
#: model:ir.model.fields,field_description:base_setup.field_sale_config_settings_write_date
msgid "Last Updated on"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_inter_company_rules
msgid "Manage Inter Company"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_group_multi_company
msgid "Manage multiple companies"
msgstr ""

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Member"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Multi Company"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Multi Currencies"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid ""
"Once installed, you can configure your API credentials for \"Google calendar"
"\""
msgstr ""

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Partner"
msgstr "Գործընկեր"

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Patient"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Portal access"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_module_share
msgid "Share or embbed any screen of Odoo."
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_company_share_partner
msgid "Share partners to all companies"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_company_share_partner
msgid ""
"Share your partners to all companies defined in your instance.\n"
" * Checked : Partners are visible for every companies, even if a company is "
"defined on the partner.\n"
" * Unchecked : Each company can see only its partner (partners where company "
"is defined). Partners not related to a company are visible for all companies."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid "Shared resources"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.base_setup_terminology_form
msgid "Specify Your Terminology"
msgstr ""

#. module: base_setup
#: selection:base.setup.terminology,partner:0
msgid "Tenant"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_module_google_calendar
msgid "This installs the module google_calendar."
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_module_google_drive
msgid "This installs the module google_docs."
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_module_inter_company_rules
msgid ""
"This installs the module inter_company_rules.\n"
" Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_partner_terminology_config_form
msgid "Use another word to say \"Customer\""
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_base_config_settings_module_auth_oauth
msgid "Use external authentication providers, sign in with Google..."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid ""
"When you send a document to a customer\n"
"                                    (quotation, invoice), your customer will "
"be\n"
"                                    able to signup to get all his "
"documents,\n"
"                                    read your company news, check his "
"projects,\n"
"                                    etc."
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_base_config_settings_group_multi_company
msgid ""
"Work in multi-company environments, with appropriate security access between "
"companies."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.base_setup_terminology_form
msgid ""
"You can use this wizard to change the terminologies for customers in the "
"whole application."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.view_general_configuration
msgid ""
"You will find more options in your company details: address for the header "
"and footer, overdue payments texts, etc."
msgstr ""

#. module: base_setup
#: model:ir.model,name:base_setup.model_base_config_settings
msgid "base.config.settings"
msgstr ""

#. module: base_setup
#: model:ir.model,name:base_setup.model_base_setup_terminology
msgid "base.setup.terminology"
msgstr ""

#. module: base_setup
#: model:ir.model,name:base_setup.model_sale_config_settings
msgid "sale.config.settings"
msgstr ""
