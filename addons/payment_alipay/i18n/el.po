# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_alipay
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON> Goutoudi<PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: Ko<PERSON><PERSON> Goutoudis <<EMAIL>>, 2019\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid ""
"\n"
"                        Only transactions in Chinese Yuan (CNY) are allowed for Alipay Express Checkout.\n"
"\n"
"                        If you wish to use another currency than CNY for your transactions, switch your\n"
"                        configuration to a Cross-border account on the Alipay payment acquirer in Odoo.\n"
"                    "
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_payment_method
msgid ""
"  * Cross-border: For the Overseas seller \n"
"  * Express Checkout: For the Chinese Seller"
msgstr ""

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr ", βρέθηκαν πολλαπλές παραγγελίες"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr ", δεν βρέθηκε παραγγελία"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_payment_method
msgid "Account"
msgstr "Λογαριασμός"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__provider__alipay
msgid "Alipay"
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_seller_email
msgid "Alipay Seller Email"
msgstr ""

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "Alipay: invalid sign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "Alipay: received data for reference %s"
msgstr ""

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "Alipay: received data with missing reference (%s) or txn_id (%s)"
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__alipay_payment_method__standard_checkout
msgid "Cross-border"
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__alipay_payment_method__express_checkout
msgid "Express Checkout (only for Chinese Merchant)"
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_md5_signature_key
msgid "MD5 Signature Key"
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_merchant_partner_id
msgid "Merchant Partner ID"
msgstr ""

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Αποδέκτης Πληρωμής"

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_transaction
msgid "Payment Transaction"
msgstr "Συναλλαγή Πληρωμής"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__provider
msgid "Provider"
msgstr "Πάροχος"

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_md5_signature_key
msgid ""
"The MD5 private key is the 32-byte string which is composed of English "
"letters and numbers."
msgstr ""

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_merchant_partner_id
msgid ""
"The Merchant Partner ID is used to ensure communications coming from Alipay "
"are valid and secured."
msgstr ""
