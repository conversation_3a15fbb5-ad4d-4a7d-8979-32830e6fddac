# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_alipay
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Ukrainian (https://www.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid ""
"\n"
"                        Only transactions in Chinese Yuan (CNY) are allowed for Alipay Express Checkout.\n"
"\n"
"                        If you wish to use another currency than CNY for your transactions, switch your\n"
"                        configuration to a Cross-border account on the Alipay payment acquirer in Odoo.\n"
"                    "
msgstr ""
"\n"
"                        Транзакції лише в Chinese Yuan (CNY) дозволені для перевірки Alipay Express.\n"
"\n"
"                        Якщо ви хочете використовувати іншу валюту замість CNY для вашої транзакції, перемкніть ваш\n"
"                        конфігуратор на транскордонний рахунок на платіжному еквайєрі Alipay в Odoo.\n"
"                    "

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_payment_method
msgid ""
"  * Cross-border: For the Overseas seller \n"
"  * Express Checkout: For the Chinese Seller"
msgstr ""
"  * Транскордонний: для закордонних продавців \n"
"  * Express Checkout: для китайських продавців"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; знайдено кілька замовлень"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; не знайдено жодного замовлення"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_payment_method
msgid "Account"
msgstr "Рахунок"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__provider__alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_seller_email
msgid "Alipay Seller Email"
msgstr "Email продавця Alipay "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "Alipay: invalid sign, received %s, computed %s, for data %s"
msgstr "Alipay: недійсний знак, отримано %s, пораховано %s, для даних %s"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "Alipay: received data for reference %s"
msgstr "Alipay: отримані дані для референсу %s"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment.py:0
#, python-format
msgid "Alipay: received data with missing reference (%s) or txn_id (%s)"
msgstr "Alipay: отримані дані з відсутнім референсом (%s) або txn_id (%s)"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__alipay_payment_method__standard_checkout
msgid "Cross-border"
msgstr "Транскордонний"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__alipay_payment_method__express_checkout
msgid "Express Checkout (only for Chinese Merchant)"
msgstr "Швидка перевірка (лише для продавців з Китаю)"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_md5_signature_key
msgid "MD5 Signature Key"
msgstr "Ключ підпису MD5"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_merchant_partner_id
msgid "Merchant Partner ID"
msgstr "ID партнера продавця"

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Платіжний еквайєр"

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__provider
msgid "Provider"
msgstr "Провайдер"

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_md5_signature_key
msgid ""
"The MD5 private key is the 32-byte string which is composed of English "
"letters and numbers."
msgstr ""
"Приватний ключ MD5 є 32-байтовим рядком, написаний англійськими буквами та "
"цифрами."

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_merchant_partner_id
msgid ""
"The Merchant Partner ID is used to ensure communications coming from Alipay "
"are valid and secured."
msgstr ""
"ID партнера продавця використовується, щоби переконатися, що повідомлення, "
"що надходять від Alipay, є дійсними та захищеними."
