# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON>, 2019
# krnkris, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Hungarian (https://www.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Oktatás"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Accounts"
msgstr "Főkönyvi számlák"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"All events have been disconnected from your previous account. You can now "
"restart the synchronization"
msgstr ""
"Mindegyik eseményt lekapcsoltuk az előző fiókjáról. Most újrakezdheti a "
"szinkronizációt"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Egy Rendszergazdának kell beállítania a Google Szinkronizációt mielőtt "
"használni tudná azt!"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"An error occured while disconnecting events from your previous account. "
"Please retry or contact your administrator."
msgstr ""
"Hiba történt az esemény előző fiókjáról történt lekapcsolásakor. Kérem "
"próbálja újra vagy lépjen kapcsolatba a rendszergazdával."

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Calendar"
msgstr "Naptár"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Naptár résztvevő információ"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Naptári esemény"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr "Naptár azonosító ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "Ügyfél azonosító"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Ügyfél titok"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Ügyfél_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Ügyfél_kulcs"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurációs beállítások"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Configuration"
msgstr "Konfiguráció"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Do you want to do this now?"
msgstr "Ezt akarja most csinálni?"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Event disconnection error"
msgstr "Esemény szétkapcsolási hiba"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Event disconnection success"
msgstr "Esemény szétkapcsolási visszaállt"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar
msgid "Google Calendar"
msgstr "Google naptár"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee__google_internal_event_id
msgid "Google Calendar Event Id"
msgstr "Google naptár rendezvény azonosító Id"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:google_calendar.ir_cron_sync_all_cals
#: model:ir.cron,name:google_calendar.ir_cron_sync_all_cals
msgid "Google Calendar: synchronization"
msgstr "Google Naptár: szinkronizálás"

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_calendar_attendee_google_id_uniq
msgid "Google ID should be unique!"
msgstr "Google azonosító ID egyedinek kell lennie!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:0
#, python-format
msgid ""
"Google is lost... the next synchro will be a full synchro. \n"
"\n"
" %s"
msgstr ""
"Google összezavart... a következő szinkronizáció egy teljes szinkronizáció lesz. \n"
"\n"
" %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar__id
msgid "ID"
msgstr "Azonosító"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"In order to do this, you first need to disconnect all existing events from "
"the old account."
msgstr ""
"Ahhoz, hogy ezt megtegye, elöször mindegyik eseményt le kell kapcsolnia a "
"régi fiókjáról."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"Utolsó szinkronizált naptár azonosító ID. Ha változott, leválasztjuk az "
"összes GoogleID és Odoo Google belső azonosító ID kapcsolatokat"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar____last_update
msgid "Last Modified on"
msgstr "Legutóbb frissítve"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_last_sync_date
msgid "Last synchro date"
msgstr "Utolsó szinkronizáció dátuma"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee__oe_synchro_date
msgid "Odoo Synchro Date"
msgstr "Odoo rendszer szinkronizáció dátuma"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__oe_update_date
msgid "Odoo Update Date"
msgstr "Odoo rendszer frissítés dátuma"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Redirection"
msgstr "Átirányítás"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Token frissítés"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Sync with <b>Google</b>"
msgstr "Szinkr. ezzel <b>Google</b>"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"A Google szinkronizálást be kell állítania, mielőtt használja, meg szeretné "
"tenni most?"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"The account you are trying to synchronize (%s) is not the same as the last "
"one used (%s)!"
msgstr ""
"A szinkronizálni kívánt fiók (%s) az nem ugyanaz mint amit legutóbb használt"
" (%s)!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:278
#, python-format
msgid ""
"The event \"%s\", %s (ID: %s) cannot be synchronized because of the "
"following error: %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr "Token érvényesség"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__server_uri
msgid "URI for tuto"
msgstr "URI elérési út az oktatáshoz"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr "Felhasználói token"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
msgid "Users"
msgstr "Felhasználók"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "You will be redirected to Google to authorize access to your calendar!"
msgstr ""
"Át lesz irányítva a Google-ra, hogy a naptár eléréshez jogosultságot kapjon!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:0
#, python-format
msgid "Your token is invalid or has been revoked !"
msgstr "Tokenje nem érvényes vagy visszavont !"
