# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <marina<PERSON><PERSON>@gmail.com>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Accounts"
msgstr "Contas"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"All events have been disconnected from your previous account. You can now "
"restart the synchronization"
msgstr ""
"Todos os eventos foram desconectados da sua conta anterior. Agora você pode "
"reiniciar a sincronização"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Um administrador precisa configurar o Google Syncronization antes de você "
"usá-lo!"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"An error occured while disconnecting events from your previous account. "
"Please retry or contact your administrator."
msgstr ""
"Ocorreu um erro ao desconectar os eventos da sua conta anterior. Por favor "
"tente novamente ou entre em contato o administrador."

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Calendar"
msgstr "Calendário"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Informações do Participante do Calendário"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Evento Calendário"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr "ID da Agenda"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "ID do Cliente"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Segredo do Cliente"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Ajuste de configurações"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Configuration"
msgstr "Configuração"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Do you want to do this now?"
msgstr "Você deseja fazer isso agora?"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Event disconnection error"
msgstr "Erro de desconexão de evento"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Event disconnection success"
msgstr "Sucesso de desconexão de evento"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar
msgid "Google Calendar"
msgstr "Agenda do Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee__google_internal_event_id
msgid "Google Calendar Event Id"
msgstr "Event id no Google Agenda"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:google_calendar.ir_cron_sync_all_cals
#: model:ir.cron,name:google_calendar.ir_cron_sync_all_cals
msgid "Google Calendar: synchronization"
msgstr "Google Agenda: sincronização"

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_calendar_attendee_google_id_uniq
msgid "Google ID should be unique!"
msgstr "O Google ID deve ser único!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:0
#, python-format
msgid ""
"Google is lost... the next synchro will be a full synchro. \n"
"\n"
" %s"
msgstr ""
"O Google está perdido ... a próxima sincronização será uma sincronização completa. \n"
"\n"
" %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"In order to do this, you first need to disconnect all existing events from "
"the old account."
msgstr "Para fazer isso, primeiro você deve desconectar da sua conta antiga."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"Último ID de Calendário que foi sincronizado. Se estiver alterado, nós "
"removeremos todos os links entre o GoogleID e o ID interno do Goole no Odoo"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_last_sync_date
msgid "Last synchro date"
msgstr "Data da última sincronização"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_attendee__oe_synchro_date
msgid "Odoo Synchro Date"
msgstr "Data de Sincronização Odoo"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__oe_update_date
msgid "Odoo Update Date"
msgstr "Data de Atualização do Odoo"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Redirection"
msgstr "Redirecionar"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Atualizar Token"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Sync with <b>Google</b>"
msgstr "Sincronizar com <b>Google</b>"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"O Google Synchronization precisar ser configurado antes de poder ser usado, "
"você deseja fazer isso agora?"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"The account you are trying to synchronize (%s) is not the same as the last "
"one used (%s)!"
msgstr ""
"A conta que você está tentando sincronizar (%s) não é a mesma que foi usada "
"pela última vez (%s)!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:278
#, python-format
msgid ""
"The event \"%s\", %s (ID: %s) cannot be synchronized because of the "
"following error: %s"
msgstr ""

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr "Validade do Token"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__server_uri
msgid "URI for tuto"
msgstr "URI para tuto"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr "Token do Usuário"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
msgid "Users"
msgstr "Usuários"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "You will be redirected to Google to authorize access to your calendar!"
msgstr ""
"Você será redirecionado ao Google para autorizar o acesso a sua agenda!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_calendar.py:0
#, python-format
msgid "Your token is invalid or has been revoked !"
msgstr "Seu token é inválido ou foi revogado!"
