# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <fold<PERSON><PERSON>@nexterp.ro>, 2020
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:13+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON> <dani<PERSON>@terrabit.ro>, 2021\n"
"Language-Team: Romanian (https://www.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_image_url
msgid ""
"A relative or absolute URL pointing to a square image of your brand or "
"product. As defined in your Stripe profile. See: "
"https://stripe.com/docs/checkout"
msgstr ""
"O adresă URL relativă sau absolută care să indice o imagine pătratică a "
"mărcii sau a produsului dvs. După cum este definit în profilul dvs. Stripe. "
"Consultați: https://stripe.com/docs/checkout"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_image_url
msgid "Checkout Image URL"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "Close"
msgstr "Închide"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "Error"
msgstr "Eroare"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:0
#, python-format
msgid "Just one more second, We are redirecting you to Stripe..."
msgstr "Încă o secundă, vă redirecționăm către Stripe ..."

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Colector plată"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Payment Method ID"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Token de plată"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Tranzacție plată"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:0
#, python-format
msgid "Payment error"
msgstr "Eroare de plată"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid ""
"Perhaps the problem can be solved by double-checking your credit card "
"details, or contacting your bank?"
msgstr ""
"Poate că problema poate fi rezolvată verificând de două ori datele cardului "
"de credit sau contactând banca dvs.?"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "Furnizor"

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Dungat"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr "Stripe Payment Intent ID"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent_secret
msgid "Stripe Payment Intent Secret"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Cheie publicabilă Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Cheia secretă a fâșiei"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe gave us the following info about the problem: '%s'"
msgstr "Stripe ne-a oferit următoarele informații despre problemă: '%s'"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe: %s orders found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe: no order found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid ""
"Unable to convert Stripe customer for SCA compatibility. Is there at least "
"one card for this customer in the Stripe backend?"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Unable to save card"
msgstr "Nu se poate salva cardul"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment. "
msgstr "În acest moment nu putem adăuga metoda dvs. de plată."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "We're sorry to report that the transaction has failed."
msgstr "Ne pare rău să raportăm că tranzacția a eșuat."
