# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:31+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2019\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_extended_form
msgid ""
"Change how the system computes the full street field based on the different "
"street subfields"
msgstr ""
"שנה את הדרך בה המערכת מחשבת את שדה הרחוב המלא על סמך שדות המשנה השונים של "
"הרחוב"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
msgid "Country"
msgstr "ארץ"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "דלת"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__street_number2
#: model:ir.model.fields,help:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,help:base_address_extended.field_res_users__street_number2
msgid "Door Number"
msgstr "מספר דלת"

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__street_format
msgid ""
"Format to use for streets belonging to this country.\n"
"\n"
"You can use the python-style string pattern with all the fields of the street (for example, use '%(street_name)s, %(street_number)s' if you want to display the street name, followed by a comma and the house number)\n"
"%(street_name)s: the name of the street\n"
"%(street_number)s: the house number\n"
"%(street_number2)s: the door number"
msgstr ""
"תבנית לשימוש עבור רחובות ששייכים לארץ זו.\n"
"\n"
"אתה יכול להשתמש בתבנית מחרוזת בסגנון פייתון עבור כל שדות הרחוב (לדוגמה, השתמש ב '%(street_name)s, %(street_number)s' אם ברצונך להציג את שם הרחוב בצירוף פסיק ומספר הבית)\n"
"%(street_name)s: שם הרחוב\n"
"%(street_number)s: מספר בית\n"
"%(street_number2)s: מספר דלת"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "בית"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__street_number
#: model:ir.model.fields,help:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,help:base_address_extended.field_res_users__street_number
msgid "House Number"
msgstr "מספר בית"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__street_format
msgid "Street Format"
msgstr "תבנית רחוב"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_company__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "שם רחוב"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_partner_address_structured_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_partner_structured_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_company_extended_form
msgid "Street Name..."
msgstr "שם רחוב..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_extended_form
msgid "Street format..."
msgstr "תבנית רחוב..."

#. module: base_address_extended
#: code:addons/base_address_extended/models/base_address_extended.py:64
#: code:addons/base_address_extended/models/base_address_extended.py:112
#, python-format
msgid "Unrecognized field %s in street format."
msgstr "שדה לא מוכר %s בתבנית רחוב."
