# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-11-25 15:03+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "(Time Now)"
msgstr "(Orario corrente)"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-building text-primary\"/>\n"
"                <b>Company type:</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-building text-primary\"/>\n"
"                <b>Tipologia azienda:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-calendar text-primary\"/>\n"
"                <b>Founded:</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-calendar text-primary\"/>\n"
"                <b>Fondazione:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-cube text-primary\"/>\n"
"                <b>Technology Used :</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-cube text-primary\"/>\n"
"                <b>Tecnologia usata:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-envelope text-primary\"/>\n"
"                <b>Email :</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-envelope text-primary\"/>\n"
"                <b>E-mail:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-globe text-primary\"/>\n"
"                <b>Timezone :</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-globe text-primary\"/>\n"
"                <b>Fuso orario :</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-industry text-primary\"/>\n"
"                <b>Sector:</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-industry text-primary\"/>\n"
"                <b>Settore:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Annual revenue:</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Ricavo annuale:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Estimated annual revenue:</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Ricavo annuale stimato:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-phone text-primary\"/>\n"
"                <b>Phone :</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-phone text-primary\"/>\n"
"                <b>Telefono:</b>"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-users text-primary\"/>\n"
"                <b>Employees:</b>"
msgstr ""
"<i class=\"fa fa-fw mr-2 fa-users text-primary\"/>\n"
"                <b>Dipendenti:</b>"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "Informazioni aggiuntive"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Buy more credits"
msgstr "Acquista altri crediti"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "ID database azienda"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Creato il"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "IAP Account Token missing"
msgstr "Token del conto IAP mancante"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "Credito insufficiente"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr "È sincronizzato"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "Not enough credits for Partner Autocomplete"
msgstr "Crediti non sufficienti per l'autocompletamento del partner"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Partner"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
#: model:ir.cron,cron_name:partner_autocomplete.ir_cron_partner_autocomplete
#: model:ir.cron,name:partner_autocomplete.ir_cron_partner_autocomplete
msgid "Partner Autocomplete : Sync with remote DB"
msgstr "Autocompletamento partner: sincronizzazione con DB remoto"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr "Sincronizzazione autocompletamento partner"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr "Partner creato dal servizio di autocompletamento di Odoo"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Placeholder"
msgstr "Segnaposto"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
#, python-format
msgid "Searching Autocomplete..."
msgstr "Ricerca autocompletamento..."

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Set Your Account Token"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "Twitter :"
msgstr "Twitter :"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "followers)"
msgstr "follower)"
