# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-11-25 15:03+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2019\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "(Time Now)"
msgstr "(עכשיו)"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-building text-primary\"/>\n"
"                <b>Company type:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-calendar text-primary\"/>\n"
"                <b>Founded:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-cube text-primary\"/>\n"
"                <b>Technology Used :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-envelope text-primary\"/>\n"
"                <b>Email :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-globe text-primary\"/>\n"
"                <b>Timezone :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-industry text-primary\"/>\n"
"                <b>Sector:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Annual revenue:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Estimated annual revenue:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-phone text-primary\"/>\n"
"                <b>Phone :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-users text-primary\"/>\n"
"                <b>Employees:</b>"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "מידע נוסף"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Buy more credits"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "מזהה בסיס נתונים של החברה"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "הצג שם"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "IAP Account Token missing"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "Not enough credits for Partner Autocomplete"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "שותף"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
#: model:ir.cron,cron_name:partner_autocomplete.ir_cron_partner_autocomplete
#: model:ir.cron,name:partner_autocomplete.ir_cron_partner_autocomplete
msgid "Partner Autocomplete : Sync with remote DB"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr ""

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Placeholder"
msgstr "בעל תפקיד"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
#, python-format
msgid "Searching Autocomplete..."
msgstr ""

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Set Your Account Token"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "Twitter :"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "followers)"
msgstr ""
