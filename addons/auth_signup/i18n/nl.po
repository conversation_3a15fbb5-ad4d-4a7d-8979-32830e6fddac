# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_signup
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid ""
"${object.create_uid.name} from ${object.company_id.name} invites you to "
"connect to Odoo"
msgstr ""
"${object.create_uid.name} van ${object.company_id.name} nodigt u uit om te "
"verbinden met Odoo"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "%s connected"
msgstr "%s verbonden"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid ""
"<strong>A password reset has been requested for this user. An email "
"containing the following link has been sent:</strong>"
msgstr ""
"<strong>Voor deze gebruiker werd een aanvraag ingediend om het wachtwoord "
"opnieuw in te stellen. Een e-mail werd verzonden met de volgende "
"link:</strong>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid ""
"<strong>An invitation email containing the following subscription link has "
"been sent:</strong>"
msgstr ""
"<strong>Een uitnodigingsemail met de volgende koppeling is "
"verzonden:</strong>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_data_unregistered_users
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                 % set invited_users = ctx['invited_users']\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Pending Invitations\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear ${object.name or ''},<br/> <br/>\n"
"                        You added the following user(s) to your database but they haven't registered yet:\n"
"                        <ul>\n"
"                            % for invited_user in invited_users:\n"
"                                <li>${invited_user}</li>\n"
"                            % endfor\n"
"                        </ul>\n"
"                        Follow up with them so they can access your database and start working with you.\n"
"                        <br/><br/>\n"
"                        Have a nice day!<br/>\n"
"                        --<br/>The ${object.company_id.name} Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                 % set invited_users = ctx['invited_users']\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Uitnodigingen in behandeling\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hallo ${object.name or ''},<br/> <br/>\n"
"                        U heeft de volgende gebruiker(s) aan uw database toegevoegd, maar zijn nog niet geregistreerd:\n"
"                        <ul>\n"
"                            % for invited_user in invited_users:\n"
"                                <li>${invited_user}</li>\n"
"                            % endfor\n"
"                        </ul>\n"
"                        Neem contact met hen op zodat ze op de hoogte worden gehouden van hun toegang tot uw database en met u kunnen samenwerken.\n"
"                        <br/><br/>\n"
"                        Geniet van uw dag!<br/>\n"
"                        --Het ${object.company_id.name} Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welcome to Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear ${object.name},<br/><br/>\n"
"                        You have been invited by ${object.create_uid.name} of ${object.company_id.name} to connect on Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${object.signup_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Accept invitation\n"
"                            </a>\n"
"                        </div>\n"
"                        % set website_url = object.env['ir.config_parameter'].sudo().get_param('web.base.url')\n"
"                        Your Odoo domain is: <b><a href=\"${website_url}\">${website_url}</a></b><br/>\n"
"                        Your sign in email is: <b><a href=\"/web/login?login=${object.email}\" target=\"_blank\">${object.email}</a></b><br/><br/>\n"
"                        Never heard of Odoo? It’s an all-in-one business software loved by 3+ million users. It will considerably improve your experience at work and increase your productivity.\n"
"                        <br/><br/>\n"
"                        Have a look at the <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> to discover the tool.\n"
"                        <br/><br/>\n"
"                        Enjoy Odoo!<br/>\n"
"                        --<br/>The ${object.company_id.name} Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.company_id.phone}\n"
"                    % if object.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.company_id.website\n"
"                        | <a href=\"'%s' % ${object.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welkom bij Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Beste ${object.name},<br/><br/>\n"
"                        Je bent uitgenodigd door ${object.create_uid.name} of ${object.company_id.name} om te verbinden met Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${object.signup_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Uitnodiging accepteren\n"
"                            </a>\n"
"                        </div>\n"
"                        % set website_url = object.env['ir.config_parameter'].sudo().get_param('web.base.url')\n"
"                        Uw Odoo domein is: <b><a href=\"${website_url}\">${website_url}</a></b><br/>\n"
"                        Uw inlog e-mail is: <b><a href=\"/web/login?login=${object.email}\" target=\"_blank\">${object.email}</a></b><br/><br/>\n"
"                        Nog nooit van Odoo gehoord? Het is een alles-in-één bedrijfssoftware die geliefd is bij meer dan 3 miljoen gebruikers. Het zal uw ervaring op het werk aanzienlijk verbeteren en uw productiviteit verhogen.\n"
"                        <br/><br/>\n"
"                        Bekijk de <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo  Rondleiding</a> om de software verder te verkennen.\n"
"                        <br/><br/>\n"
"                        Veel plezier met Odoo!<br/>\n"
"                        --Het team van  ${object.company_id.name}\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.company_id.phone}\n"
"                    % if object.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.company_id.website\n"
"                        | <a href=\"'%s' % ${object.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.reset_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear ${object.name},<br/><br/>\n"
"                        A password reset was requested for the Odoo account linked to this email.\n"
"                        You may change your password by following this link which will remain valid during 24 hours:<br/>\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${object.signup_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Change password\n"
"                            </a>\n"
"                        </div>\n"
"                        If you do not expect this, you can safely ignore this email.<br/><br/>\n"
"                        Thanks,<br/>\n"
"                        ${user.signature | safe}<br/>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.company_id.phone}\n"
"                    % if object.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.company_id.website\n"
"                        | <a href=\"'%s' % ${object.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Uw account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Beste ${object.name},<br/><br/>\n"
"                        Een wachtwoordreset is aangevraagd voor het Odoo-account dat aan deze e-mail is gekoppeld.\n"
"                        U kunt uw wachtwoord wijzigen door deze link te volgen die gedurende 24 uur geldig blijft:<br/>\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"${object.signup_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Wijzig wachtwoord\n"
"                            </a>\n"
"                        </div>\n"
"                        Als u dit niet verwacht, kunt u deze e-mail veilig negeren<br/><br/>\n"
"Bedankt,<br/>\n"
"                        ${user.signature | safe}<br/>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.company_id.phone}\n"
"                    % if object.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.company_id.website\n"
"                        | <a href=\"'%s' % ${object.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Aangeboden door <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_user_signup_account_created
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear ${object.name},<br/><br/>\n"
"                        Your account has been successfully created!<br/>\n"
"                        Your login is <strong>${object.email}</strong><br/>\n"
"                        To gain access to your account, you can use the following link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"/web/login?${ctx['auth_login']}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Go to My Account\n"
"                            </a>\n"
"                        </div>\n"
"                        Thanks,<br/><br/>\n"
"                        ${user.signature | safe}<br/>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.company_id.phone}\n"
"                    % if object.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.company_id.website\n"
"                        | <a href=\"'%s' % ${object.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Uw account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Beste ${object.name},<br/><br/>\n"
"                        Uw account is succesvol aangemaakt!<br/>\n"
"                        Uw login is <strong>${object.email}</strong><br/>\n"
"                        Om toegang te krijgen tot uw account, kunt u de volgende link gebruiken:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a href=\"/web/login?${ctx['auth_login']}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Ga naar mijn account\n"
"                            </a>\n"
"                        </div>\n"
"                        Bedankt,<br/><br/>\n"
"                        ${user.signature | safe}<br/>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.company_id.phone}\n"
"                    % if object.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.company_id.website\n"
"                        | <a href=\"'%s' % ${object.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Aangeboden door <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Already have an account?"
msgstr "Heeft u al een account?"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "An email has been sent with credentials to reset your password"
msgstr ""
"Er is een e-mail verzonden met informatie over het resetten van uw "
"wachtwoord."

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Another user is already registered using this email address."
msgstr "Er is al een gebruiker geregistreerd met dit e-mailadres."

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Authentication Failed."
msgstr "Authenticatie mislukt."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Back to Login"
msgstr "Terug naar login"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr ""
"E-mail kan niet verzonden worden: gebruiker %s heeft geen e-mailadres. "

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Close"
msgstr "Sluiten"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Confirm"
msgstr "Bevestigen"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr "Bevestig wachtwoord"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__active
msgid "Confirmed"
msgstr "Bevestigd"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Could not create a new account."
msgstr "Het was niet mogelijk een nieuw account aan te maken."

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Could not reset your password"
msgstr "Uw wachtwoord kon niet opnieuw ingesteld worden"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Klant account"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Standaard toegangsrechten"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Don't have an account?"
msgstr "Heeft u geen account?"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Enable password reset from Login page"
msgstr "Wachtwoord reset vanaf login pagina toestaan"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Gratis registratie"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Invalid signup token"
msgstr "Aanmeld toekenning ongeldig"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Laat klanten inloggen voor toegang tot hun documenten"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__new
msgid "Never Connected"
msgstr "Nog nooit verbonden"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "No login provided."
msgstr "Geen login opgegeven."

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Op uitnodiging"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr "Wachtwoord"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Password Reset"
msgstr "Reset wachtwoord"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.reset_password_email
msgid "Password reset"
msgstr "Reset wachtwoord"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Passwords do not match; please retype them."
msgstr "Wachtwoorden komen niet overeen, probeer het opnieuw."

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_data_unregistered_users
msgid "Reminder for unregistered users"
msgstr "Herinnering voor niet geregistreerde gebruikers"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Reset Password"
msgstr "Stel wachtwoord opnieuw in"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Reset password: invalid username or email"
msgstr "Reset wachtwoord: Ongeldige gebruikersnaam en wachtwoord"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send Reset Password Instructions"
msgstr "Verzend wachtwoord reset instructies"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send an Invitation Email"
msgstr "Verzend een uitnodigings e-mail"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr "Aanmelden"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_expiration
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_expiration
msgid "Signup Expiration"
msgstr "Aanmeld vervallen"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_token
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_token
msgid "Signup Token"
msgstr "Aanmeld token"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_type
msgid "Signup Token Type"
msgstr "Aanmeld token soort"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_valid
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_valid
msgid "Signup Token is Valid"
msgstr "Aanmeld token is geldig"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_url
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_url
msgid "Signup URL"
msgstr "URL voor aanmelden"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup is not allowed for uninvited users"
msgstr "Registratie is niet toegestaan voor gebruikers zonder uitnodiging"

#. module: auth_signup
#: code:addons/auth_signup/models/res_partner.py:0
#, python-format
msgid "Signup token '%s' is no longer valid"
msgstr "Registratie token '%s' is niet meer geldig"

#. module: auth_signup
#: code:addons/auth_signup/models/res_partner.py:0
#, python-format
msgid "Signup token '%s' is not valid"
msgstr "Registratie token '%s' is niet geldig."

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: invalid template user"
msgstr "Inschrijven: foutieve sjabloon gebruiker"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: no login given for new user"
msgstr "Inschrijven: geen login opgegeven voor nieuwe gebruiker"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: no name or partner given for new user"
msgstr "Inschrijven: geen naam of partner opgegeven voor nieuwe gebruiker"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users__state
msgid "Status"
msgstr "Status"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""
"Sjabloon gebruiker voor het aanmaken van nieuwe gebruikers, bij aanmelden"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "The form was not properly filled in."
msgstr "Dit formulier is niet correct ingesteld."

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "This is his first connection. Wish him welcome"
msgstr "Dit is zijn/haar eerste verbinding met Odoo. Heet hem/haar welkom"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Om uitnodigingen in B2B modus te sturen, opent u een contact of selecteert u"
" er meerdere in een lijstweergave. Klik dan op 'Portaal toegangsbeheer' "
"optie in het actie menu."

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "Users"
msgstr "Gebruikers"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder_ir_actions_server
#: model:ir.cron,cron_name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder
#: model:ir.cron,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder
msgid "Users: Notify About Unregistered Users"
msgstr "Gebruikers: informeren over niet-geregistreerde gebruikers"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_user_signup_account_created
msgid "Welcome to ${object.company_id.name}!"
msgstr "Welkom bij ${object.company_id.name}!"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr "Uw e-mail"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr "Uw naam"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr "b.v. John Doe"
