# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Leaanika Randmets, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:21+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Estonian (https://www.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "%d credits will be consumed to find %d companies."
msgstr "%dkrediiti tarbitakse %d ettevõtte leidmiseks"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "(Time Now)"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "<b>Contacts</b>"
msgstr "<b>Kontaktid</b>"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "<b>Phone :</b>"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "<b>Timezone : </b>"
msgstr ""

#. module: crm_iap_lead
#: model:mail.template,body_html:crm_iap_lead.lead_generation_no_credits
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear,</p>\n"
"    <p>There are no more credits on your IAP Lead Generation account.<br/>\n"
"    You can charge your IAP Lead Generation account in the settings of the CRM app.<br/></p>\n"
"    <p>Best regards,</p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Lugupeetud,</p>\n"
"    <p>Teie IAP müügivihjete täiendamise kontol ei ole piisavalt punkte.<br/>\n"
"    Punkte on võimalik lisada CRMi seadete alt.<br/></p>\n"
"    <p>Parimate soovidega,</p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_empty_folder\">\n"
"            No leads found\n"
"        </p><p>\n"
"            No leads could be generated according to your search criteria\n"
"        </p>"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_empty_folder\">\n"
"            No opportunities found\n"
"        </p><p>\n"
"            No opportunities could be generated according to your search criteria\n"
"        </p>"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "<span class=\"o_stat_text\">Leads</span>"
msgstr "<span class=\"o_stat_text\">Müügivihjed</span>"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "<span class=\"o_stat_text\">Opportunities</span>"
msgstr "<span class=\"o_stat_text\">Müügivõimalused</span>"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_238
msgid "Automobiles & Components"
msgstr "Autod ja varuosad"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_157
msgid "Banks"
msgstr "Pangad"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_mining_request
msgid "CRM Lead Mining Request"
msgstr "CRM Müügivihje genereerimise taotlus"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Cancel"
msgstr "Tühista"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_162
msgid "Capital Goods"
msgstr "Kapitalikaubad"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__color
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__color
msgid "Color Index"
msgstr "Värvikood"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_163
msgid "Commercial & Professional Services"
msgstr "Äri- ja kutseteenused"

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__search_type__companies
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Companies"
msgstr "Ettevõtted"

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__search_type__people
msgid "Companies and their Contacts"
msgstr "Ettevõtted ja nende kontaktid"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__company_size_max
msgid "Company Size Max"
msgstr "Ettevõtte maksimaalne suurus"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_167
msgid "Construction Materials"
msgstr "Ehitusmaterjalid"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_30
msgid "Consumer Discretionary"
msgstr "Tarbija oma äranägemisel"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_239
msgid "Consumer Durables & Apparel"
msgstr "Tarbekaubad ja rõivad"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_150
msgid "Consumer Services"
msgstr "Tarbija teenused"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_33
msgid "Consumer Staples"
msgstr "Tarbeklambrid"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Contacts"
msgstr "Kontaktid"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__country_ids
msgid "Countries"
msgstr "Riigid"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Create a Lead Mining Request"
msgstr "Loo müügivihje genereerimise taotlus"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_151
msgid "Diversified Consumer Services"
msgstr "Mitmekesised tarbeteenused"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_158
msgid "Diversified Financial Services"
msgstr "Mitmekesised finantsteenused"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_159
msgid "Diversified Financials"
msgstr "Mitmekesine finants"

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__state__done
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Done"
msgstr "Tehtud"

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__state__draft
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Draft"
msgstr "Mustand"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Email"
msgstr "E-post"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_156
msgid "Energy Equipment & Services"
msgstr "Energiaseadmed ja -teenused"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__error
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__state__error
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Error"
msgstr "Viga"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_ir_model_fields
msgid "Fields"
msgstr "Väljad"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__contact_filter_type
msgid "Filter on"
msgstr "Filtreeri"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__filter_on_size
msgid "Filter on Size"
msgstr "Filtreeri vastavalt suurusele"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_153
msgid "Food & Staples Retailing"
msgstr "Toidu ja põhitoodete jaemüük"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_154
msgid "Food, Beverage & Tobacco"
msgstr "Toit, jook ja tubakas"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "From"
msgstr "Lähtekoht"

#. module: crm_iap_lead
#. openerp-web
#: code:addons/crm_iap_lead/static/src/xml/leads_tree_generate_leads_views.xml:0
#, python-format
msgid "Generate Leads"
msgstr "Loo müügivihjed"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Generate new leads based on their country, industry, size, etc."
msgstr "Loo uusi müügivihjeid riigi, valdkonna, suuruse jne alusel."

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Genereeritud müügivihje/võimalus"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Group By"
msgstr "Grupeerimine"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_160
msgid "Health Care Equipment & Services"
msgstr "Tervishoiu seadmed ja teenused"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_helpers
msgid "Helper methods for crm_iap_lead modules"
msgstr "Abimeetodid moodulite crm_iap_lead jaoks"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_155
msgid "Household & Personal Products"
msgstr "Kodutarbed ja isiklikud tooted"

#. module: crm_iap_lead
#: model:mail.template,subject:crm_iap_lead.lead_generation_no_credits
msgid "IAP Lead Generation Notification"
msgstr "IAP müügivihje loomise teade"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_168
msgid "Independent Power and Renewable Electricity Producers"
msgstr "Sõltumatud elektri- ja taastuvenergia tootjad"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_146
msgid "Industrials"
msgstr "Tööstus"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__industry_ids
msgid "Industries"
msgstr "Valdkonnad"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_industry
msgid "Industry Tag"
msgstr "Tööstuse silt"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_69
msgid "Insurance"
msgstr "Kindlustus"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud (millal)"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud (kelle poolt)"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud (millal)"

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__lead_type__lead
msgid "Lead"
msgstr "Müügivihje"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_contacts_credits
msgid "Lead Contacts Credits"
msgstr "Müügivihje kontakti krediit"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_credits
msgid "Lead Credits"
msgstr "Müügivihje krediit"

#. module: crm_iap_lead
#: model:ir.ui.menu,name:crm_iap_lead.crm_menu_lead_generation
msgid "Lead Generation"
msgstr "Müügivihje loomine"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Lead Information"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_lead__lead_mining_request_id
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Lead Mining Request"
msgstr "Müügivihje genereerimise taotlus"

#. module: crm_iap_lead
#: model:ir.actions.act_window,name:crm_iap_lead.crm_iap_lead_mining_request_action
#: model:ir.ui.menu,name:crm_iap_lead.crm_iap_lead_mining_request_menu_action
msgid "Lead Mining Requests"
msgstr "Müügivihje genereerimise taotlused"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_total_credits
msgid "Lead Total Credits"
msgstr "Müügivihje kogukrediit"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Müügivihje/võimalus"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Leads"
msgstr "Müügivihjed"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"Veenduge, kas te peate olema kooskõlas GDPRiga, et isiklikke andmeid "
"hallata."

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_148
msgid "Materials"
msgstr "Materjalid"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_86
msgid "Media"
msgstr "Meedia"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__name
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Name"
msgstr "Nimi"

#. module: crm_iap_lead
#: model:ir.model.constraint,message:crm_iap_lead.constraint_crm_iap_lead_seniority_name_uniq
msgid "Name already exists!"
msgstr "Nimi on juba olemas!"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "New"
msgstr "Uus"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__contact_number
msgid "Number of Contacts"
msgstr "Kontaktide arv"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__leads_count
msgid "Number of Generated Leads"
msgstr "Genereeritud müügivihjete arv"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_number
msgid "Number of Leads"
msgstr "Müügivihjete arv"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Opportunities"
msgstr "Võimalused"

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__lead_type__opportunity
msgid "Opportunity"
msgstr "Müügivõimalus"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Müügivõimalus on loodud Odoo Müügivihjete genereerimise kaudu"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__role_ids
msgid "Other Roles"
msgstr "Teised rollid"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_role
msgid "People Role"
msgstr "Inimeste roll"

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_seniority
msgid "People Seniority"
msgstr "Inimeste staaž"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_161
msgid "Pharmaceuticals, Biotechnology & Life Sciences"
msgstr "Farmaatsiatooted, biotehnoloogia ja bioteadused"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Phone"
msgstr "Telefon"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__preferred_role_id
msgid "Preferred Role"
msgstr "Eelistatud roll"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_114
msgid "Real Estate"
msgstr "Kinnisvara"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Request"
msgstr "Päring"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__name
msgid "Request Number"
msgstr "Taotluse number"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_152
msgid "Retailing"
msgstr "Jaemüük"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Retry"
msgstr "Proovi uuesti"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__reveal_id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__reveal_id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__reveal_id
msgid "Reveal"
msgstr "Ilmuta"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_lead__reveal_id
msgid "Reveal ID"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__contact_filter_type__role
msgid "Role"
msgstr "Roll"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__name
msgid "Role Name"
msgstr "Rolli nimi"

#. module: crm_iap_lead
#: model:ir.model.constraint,message:crm_iap_lead.constraint_crm_iap_lead_role_name_uniq
msgid "Role name already exists!"
msgstr "Rolli nimi juba eksisteerib!"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__team_id
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Sales Team"
msgstr "Müügi meeskond"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__user_id
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Salesperson"
msgstr "Müügiesindaja"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_164
msgid "Semiconductors & Semiconductor Equipment"
msgstr "Vaheastme juhid ja vahejuhtimise vahendid"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__contact_filter_type__seniority
msgid "Seniority"
msgstr "Staaž"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__company_size_min
msgid "Size"
msgstr "Suurus"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_165
msgid "Software & Services"
msgstr "Tarkvara ja teenused"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__state_ids
msgid "States"
msgstr "Maakonnad"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__state
msgid "Status"
msgstr "Staatus"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Submit"
msgstr "Esita"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__name
msgid "Tag Name"
msgstr "Sildi nimi"

#. module: crm_iap_lead
#: model:ir.model.constraint,message:crm_iap_lead.constraint_crm_iap_lead_industry_name_uniq
msgid "Tag name already exists!"
msgstr "Sildi nimi on juba loodud!"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__tag_ids
msgid "Tags"
msgstr "Sildid"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__search_type
msgid "Target"
msgstr "Eesmärk"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_166
msgid "Technology Hardware & Equipment"
msgstr "Tehnoloogia riistvara ja seadmed"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Technology Used :"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_149
msgid "Telecommunication Services"
msgstr "Telekommunikatsiooni teenused"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "This makes a total of %d credits for this request."
msgstr "See teeb selle päringu eest kokku %d krediiti."

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Title"
msgstr "Nimi"

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_136
msgid "Transportation"
msgstr "Transport"

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_type
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Type"
msgstr "Tüüp"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"Up to %d additional credits will be consumed to identify %d contacts per "
"company."
msgstr ""
"Kuni %d täiendavat krediiti kulub, et tuvastada %d kontakte ettevõtte kohta."
" "

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_138
msgid "Utilities"
msgstr "Utiilid"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_1
msgid "ceo"
msgstr "tegevjuht"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_2
msgid "communications"
msgstr "kommunikatsioon"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_3
msgid "consulting"
msgstr "konsultatsioon"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_4
msgid "customer_service"
msgstr "kasutajatugi"

#. module: crm_iap_lead
#: model:crm.iap.lead.seniority,name:crm_iap_lead.crm_iap_lead_seniority_1
msgid "director"
msgstr "direktor"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_5
msgid "education"
msgstr "haridus"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "employees"
msgstr "töötajad"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_6
msgid "engineering"
msgstr "tehnika"

#. module: crm_iap_lead
#: model:crm.iap.lead.seniority,name:crm_iap_lead.crm_iap_lead_seniority_2
msgid "executive"
msgstr "juhataja"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_7
msgid "finance"
msgstr "finants"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_8
msgid "founder"
msgstr "asutaja"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_9
msgid "health_professional"
msgstr "tervise_spetsialist"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_10
msgid "human_resources"
msgstr "inimressursid"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_11
msgid "information_technology"
msgstr "infotehnoloogia"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_12
msgid "legal"
msgstr "seaduslik"

#. module: crm_iap_lead
#: model:crm.iap.lead.seniority,name:crm_iap_lead.crm_iap_lead_seniority_3
msgid "manager"
msgstr "juhataja"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_13
msgid "marketing"
msgstr "turundus"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_14
msgid "operations"
msgstr "tegevused"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_15
msgid "owner"
msgstr "omanik"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_16
msgid "president"
msgstr "president"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_17
msgid "product"
msgstr "toode"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_18
msgid "public_relations"
msgstr "avalikud suhted"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_19
msgid "real_estate"
msgstr "kinnisvara"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_20
msgid "recruiting"
msgstr "värbamine"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_21
msgid "research"
msgstr "uuringud"

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_22
msgid "sale"
msgstr "müük"

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "to"
msgstr "kuni"
