# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# 966ff43e6966712895a590e7320ca288, 2019
# 36700cd705c35e1852bdffcd0b296648, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "<strong>Warning!</strong>"
msgstr "<strong>¡Aviso!</strong>"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_product_product__inventory_availability
#: model:ir.model.fields,help:website_sale_stock.field_product_template__inventory_availability
msgid "Adds an inventory availability status on the web product page."
msgstr ""
"Añade un estado de disponibilidad en inventario en la página web del "
"producto."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Availability"
msgstr "Disponibilidad"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Availability Threshold"
msgstr "Umbral de disponibilidad"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__cart_qty
msgid "Cart Qty"
msgstr "Cant. carro"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de Configuración"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__custom_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__custom_message
msgid "Custom Message"
msgstr "Mensaje personalizado"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""
"Modo de disponibilidad predeterminado se aplica en productos almacenables "
"recién creados. Esto se puede cambiar a nivel del producto."

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "In stock"
msgstr "En existencias"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory"
msgstr "Inventario"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__inventory_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__inventory_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__inventory_availability
msgid "Inventory Availability"
msgstr "Disponibilidad del inventario"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Manage the inventory of your products and display their availability status "
"on the website."
msgstr ""
"Gestione su inventario de productos y muestre su estado de disponibilidad en"
" el sitio web."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Mode"
msgstr "Modo"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product"
msgstr "Producto"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de pedido de venta"

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__never
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__never
msgid "Sell regardless of inventory"
msgstr "Vender independientemente del inventario"

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__threshold
msgid "Show inventory below a threshold and prevent sales if not enough stock"
msgstr ""
"Mostrar el inventario por debajo de un determinado umbral y bloquear las "
"ventas si no hay suficientes existencias"

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__always
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__always
msgid "Show inventory on website and prevent sales if not enough stock"
msgstr ""
"Mostrar el inventario en el sitio web y bloquear las ventas si no hay "
"suficientes existencias"

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__threshold
msgid ""
"Show inventory when below the threshold and prevent sales if not enough "
"stock"
msgstr ""
"Mostrar el inventario si desciende bajo el umbral y bloquear las ventas si "
"no hay suficientes existencias"

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__custom
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__custom
msgid "Show product-specific notifications"
msgstr "Mostrar notificaciones sobre el producto"

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""
"Algunos productos dejaron de estar disponibles, así que su carro se ha "
"actualizado. Sentimos las molestias."

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Temporarily out of stock"
msgstr "Temporalmente sin existencias"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Threshold"
msgstr "Umbral"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Albarán"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "Almacén"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order__warning_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order_line__warning_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
#, python-format
msgid "Warning"
msgstr "Alerta"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website this picking belongs to."
msgstr "Sitio web al que pertenece esta selección."

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added"
msgstr "Ya ha añadido"

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid "You ask for %s products but only %s is available"
msgstr "Ha solicitado %s productos, pero hay %s disponible."

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "all"
msgstr "todos"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "available"
msgstr "disponible"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "in your cart."
msgstr "en su carro."
