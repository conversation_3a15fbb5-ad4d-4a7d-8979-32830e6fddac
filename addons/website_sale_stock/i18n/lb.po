# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "<strong>Warning!</strong>"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_product_product__inventory_availability
#: model:ir.model.fields,help:website_sale_stock.field_product_template__inventory_availability
msgid "Adds an inventory availability status on the web product page."
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
msgid "Availability"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Availability Threshold"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__cart_qty
msgid "Cart Qty"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__custom_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__custom_message
msgid "Custom Message"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "In stock"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__inventory_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__inventory_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__inventory_availability
msgid "Inventory Availability"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Manage the inventory of your products and display their availability status "
"on the website."
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Mode"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product Template"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__never
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__never
msgid "Sell regardless of inventory"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__threshold
msgid "Show inventory below a threshold and prevent sales if not enough stock"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__always
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__always
msgid "Show inventory on website and prevent sales if not enough stock"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__threshold
msgid ""
"Show inventory when below the threshold and prevent sales if not enough "
"stock"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields.selection,name:website_sale_stock.selection__product_template__inventory_availability__custom
#: model:ir.model.fields.selection,name:website_sale_stock.selection__res_config_settings__inventory_availability__custom
msgid "Show product-specific notifications"
msgstr ""

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Temporarily out of stock"
msgstr ""

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Threshold"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order__warning_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order_line__warning_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
#, python-format
msgid "Warning"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr ""

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website this picking belongs to."
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added"
msgstr ""

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid "You ask for %s products but only %s is available"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "all"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "available"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "in your cart."
msgstr ""
