# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_extended
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_view_form_normal_inherit_extended
msgid "Compute from BOM"
msgstr ""

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_view_form_normal_inherit_extended
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""

#. module: product_extended
#: model:ir.model,name:product_extended.model_product_product
msgid "Product"
msgstr "Proizvod"

#. module: product_extended
#: model:ir.model,name:product_extended.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"
