# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <ma<PERSON><PERSON><PERSON><PERSON>@entersys.it>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <mfe<PERSON><PERSON><EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON>-<PERSON>essel<PERSON>ch, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-07 12:31+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: Martin Trigaux, 2022\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid " Add Images"
msgstr "Aggiungi immagini"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr "\" con un"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "\"A URL\" non può essere vuoto."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr ""
"\"A URL\" non può contenere il parametro %s, che non è utilizzato in \"Da "
"URL\"."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL to\" non è valido: %s"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "\"A URL\" deve contenere il parametro %s utilizzato in \"Da URL\"."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"A URL\" deve iniziare con una barra."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "N. pagine visitate"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "%s (id:%s)"
msgstr "%s (ID:%s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "' did not match any pages."
msgstr "\" non corrisponde ad alcuna pagina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(può essere usato in"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", autore:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""
".\n"
"            Modificare il suo nome interromperà queste chiamate."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1/5"
msgstr "1/5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "10s"
msgstr "10s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1s"
msgstr "1s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr "2 <span class=\"sr-only\">(corrente)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2/5"
msgstr "2/5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "Assistenza tecnica gratuita 24/7"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2s"
msgstr "2s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3/5"
msgstr "3/5"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
msgid "301 Moved permanently"
msgstr "301 - Trasferimento permanente"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
msgid "302 Moved temporarily"
msgstr "302 - Trasferimento temporaneo"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 - Redireziona / Riscrivi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3s"
msgstr "3s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "4/5"
msgstr "4/5"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 - Non trovato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5/5"
msgstr "5/5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5s"
msgstr "5s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>Oltre 50.000 aziende</b> fanno crescere le loro attività grazie a Odoo."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr ""
"<b>Fai clic su Modifica</b> per iniziare a creare la tua pagina principale."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid ""
"<b>Click on a text</b> to start editing it. <i>It's that easy to edit your "
"content!</i>"
msgstr ""
"<b>Fai clic su un testo</b> per modificarlo. <i>È così semplice cambiare il "
"contenuto!</i>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories are for everyone even when only written for just one "
"person.</b> If you try to write with a wide general audience in mind, your "
"story will ring false and be bland. No one will be interested. Write for one"
" person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"<b>Le grandi storie sono per tutti, anche se sono scritte per una sola "
"persona.</b> Se cerchi di scrivere per un pubblico ampio e generico, la tua "
"storia suonerà falsa e sarà noiosa. Nessuno sarà interessato. Scrivila per "
"una persona. Se è autentica per una persona, sarà autentica per tutti. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories have personality.</b> Consider telling a great story that "
"provides personality. Writing a story with personality for potential clients"
" will assists with making a relationship connection. This shows up in small "
"quirks like word choices or phrases. Write from your point of view, not from"
" someone else's experience."
msgstr ""
"<b>Le grandi storie hanno personalità.</b> Valuta l'opportunità di "
"raccontare una grande storia che esprime carattere. Scrivere per i "
"potenziali clienti una storia con personalità aiuta a stabilire una "
"relazione. Questo si evidenzia nei piccoli dettagli come la scelta delle "
"parole o delle frasi. Scrivi usando il tuo punto di vista, non l'esperienza "
"di qualcun altro. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "<b>Install a contact form</b> to improve this page."
msgstr "<b>Configura un modulo contatto</b> per migliorare questa pagina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid ""
"<b>Install new apps</b> to get more features. Let's install the <i>'Contact "
"form'</i> app."
msgstr ""
"<b>Installa nuove applicazioni</b> per avere maggiori funzionalità. Parti "
"con l'app <i>«Modulo contatto»</i>."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid ""
"<b>That's it!</b><p>Your page is all set to go live. Click the "
"<b>Publish</b> button to publish it on the website.</p>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\">Scrivere bene è "
"semplice, ma non semplicistico.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, "
"255);\">Modifica questo titolo</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\"><b>Sell Online.</b> Easily.</font>"
msgstr "<font style=\"font-size: 62px;\"><b>Vendi online.</b> Facilmente.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">Un titolo incisivo</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "<font style=\"font-size: 62px;\">FAQ</font>"
msgstr "<font style=\"font-size: 62px;\">FAQ</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<font style=\"font-size: 62px;\">Our offers</font>"
msgstr "<font style=\"font-size: 62px;\">Le nostre offerte</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">Titolo diapositiva</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">Titolo del sito</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 giorni fa</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"<i class=\"fa fa-1x fa-picture-o mr-2\"/>Add a caption to enhance the "
"meaning of this image."
msgstr ""
"<i class=\"fa fa-1x fa-picture-o mr-2\"/>Aggiungi una didascalia per "
"potenziare il significato dell'immagine. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Crea un progetto Google e ottieni una chiave"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Abilita fatturazione sul progetto Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Come ottenere un ID cliente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Tracking ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Ottenere un ID di monitoraggio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-diamond\"/> Features"
msgstr "<i class=\"fa fa-diamond\"/> Funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-align-left\"/> Alignment"
msgstr "<i class=\"fa fa-fw fa-align-left\"/> Allineamento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-anchor\"/> Link Anchor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-arrows\"/> Background Image Sizing"
msgstr "<i class=\"fa fa-fw fa-arrows\"/> Dimensionamento immagine di sfondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-arrows\"/> Size"
msgstr "<i class=\"fa fa-fw fa-arrows\"/> Dimensione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-arrows-h\"/> Images spacing"
msgstr "<i class=\"fa fa-fw fa-arrows-h\"/> Spaziatura immagini"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-arrows-h\"/> Size"
msgstr "<i class=\"fa fa-fw fa-arrows-h\"/> Dimensione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-arrows-h\"/> Width"
msgstr "<i class=\"fa fa-fw fa-arrows-h\"/> Larghezza"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-arrows-v\"/> Alignment"
msgstr "<i class=\"fa fa-fw fa-arrows-v\"/> Allineamento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Scroll Speed"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Velocità di scorrimento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Slideshow speed"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Velocità presentazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Speed"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Velocità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-clone\"/> Transition"
msgstr "<i class=\"fa fa-fw fa-clone\"/> Transizione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-columns\"/> Number of columns"
msgstr "<i class=\"fa fa-fw fa-columns\"/> Numero di colonne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-eye-slash\"/> Transparent"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-eyedropper\"/> Background Color"
msgstr "<i class=\"fa fa-fw fa-eyedropper\"/> Colore di sfondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-eyedropper\"/> Color"
msgstr "<i class=\"fa fa-fw fa-eyedropper\"/> Colore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-eyedropper\"/> Filter"
msgstr "<i class=\"fa fa-fw fa-eyedropper\"/> Filtro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-facebook\"/> Options"
msgstr "<i class=\"fa fa-fw fa-facebook\"/> Opzioni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-font\"/> Typography"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-heart\"/> Cuori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-magic\"/> Mode"
msgstr "<i class=\"fa fa-fw fa-magic\"/> Modalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-magic\"/> Style"
msgstr "<i class=\"fa fa-fw fa-magic\"/> Stile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-magic\"/> Styles"
msgstr "<i class=\"fa fa-fw fa-magic\"/> Stili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-minus\"/> Remove Tab"
msgstr "<i class=\"fa fa-fw fa-minus\"/> Rimuovi scheda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-minus\"/> Thickness"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-paint-brush\"/> Styling"
msgstr "<i class=\"fa fa-fw fa-paint-brush\"/> Stile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-pencil\"/> Style"
msgstr "<i class=\"fa fa-fw fa-pencil\"/> Stile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-picture-o\"/> Background"
msgstr "<i class=\"fa fa-fw fa-picture-o\"/> Sfondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-plus\"/> Add Tab"
msgstr "<i class=\"fa fa-fw fa-plus\"/> Aggiungi scheda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-plus-circle\"/> Add Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-plus-circle\"/> Add images"
msgstr "<i class=\"fa fa-fw fa-plus-circle\"/> Aggiungi immagini"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-refresh\"/> Re-order"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Quadrati"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Stelle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-tasks\"/> Progress Bar"
msgstr "<i class=\"fa fa-fw fa-tasks\"/> Barra di avanzamento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-th\"/> Columns"
msgstr "<i class=\"fa fa-fw fa-th\"/> Colonne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-trash\"/> Remove all images"
msgstr "<i class=\"fa fa-fw fa-trash\"/> Rimuovi tutte le immagini"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-fw fa-trash-o\"/> Remove Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Fuori rete</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connesso</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-indent\"/> Inner content"
msgstr "<i class=\"fa fa-indent\"/> Contenuto interno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"<i class=\"fa fa-info-circle mr-1\"/> <small>Additional information</small>"
msgstr ""
"<i class=\"fa fa-info-circle mr-1\"/> <small>Informazioni aggiuntive</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-magic icon-fix\"/> Effects"
msgstr "<i class=\"fa fa-magic icon-fix\"/> Effetti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Add a language..."
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Aggiungi una lingua..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-puzzle-piece\"/> Mega Menu"
msgstr "<i class=\"fa fa-puzzle-piece\"/> Megamenù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-th-large\"/> Structure"
msgstr "<i class=\"fa fa-th-large\"/> Struttura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large\"/> WEBSITE"
msgstr "<i class=\"fa fa-th-large\"/> SITO WEB"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"
msgstr "<i title=\"La pagina è ottimizzata SEO?\" class=\"fa fa-search\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"
msgstr ""
"<i title=\"La pagina è inclusa nel menù principale?\" class=\"fa fa-thumb-"
"tack\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"
msgstr ""
"<i title=\"La pagina è indicizzata dai motori di ricerca?\" class=\"fa fa-"
"globe\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"
msgstr "<i title=\"La pagina è stata pubblicata?\" class=\"fa fa-eye\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i>Installazione istantanea, soddisfatti o rimborsati.</i>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"<link href='https://fonts.googleapis.com/css?family=Bonbon&display=swap' "
"rel='stylesheet'>"
msgstr ""
"<link href='https://fonts.googleapis.com/css?family=Bonbon&display=swap' "
"rel='stylesheet'>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "<p><b>Click here</b> to create a new page.</p>"
msgstr "<p><b>Fai clic qui</b> per creare una nuova pagina.</p>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid ""
"<p><b>Your homepage is live.</b></p><p>Let's add a new page for your "
"site.</p>"
msgstr ""
"<p><b>La pagina principale è in rete.</b></p><p>Aggiungiamo una nuova pagina"
" per il sito.</p>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "<p>Enter a title for the page.</p>"
msgstr "<p>Inserisci un titolo per la pagina.</p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">La tua e-mail non "
"verrà mai condivisa con nessuno.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ mese</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"            <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"            <span class=\"sr-only\">Successivo</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"            <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"            <span class=\"sr-only\">Precedente</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"I valori impostati qui sono "
"specifici per sito web.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.company_description
msgid ""
"<span class=\"fa fa-map-marker fa-fw mt16\" role=\"img\" aria-"
"label=\"Address\" title=\"Address\"/>"
msgstr ""
"<span class=\"fa fa-map-marker fa-fw mt16\" role=\"img\" aria-"
"label=\"Address\" title=\"Indirizzo\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil mr-2\"/>Edit"
msgstr "<span class=\"fa fa-pencil mr-2\"/>Modifica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus mr-2\"/>New"
msgstr "<span class=\"fa fa-plus mr-2\"/>Nuovo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"
msgstr "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Ordina\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page
msgid ""
"<span class=\"o_add_facebook_page\">\n"
"                <i class=\"fa fa-plus-circle\"/> Add Facebook Page\n"
"            </span>"
msgstr ""
"<span class=\"o_add_facebook_page\">\n"
"                <i class=\"fa fa-plus-circle\"/> Aggiungi pagina Facebook\n"
"            </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"o_add_images\" style=\"cursor: pointer;\"><i class=\"fa fa-"
"plus-circle\"/> Add Images</span>"
msgstr ""
"<span class=\"o_add_images\" style=\"cursor: pointer;\"><i class=\"fa fa-"
"plus-circle\"/> Aggiungi immagini</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">,00</span>\n"
"                                <span class=\"s_comparisons_currency\">€</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">,00</span>\n"
"                                <span class=\"s_comparisons_currency\">€</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">,00</span>\n"
"                                <span class=\"s_comparisons_currency\">€</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr "<span class=\"sr-only\">Aziona elenco a discesa</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"
msgstr ""
"<span title=\"Anteprima dispositivi mobili\" role=\"img\" aria-"
"label=\"Mobile preview\" class=\"fa fa-mobile\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span/>\n"
"                            <span class=\"css_publish\">Unpublished</span>\n"
"                            <span class=\"css_unpublish\">Published</span>"
msgstr ""
"<span/>\n"
"                            <span class=\"css_publish\">Non pubblicata</span>\n"
"                            <span class=\"css_unpublish\">Pubblicata</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid ""
"<span>Contact us</span>\n"
"                        <i class=\"fa fa-1x fa-fw fa-arrow-circle-right ml-1\"/>"
msgstr ""
"<span>Contattaci</span>\n"
"                        <i class=\"fa fa-1x fa-fw fa-arrow-circle-right ml-1\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid "<span>— Jane DOE, CEO of <b>MyCompany</b></span>"
msgstr "<span>— Jane DOE - Amministratore delegato di <b>MyCompany</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid "<span>— John DOE, CEO of <b>MyCompany</b></span>"
msgstr "<span>— John DOE - Amministratore delegato di <b>MyCompany</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""
"Una CDN consente di offrire il contenuto del sito web con alta disponibilità"
" e prestazioni a tutti i visitatori, ovunque si trovino."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "Sottotitolo della sezione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"Una scheda è un contenitore flessibile ed estensibile. Include opzioni per "
"intestazioni e piè di pagina, un'ampia varietà di contenuti, colori di "
"sfondo contestuali e potenti opzioni di visualizzazione."

#. module: website
#: model:ir.model.fields,help:website.field_base_automation__website_published
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"Un'azione server di codice può essere eseguita dal sito web con un "
"controller. L'indirizzo è <base>/website/action/<website_path>. Impostare il"
" valore di questo campo a Vero per consentire agli utenti di eseguire "
"un'azione. Se questo campo è impostato a Falso, l'azione non può essere "
"eseguita tramite il sito web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"A great way to catch your reader's attention is to tell a story. "
"<br/>Everything you consider writing can be told as a story."
msgstr ""
"Un ottimo modo per catturare l'attenzione dei lettori è quello di raccontare"
" una storia. <br/>Tutto ciò che ritieni di scrivere può essere raccontato "
"come storia. "

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr ""
"Una visita viene considerata nuova se l'ultima connessione è avvenuta più di"
" 8 ore fa."

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_partner_uniq
msgid "A partner is linked to only one visitor."
msgstr "Un partner viene collegato a un solo visitatore."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr ""
"Una breve spiegazione, con parole chiare, <br/>di questa grandiosa "
"funzionalità."

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""
"Un visitatore viene considerato connesso se ha visualizzato una pagina negli"
" ultimi 5 minuti."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_version
msgid "A/B Testing"
msgstr "Test A/B"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "API Key"
msgstr "Chiave API"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "About us"
msgstr "Chi siamo"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Access Error"
msgstr "Errore di accesso"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "Token di accesso"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "Il token di accesso deve essere univoco."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "Gestione contabilità e vendite"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "Azione"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model:ir.model.fields,field_description:website.field_website_visitor__active
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Active"
msgstr "Attivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adatta queste tre colonne alle tue esigenze grafiche. Per duplicarle, "
"eliminarle o spostarle, selezionane una e usa le icone in alto per portare a"
" termine l'azione. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Add"
msgstr "Aggiungi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "Aggiungi funzionalità"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "Aggiungi voce di Megamenù"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "Aggiungi voce di menù"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/widgets/theme.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "Aggiungi un font Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "Aggiungi un grande slogan."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Add a menu item"
msgstr "Aggiungi una voce di menù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr "Aggiungi funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr "Aggiunge collegamenti ai social network nel sito web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Add to menu"
msgstr "Aggiungi al menù"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "After having checked how it looks on mobile, <b>close the preview</b>."
msgstr ""
"Dopo aver controllato il risultato sul dispositivo mobile, <b>chiudi "
"l'anteprima</b>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Alert"
msgstr "Avviso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner - Direttore tecnologico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic person in life who can say she loves what she does.\n"
"                                She mentors 100+ in-house developers and looks after the community of over\n"
"                                thousands developers."
msgstr ""
"Aline è una persona iconica, di quelle che possono vantarsi di amare il proprio lavoro.\n"
"                                Supervisiona oltre 100 sviluppatori interni e si occupa della comunità, che conta\n"
"                                migliaia di sviluppatori."

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "Tutti i percorsi del sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "Queste icone sono totalmente gratuite per uso commerciale. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr "Consente di specificare se una pagina del sito web è tracciabile o no"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Nonostante questo sito web possa avere collegamenti ad altri siti, ciò non "
"implica, direttamente o indirettamente, alcuna approvazione, associazione, "
"sponsorizzazione, sostegno o affiliazione a tali siti web, a meno che ciò "
"non venga espressamente citato."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occured while rendering the template"
msgstr "Si è verificato un errore durante l'elaborazione del modello."

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
msgid "Analytics"
msgstr "Analitiche"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Analyze the efficiency of your marketing campaigns by using trackable UTM "
"trackers (campaigns, medium, sources). Create trackers and follow clicks "
"from the Promote menu of your website. Those trackers can be used in Google "
"Analytics or in Odoo reports where you can see the opportunities and sales "
"revenue generated thanks to your links."
msgstr ""
"Analizza l'efficienza delle tue campagne di marketing utilizzando un sistema"
" di tracciamento UTM tracciabile (campagne, media, fonti). Crea tracciatori "
"e segui i clic dal menu Promuovi del tuo sito web. Questo sistema di "
"tracciamento può essere utilizzato in Google Analytics o nei resoconti Odoo "
"dove puoi vedere le opportunità e i ricavi di vendita generati grazie ai "
"tuoi link."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "Nome di ancoraggio"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "Applicazioni"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "Nome file arch"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Archived"
msgstr "In archivio"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr ""
"Indica se i nuovi account utente creati devono essere specifici per sito web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "Eliminare veramente questa pagina?"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "Utilità asset"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "Allegato"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "Reindirizzare lingua automaticamente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr "Ridimensionamento automatico"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__website_published
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "Disponibile nel sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Background"
msgstr "Sfondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Badge"
msgstr "Etichetta di evidenziazione"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Base"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "Architettura vista base"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "Vendite e marketing di base per massimo 2 utenti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Principiante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr "Grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Black"
msgstr "Nero"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Blue"
msgstr "Blu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Body"
msgstr "Corpo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border"
msgstr "Bordo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "Con bordo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bottom"
msgstr "In basso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Boxed"
msgstr "Box"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Breadcrumb"
msgstr "Percorso di navigazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "Pulsante"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "URL base CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "Filtri CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
msgid "Can Publish"
msgstr "Può pubblicare"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/menu/new_content.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#, python-format
msgid "Cancel"
msgstr "Annulla"

#. module: website
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr "Impossibile disattivare una lingua attualmente in uso in un sito web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card"
msgstr "Scheda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr "Corpo scheda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr "Piè di pagina scheda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr "Intestazione scheda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "Catchy Headline"
msgstr "Titolo accattivante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "Al centro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "Al centro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Change Cover"
msgstr "Cambia copertina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "Modifica delle icone"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/widgets/theme.js:0
#, python-format
msgid "Changing this color will regenerate the default theme color scheme"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "Campo figlio"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "Sottomenù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose a pattern"
msgstr "Scegli un motivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"Scegli un'immagine vivace e scrivi un paragrafo stimolante a riguardo.<br/> "
"Non deve essere lungo, deve servire a rafforzare l'immagine."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "Scegli un nome di ancoraggio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose an image"
msgstr "Scegli un'immagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose the theme colors"
msgstr "Scegli i colori del tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose your fonts"
msgstr "Scegli i font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose your layout"
msgstr "Scegli la struttura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose your navbar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr "Cerchio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "Slogan intelligente"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/customize.js:0
#, python-format
msgid ""
"Click here to choose your main branding color.<br/>It will recompute the "
"palette with suggested matching colors."
msgstr ""
"Clicca qui per scegliere il colore principale della tua immagine di "
"marca.<br/> La tavolezza verrà rinfrescata con colori coordinati."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "Click on <b>Continue</b> to create the page."
msgstr "Fai clic su <b>Continua</b> per creare la pagina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "Click the <b>Save</b> button."
msgstr "Fai clic sul pulsante <b>Salva</b>."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "Clicca per scegliere più immagini"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "ID client"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr "Chiave privata client"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr "Clona pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/wysiwyg_multizone_translate.js:0
#: code:addons/website/static/src/xml/website.gallery.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "Chiudi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Colors"
msgstr "Colori"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Company"
msgstr "Azienda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Company team"
msgstr "Team dell'azienda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "Un CRM completo per team di ogni dimensione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "Componenti"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Compose Email"
msgstr "Componi e-mail"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Configurazione"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "Configurazione social network"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Confirmation"
msgstr "Conferma"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Google Analytics"
msgstr "Connetti Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "Entra in contatto con noi"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Contact"
msgstr "Contatto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Contact Us"
msgstr "Contattaci"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model:website.menu,name:website.menu_contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "Contattaci"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                We'll do our best to get back to you as soon as possible."
msgstr ""
"Contattaci per qualsiasi domanda che riguarda l'azienda o i servizi che offriamo.<br/>\n"
"                                                                Faremo del nostro meglio per rispondere il prima possibile."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Rete di distribuzione dei contenuti (CDN)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "Contenuto da tradurre"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Continue"
msgstr "Continua"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "Nazione"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "Bandiera nazione"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "Gruppi di nazioni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Crea pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "Crea un"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr "Crea un nuovo sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "Creata da"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
msgid "Created on"
msgstr "Creata il"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Custom"
msgstr "Personalizzato"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Account cliente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Customers"
msgstr "Clienti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr "Personalizza"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/widgets/theme.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Customize Theme"
msgstr "Personalizza tema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid ""
"Customize any block through this menu. Try to change the background color of"
" this block."
msgstr ""
"Personalizza i blocchi usando questo menù. Prova a cambiare il colore di "
"sfondo del blocco."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "TRASCINA QUI I VARI COMPONENTI"

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "Bacheca"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dashed"
msgstr "Tratteggiato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Data"
msgstr "Dati"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "Data"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "Predefinito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Diritti di accesso predefiniti"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "Lingua predefinita"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "Menù principale predefinito"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "Immagine predefinita per condivisione social"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Lingua predefinita"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "Codice lingua predefinito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "Rimozione dei blocchi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "Elimina voce di menù"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Delete Page"
msgstr "Elimina pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Elimina l'immagine qui sopra o sostituiscila con una foto che illustri il "
"messaggio. Fai clic sull'immagine per cambiarne lo stile <em>angolo "
"arrotondato</em>."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "Elimina il font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr "Elimina pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Demo Logo"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Dependencies"
msgstr "Dipendenze"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Description"
msgstr "Descrizione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Details"
msgstr "Dettagli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disable autoplay"
msgstr "Disattiva riproduzione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Disabled"
msgstr "Disabilitato"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/js/widgets/theme.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Discard"
msgstr "Abbandona"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "Scopri di più"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "Gruppo di discussione"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_published_mixin__display_name
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "Visualizzare i badge"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "Visualizzare la biografia"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "Visualizzare la descrizione del sito web"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this logo on the website."
msgstr "Visualizza il logo nel sito web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "Visualizzare il sito web quando gli utenti visitano questo dominio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Do something"
msgstr "Fai qualcosa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "Modificare i dati dell'azienda ?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "Installare l'applicazione «%s»?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "Dominio"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr ""
"È necessario aggiornare tutti i collegamenti che fanno riferimento alla "
"pagina."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dotted"
msgstr "Punteggiato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Double"
msgstr "Doppio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Fai doppio clic su un'icona per sostituirla con una a tua scelta."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "Drag another block in your page, below the cover."
msgstr "Trascina un altro blocco nella pagina, sotto la copertina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "Drag the <i>Cover</i> block and drop it in your page."
msgstr "Trascina il blocco <i>Copertina</i> e rilascialo nella pagina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "Drag the block and drop it in your new page."
msgstr "Trascina il blocco e rilascialo nella nuova pagina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "Spostare verso destra per creare un sottomenù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr "Menù a discesa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Menù a discesa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "Duplica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Duplica i blocchi e le colonne per aggiungere funzionalità."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit"
msgstr "Modifica"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Modifica menù"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "Modifica voce di menù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "Modifica degli stili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr "Modifica menù principale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr "Modifica il codice della pagina (avanzato)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr "Modifica in backend"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Edit my Analytics Client ID"
msgstr "Modifica ID cliente di Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"Edit the content below this line to adapt the default \"page not found\" "
"page."
msgstr ""
"Modifica il contenuto sottostante per personalizzare la pagina predefinita "
"\"pagina non trovata\"."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Edit the menu"
msgstr "Modifica menù"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "Redattore e progettista"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Email"
msgstr "E-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr "Indirizzo e-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "Assistenza tecnica via e-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr "Inserisci e-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Equal height"
msgstr "Stessa altezza"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Error"
msgstr "Errore"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Events"
msgstr "Eventi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Esperto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""
"Spiega i vantaggi della tua offerta. <br/>Non citare prodotti o servizi, ma "
"parla delle soluzioni che proponi."

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__xml_id
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "ID esterno"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/widgets/theme.js:0
#: code:addons/website/static/src/js/widgets/theme.js:0
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#, python-format
msgid "Extra Color"
msgstr "Colore aggiuntivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Account Facebook"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Facebook Page"
msgstr "Pagina Facebook"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "Dissolvenza"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "Impossibile installare «%s»"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fast"
msgstr "Veloce"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "Icona"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Funzionalità uno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Funzionalità tre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "Titolo funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Funzionalità due"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Features"
msgstr "Funzionalità"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"File dal quale ha origine la vista.\n"
"                                                          Utile per il ripristino (forte) delle viste danneggiate o per leggere, nella modalità dev-xml, l'architettura dal file."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Color"
msgstr "Filtro colore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "Filtro intensità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Prima funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "Primo menù"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
msgid "First connection date"
msgstr "Data prima connessione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "Primo elenco di funzionalità"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "Prima pagina collegata a questa vista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "Adatta al testo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fixed"
msgstr "Fisso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Float"
msgstr "Mobile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Folded list"
msgstr "Elenco minimizzato"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Follow all the"
msgstr "Segui tutti i"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr "Segue il traffico del sito web in Odoo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Font Size"
msgstr "Dimensione carattere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Fonts"
msgstr "Caratteri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Footer"
msgstr "Piè di pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr "Forza l'utente a creare un account per sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "Modulo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Form Builder"
msgstr "Generatore modulo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind Company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing and the Customer Experience strategies."
msgstr ""
"Fondatore e capo visionario, Tony è la forza trainante dell'azienda. Ama tenersi\n"
"                                sempre occupato partecipando allo sviluppo del software, al marketing e\n"
"                                alle strategie sull'esperienza del cliente."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Registrazione gratuita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Full"
msgstr "Intera"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "Schermo intero"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "Intera larghezza"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Accesso a tutti i moduli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Accesso a tutti i moduli e funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "Account GitHub"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Go To Page"
msgstr "Vai alla pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid "Go back to the blocks menu."
msgstr "Ritorna al menù blocchi."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "Vai a "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Go to Link"
msgstr "Vai al link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "Vai al gestore pagine"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr "Vai al sito web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:0
#, python-format
msgid ""
"Good Job! You have designed your homepage. Let's check how this page looks "
"like on <b>mobile devices</b>."
msgstr ""
"Ottimo lavoro! Hai creato la pagina principale. Controlla come verrà "
"visualizzata sui <b>dispositivi mobili</b>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"Un buon testo inizia con il comprendere come il prodotto o il servizio aiuta"
" il cliente. Le parole semplici sono più efficaci di quelle boriose e del "
"linguaggio pomposo. "

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "Bacheca di Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Chiave Google Analytics"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""
"Inizializzazione di Google Analytics non riuscita. Forse questo dominio non "
"è incluso nel progetto Google Analytics di questo ID cliente."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr "ID cliente Google"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr "Chiave privata client Google"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font HTML"
msgstr "HTML font Google"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
#: model_terms:ir.ui.view,arch_db:website.company_description
msgid "Google Maps"
msgstr "Mappe Google"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Chiave API Google Maps"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "Ottimo affare"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "Great products for great people"
msgstr "Grandi prodotti per grandi persone"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Green"
msgstr "Verde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "Griglia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "Gruppi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H1"
msgstr "Titolo1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H2"
msgstr "Titolo2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS/JS Editor"
msgstr "Editor HTML/CSS/JS"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "Mezzo schermo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "Intestazione"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "Colore intestazione"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "Sovrapposizione intestazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr "Titolo 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr "Titolo 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr "Titolo 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr "Titolo 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr "Titolo 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr "Titolo 6"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "Gli elementi visivi usati per tradurre in modo funzionale sono:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hero"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Hide Cover Photo"
msgstr "Nascondi la foto di copertina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Hide this page from search results"
msgstr "Nasconde questa pagina nei risultati di ricerca"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "Alta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""
"Suggerimento: digita \"/\" per cercare una pagina esistente e \"#\" per "
"collegare a un ancoraggio."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model:website.menu,name:website.menu_home
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "Home"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr "Home <span class=\"sr-only\">(corrente)</span>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.portal_404
#, python-format
msgid "Homepage"
msgstr "Pagina principale"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Client ID"
msgstr "Come ottenere un ID cliente"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Tracking ID"
msgstr "Come ottenere un ID di monitoraggio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Huge"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_published_mixin__id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_base_automation__xml_id
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID dell'azione, se definita in un file XML"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "ID della vista definita nel file XML"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Icon"
msgstr "Icona"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr "Se vero, i nuovi account vengono associati al corrente sito web"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the company logo as the default social share image."
msgstr ""
"Se impostata, sostituisce il logo aziendale come immagine predefinita per la"
" condivisione nei social."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""
"Se questo errore è causato da una modifica ai modelli, è possibile "
"effettuarne il ripristino alle <strong>impostazioni di fabbrica</strong>."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"Se il campo è vuoto, la vista si applica a tutti gli utenti. In caso "
"contrario, si applica solo agli utenti di questi gruppi."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"Se questa è una vista ereditata,\n"
"* se True, la vista estende sempre la vista genitore\n"
"* se False, la vista attualmente non estende la vista genitore ma si può abilitare"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/editor_menu.js:0
#, python-format
msgid ""
"If you discard the current edition, all unsaved changes will be lost. You "
"can cancel to return to the edition mode."
msgstr ""
"Se viene abbandonata la versione corrente, tutte le modifiche non salvate "
"andranno perse. È possibile annullare per tornare alla modalità di modifica."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
msgid "Image"
msgstr "Immagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr "Nel menù principale"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"In questa modalità, puoi tradurre solo i testi. Per cambiare la struttura della pagina, devi modificare la pagina principale.\n"
"Ogni modifica sulla pagina master viene automaticamente applicata a tutte le versioni tradotte."

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Incorrect Client ID / Key"
msgstr "ID cliente / Chiave non corretti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "Indicizzata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#, python-format
msgid "Info"
msgstr "Informazioni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Informazioni su"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "Vista ereditata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "Account Instagram "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Install"
msgstr "Installa"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Installazione lingua"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr "Installa nuova lingua"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Applicazioni installate"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "Localizzazioni / Piani dei conti installati"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "Tema installato"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "Installazione di \"%s\""

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "Interact with them by sending them messages."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Invalid Facebook Page Url"
msgstr "URL della pagina Facebook non valido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe - Direttore finanziario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the numbers and\n"
"                                improves them. She is determined to drive success and delivers her professional\n"
"                                acumen to bring Company at the next level."
msgstr ""
"Grazie alla sua esperienza internazionale, Iris aiuta a comprendere le cifre e a\n"
"                                migliorarle. È determinata ad avere successo mettendo le sue capacità\n"
"                                professionali al servizio dell'azienda per aiutarla a progredire. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Is Connected"
msgstr "È connesso"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "È un Megamenù"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
msgid "Is Published"
msgstr "È pubblicato"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "È visibile"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "È connesso ?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "Sembra che il file sia stato chiamato da"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr "Voce 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr "Voce 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "Join us and make your company a better place."
msgstr "Unisciti a noi e migliora la tua azienda."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "Lascia vuoto per usare un valore predefinito"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_mrp_document__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "Chiave"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keyword"
msgstr "Parola chiave"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keywords"
msgstr "Parole chiave"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "Lingua"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "Lingua del sito web al momento della creazione del visitatore"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "Lingue"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "Lingue disponibili nel sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "Grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "Ultima azione"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
msgid "Last Connection"
msgstr "Ultima connessione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "Ultimo menù"

#. module: website
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_multi_mixin____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_published_mixin____last_update
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_seo_metadata____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "Ultimo mese"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Page"
msgstr "Ultima pagina"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "Ultima pagina visitata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "Ultima settimana"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "Ultimo anno"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "Ultima azione"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "Data di ultima visualizzazione della pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Layout"
msgstr "Struttura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Learn more"
msgstr "Scopri di più"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Sinistra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "Menù sinistro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Consente l'accesso ai clienti per la visualizzazione dei documenti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Let's start designing."
msgstr "Inizia a creare."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Library"
msgstr "Libreria"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Personalizzazione limitata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Line-On-Sides"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link"
msgstr "Link"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "Link di ancoraggio"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_links
msgid "Link Trackers"
msgstr "Monitoraggi link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr "Pulsante con link"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
msgid "Linked Partner"
msgstr "Partner collegato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "Account LinkedIn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "Collegamenti ad altri siti web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "Caricamento..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Local Events"
msgstr "Eventi locali"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Logo"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Logo Height"
msgstr "Altezza logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "Bassa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Main"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Main Layout"
msgstr "Struttura principale"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "Menù principale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr "Gestione pagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr "Gestione pagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr "Gestione pagine del sito web personali"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Manage multiple websites"
msgstr "Gestione siti web multipli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr "Gestione pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Masonry"
msgstr "Muro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Medio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Meet the Executive Team"
msgstr "Incontra il team esecutivo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "Megamenù"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "Classi del Megamenù"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "Contenuto del Megamenù"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "Menù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "Voce di menù %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Menu Label"
msgstr "Etichetta menù"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_menu_list
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#, python-format
msgid "Menus"
msgstr "Menù"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Messages"
msgstr "Messaggi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark - Direttore operativo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as Commercial\n"
"                                Director in the software industry, Mich has helped Company to get where it\n"
"                                is today. Mich is among the best minds."
msgstr ""
"Mich ama affrontare le sfide. Grazie alla sua pluriennale esperienza come direttore \n"
"                               commerciale nell'industria del software, Mich ha contributo al successo attuale\n"
"                                dell'azienda. È una tra le menti più brillanti."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Middle"
msgstr "Al centro"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
msgid "Mobile Phone"
msgstr "Dispositivo mobile"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:0
#, python-format
msgid "Mobile preview"
msgstr "Anteprima dispositivi mobili"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "Modello"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "Dati modello"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model_ids
msgid "Models"
msgstr "Modelli"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "Architettura modificata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""
"Gli argomenti correlati alla tua parola chiave i più ricercati, ordinati per"
" importanza:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr "Muovi per primo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr "Muovi per ultimo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr "Muovi al successivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr "Muovi al precedente"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "Mixin siti web multipli"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "Mixin siti web multipli pubblicati "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Multi-Website"
msgstr "Sito web multiplo"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "Sito web multiplo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "My Website"
msgstr "Sito web personale"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Name"
msgstr "Nome"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr "Nome e icona del sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Stretto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr "Barra di navigazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "New"
msgstr "Nuovo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Blog Post"
msgstr "Nuovo articolo del blog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Course"
msgstr "Nuovo corso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Event"
msgstr "Nuovo evento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Forum"
msgstr "Nuovo forum"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Job Offer"
msgstr "Nuova offerta di lavoro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Livechat Channel"
msgstr "Nuovo canale chat dal vivo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "New Page"
msgstr "Nuova pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Product"
msgstr "Nuovo prodotto"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "Nuova finestra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr "Nuova pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Newsletter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Block"
msgstr "Blocco newsletter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "Finestra a comparsa newsletter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:0
#: code:addons/website/static/src/xml/website.gallery.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "Successivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Nessuna personalizzazione"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "Ancora nessuna pagina visualizzata da questo visitatore"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "Nessun partner collegato a questo visitatore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Nessuna assistenza tecnica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No-scroll"
msgstr "Nessuno scorrimento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "None"
msgstr "Nessuno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr "Non ottimizzato SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr "Non presente nel menù principale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr "Non indicizzata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "Non pubblicata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "Non monitorata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not visible"
msgstr "Non visibile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr ""
"Nota: per nascondere la pagina, deselezionarla dal menù Personalizza in "
"alto."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
msgid "Number of languages"
msgstr "Numero di lingue"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
msgid "Number of visits"
msgstr "Numero di visite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 1 for three columns"
msgstr "Odoo - Esempio 1 per tre colonne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 2 for three columns"
msgstr "Odoo - Esempio 2 per tre colonne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 3 for three columns"
msgstr "Odoo - Esempio 3 per tre colonne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Versione Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Odoo provides essential platform for our project management. Things are "
"better organized and more visible with it."
msgstr ""
"Odoo fornisce una piattaforma fondamentale per la gestione dei nostri "
"progetti. Le informazioni sono più visibili e organizzate in modo migliore. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Odoo • A picture with a caption"
msgstr "Odoo • Un'immagine con didascalia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Odoo • Image and Text"
msgstr "Odoo • Immagine e testo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Odoo • Text and Image"
msgstr "Odoo • Testo e immagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "Fuori linea"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/editor_menu_translate.js:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/editor_menu_translate.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "Ok, non mostrare più"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "On Website"
msgstr "sul sito web"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Su invito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr ""
"Una volta selezionati i siti web disponibili per dominio, è possibile "
"filtrare per gruppo di nazioni. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "In linea"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"Viene impiegato solo se la vista eredita da un'altra vista (inherit_id non è falso/nullo).\n"
"\n"
"* Se estensione (predefinito), ed è richiesta la vista, viene cercata la\n"
"primaria più vicina (via inherit_id). Quindi vengono applicate, con il\n"
"suo modello, tutte quelle che ereditano dalla vista stessa\n"
"* Se primario, viene pienamente risolta la vista primaria più vicina (anche\n"
"se usa un modello diverso), quindi vengono applicate le specifiche\n"
"di ereditarietà della vista stessa (<xpath/>) usando il risultato come fosse\n"
"l'attuale architettura.\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "ERP open source"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr "Ottimizzazione SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr "Ottimizzazione SEO della pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Options"
msgstr "Opzioni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Ordina ora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Our Products &amp; Services"
msgstr "I nostri prodotti e servizi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "Our Team"
msgstr "Il nostro team"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid ""
"Our products are designed for small to medium size companies willing to optimize\n"
"                                                    their performance."
msgstr ""
"I nostri prodotti sono pensati per le piccole e medie aziende che vogliono ottimizzare\n"
"                                                    le loro prestazioni."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"Our products are designed for small to medium size companies willing to optimize\n"
"                                their performance."
msgstr ""
"I nostri prodotti sono pensati per le piccole e medie aziende che vogliono ottimizzare\n"
"                                le loro prestazioni."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr "Contorno"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "Pagina"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr "La pagina <b>%s</b> contiene un link a questa pagina"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr "La pagina <b>%s</b> richiama questo file"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Page Anchor"
msgstr "Ancoraggio pagina"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Page Indexed"
msgstr "Pagina indicizzata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Page Name"
msgstr "Nome pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr "Proprietà pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page Published"
msgstr "Pagina pubblicata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Page Title"
msgstr "Titolo pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__url
#, python-format
msgid "Page URL"
msgstr "URL pagina"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
msgid "Page Views"
msgstr "Visite alla pagina"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "Cronologia visite alla pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page views"
msgstr "Visite alla pagina"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Pages"
msgstr "Pagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr "Numerazione pagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"Paragrafo con testi in <strong>grassetto</strong>, <span class=\"text-"
"muted\">non enfatizzato</span> e <em>corsivo</em>"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "Menù padre"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "Percorso padre"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "Partner dell'ultimo utente che ha effettuato l'accesso."

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "Dati utente collegati al partner"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "Partner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr "Pillola"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Please enter valid facebook page URL for preview"
msgstr "Per l'anteprima inserire un URL di pagina Facebook valido "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
msgid "Prev"
msgstr "Prec"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Preview"
msgstr "Anteprima"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:0
#: code:addons/website/static/src/xml/website.gallery.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr "Precedente"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "Architettura vista precedente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Primary"
msgstr "Primario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Products Recently Viewed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Products Search"
msgstr "Ricerca prodotti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Products Search Input"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Professionista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "Profilo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "Promuovi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "Promuovi la pagina sul web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "Partner pubblico"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "Utente pubblico"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Publish"
msgstr "Pubblicazione"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "Pubblicata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr "Data di pubblicazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Purple"
msgstr "Viola"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "Qualità"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "QWeb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Campo QWeb contatto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_btn
msgid "Read more"
msgstr "Leggi di più"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "Regola su record"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Red"
msgstr "Rosso"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Redirect Old URL"
msgstr "Reindirizza vecchia URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "Tipo di reindirizzamento"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "Reindirizzamenti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "References"
msgstr "Referenze"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "Aggiorna elenco percorsi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "Normale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "Voci di menù collegate"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "Menù collegati"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "Pagina collegata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "Parole chiave collegate"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Remove"
msgstr "Rimuovi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Cover"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Rename Page To:"
msgstr "Rinomina pagina a:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Reset"
msgstr "Azzera"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "Ripristina modelli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "Ripristina versione iniziale (ripristino forte)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "Ripristina versione precedente (ripristino leggero)"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
msgid "Restrict publishing to this website."
msgstr "Limita la pubblicazione a questo sito web."

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr "Redattore con limitazioni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Review"
msgstr "Recensire"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "Riscrittura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Destra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "Menù destro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "Arrotondato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded corners"
msgstr "Angoli arrotondati"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "Rotta"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "Metadati SEO"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr "Ottimizzato SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "Icone di esempio"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/js/widgets/theme.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Save"
msgstr "Salva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "Ricerca"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Cerca nei menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "Reindirizzamento della ricerca"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "Ricerca visitatore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Ricerca..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Seconda funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "Secondo menù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "Secondo elenco di funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Secondary"
msgstr "Secondario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "Sottotitolo sezione"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Select a Menu"
msgstr "Seleziona un menù"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select a website to load its settings."
msgstr "Seleziona un sito web per caricare le sue impostazioni."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "Seleziona un'immagine da condividere nei social"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove some features."
msgstr "Seleziona e rimuovi blocchi per eliminare funzionalità."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "Seleziona un font da"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr "Selezionare il sito web da configurare"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Send Email"
msgstr "Invia e-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Send us an email"
msgstr "Inviaci una e-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr "Link separato"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
msgid "Server Action"
msgstr "Azione server"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "Impostazioni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "Le impostazioni di questa pagina verranno applicate a questo sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadow"
msgstr "Ombra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadows"
msgstr "Ombre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "Share"
msgstr "Condividi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Shipping"
msgstr "Trasmettere"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr ""
"Indica se gli utenti devono essere reindirizzati alla lingua del loro "
"browser"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__customize_show
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "Mostra come eredità opzionale"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Show Friend's Faces"
msgstr "Mostra il volto degli amici"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Show in Top Menu"
msgstr "Visualizzare nel menù principale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "Dimensione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "Scorrimento"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:0
#, python-format
msgid "Slide image"
msgstr "Immagine presentazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow"
msgstr "Presentazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slow"
msgstr "Lento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "Piccolo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small Caps"
msgstr "Maiuscoletto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "Social network"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "Anteprima social"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Solid"
msgstr "Continuo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr "Altre cose qui"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Name"
msgstr "Ordina per nome"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Url"
msgstr "Ordina per URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "Account utente specifico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr "Quadrato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "Inizia ora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Inizia con il cliente: scopri quello che cerca e dagli la soluzione."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "Inizia il viaggio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Status"
msgstr "Stato"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Stay on this page"
msgstr "Rimani nella pagina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr "La narrazione è potente.<br/> Attrae i lettori e li coinvolge."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Success"
msgstr "Successo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr "TRADUCI"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Tabs"
msgstr "Schede"

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
#: model:ir.model.fields,help:website.field_mrp_document__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr ""
"Campo tecnico usato per gestire allegati multipli in un ambiente multisito."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Nome tecnico:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "Indica qual'è il valore di questa <br/>funzionalità per il cliente."

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template"
msgstr "Modello"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr "Il modello <b>%s (id:%s)</b> contiene un link a questa pagina"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr "Il modello <b>%s (id:%s)</b> richiama questo file"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "Modello di riserva"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Templates"
msgstr "Modelli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "Termini di servizio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Text"
msgstr "Testo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "Allineamento testo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Size"
msgstr "Dimensione testo"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr ""
"L'ID cliente o la chiave Google Analytics inseriti non sembrano corretti."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "Il nome scelto esiste già"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name is not valid (use only a-Z A-Z 0-9 - _)"
msgstr "Il nome scelto non è valido (usare solo a-Z A-Z 0-9 - _)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "L'azienda a cui appartiene questo sito web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""
"Se non viene specificata una descrizione, verrà generata dai motori di "
"ricerca in base al contenuto della pagina."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""
"Se non viene specificata una descrizione, verrà generata dai social network "
"in base al contenuto della pagina."

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
msgid "The full URL to access the document through the website."
msgstr "URL completo per accedere al documento dal sito web."

#. module: website
#: model:ir.model.fields,help:website.field_base_automation__website_url
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr "URL completo per accedere all'azione server tramite il sito web."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "Un'applicazione è già in corso di installazione."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "La lingua della parola chiave e di quelle collegate."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr ""
"I modelli selezionati verranno ripristinati alle impostazioni iniziali."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr ""
"Se non viene specificato un titolo verrà assegnato un valore predefinito."

#. module: website
#: model:ir.model.fields,field_description:website.field_website__theme_id
msgid "Theme"
msgstr "Tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr "Al momento non sono presenti pagine per il sito web."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "There is no data currently available."
msgstr "Nessun dato attualmente disponibile."

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There is no email linked this visitor."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"There is no website available for this company. You could create a new one."
msgstr ""
"Non sono presenti siti web per l'azienda. È possibile crearne uno nuovo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Questi termini di servizio (\"Termini\", \"Accordo\") sono un accordo tra "
"Lei (\"Utente\", \"Lei\" o \"Suo\") e il sito web (\"Operatore sito web\", "
"\"noi\" o \"nostro\"). Il presente contratto stabilisce i termini e le "
"condizioni generali di utilizzo di questo sito web e di tutti i prodotti o "
"servizi presenti (collettivamente, \"Sito web\" o \"Servizi\")."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thin"
msgstr "Sottile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Terza funzionalità"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "Terzo menù"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr "Questo campo contiene l'immagine usata come favicon dal sito web."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "Questo campo è identico al campo \"arch\" senza traduzioni"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "Questo campo è usato per impostare/leggere fuso locale per utente"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"Questo campo dovrebbe essere usato quando si accede all'architettura della vista (view arch). Utilizzerà la traduzione.\n"
"Notare che leggerà `arch_db` oppure `arch_fs` se in modalità dev-xml."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "Il campo memorizza l'architettura della vista."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"Campo che salva l'attuale `arch_db` prima di effettuare la scrittura.\n"
"                                                                         Utile per il ripristino (leggero) di una vista danneggiata."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr "Questo è un avviso \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component <br/>for "
"calling extra attention to featured content or information."
msgstr ""
"Questa è una unità Hero, un semplice componente stile Jumbotron <br/> per la"
" presentazione di contenuti o informazioni in evidenza."

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "L'operatore non è supportato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are administrator of "
"this site."
msgstr ""
"Questa pagina non esiste, ma come amministratore del sito è possibile "
"crearla."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr "Questa pagina si trova nel menù <b>%s</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be visible on {{ date_formatted }}"
msgstr "Questa pagina sarà visibile il {{ date_formatted }}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Miniature"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "Tempo dall'ultima visualizzazione della pagina. Es. 2 minuti fa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Timeline"
msgstr "Timeline"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
msgid "Timezone"
msgstr "Fuso orario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tiny"
msgstr "Winzig"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#, python-format
msgid "Title"
msgstr "Titolo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Per aggiungere una quarta colonna, riduci la dimensione delle altre tre "
"usando l'icona a destra di ciascun blocco. Quindi duplica una delle colonne "
"per creare una nuova. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Per spedire gli inviti in modalità B2B, aprire un contatto o selezionarne "
"alcuni nella vista elenco, quindi fare clic sull'opzione \"Gestione accessi "
"al portale\" nel menù a tendina *Azione*."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr "Commuta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr "Commuta navigazione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, AD"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Top"
msgstr "In alto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Top Menu"
msgstr "Menù principale"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "Menù principale per il sito web %s"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "Numero totale di pagine monitorate visitate"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "Numero totale di visite alle pagine monitorate"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "Monitora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track clicks on UTM links"
msgstr "Monitora i clic sui link UTM"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/track_page.xml:0
#, python-format
msgid "Track visitor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr "Monitora le visite con Google Analitycs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "Monitorata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Tracking ID"
msgstr "ID monitoraggio"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""
"Trasforma l'immagine (fai doppio clic per resettare la trasformazione)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/wysiwyg_multizone_translate.js:0
#, python-format
msgid "Translate Attribute"
msgstr "Attributo traduzione"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "Contenuto tradotto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/editor_menu_translate.js:0
#, python-format
msgid "Translation Info"
msgstr "Informazioni traduzione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transparent"
msgstr "Trasparente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Trasforma le funzionalità in un servizio per il lettore."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Account Twitter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Barra di scorrimento Twitter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Type"
msgstr "Tipologia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"Digitare \"<i class=\"confirm_word\">sì</i>\" nella casella sottostante per "
"confermare."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"Tipo di reindirizzamento/riscrittura:\n"
"\n"
"        301 - Trasferimento permanente: il browser mantiene in cache il nuovo URL.\n"
"        302 - Trasferimento temporaneo: il browser non mantiene in cache il nuovo URL, che viene chiesto nuovamente la volta successiva.\n"
"        404 - Non trovato: serve a rimuovere una pagina/controller specifica (es. e-commerce installato ma /shop non visualizzato in un particolare sito web)\n"
"        308 - Reindirizzamento / Riscrittura: serve a rinominare un controller con un nuovo URL. (Es.: /shop → /garden - Entrambi gli URL sono accessibili ma /shop viene reindirizzato automaticamente a /garden)\n"
"    "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "UA-XXXXXXXX-Y"
msgstr "UA-XXXXXXXX-Y"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL da"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""
"Gli URL che corrispondono a questi filtri verranno riscritti usando l'URL "
"base della CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underlined"
msgstr "Sottolineato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr "Non indicizzato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "Potenza e supporto CRM illimitati"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Personalizzazione illimitata"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#, python-format
msgid "Unpublish"
msgstr "Non pubblicare"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr "Non pubblicata"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "URL e pagine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr "Usa Google Maps nel sito web ("

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#, python-format
msgid "Use Small Header"
msgstr "Usa un'intestazione piccola"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr ""
"Usa una CDN per ottimizzare la disponibilità dei contenuti del sito web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Use as Homepage"
msgstr "Usare come pagina principale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "Uso dei cookie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format.<br/> "
"Don't write about products or services here, write about solutions."
msgstr ""
"Usa questo snippet per esporre i contenuti in forma di presentazione.<br/> "
"Non scrivere di prodotti o servizi, scrivi di soluzioni."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "Usato nel contenuto della pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "Usato nella descrizione della pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "Usato nel titolo 1 della pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "Usato nel titolo 2 della pagina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "Usato nel titolo della pagina"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr "Usati quando più siti web possiedono lo stesso dominio."

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User need to be at least in one of these groups to see the menu"
msgstr ""
"Per vedere il menù l'utente deve far parte di almeno uno di questi gruppi"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "Utenti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr "Utilità e tipografia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Value"
msgstr "Valore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Fast"
msgstr "Molto veloce"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Slow"
msgstr "Molto lento"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "Visualizza"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "Visualizza architettura"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "Nome vista"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "Tipo vista"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "Modo ereditarietà della vista"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
msgid "Views"
msgstr "Viste"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "Viste che ereditano da questa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Visible"
msgstr "Visibile"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr "Gruppi visibili"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
msgid "Visible on current website"
msgstr "Visibile nel sito web corrente"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "Data visita"

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "Pagine visitate"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "Cronologia pagine visitate"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "Visitatore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Visitor Informations"
msgstr "Informazioni visitatore"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "Visualizzazioni pagina visitatori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "Cronologia visualizzazioni pagina visitatori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "Visualizzazioni visitatori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "Cronologia visualizzazioni visitatori"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.menu_visitor_sub_menu
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Visitors"
msgstr "Visitatori"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#, python-format
msgid "Visits"
msgstr "Visite"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr ""
"Aspetta che i visitatori vengano sul tuo sito per vedere le pagine che hanno"
" visto."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "Wait for visitors to come to your website to see their history."
msgstr ""
"Attendi l'arrivo dei visitatori nel sito web per vedere la loro cronologia."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:0
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#, python-format
msgid "Warning"
msgstr "Attenzione"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                                    life through disruptive products. We build great products to solve your\n"
"                                                    business problems."
msgstr ""
"Siamo un team di entusiasti con l'obiettivo di migliorare la vita di tutti grazie\n"
"                                                    a soluzioni rivoluzionarie. Sviluppiamo prodotti eccellenti per risolvere\n"
"                                                    i problemi della tua azienda."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                life through disruptive products. We build great products to solve your\n"
"                                business problems."
msgstr ""
"Siamo un team di entusiasti con l'obiettivo di migliorare la vita di tutti grazie\n"
"                                      a soluzioni rivoluzionarie. Sviluppiamo prodotti eccellenti per risolvere\n"
"                                      i problemi della tua azienda."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "We found these ones:"
msgstr "Sono stati trovati i seguenti elementi:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "Visitatori web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_mrp_document__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#, python-format
msgid "Website"
msgstr "Sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "Azienda sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "Dominio sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "Icona sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
msgid "Website Logo"
msgstr "Logo sito web"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Menù sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "Impostazioni menù del sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "Nome sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "Pagina sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "Impostazioni pagina sito web"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "Pagine sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__website_path
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "Percorso sito web"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "Mixin siti web pubblicati"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr "Impostazioni sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr "Titolo sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_mrp_document__website_url
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
msgid "Website URL"
msgstr "URL del sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__website_url
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "URL sito web"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "Visitatore sito web"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "Visitatore sito web n. %s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
#: model:ir.cron,name:website.website_visitor_cron
msgid "Website Visitor : Archive old visitors"
msgstr "Visitatore sito web: archiviazione vecchi visitatori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Il sito web può usare cookie per personalizzare e agevolarne al massimo la "
"navigazione. L'utente può configurare il proprio browser per notificare e "
"rifiutare l'installazione dei cookie che vengono inviati. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Menù sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "Meta descrizione sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta parole chiave sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "Meta titolo sito web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "Immagine Open Graph sito web"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "Riscrittura per sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "Impostazioni riscrittura per sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "Riscritture per sito web"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Dashboard"
msgstr "Sito web: bacheca"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Siti web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "Siti web da tradurre"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Welcome to your"
msgstr "Benvenuto nella"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "What do you want to do?"
msgstr "Cosa vuoi fare?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "White"
msgstr "Bianco"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "Will be prefixed by http in canonical URLs if no scheme is specified"
msgstr ""
"Se non viene indicato alcuno schema, agli URL canonici viene aggiunto il "
"prefisso http"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Scrivi una citazione proveniente da un cliente. Le citazioni sono un ottimo "
"modo per costruire la fiducia nei prodotti o nei servizi offerti."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. <br/>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Scrivi uno o due paragrafi che descrivono il prodotto o i servizi offerti. "
"<br/>Per essere efficace, il contenuto deve essere utile ai lettori. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Scrivi uno o due paragrafi che descrivono il prodotto, i servizi o una "
"particolare funzionalità offerta. <br/>Per essere efficace, il contenuto "
"deve essere utile ai lettori. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr ""
"Scrivi ciò che il cliente vorrebbe sapere, <br/>non ciò che vuoi mostrare."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Yellow"
msgstr "Giallo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Accesso alla modalità traduzione."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and background to highlight features."
msgstr "Per evidenziare le funzionalità puoi modificare i colori e lo sfondo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr ""
"È possibile avere 2 siti web con lo stesso dominio E una condizione sul "
"gruppo di nazioni per selezionare il sito web da usare. "

#. module: website
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "Non puoi avere due utenti con lo stesso login!"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr "Diritti non sufficienti per eseguire questa azione."

#. module: website
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "Non hai i diritti di pubblicare/rimuovere la pubblicazione"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr "Sembra che tu non abbia accesso a questo account Analytics."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""
"Questa pagina è stata nascosta dai risultati di ricerca. Non verrà "
"indicizzata dai motori di ricerca. "

#. module: website
#: code:addons/website/models/website.py:162
#, python-format
msgid "You must keep at least one website."
msgstr "Deve essere conservato almeno un sito web."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr "Prima è necessario accedere al proprio account Google:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"È necessario esaminare attentamente le dichiarazioni legali e le altre "
"condizioni d'uso dei siti web ai quali si accede attraverso link presenti in"
" questo. I collegamenti personali a pagine esterne o ad altri siti web sono "
"a proprio rischio e pericolo. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Client ID:"
msgstr "ID cliente:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Tracking ID:"
msgstr "ID monitoraggio:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Your current changes will be saved automatically."
msgstr "Le modifiche correnti verranno salvate automaticamente."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "La descrizione risulta troppo lunga."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "La descrizione risulta troppo breve."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Your search '"
msgstr "La ricerca \""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Account YouTube"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the embed code here."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "breadcrumb"
msgstr "percorso di navigazione"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "e.g. About Us"
msgstr "es. Chi siamo"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr "Bacheca e-commerce"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "found(s)"
msgstr "trovato/i"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "sito web gratuito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "istanza di Odoo, il"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr "link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "name"
msgstr "nome"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "o Modifica originale"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr ", snippet, ...)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "px"
msgstr "px"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "signs to get your website ready in no time."
msgstr "per configurare rapidamente il tuo sito web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "url"
msgstr "url"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "website"
msgstr "sito web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "sì"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "— Jane DOE, CEO of <b>MyCompany</b>"
msgstr "— Jane DOE - Amministratore delegato di <b>MyCompany</b>"
