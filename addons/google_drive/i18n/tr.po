# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_drive
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# nle_odoo, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:12+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"<b>To create a new filter:</b><br/>\n"
"                                - Go to the Odoo document you want to filter. For instance, go to Opportunities and search on Sales Department.<br/>\n"
"                                - In this \"Search\" view, select the option \"Save Current Filter\", enter the name (Ex: Sales Department)<br/>\n"
"                                - If you select \"Share with all users\", link of google document in \"More\" options will appear for all users in opportunities of Sales Department.<br/>\n"
"                                - If you don't select \"Share with all users\", link of google document in \"More\" options will not appear for other users in opportunities of Sales Department.<br/>\n"
"                                - If filter is not specified, link of google document will appear in \"More\" option for all users for all opportunities."
msgstr ""
"<b>yeni bir süzgeç oluşturmak için:</b><br/>\n"
"                                - Süzgeç oluşturmak istediğiniz Odoo belgesine gidin. Örneğin, Fırsatlara gidin ve Satış Bölümünde arayın.<br/>\n"
"                                - Bu \"Arama\" görünümünde \"Geçerli Süzgeçi Kaydet\"i seçin, adını girin (Örn: Satış Bölümü)<br/>\n"
"                                - Eğer \"Bütün kullanıcılarla paylaş\"ı seçerseniz, \"Fazlası\" seçeneğinde google belgeleri bağlantısı Satış Bölümündeki fırsatlardaki tüm kullanıcılarda görünecektir.<br/>\n"
"                                - Eğer \"Bütün kullanıcılarla paylaş\"ı seçmezseniz, \"Fazlası\" seçeneğinde google belgeleri bağlantısı fırsatlardaki tüm kullanıcılarda görünmeyecektir.<br/>\n"
"                                - Eğer süzgeç belirtilmemişse,  \"Fazlası\" seçeneğinde google belgeleri bağlantısı fırsatlardaki tüm kullanıcılarda görünecektir."

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Reset token"
msgstr "<i class=\"fa fa-arrow-right\"/> Token sıfırlama"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Set up token"
msgstr "<i class=\"fa fa-arrow-right\"/> Token kurulum"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; No refresh"
" token set"
msgstr ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; Yenileme "
"tokenı ayarlanmadı"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"text-success fa fa-check\"/> &amp;nbsp; Refresh token set"
msgstr ""
"<i class=\"text-success fa fa-check\"/> &amp;nbsp; Token ayarlamayı yenile"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "<span>Get an authorization code and set it in the field below.</span>"
msgstr ""
"<span>Bir yetkilendirme kodu alın ve kodu aşağıdaki alana ayarlayın.</span>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>Etkin</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Model</strong>"
msgstr "<strong>Model</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Template</strong>"
msgstr "<strong>Şablon</strong>"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__active
msgid "Active"
msgstr "Etkin"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid "Add a new template"
msgstr "Yeni bir şablon ekleyin"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "At least one key cannot be found in your Google Drive name pattern."
msgstr "Google Drive isim örneğinizde, en az bir anahtar bulunamadı."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_authorization_code
msgid "Authorization Code"
msgstr "Yetki Kodu"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Cancel"
msgstr "İptal"

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_google_drive_config__name_template
msgid ""
"Choose how the new google drive will be named, on google side. Eg. "
"gdoc_%(field_name)s"
msgstr ""
"Yeni google sürücü, google tarafında adlandırılacağını seçin. Örneğin. gdoc_"
" %(field_name)s"

#. module: google_drive
#: model:ir.model,name:google_drive.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigürasyon Ayarları"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Confirm"
msgstr "Doğrula"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Creating google drive may only be done by one at a time."
msgstr "Google sürücü oluşturma yalnızca tek tek tarafından yapılabilir."

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:161
#, python-format
msgid ""
"The document filter must not include any 'dynamic' part, so it should not be"
" based on the current time or current user, for example."
msgstr ""
"Belge filtresi herhangi bir 'dinamik' kısım içermemelidir,  örneğin mevcut "
"saati veya mevcut kullanıcıyı temel almamalıdır."

#. module: google_drive
#: model:ir.filters,name:google_drive.filter_partner
msgid "Customer"
msgstr "Müşteri"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__filter_id
msgid "Filter"
msgstr "Filtre"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Yapılandırma paneline git"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_client_id
msgid "Google Client"
msgstr "Google Kullanıcı"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_tree
msgid "Google Drive Configuration"
msgstr "Google Drive Konfigürasyonu"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name_template
msgid "Google Drive Name Pattern"
msgstr "Google Drive Adı"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "Google Drive Templates"
msgstr "Google Drive Şablonları"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Google Drive is not yet configured. Please contact your administrator."
msgstr "Google sürücü henüz yapılandırılmadı. Lütfen yöneticinize başvurun."

#. module: google_drive
#: model:ir.model,name:google_drive.model_google_drive_config
msgid "Google Drive templates config"
msgstr "Google Drive Şablon Ayarı"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__id
msgid "ID"
msgstr "ID"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid ""
"Link your own google drive templates to any record of Odoo. If you have "
"really specific documents you want your collaborator fill in, e.g. Use a "
"spreadsheet to control the quality of your product or review the delivery "
"checklist for each order in a foreign country, ... Its very easy to manage "
"them, link them to Odoo and use them to collaborate with your employees."
msgstr ""
"Kendi google drive şablonlarınızı herhangi bir Odoo kaydına bağlantılayın. "
"Örn. gerçekten işbirliği yaptığınız kimsenin doldurmasını istediğiniz özel "
"belgeler varsa. Ürününüzün kalitesini kontrol etmek için bir çalışma tablosu"
" kullanın veya yabancı bir ülkeye olan her sipariş için teslimat denetim "
"listesini inceleyin, ... Onları yönetmek çok kolaydır, Odoo'ya bağlantılayın"
" ve çalışanlarınızla işbirliği için kullanın."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model_id
msgid "Model"
msgstr "Model"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Please enter a valid Google Document URL."
msgstr "Lütfen geçerli bir Google belge URL'si girin."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__is_google_drive_token_generated
msgid "Refresh Token Generated"
msgstr "Oluşturulan Tokenı Yenile"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model
msgid "Related Model"
msgstr "İlişkili Model"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_resource_id
msgid "Resource Id"
msgstr "Kaynak ID"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Search Google Drive Config"
msgstr "Google Drive Yapılandırmasında Ara"

#. module: google_drive
#: code:addons/google_drive/models/res_config_settings.py:0
#, python-format
msgid "Set up refresh token"
msgstr "Yenileme belirteci ayarla"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"Something went wrong during the token generation. Please request again an "
"authorization code ."
msgstr ""
"Simge oluşturma sırasında bir şeyler ters gitti. Lütfen tekrar bir "
"yetkilendirme kodu isteyin."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name
msgid "Template Name"
msgstr "Şablon Adı"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_template_url
msgid "Template URL"
msgstr "Şablon"

#. module: google_drive
#: model:ir.actions.act_window,name:google_drive.action_google_drive_users_config
msgid "Templates"
msgstr "Şablonlar"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "The Google Template cannot be found. Maybe it has been deleted."
msgstr "Google şablonu bulunamıyor. Belki silindi."

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_res_config_settings__google_drive_uri
msgid "The URL to generate the authorization code from Google"
msgstr "Google'dan yetkilendirme kodu oluşturmak için URL"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"The name of the attached document can use fixed or variable data. To distinguish between documents in\n"
"                                Google Drive, use fixed words and fields. For instance, in the example above, if you wrote Deco_Addict_%(name)s_Sales\n"
"                                in the Google Drive name field, the document in your Google Drive and in Odoo attachment will be named\n"
"                                'Deco_Addict_SO0001_Sales'."
msgstr ""
"Eklenen dosyanın adı sabit ya da değişken bir data kullanabilir. Google drive'da verileri birbirinden ayırmak için, sabit kelimeler ve alanlar kullanın.Örnekte olduğu gibi eğer Google drivedaki dokumanın adına  Deco_Addict_%(name)s_Sales yazarsanız\n"
"                                          'Deco_Addict_SO0001_Sales' olarak kaydedilecektir."

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"The permission 'reader' for 'anyone with the link' has not been written on "
"the document"
msgstr ""
"İzin 'herkes' ile belgili tanımlık bağlantı için ' okuyucu' belge üzerinde "
"yazılı değil"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"There is no refresh code set for Google Drive. You can set it up from the "
"configuration panel."
msgstr ""
"Google Drive için ayarlanmış bir yenileme kodu yok. Yapılandırma panelinden "
"ayarlayabilirsiniz."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_uri
msgid "URI"
msgstr "URI"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
msgstr ""
"https://docs.Google.com/Document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/Edit"
