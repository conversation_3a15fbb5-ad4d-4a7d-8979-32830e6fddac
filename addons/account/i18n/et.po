# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account
# 
# Translators:
# <PERSON><PERSON>ru <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <He<PERSON>.<PERSON>@gmail.com>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON>dre<PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# Algo <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Anna, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-01 12:20+0000\n"
"PO-Revision-Date: 2019-08-26 09:06+0000\n"
"Last-Translator: Anna, 2023\n"
"Language-Team: Estonian (https://www.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * 10% = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__code_digits
msgid "# of Digits"
msgstr "Numbrite arv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__number
msgid "#Coins/Bills"
msgstr "#Mündid/Kupüürid"

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr "${(object.name or '').replace('/','-')}"

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.name or 'n/a'})"
msgstr "${object.company_id.name} Arve (Ref ${object.name or 'n/a'})"

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} Maksekinnitus (Viide ${object.number or 'n/a'})"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "%s (Copy)"
msgstr "%s (koopia)"

#. module: account
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (koopia)"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "%s (rounding)"
msgstr "%s (ümardamine)"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "%s Sequence"
msgstr "%s Järjestus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr "&amp;nbsp;<span>on</span>"

#. module: account
#: model:ir.actions.report,print_report_name:account.account_invoices
#: model:ir.actions.report,print_report_name:account.account_invoices_without_payment
msgid "(object._get_report_base_filename())"
msgstr "(object._get_report_base_filename())"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr "-> Sobita"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr "-> Vaata osaliselt sobitatud kirjeid"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr "15 päeva"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_2months
msgid "2 Months"
msgstr "2 kuud"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_21days
msgid "21 Days"
msgstr "21 Päeva"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_30days
msgid "30 Days"
msgstr "30 Päeva"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr "30% ettemaks järgmise kuu lõpuks"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance_60days
msgid "30% Now, Balance 60 Days"
msgstr "30% kohe, jääksumma 60 päeva pärast"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_45days
msgid "45 Days"
msgstr "45 Päeva"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ": Refund"
msgstr ": Tagasimakse"

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name}<br/><br/>\n"
"        Thank you for your payment.\n"
"        Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting\n"
"        to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/><br/>\n"
"        Best regards,<br/>\n"
"        % if user and user.signature:\n"
"        ${user.signature | safe}\n"
"        % endif\n"
"    </p>\n"
"</div>\n"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear\n"
"        % if object.partner_id.parent_id:\n"
"            ${object.partner_id.name} (${object.partner_id.parent_id.name}),\n"
"        % else:\n"
"            ${object.partner_id.name},\n"
"        % endif\n"
"        <br/><br/>\n"
"        Here is your\n"
"        % if object.name:\n"
"            invoice <strong>${object.name}</strong>\n"
"        % else:\n"
"            invoice\n"
"        %endif\n"
"        % if object.invoice_origin:\n"
"            (with reference: ${object.invoice_origin})\n"
"        % endif\n"
"        amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"        from ${object.company_id.name}.\n"
"        % if object.invoice_payment_state == 'paid':\n"
"            This invoice is already paid.\n"
"        % else:\n"
"            Please remit payment at your earliest convenience.\n"
"        % endif\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
msgid "<em>Draft Invoice</em>"
msgstr "<em>Arve mustand</em>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_move_line_view_kanban
#: model_terms:ir.ui.view,arch_db:account.view_account_move_kanban
msgid "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "lae alla"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-fw fa-comments\"/><b>Send message</b>"
msgstr "<i class=\"fa fa-fw fa-comments\"/><b>Saada sõnum</b>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/> Configure Email Servers"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/> Seadista e-posti "
"serverid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Trüki"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid ""
"<span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'out_invoice'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Invoice</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'out_refund'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Credit Note</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'in_invoice'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Bill</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'in_refund'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Refund</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'out_receipt'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Sales Receipt</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'in_receipt'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Purchase Receipt</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                        <strong>Email mass mailing</strong> on\n"
"                                        <span>the selected records</span>\n"
"                                    </span>\n"
"                                    <span>Followers of the document and</span>"
msgstr ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"   <strong>Saada massimeil</strong>\n"
"<span>valitud kirjetele</span>\n"
"</span>\n"
"<span>Dokumendiga kaasnevad ning</span>\n"
" "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid ""
"<span attrs=\"{'invisible':[('reconciled_invoices_count','&gt;',1)]}\">Invoice</span>\n"
"                                <span attrs=\"{'invisible':[('reconciled_invoices_count','&lt;=',1)]}\">Invoices</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-clock-o\""
" aria-label=\"Opened\" title=\"Opened\" role=\"img\"/><span class=\"d-none "
"d-md-inline\"> Waiting for Payment</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-clock-o\""
" aria-label=\"Opened\" title=\"Opened\" role=\"img\"/><span class=\"d-none "
"d-md-inline\"> Makse ootel</span></span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\" aria-label=\"Paid\" title=\"Paid\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Paid</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\" aria-label=\"Paid\" title=\"Paid\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Makstud</span></span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-warning\"><i class=\"fa fa-fw fa-"
"remove\" aria-label=\"Cancelled\" title=\"Cancelled\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Cancelled</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-warning\"><i class=\"fa fa-fw fa-"
"remove\" aria-label=\"Cancelled\" title=\"Cancelled\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Tühistatud</span></span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid ""
"<span class=\"badge badge-warning text-uppercase "
"o_sample_data_label\">Sample data</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid ""
"<span class=\"o_form_label mx-3 oe_edit_only\" attrs=\"{'invisible': [ '|', "
"'|', '|', ('state', '!=', 'draft'), ('invoice_payment_term_id', '!=', "
"False), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', "
"'in_refund', 'out_receipt', 'in_receipt'))]}\"> or </span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('invoice_sequence_number_next_prefix', '=', False)]}\">First "
"Number:</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('match_amount', '!=', "
"'between')]}\">and</span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('match_amount', '!=', "
"'between')]}\">ja</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"<span class=\"o_form_label\">All selected journal entries will be validated "
"and posted. You won't be able to modify them afterwards.</span>"
msgstr ""
"<span class=\"o_form_label\"> Kõik valitud andmike kanded kinnitatakse ja "
"kajastatakse. Neid ei saa enam tagantjärgi muuta. </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default Incoterm</span>"
msgstr "<span class=\"o_form_label\">Vaikimisi tarnetingimused</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default Sending Options</span>"
msgstr "<span class=\"o_form_label\">Vaikimisi saatmise valikud</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Default Taxes</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">Vaikimisi seadistatud maksud</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Localization</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">Maksukeskkond</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Main Currency</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">Põhivaluuta</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Rounding Method</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">Ümardamise meetod</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "<span class=\"o_form_label\">of the month</span>"
msgstr "<span class=\"o_form_label\">kuust</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr "<span class=\"o_stat_text\">Arveldatud</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid ""
"<span class=\"text-muted\">Only journals not yet linked to a bank account "
"are proposed</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                    <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Summa</span>\n"
"                                    <span groups=\"account.group_show_line_subtotals_tax_included\">Kogusumma</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span name=\"button_import_placeholder\"/> Statements"
msgstr "<span name=\"button_import_placeholder\"/> väljavõtted"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">New</span>"
msgstr "<span role=\"separator\">Uus</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr "<span role=\"separator\">Sobitamine</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">View</span>"
msgstr "<span role=\"separator\">Vaade</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in General Ledger\">Balance in GL</span>"
msgstr "<span title=\"Bilanss Odoos\">Pearaamatu saldo</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Viimane väljavõte</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr "<span> Lähtekoht </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr "<span> Kuni </span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Amount Paid</span>"
msgstr "<span>Kogus makstud</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Balance</span>"
msgstr "<span>Saldo</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Description</span>"
msgstr "<span>Kirjeldus</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Disc.%</span>"
msgstr "<span>Allah.%</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Date</span>"
msgstr "Arve kuupäev"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Number</span>"
msgstr "<span>Arve number</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr "<span>Loo ostuarve</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Entry</span>"
msgstr "Uus kanne"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr "<span>Loo arve</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr "<span>Uus</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr "<span>Toimingud</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Original Amount</span>"
msgstr "<span>Esialgne summa</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>Kogus</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Reference</span>"
msgstr "<span>Viide</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr "<span>Aruandlus</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Source Document</span>"
msgstr "<span>Alusdokument</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Taxes</span>"
msgstr "<span>Maksud</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Unit Price</span>"
msgstr "<span>Ühikhind</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Upload Bills</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Upload Invoices</span>"
msgstr "Arvete üleslaadimine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr "<span>Vaata</span>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Vahesumma</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">Scan me with your banking "
"app.</strong><br/><br/>"
msgstr ""
"<strong class=\"text-center\">Skaneeri mind oma panga "
"rakendusega.</strong><br/><br/>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">The SEPA QR Code informations are not set "
"correctly.</strong><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr "<strong class=\"text-muted\">Müügiesindaja</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr "<strong>Summa üle tähtaja</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Company:</strong>"
msgstr "<strong>Ettevõte:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Credit Note Date:</strong>"
msgstr "<strong>Kreeditarve kuupäev:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr "<strong>Kliendikood:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Customer: </strong>"
msgstr "<strong>Klient: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Date:</strong>"
msgstr "<strong>Kuupäev:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Maksetähtaeg:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_statement
msgid "<strong>Ending Balance</strong>"
msgstr "<strong>Lõppsaldo</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr "<strong>Kanded sorteeritud:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Arve kuupäev:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr "<strong>Andmik:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Memo: </strong>"
msgstr "<strong>Märge: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Amount: </strong>"
msgstr "<strong>Makse summa: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Date: </strong>"
msgstr "<strong>Makse kuupäev: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Method: </strong>"
msgstr "<strong>Maksemeetod: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr "<strong>Viide:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr "<strong>Allikas:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_statement
msgid "<strong>Starting Balance</strong>"
msgstr "<strong>Algne saldo</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Vahekokkuvõte</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Target Moves:</strong>"
msgstr "<strong>Sihitud liikumised:</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr "<strong>Kokku</strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr "<strong>Liik: </strong>"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Vendor: </strong>"
msgstr "<strong>Tarnija: </strong>"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""
"Kassa võimaldab hallata sularaha kirjeid kassa andmikes.\n"
"                See võimaldab jälgida igapäevaseid sularaha\n"
"                tehiguid ja kajastada sularaha sissetulekuid ja väljaminekuid."

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "A Payment Term should have only one line of type Balance."
msgstr "Maksetingimus peaks sisaldama ainult ühte \"Saldo\" tüüpi rida."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "A bank account can belong to only one journal."
msgstr "Pangakonto saab kuuluda ainult ühele andmikule."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""
"Pangaväljavõte on  kindlal ajavahemikul pangakontol toimunud\n"
"                rahaliste tehingute kokkuvõte. See tuleks pangast\n"
"                võtta kindla ajavahemiku tagant."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account"
msgstr "Pangaväljavõtte rida on rahaline tehing pangakontol. "

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""
"Andmiku kanne sisaldab mitut kanderida, mis võivad olla kas deebet- või "
"kreedit-tehingud."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""
"Andmikku kasutatakse igapäevaste raamatupidamistehingute kajastamiseks."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"A payment journal entry generated in a journal configured to post entries "
"only when payments are reconciled with a bank statement cannot be manually "
"posted. Those will be posted automatically after performing the bank "
"reconciliation."
msgstr ""

#. module: account
#: code:addons/account/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Sobitamine peab sisaldama vähemalt 2 kande rida."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""
"Ümardamine rea kaupa on soovituslik, kui hinnad sisaldavad maksu. Sellisel "
"juhul ridade summa on võrdne kogusummaga."

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Valitud kande rida on juba sobitatud."

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr "Valitud väljavõtte rida on juba sobitatud konto kandega."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "A tag defined to be used on taxes must always have a country set."
msgstr "Maksude märge peab alati olema defineeritud riiklikult."

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_fiscal_position_tax_tax_src_dest_uniq
msgid "A tax fiscal position could be defined only one time on same taxes."
msgstr "Ühele maksule saab määrata ainult ühe finantspositsiooni."

#. module: account
#: code:addons/account/models/res_users.py:0
#, python-format
msgid ""
"A user cannot have both Tax B2B and Tax B2C.\n"
"You should go in General Settings, and choose to display Product Prices\n"
"either in 'Tax-Included' or in 'Tax-Excluded' mode\n"
"(or switch twice the mode if you are already in the desired one)."
msgstr ""
"Kasutaja ei saa omada samaaegselt Tax B2B ja Tax B2C.\n"
"Mine üldsätetesse ja vali kuvamiseks Toote Hinnad\n"
"kas 'koos maksuga' või 'ilma maksuta' kuju\n"
"(või lülita 2x kuju kui oled juba soovitud kujul)."

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr "Partnerile (või kontole) saab määrata hoiatuse."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__access_warning
msgid "Access warning"
msgstr "Juurdepääsu hoiatus"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__account_id
#: model:ir.model.fields,field_description:account.field_account_move_line__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__account_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__account_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__account_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr "Konto"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Account %s (%s) does not allow reconciliation. First change the "
"configuration of this account to allow it."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Konto sulara ümardus"

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Konto tabeli mall"

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr "Konto väljavõte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template__currency_id
msgid "Account Currency"
msgstr "Konto valuuta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_dest_id
msgid "Account Destination"
msgstr "Konto sihtpunkt"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr "Konto kanne"

#. module: account
#: model:ir.model,name:account.model_account_group
#: model_terms:ir.ui.view,arch_db:account.view_account_group_form
#: model_terms:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr "Konto grupp"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr "Kontogrupid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__company_partner_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__partner_id
msgid "Account Holder"
msgstr "Konto valdaja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_holder_name
msgid "Account Holder Name"
msgstr "Konto valdaja nimi"

#. module: account
#: model:ir.model,name:account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr "Konto Arve Saatmine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank__journal_id
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr "Konto andmik"

#. module: account
#: model:ir.model,name:account.model_account_journal_group
msgid "Account Journal Group"
msgstr "Konto andmiku grupp"

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "Account Journal Report"
msgstr "Konto andmiku aruanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__account_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr "Konto kaardistamine"

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Konto kande tagasipööramine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_acc_number
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_number
msgid "Account Number"
msgstr "Konto number"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_payable_id
msgid "Account Payable"
msgstr "Tasumata kohustused"

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr "Konto trükiandmik"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr "Konto seaded"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_receivable_id
msgid "Account Receivable"
msgstr "Laekumata nõuded"

#. module: account
#: model:ir.model,name:account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Konto vastavusse viimise vidin"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__account_root_id
msgid "Account Root"
msgstr "Konto Põhi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_src_id
msgid "Account Source"
msgstr "Konto allikas"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model_terms:ir.ui.view,arch_db:account.account_move_line_graph_date
msgid "Account Statistics"
msgstr "Konto statistika"

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr "Konto silt"

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr "Kontode sildid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_tree
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr "Konto käibemaks"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_tax_group_tree
msgid "Account Tax Group"
msgstr "Konto Maksude Grupp"

#. module: account
#: model:ir.model,name:account.model_account_tax_report_line
msgid "Account Tax Report Line"
msgstr "Konto Makseraporti Viis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr "Konto maksumall"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_taxcloud
msgid "Account TaxCloud"
msgstr "TaxCloud konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr "Konto mall"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr "Konto mall varude hindamiseks"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr "Kontomallid"

#. module: account
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type__name
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__account_type
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__related_acc_type
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_account_type_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr "Kontotüüp"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Konto tüüpi kasutatakse informatiivsena, et luua riigipõhiseid põhiaruandeid"
" ja panna paika reeglid majandusaasta sulgemiseks ja algsaldode "
"sisestamiseks."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
msgid "Account Types"
msgstr "Kontotüübid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__type_control_ids
msgid "Account Types Allowed"
msgstr "Lubatud kontotüübid"

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr "Kontot lahti ühendama"

#. module: account
#: model:ir.model,name:account.model_account_root
msgid "Account codes first 2 digits"
msgstr "Konto koodi esimesed 2 nubrikohta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr "Konto grupp"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr "Konto grupid"

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_holder_name
msgid ""
"Account holder name, in case it is different than the name of the Account "
"Holder"
msgstr "Konto omaniku nimi, juhuks kui see erineb Konto Omanikust"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_src_id
msgid "Account on Product"
msgstr "Konto tootel"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__account_id
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__account_id
msgid "Account on which to post the tax amount"
msgstr "Konto millele konteerida maksu suurus"

#. module: account
#: model:ir.model,name:account.model_report_account_report_invoice_with_payments
msgid "Account report with payment lines"
msgstr "Konto raport koos maksete joontega"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__tag_ids
msgid "Account tag"
msgstr "Konto silt"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_dest_id
msgid "Account to Use Instead"
msgstr "Asenduskonto"

#. module: account
#: model:ir.model.fields,help:account.field_account_accrual_accounting_wizard__revenue_accrual_account
#: model:ir.model.fields,help:account.field_res_company__revenue_accrual_account_id
msgid "Account used to move the period of a revenue"
msgstr "Konto tulude perioodi muutmiseks"

#. module: account
#: model:ir.model.fields,help:account.field_account_accrual_accounting_wizard__expense_accrual_account
#: model:ir.model.fields,help:account.field_res_company__expense_accrual_account_id
msgid "Account used to move the period of an expense"
msgstr "Konto kulude perioodi muutmiseks"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_transition_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_transition_account_id
msgid ""
"Account used to transition the tax amount for cash basis taxes. It will "
"contain the tax amount as long as the original invoice has not been "
"reconciled ; at reconciliation, this amount cancelled on this account and "
"put on the regular tax account."
msgstr ""
"Konto maksude summa siirdamiseks kassapõhistele maksudele. Sisaldab maksu "
"summat seni kuni algset arvet pole kooskõlastatud; viimasel juhul makse määr"
" tühistatakse antud kontolt ja lisatakse tavalise maksu kontole."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Account with Entries"
msgstr "Konto koos Kirjetega"

#. module: account
#: model:ir.actions.server,name:account.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account.ir_cron_auto_post_draft_entry
msgid "Account; Post draft entries with auto_post set to True up to today"
msgstr ""
"Konto; Hilisemate mustandite sisestused koos auto_post set to True kuni "
"käesoleva hetkeni"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_accountant
#: model:ir.ui.menu,name:account.account_account_menu
#: model:ir.ui.menu,name:account.menu_finance_entries
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_form_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Accounting"
msgstr "Raamatupidamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__accounting_date
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Accounting Date"
msgstr "Raamatupidamiskande kuupäev"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr "Raamatupidamisdokumendid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr "Raamatupidamiskanded"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Overview"
msgstr "Raamatupidamise ülevaade"

#. module: account
#: code:addons/account/models/company.py:0
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#, python-format
msgid "Accounting Periods"
msgstr "Raamatupidamise perioodid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr "Raamatupidamisega seotud seadistusi hallatakse"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_tag__applicability__accounts
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr "Kontod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__account_control_ids
msgid "Accounts Allowed"
msgstr "Lubatud kontod"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr "Kontode kaardistamine"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Accounts Mapping Template of Fiscal Position"
msgstr "Eelarvepositsiooni kontode kaardistamise mall"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Mapping of Fiscal Position"
msgstr "Eelarvepositsiooni kontode kardistamine"

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid ""
"Accrual Adjusting Entries ({percent}%% recognized) have been created for "
"this invoice on {date}"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Accrual Adjusting Entry (%s%% recognized) for invoice: %s"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Accrual Adjusting Entry ({percent}% recognized) for invoice:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__journal_id
#: model:ir.model.fields,field_description:account.field_res_company__accrual_default_journal_id
msgid "Accrual Default Journal"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Accrued Account"
msgstr "Viitkonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,field_description:account.field_account_journal__message_needaction
#: model:ir.model.fields,field_description:account.field_account_move__message_needaction
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr "Tegevused"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr "Aktiveeri teisi valuutasid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__active
#: model:ir.model.fields,field_description:account.field_account_incoterms__active
#: model:ir.model.fields,field_description:account.field_account_journal__active
#: model:ir.model.fields,field_description:account.field_account_payment_term__active
#: model:ir.model.fields,field_description:account.field_account_tax__active
#: model:ir.model.fields,field_description:account.field_account_tax_template__active
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr "Aktiivne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__active_move_line_ids
msgid "Active Move Line"
msgstr "Aktiivne siirde rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__active_domain
msgid "Active domain"
msgstr "Aktiivne domeen"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_ids
#: model:ir.model.fields,field_description:account.field_account_move__activity_ids
#: model:ir.model.fields,field_description:account.field_account_payment__activity_ids
msgid "Activities"
msgstr "Tegevused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_exception_decoration
#: model:ir.model.fields,field_description:account.field_account_move__activity_exception_decoration
#: model:ir.model.fields,field_description:account.field_account_payment__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandlik kohendus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_state
#: model:ir.model.fields,field_description:account.field_account_move__activity_state
#: model:ir.model.fields,field_description:account.field_account_payment__activity_state
msgid "Activity State"
msgstr "Tegevuse staatus"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Add"
msgstr "Lisa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add Credit Note"
msgstr "Loo kreeditarve"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__add_sign
msgid "Add Sign"
msgstr "Lisa märk"

#. module: account
#: model:ir.actions.server,name:account.action_new_bank_setting
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
msgid "Add a Bank Account"
msgstr "Lisage pangakonto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Add a bank"
msgstr "Lisa pank"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid "Add a journal"
msgstr "Lisage andmik"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_group_list
msgid "Add a journal group"
msgstr "Lisa andmiku grupp"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add a line"
msgstr "Lisa rida"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid "Add a new account"
msgstr "Lisage uus konto"

#. module: account
#: model_terms:ir.actions.act_window,help:account.account_tag_action
msgid "Add a new tag"
msgstr "Lisage uus silt"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add a note"
msgstr "Lisa märkus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Add a payment QR code to your invoices"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__strategy__add_invoice_line
msgid "Add a rounding line"
msgstr "Lisa ümardamise rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__has_second_line
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr "Lisa teine rida"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add a section"
msgstr "Lisa sektsioon"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Add an EPC QR code to your invoices so that your customers can pay instantly"
" with their mobile banking application. EPC QR codes are used by many "
"European banks to process SEPA payments."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add an internal note..."
msgstr "Lisa märkus …"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Add contacts to notify..."
msgstr "Lisa kontaktid, keda teavitada ..."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__partner_ids
msgid "Additional Contacts"
msgstr "Lisakontaktid"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__tag_ids
msgid ""
"Additional tags that will be assigned by this repartition line for use in "
"financial reports"
msgstr ""
"Lisamärked mis eraldatakse antud jaotusjoonega, kasutamiseks "
"finantsraportites"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__adjustment_type
msgid "Adjustment Type"
msgstr "Korrigeerimistüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_group__property_advance_tax_payment_account_id
msgid "Advance Tax payment account"
msgstr "Avansilise maksu maksete konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_advance_tax_payment_account_id
msgid "Advance tax payment account"
msgstr "Avansilise maksu maksete konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr "Lisavalikud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr "Lisaseaded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr "Mõjutab järgnevate maksude baasi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__include_base_amount
msgid "Affect Subsequent Taxes"
msgstr "Mõjutab järgnevaid makse"

#. module: account
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
msgid "Aged Partner Balance"
msgstr "Aegunud partneri saldo"

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_id
msgid "Alias"
msgstr "Alias"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_name
msgid "Alias Name"
msgstr "Aliase nimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_domain
msgid "Alias domain"
msgstr "Aliase domeen"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_common_journal_report__target_move__all
#: model:ir.model.fields.selection,name:account.selection__account_common_report__target_move__all
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__target_move__all
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Entries"
msgstr "Kõik kanded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__all_lines_reconciled
msgid "All Lines Reconciled"
msgstr "Kõik read on sobitatud"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_common_journal_report__target_move__posted
#: model:ir.model.fields.selection,name:account.selection__account_common_report__target_move__posted
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__target_move__posted
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Posted Entries"
msgstr "Kõik kinnitatud kanded"

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "All accounts on the lines must be from the same type."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "All entries are hashed."
msgstr "Kõik kirjed on kirjendatud."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "Kõik arved ja maksed on sobitatud, kontode saldod on korras."

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "All lines must be from the same company."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr "Kõik konto kannete read tuleb väljavõte sulgemiseks menetleda."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__reconcile
msgid "Allow Invoices & payments Matching"
msgstr "Luba arvete ja maksete sobitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_product_margin
msgid "Allow Product Margin"
msgstr "Luba toote marginaal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__reconcile
msgid "Allow Reconciliation"
msgstr "Luba sobitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_check_printing
msgid "Allow check printing and deposits"
msgstr "Luba tšekkide trükkimine ja deposiidid"

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr "Luba sularaha ümardamise haldamine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr "Luba kassapõhiste maksude seadistamine"

#. module: account
#: model:res.groups,name:account.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Luba määrata majandusaastad ühest aastast pikemaks või lühemaks"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows to tag analytic entries and to manage analytic distributions"
msgstr ""
"Võimaldab märkida analüütilisi kandeid ja hallata analüütilisi jaotusi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr "Lubab kasutada analüütilist raamatupidamist."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount
#: model:ir.model.fields,field_description:account.field_account_payment__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount
#: model:ir.model.fields,field_description:account.field_account_tax__amount
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount
#: model:ir.model.fields,field_description:account.field_cash_box_out__amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__amount
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr "Summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount_currency
msgid "Amount Currency"
msgstr "Summa valuuta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_residual
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Amount Due"
msgstr "Tasumisele kuuluv summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_residual_signed
msgid "Amount Due Signed"
msgstr "Allkirjastatud tasumisele kuuluv summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount
msgid "Amount Matching"
msgstr "Summa sobitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount_param
msgid "Amount Matching %"
msgstr "Summa sobitamine %"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_max
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_max
msgid "Amount Max Parameter"
msgstr "Väärtuse maksimummäär"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_min
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_min
msgid "Amount Min Parameter"
msgstr "Väärtuse miinimummäär"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_nature
msgid "Amount Nature"
msgstr "Summa olemus"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_nature__amount_paid
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_nature__amount_paid
msgid "Amount Paid"
msgstr "Tasutud summa"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_nature__both
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_nature__both
msgid "Amount Paid/Received"
msgstr "Makstud/Saadud summa"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_nature__amount_received
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_nature__amount_received
msgid "Amount Received"
msgstr "Saadud summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount_type
msgid "Amount Type"
msgstr "Arvutuse liik"

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr ""
"Selle sobitamisega seotud summa. Eeldatakse, et see on alati positiivne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount_from_label_regex
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount_from_label_regex
msgid "Amount from Label (regex)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_currency
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount_currency
msgid "Amount in Currency"
msgstr "Summa valuutas"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr "Summa liik"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Amount:"
msgstr "Summa:"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "An Off-Balance account can not be reconcilable"
msgstr "Mitte tasakaalus kontot pole võimalik kinnitada"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "An Off-Balance account can not have taxes"
msgstr "Mitte tasakaalus kontol ei saa olla maksusid"

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_fiscal_position_account_account_src_dest_uniq
msgid ""
"An account fiscal position could be defined only one time on same accounts."
msgstr "Konto finantspositsioon saab samal kontol määrata ainult üks kord."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""
"Konto on osa pearaamatust, mis võimaldab registreerida \n"
"                 kõikvõimalikke deebet ja kreedit kandeid. Ettevõtted\n"
"                 avaldavad oma majandusaasta tehingud kahes vormis: bilanss\n"
"                 ja kasumiaruanne. Majandusaasta aruanne on seadusega\n"
"                 nõutav informatsioon ettevõtte kohta."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""
"Kontotüüp määrab, kuidas kontot kasutatakse andmikes.\n"
"                Edasilükkamise meetod konto liigil määrab aasta sulgemise\n"
"                protsessi. Näiteks bilansis ja kasumiaruandes kasutatakse \n"
"                konto kategooriat (kasumiaruanne või bilanss)."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"An error occured when computing the inalterability. Impossible to get the "
"unique previous posted journal entry."
msgstr ""
"Viga tekkis asendamatuse arvutamisel. Võimatu on leida algset eelmise kande "
"sisestust."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr "Analüütiline"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Analüütiline konto."

#. module: account
#: model:ir.model,name:account.model_account_analytic_account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search_analytic_accounting
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr "Analüütiline konto"

#. module: account
#: model:ir.ui.menu,name:account.account_analytic_group_menu
msgid "Analytic Account Groups"
msgstr "Analüütilised kontogrupid"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr "Analüütiline raamatupidamine"

#. module: account
#: model:ir.ui.menu,name:account.account_analytic_def_account
msgid "Analytic Accounts"
msgstr "Analüütilised kontod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__analytic
msgid "Analytic Cost"
msgstr "Analüütiline kulu"

#. module: account
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Items"
msgstr "Analüütilised kanded"

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analüütiline rida"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr "Analüütilised read"

#. module: account
#: model:ir.model,name:account.model_account_analytic_tag
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_tags
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr "Analüütilised sildid"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "Analüütilised sildid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Analüütilised read"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr "Analüütika"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"Vastused ei liigu algse dokumendi arutellu. Mõjutab loodud sõnumi ID-d."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_exigible
msgid "Appears in VAT report"
msgstr "Ilmub käibemaksu aruandes"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__applicability
msgid "Applicability"
msgstr "Rakendatavus"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__tax_adjustments_wizard__adjustment_type__credit
msgid "Applied on credit journal item"
msgstr "Applied on credit journal item"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__tax_adjustments_wizard__adjustment_type__debit
msgid "Applied on debit journal item"
msgstr "Applied on debit journal item"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Apply"
msgstr "Kinnita"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__auto_apply
msgid "Apply automatically this fiscal position."
msgstr "Rakenda automaatselt see finantspositsioon."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr "Rakenda ainult siis, kui saadetise või arve riik vastab grupile."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_id
msgid "Apply only if delivery or invoicing country match."
msgstr "Rakenda siis, kui saadetise või arve riik on vastavuses."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__vat_required
msgid "Apply only if partner has a VAT number."
msgstr "Rakenda ainult siis, kui partneril on käibemaksukohustuslase number."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr "Rakenda õige KM-määr Euroopa Liidus müüdud digitaalsete toodetele"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__4
msgid "April"
msgstr "Aprill"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_form
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_form
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__asset
msgid "Asset"
msgstr "Aktiivne"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Assets"
msgstr "Varad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__account_ids
msgid "Associated Account Templates"
msgstr "Seotud konto mallid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_inbound
msgid "At Least One Inbound"
msgstr "Vähemalt üks sisenev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_outbound
msgid "At Least One Outbound"
msgstr "Vähemalt üks väljuv"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Attach a file"
msgstr "Attach a file"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_journal__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_move__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_payment__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__attachment_ids
msgid "Attachments"
msgstr "Manused"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__8
msgid "August"
msgstr "August"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__author_id
msgid "Author"
msgstr "Autor"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Sõnumi autor. Kui pole määratud, siis email_from võib sisaldada emaili "
"aadressi mis pole seotud ühegi partneriga."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Auto-Complete"
msgstr "Automaatne täitmine"

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_vendor_bill_id
msgid "Auto-complete from a past bill."
msgstr "Varasema arve automaatne täitmine."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__auto_reconcile
msgid "Auto-validate"
msgstr "Automaatne kinnitamine"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Automatic Balancing Line"
msgstr "Automaatne saldo võrdsustamise rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "Automaatsed valuutakursid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr "Automaatne import"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_average
msgid "Average Price"
msgstr "Keskmine hind"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "BILL"
msgstr "ARVE"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__trust__bad
msgid "Bad Debtor"
msgstr "Halb võlgnik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__balance
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__value__balance
msgid "Balance"
msgstr "Bilanss"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr "Bilanss arvutatuna algsaldodest ja tehingute ridadelt"

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model:ir.model.fields,field_description:account.field_account_journal__bank_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_id
#: model:ir.model.fields,field_description:account.field_res_partner__bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users__bank_account_count
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__bank
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Bank"
msgstr "Pank"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr "Pank ja kassa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal__bank_account_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_partner_bank_id
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Bank Account"
msgstr "Pangakonto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr "Pangakonto nimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_number
msgid "Bank Account Number"
msgstr "Pangakonto number"

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"Pangakonto number kuhu arve makstakse. Ettevõte pangakonto kui tegemist on "
"kliendiarve või tarnija kreeditarvega, muudel juhtudel partneri pangakonto."

#. module: account
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_invoice_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Bank Accounts"
msgstr "Pangakontod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_statements_source
msgid "Bank Feeds"
msgstr "Pangaväljavõtted"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_bic
msgid "Bank Identifier Code"
msgstr "Panga identifitseerimiskood (BIC)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr "Pangaliides - automaatne andmete vahetamine pangaga"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__bank_journal_ids
msgid "Bank Journals"
msgstr "Panga andmik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__bank_partner_id
msgid "Bank Partner"
msgstr "Panga Partner"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.model.fields.selection,name:account.selection__account_journal__post_at__bank_rec
#, python-format
msgid "Bank Reconciliation"
msgstr "Panga sobitamine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr "Pangakannete sobitamise eelsäte"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr "Pangakannete sobitamise eelsäte"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,field_description:account.field_res_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account.report_statement
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr "Pangaväljavõte"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s"
msgstr "Bank Statement %s"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s.pdf"
msgstr "Pangaväljavõte %s.pdf"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Bank Statement Cashbox"
msgstr "Panga Väljavõte Kassas"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Bank Statement Closing Balance"
msgstr "Panga Väljavõte Lõppsaldo"

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Pangaväljavõtte rida"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr "Pangaväljavõtte read"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement.pdf"
msgstr "Panga Väljavõte.pdf"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr "Pangaväljavõtted"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__bank_account_id
msgid "Bank account that was used in this transaction."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_type
msgid ""
"Bank account type: Normal or IBAN. Inferred from the bank account number."
msgstr "Pangakonto tüüp: Tavaline või IBAN. Pangakonto numbri alusel."

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
#: model:ir.actions.act_window,name:account.action_account_moves_journal_bank_cash
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_bank_cash
msgid "Bank and Cash"
msgstr "Pank ja kassa"

#. module: account
#: model:ir.model,name:account.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "Panga ülesseadmise käsitsi konfigureerimine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr "Pangaväljavõtte rida on sobitatud selle kandega"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Bank: Balance"
msgstr "Pank: Saldo"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line__repartition_type__base
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line_template__repartition_type__base
msgid "Base"
msgstr "Alus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_base_amount
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr "Alussumma"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr "Base Tax Received Account"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__repartition_type
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__repartition_type
msgid "Base on which the factor will be applied."
msgstr "Tegur millest lähtuda."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__repartition_type
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__repartition_type
msgid "Based On"
msgstr "Lähtuvalt"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_type__partner
msgid "Based on Customer"
msgstr "Lähtuvalt Kliendist"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_type__invoice
#: model:ir.model.fields.selection,name:account.selection__account_tax__tax_exigibility__on_invoice
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__tax_exigibility__on_invoice
msgid "Based on Invoice"
msgstr "Arve-põhine"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template__tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""
"Arve-põhine: maksukohustus tekib kohe, kui arve on kinnitatud.\n"
"Makse-põhine: maksukohustus tekib siis, kui makse on laekunud."

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__tax_exigibility__on_payment
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__tax_exigibility__on_payment
msgid "Based on Payment"
msgstr "Baseerub maksel"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Payments"
msgstr "Koondmaksed"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bill"
msgstr "Arve"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Bill Date"
msgstr "Arve kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_invoice_extract
msgid "Bill Digitalization"
msgstr "Arve digitaliseerimine"

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr "Arveldus"

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Administrator"
msgstr "Arvelduse admin"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_in_invoice_type
#: model:ir.ui.menu,name:account.menu_action_move_in_invoice_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr "Ostuarved"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr "Ostuarvete analüüs"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Pay"
msgstr "Arved maksta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Validate"
msgstr "Arved kinnitamiseks"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Bills to pay"
msgstr "Maksmist vajavad ostuarved"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__invoice_warn__block
msgid "Blocking Message"
msgstr "Veateade"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type__include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr "Too konto saldod edasi"

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr "Sirvige olemasolevaid riike."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_budget
msgid "Budget Management"
msgstr "Eelarvestamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__name
msgid "Button Label"
msgstr "Nupu silt"

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr "Riigi järgi"

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr "Kreeditarve alusel"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr "Toote järgi"

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr "Toote kategooria järgi"

#. module: account
#: model:ir.filters,name:account.filter_invoice_report_salespersons
msgid "By Salespersons"
msgstr "Müügiesindajate järgi"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""
"Kui jätate aktiivse välja märkimata, saate peita finantspositsioon seda "
"kustutamata."

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr ""
"Tühjendades aktiivse välja võite ära peita INCOTERM'i, mida te ei kasuta."

#. module: account
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "CABA"
msgstr "CABA"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr "CAMT Import"

#. module: account
#: model:account.incoterms,name:account.incoterm_CIP
msgid "CARRIAGE AND INSURANCE PAID TO"
msgstr "VEDU JA KINDLUSTUS MAKSTUD KUNI"

#. module: account
#: model:account.incoterms,name:account.incoterm_CPT
msgid "CARRIAGE PAID TO"
msgstr "VEDU MAKSTUD KUNI"

#. module: account
#: model:account.incoterms,name:account.incoterm_CFR
msgid "COST AND FREIGHT"
msgstr "HIND JA PRAHIRAHA"

#. module: account
#: model:account.incoterms,name:account.incoterm_CIF
msgid "COST, INSURANCE AND FREIGHT"
msgstr "KULUD, KINDLUSTUS JA VEDU"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr "CSV Import"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "CUST"
msgstr "CUST"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__visible
msgid "Can be Visible?"
msgstr "Võib olla nähtav?"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox_footer
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form_multi
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr "Tühista"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Cancel Entry"
msgstr "Tühista kanne"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__state__cancel
#: model:ir.model.fields.selection,name:account.selection__account_move__state__cancel
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__cancelled
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Cancelled"
msgstr "Tühistatud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr "Tühistatud arve"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Cannot create an invoice of type %s with a journal having %s as type."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Cannot create unbalanced journal entry. Ids: %s\n"
"Differences debit - credit: %s"
msgstr ""
"Ei saa luua mittetasakaalus artikli sisestust. ID-d: %s\n"
"Erivevad deebet - kreedit: %s"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""
"Ei suuda tuvastada kontoplaani selle ettevõtte jaoks. Palun seadista see. \n"
"Palun mine konto seadistusse."

#. module: account
#: code:addons/account/models/account.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Cannot generate an unused account code."
msgstr "Kasutamata konto koodi ei saa luua."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr "Kasutamata andmiku koodi ei saa luua. Palun täida 'Lühikood' väli."

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__cash
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Cash"
msgstr "Kassa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_exigibility
msgid "Cash Basis"
msgstr "Kassapõhine"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr "Kassapõhine andmik"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Cash Basis Taxes"
msgstr "Sularahaga seonduvad maksud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_transition_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_transition_account_id
msgid "Cash Basis Transition Account"
msgstr "Sularahal põhinev siirdekonto"

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "Cash Box Out"
msgstr "Kassa välja"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Cash Control"
msgstr "Sularaha kontroll"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__default_cash_difference_expense_account_id
#: model:ir.model.fields,field_description:account.field_res_company__default_cash_difference_expense_account_id
msgid "Cash Difference Expense Account"
msgstr "Sularaha erinevuse Kulude Konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__default_cash_difference_income_account_id
#: model:ir.model.fields,field_description:account.field_res_company__default_cash_difference_income_account_id
msgid "Cash Difference Income Account"
msgstr "Kassavahe Sissetulekukonto"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr "Kassa"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_cash_rounding
msgid "Cash Rounding"
msgstr "Raha ümardamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr "Raha ümardamise meetod"

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Sularaha ümardamised"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_statement
msgid "Cash Statement"
msgstr "Kassa väljavõte"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr "Loendamise ajal täheldatud rahaerinevus (%s)"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Cash: Balance"
msgstr "Sularaha: Saldo"

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr "Kassasahtli rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__cashbox_id
msgid "Cashbox"
msgstr "Kassasahtel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__cashbox_lines_ids
msgid "Cashbox Lines"
msgstr "Kassasahtli read"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr "Kulukonto kategooria"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_categ_id
msgid "Category of Income Account"
msgstr "Tulukonto kategooria"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr "Muuda silti vastaspoolel, millel on maksete erinevus"

#. module: account
#: code:addons/account/controllers/portal.py:0
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"KMKR numbri muutmine ei ole lubatud, kui teie kontole on juba esitatud "
"arveid. Palun võtke meiega otse ühendust, kui seda on vaja."

#. module: account
#: code:addons/account/controllers/portal.py:0
#, python-format
msgid ""
"Changing your company name is not allowed once invoices have been issued for"
" your account. Please contact us directly for this operation."
msgstr ""
"Nime muutmine ei ole lubatud, kui teie kontole on juba esitatud arveid. "
"Palun võtke meiega otse ühendust, kui seda on vaja."

#. module: account
#: code:addons/account/controllers/portal.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Nime muutmine ei ole lubatud, kui teie kontole on juba esitatud arveid. "
"Palun võtke meiega otse ühendust, kui seda on vaja."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company__chart_template_id
msgid "Chart Template"
msgstr "Tabeli mall"

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr "Tabeli mallid"

#. module: account
#: code:addons/account/models/company.py:0
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
#, python-format
msgid "Chart of Accounts"
msgstr "Kontoplaan"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr "Kontoplaani mall"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr "Kontoplaani mallid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Chart of account set."
msgstr "Kontoplaan seatud."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr "Kontoplaan"

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr "Kontrolli lõppsaldot"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "Kontrollida kõiki"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__is_difference_zero
msgid "Check if difference is zero."
msgstr "Kontrolli, kas erinevus on null."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "Kontrollige, et teil poleks pangaväljavõtte ridu"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Check them"
msgstr "Kontrolli neid"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""
"Vali, kui see konto lubab arvete ja maksete sobitamist andmike kanderidadel."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Märgi see kui sa ei soovi jagada sama numeratsiooni arvete ja kreeditarvete "
"osas selles andmikus"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__tax_negate
msgid ""
"Check this box to negate the absolute value of the balance of the lines "
"associated with this tag in tax report computation."
msgstr ""
"Märgi linnuke eiramaks absoluutväärtusi saldo ridades mis on vastavalt "
"märgistatud maksuraporti arvutustes.  "

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__price_include
#: model:ir.model.fields,help:account.field_account_tax_template__price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr "Vali see, kui toodetel ja arvetel kasutatav hind sisaldab seda maksu."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""
"Märgi see valik, kui tahad, et kasutaja sobitaks kandeid sellel kontol."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr "Tšekid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__children_line_ids
msgid "Children Lines"
msgstr "Alamread"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__children_tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr "Alammaksud"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Choose Accounting Template"
msgstr "Vali raamatupidamise mall"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Choose a default sales tax for your products."
msgstr "Vali oma toodete jaoks müügimaksu vaikeväärtus."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Vali vastaspool või kanna summa mingile kontole"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal__refund_method
msgid ""
"Choose how you want to credit this invoice. You cannot \"modify\" nor "
"\"cancel\" if the invoice is already reconciled."
msgstr ""
"Vali kuidas soovid oma arvet krediteerida. Sa ei saa muuta ega tühistada "
"juba kinnitatud arvet."

#. module: account
#: model_terms:ir.actions.act_window,help:account.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Uue eelarveaasta loomiseks vajuta siia."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tours/account.js:0
#, python-format
msgid "Click to <b>send the invoice by email.</b>"
msgstr "Vajuta <b>saada arve e-mailiga.</b>"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tours/account.js:0
#, python-format
msgid "Click to <b>send the invoice.</b>"
msgstr "Vajuta <b>saada arve.</b>"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tours/account.js:0
#, python-format
msgid ""
"Click to <b>validate your invoice.</b> A reference will be assigned to this "
"invoice and you will not be able to modify it anymore."
msgstr ""
"Vajuta siia <b>oma arve kinnitamiseks.</b> Sellele arvele määratakse viide "
"ja te ei saa seda hiljem enam muuta."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Kinnitatud"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "Sulge väljavõte"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__closed
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__closed
msgid "Closed"
msgstr "Suletud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date_done
msgid "Closed On"
msgstr "Suletud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__code
#: model:ir.model.fields,field_description:account.field_account_account_template__code
#: model:ir.model.fields,field_description:account.field_account_analytic_line__code
#: model:ir.model.fields,field_description:account.field_account_incoterms__code
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_method__code
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_code
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__code
msgid "Code"
msgstr "Kood"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__code_prefix
msgid "Code Prefix"
msgstr "Koodi eesliide"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__coin_value
msgid "Coin/Bill Value"
msgstr "Mündi/kupüüri väärtus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service"
msgstr "Kliendi maksete kogumine ühe klõpsuga kasutades Euro SEPA teenust."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Collect information and produce statistics on the trade in goods in Europe "
"with intrastat"
msgstr ""
"Koguge informatsiooni ja koostage statistikat Euroopa kaubavahetuse kohta "
"intrastati abil"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__color
#: model:ir.model.fields,field_description:account.field_account_journal__color
msgid "Color Index"
msgstr "Värvikood"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Äriühing"

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Common Journal Report"
msgstr "Üldandmiku raport"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr "Ühine aruanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Suhtluse standard"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__invoice_reference_type
msgid "Communication Type"
msgstr "Suhtluse viis"

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr "Ettevõtted"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users__ref_company_ids
msgid "Companies that refers to partner"
msgstr "Ettevõtted, mis viitavad partnerile"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__company_id
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__company_id
#: model:ir.model.fields,field_description:account.field_account_common_report__company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__company_id
#: model:ir.model.fields,field_description:account.field_account_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_journal_group__company_id
#: model:ir.model.fields,field_description:account.field_account_move__company_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_id
#: model:ir.model.fields,field_description:account.field_account_payment__company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__company_id
#: model:ir.model.fields,field_description:account.field_account_root__company_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__company_id
#: model:ir.model.fields,field_description:account.field_account_tax__company_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__company_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr "Ettevõte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__company_currency_id
msgid "Company Currency"
msgstr "Ettevõtte valuuta"

#. module: account
#: model:ir.model,name:account.model_base_document_layout
msgid "Company Document Layout"
msgstr "Ettevõtte Dokumendi kavand"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "Ettevõttel on kontoplaan"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,help:account.field_account_journal__company_id
#: model:ir.model.fields,help:account.field_account_move__company_id
#: model:ir.model.fields,help:account.field_account_move_line__company_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile__company_id
#: model:ir.model.fields,help:account.field_account_payment__company_id
msgid "Company related to this journal"
msgstr "Andmikuga seotud ettevõte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__complete_tax_set
msgid "Complete Set of Taxes"
msgstr "Täida maksude komplekt"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__name
msgid "Complete name for this report line, to be used in report."
msgstr "Täielik nimi siia raporti reale; kasutatakse raportis."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composer_id
msgid "Composer"
msgstr "Koostaja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composition_mode
msgid "Composition mode"
msgstr "Koostamise režiim"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr "Kasuta USA kohaindekseid maksumäärade arvutamiseks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end
msgid "Computed Balance"
msgstr "Arvutatud saldo"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_audit
msgid ""
"Computed field, listing the tax grids impacted by this line, and the amount "
"it applies to each of them."
msgstr ""
"Arvutatud väli, loetledes maksujoonestikku mõjutuses sellest reast, ja "
"määrasid mis rakenduvad igale neist."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Conditions on Bank Statement Line"
msgstr "Pangaväljavõtte rea tingimused"

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr "Seadistused"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Configuration review"
msgstr "Konfiguratsiooni ülevaade"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Configure"
msgstr "Seadista"

#. module: account
#: model:ir.actions.act_window,name:account.action_configure_tax_report
msgid "Configure Tax Report"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox_footer
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr "Kinnita"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr "Kinnitatud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""
"Selle kinnitamine loob automaatsed andmiku kanded, millel on erinevus raha "
"andmikus seadistatud kasumi/kahjumi kontoga ."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Palju õnne, said hakkama!"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "Congratulations! You are all set."
msgstr "Palju õnne! Kõik on valmis."

#. module: account
#: model:ir.model,name:account.model_res_partner
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Contact"
msgstr "Kontakt"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_label__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_note__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_transaction_type__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_label__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_note__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_transaction_type__contains
msgid "Contains"
msgstr "Sisaldab"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__body
msgid "Contents"
msgstr "Sisukord"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr "Ligipääsu kontroll"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Corrupted data on journal entry with id %s."
msgstr "Vigased andmed andmiku sisestuses, id %s."

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr "Müügikulud (kaubad, toore, materjal ja teenused)"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing."
msgstr ""
"Ei saanud paigaldada uut kontoplaani, sest raamatupidamise kanded on juba "
"olemas."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_id
msgid "Counterpart Account"
msgstr "Vastaspoole konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Counterpart Values"
msgstr "Vastaspoole väärtused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__country_id
#: model:ir.model.fields,field_description:account.field_account_move_line__country_id
#: model:ir.model.fields,field_description:account.field_account_tax__country_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__country_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__country_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__country_id
#: model_terms:ir.ui.view,arch_db:account.account_tax_report_line_search
msgid "Country"
msgstr "Riik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_group_id
msgid "Country Group"
msgstr "Riikide grupp"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__country_id
msgid "Country for which this line is available."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__country_id
msgid "Country for which this tag is available, when applied on taxes."
msgstr "Riik kelle jaoks see märgis on saadaval, kui rakenduvad maksud."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Coverage"
msgstr "Kindlustussumma"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "Create"
msgstr "Loo"

#. module: account
#: model:ir.actions.server,name:account.action_accrual_entry
msgid "Create Accrual Entry"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.account_accrual_accounting_wizard_action
msgid "Create Accrual Entry for the expense/revenue recognition"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Create Journal Entry"
msgstr "Loo andmiku kanne"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form_multi
msgid "Create Payment"
msgstr "Loo makse"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Create a Bank Account"
msgstr "Loo pangakonto"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Create a bank account"
msgstr "Loo uus pangakonto"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "Loo duplikaat"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_refund_type
msgid "Create a credit note"
msgstr "Loo kreeditarve"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_invoice_type
msgid "Create a customer invoice"
msgstr "Loo müügiarve"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid "Create a journal entry"
msgstr "Loo andmiku kanne"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_group_tree
msgid "Create a new account group"
msgstr "Looge uus kontogrupp"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Create a new cash log"
msgstr "Loo uus rahavoog"

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_customer
msgid "Create a new customer in your address book"
msgstr "Loo aadressiraamatusse uus klient"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_form
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_template_form
msgid "Create a new fiscal position"
msgstr "Looge uus finantspositsioon"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid "Create a new incoterm"
msgstr "Loo uus incoterm'i termin"

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_purchasable
msgid "Create a new purchasable product"
msgstr "Looge uus ostetav toode"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Create a new reconciliation model"
msgstr "Looge uus sobitamismudel"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_receipt_type
msgid "Create a new sales receipt"
msgstr "Looge uus müügitšekk"

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_sellable
msgid "Create a new sellable product"
msgstr "Loo uus müügitoode"

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_supplier
msgid "Create a new supplier in your address book"
msgstr "Lisa oma aadressiraamatusse uus tarnija"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_tax_form
msgid "Create a new tax"
msgstr "Loo uus maks"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_invoice_type
msgid "Create a vendor bill"
msgstr "Loo ostuarve"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_refund_type
msgid "Create a vendor credit note"
msgstr "Looge tarnija kreeditarve"

#. module: account
#: model:ir.model,name:account.model_account_accrual_accounting_wizard
msgid "Create accrual entry."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr "Loo ja postita kanne"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Create cash statement"
msgstr "Loo raha aruanne"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Create invoice/bill"
msgstr "Loo arve"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_invoice_type
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""
"Looge arveid, registreerige makseid ja pidage oma klientidega peetavatel "
"aruteludel silma peal."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_invoice_type
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your vendors."
msgstr ""
"Looge arveid, registreerige makseid ja pidage oma klientidega peetavatel "
"aruteludel silma peal."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Loo mudel"

#. module: account
#: model_terms:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr "Loo esimene raha ümardus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__create_uid
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_uid
#: model:ir.model.fields,field_description:account.field_account_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_move__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_register__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Created by: %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_date
#: model:ir.model.fields,field_description:account.field_account_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_account_type__create_date
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_date
#: model:ir.model.fields,field_description:account.field_account_common_report__create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_group__create_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_date
#: model:ir.model.fields,field_description:account.field_account_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_journal_group__create_date
#: model:ir.model.fields,field_description:account.field_account_move__create_date
#: model:ir.model.fields,field_description:account.field_account_move_line__create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_payment__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_register__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_date
#: model:ir.model.fields,field_description:account.field_account_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__credit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Credit"
msgstr "Kreedit"

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr "Kreeditkaart"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__refund_method
msgid "Credit Method"
msgstr "Kreediti meetod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__credit_move_id
msgid "Credit Move"
msgstr "Kreeditkanne"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Credit Note"
msgstr "Kreedit"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Credit Note Created"
msgstr "Kreeditarve loodud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "Kreeditarve kirje järjekord"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_out_refund_type
#: model:ir.ui.menu,name:account.menu_action_move_out_refund_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Credit Notes"
msgstr "Kreeditarved"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_number_next
msgid "Credit Notes Next Number"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__credit_account_id
msgid "Credit account"
msgstr "Kreeditkonto"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr "Kreedit andmiku kanderida, mis on sobitatud selle andmiku kannetega."

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr "Valuutad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_id
#: model:ir.model.fields,field_description:account.field_account_journal__currency_id
#: model:ir.model.fields,field_description:account.field_account_move__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__currency_id
#: model:ir.model.fields,field_description:account.field_account_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_id
#: model:ir.model.fields,field_description:account.field_res_partner__currency_id
#: model:ir.model.fields,field_description:account.field_res_users__currency_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Valuuta"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Currency exchange rate difference"
msgstr "Valuutakursi erinevus"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
msgid "Current Assets"
msgstr "Käibevara"

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr "Lühiajalised kohustused"

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr "Käesoleva aasta kasum"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__partner_type__customer
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Customer"
msgstr "Klient"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__out_refund
#: model:ir.model.fields.selection,name:account.selection__account_move__type__out_refund
#, python-format
msgid "Customer Credit Note"
msgstr "Müügi kreeditarve"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__out_invoice
#: model:ir.model.fields.selection,name:account.selection__account_move__type__out_invoice
msgid "Customer Invoice"
msgstr "Müügiarve"

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#, python-format
msgid "Customer Invoices"
msgstr "Müügiarved"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Customer Payment"
msgstr "Kliendi makse"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Kliendi maksetingimused"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Customer Payments"
msgstr "Kliendi maksed"

#. module: account
#: model:ir.model.fields,help:account.field_account_move__access_url
msgid "Customer Portal URL"
msgstr "Kliendiportaali URL"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__customer_rank
#: model:ir.model.fields,field_description:account.field_res_users__customer_rank
msgid "Customer Rank"
msgstr "Kliendi hinnang"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product__taxes_id
#: model:ir.model.fields,field_description:account.field_product_template__taxes_id
msgid "Customer Taxes"
msgstr "Müügimaksud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Customer/Vendor"
msgstr "Klient/Müüja"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "Kliendi/tarnija sidumine"

#. module: account
#: model:ir.actions.act_window,name:account.res_partner_action_customer
#: model:ir.ui.menu,name:account.menu_account_customer
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model_terms:ir.ui.view,arch_db:account.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Customers"
msgstr "Kliendid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize"
msgstr "Kohanda"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize the look of your invoices."
msgstr "Arve väljanägemise kohendamine."

#. module: account
#: model:account.incoterms,name:account.incoterm_DAF
msgid "DELIVERED AT FRONTIER"
msgstr "TARNITUD PIIRIL"

#. module: account
#: model:account.incoterms,name:account.incoterm_DAP
msgid "DELIVERED AT PLACE"
msgstr "TARNITUD KOHALE"

#. module: account
#: model:account.incoterms,name:account.incoterm_DAT
msgid "DELIVERED AT TERMINAL"
msgstr "TARNITUD TERMINALI"

#. module: account
#: model:account.incoterms,name:account.incoterm_DDP
msgid "DELIVERED DUTY PAID"
msgstr "TARNITUD, TOLL TASUTUD"

#. module: account
#: model:account.incoterms,name:account.incoterm_DDU
msgid "DELIVERED DUTY UNPAID"
msgstr "TARNITUD, TOLL TASUMATA"

#. module: account
#: model:account.incoterms,name:account.incoterm_DEQ
msgid "DELIVERED EX QUAY"
msgstr "TARNITUD KAILT"

#. module: account
#: model:account.incoterms,name:account.incoterm_DES
msgid "DELIVERED EX SHIP"
msgstr "TARNITUD LAEVALT"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__rounding_method__down
msgid "DOWN"
msgstr "Alla"

#. module: account
#: model:ir.actions.server,name:account.action_check_hash_integrity
msgid "Data Inalterability Check"
msgstr "Andmete samasuse kontroll"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Data consistency check"
msgstr "Andmete järjepidevuse kontroll"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__date
#: model:ir.model.fields,field_description:account.field_account_move__date
#: model:ir.model.fields,field_description:account.field_account_move_line__date
#: model:ir.model.fields,field_description:account.field_account_payment__payment_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__date
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__sort_selection__date
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Date"
msgstr "Kuupäev"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr ""
"Kuupäev, millal selle ettevõtte avamiskanne on tehtud raamatupidamises."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr "Kuupäev, millest alates on raamatupidamist tehtud Odoo's."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Date:"
msgstr "Kuupäev:"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr "Kuupäevad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__day_of_the_month
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Day of the month"
msgstr "Päev"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__day_of_the_month
msgid ""
"Day of the month on which the invoice must come to its term. If zero or "
"negative, this value will be ignored, and no specific day will be set. If "
"greater than the last day of a month, this number will instead select the "
"last day of this month."
msgstr ""
"Kuupäev kalendrikuus mis on arve tähtajaks. Kui on 0 või negatiivne, siis "
"antud väärtust ignoreeritakse ja kindlat kuupäeva ei seata. Kui suurem kuu "
"viimasest päevast, siis valitakse selle asemel käesoleva kuu viimane päev."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__debit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Debit"
msgstr "Deebet"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__debit_move_id
msgid "Debit Move"
msgstr "Deebetkanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__debit_account_id
msgid "Debit account"
msgstr "Deebetkonto"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr "Deebet andmiku kanderida, mis on sobitatud selle andmiku kannetega."

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__12
msgid "December"
msgstr "Detsember"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__decimal_separator
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__decimal_separator
msgid "Decimal Separator"
msgstr "Kümnendkohtade eraldaja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Eraldi kreeditarve kirje järjekord"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_credit_account_id
msgid "Default Credit Account"
msgstr "Vaikimisi kreeditkonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_debit_account_id
msgid "Default Debit Account"
msgstr "Vaikimisi deebetkonto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default Incoterm of your company"
msgstr "Vaikimisi incoterm sinu ettevõttes"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_default_pos_receivable_account_id
msgid "Default PoS Receivable Account"
msgstr "Vaikimisi PoS nõuete konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Vaikimisi ostu käibemaks"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_sale_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Vaikimisi müügi käibemaks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr "Vaikimisi seadistatud maksud"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__use_invoice_terms
msgid "Default Terms & Conditions"
msgstr "Vaikimisi tingimused"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_terms
msgid "Default Terms and Conditions"
msgstr "Vaikimisi tingimused"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__incoterm_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__incoterm_id
msgid "Default incoterm"
msgstr "Vaikimisi incoterm"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr "Kohalikele tehingutele vaikimisi seadistatud maksud "

#. module: account
#: model:ir.model.fields,help:account.field_product_product__supplier_taxes_id
#: model:ir.model.fields,help:account.field_product_template__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr "Vaikimisi maksud mis rakenduvad ostudele."

#. module: account
#: model:ir.model.fields,help:account.field_product_product__taxes_id
#: model:ir.model.fields,help:account.field_product_template__taxes_id
msgid "Default taxes used when selling the product."
msgstr "Vaikimisi maksud mis rakenduvad kauba müümisele."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid "Define a new account type"
msgstr "Määratlege uus kontotüüp"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "Määrake väikseim mündi väärtus sularahaga maksmisel."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Define your fiscal years &amp; tax returns periodicity."
msgstr "Määrake oma majandusaastate ja maksudeklaratsioonide perioodilisus. "

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr "Määrab, kuidas pangaväljavõtteid registreeritakse"

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr "Määrab väikseima mündi väärtuse sularahaga maksmisel."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr "Määratlus"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__trust
#: model:ir.model.fields,field_description:account.field_res_users__trust
msgid "Degree of trust you have in this debtor"
msgstr "Võlgniku usaldusväärsus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete
msgid "Delete Emails"
msgstr "Kustutage e-kirju"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete_message
msgid "Delete Message Copy"
msgstr "Kustutage sõnumikoopia"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__deprecated
msgid "Deprecated"
msgstr "Ei kasutata enam"

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
msgid "Depreciation"
msgstr "Amortisatsioon"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you put/take money from the cash register:"
msgstr "Kirjeldage, miks panete/võtate raha kassast:"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_account_type__note
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Description"
msgstr "Kirjeldus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__note
msgid "Description on the Invoice"
msgstr "Kirjeldus arvel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_account_id
msgid "Destination Account"
msgstr "Sihtkoha konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__auto_apply
msgid "Detect Automatically"
msgstr "Tuvasta automaatselt"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template__type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""
"Määrab, milline maks on valitav. Märge: 'pole ühtegi' tähendab,  et maksu "
"ennast ei saa valida, kuid maksu saab kasutada maksugrupis."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group. 'adjustment' is "
"used to perform tax adjustment."
msgstr ""
"Määrab kus maks on valikuline. Märge: 'mitte ühtegi' tähendab et maks pole "
"rakendatav üksinda, kuid on kasutatav maksugrupis. 'vastavusse viimine' "
"kasutatakse maksu kohandamiseks."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__difference
msgid "Difference"
msgstr "Vahe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_account_id
msgid "Difference Account"
msgstr "Erinevuse konto"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr "Erinevus arvutatud lõppsaldo ja määratletud lõppsaldo vahel."

#. module: account
#: model:ir.model,name:account.model_digest_digest
msgid "Digest"
msgstr "Ülevaade"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Digitalize your scanned or PDF vendor bills with OCR and Artificial "
"Intelligence"
msgstr "Digitaliseerige skanneeritud PDF arved OCR ja tehisintellektiga"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Disc.%"
msgstr "Allah. %"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__discount
msgid "Discount (%)"
msgstr "Allahindlus (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag__display_name
#: model:ir.model.fields,field_description:account.field_account_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_account_type__display_name
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__display_name
#: model:ir.model.fields,field_description:account.field_account_common_report__display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_group__display_name
#: model:ir.model.fields,field_description:account.field_account_incoterms__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_send__display_name
#: model:ir.model.fields,field_description:account.field_account_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_journal_group__display_name
#: model:ir.model.fields,field_description:account.field_account_move__display_name
#: model:ir.model.fields,field_description:account.field_account_move_line__display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_payment__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_register__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__display_name
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__display_name
#: model:ir.model.fields,field_description:account.field_account_root__display_name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__display_name
#: model:ir.model.fields,field_description:account.field_account_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile__display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_hash_integrity__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__qr_code
#: model:ir.model.fields,field_description:account.field_res_config_settings__qr_code
msgid "Display SEPA QR code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__display_type
msgid "Display Type"
msgstr "Kuvamise tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Näita arvetel"

#. module: account
#: code:addons/account/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Puudub ligipääs. Jäta need andmed kasutaja kokkuvõtte kirjast välja"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Ära hoia alles e-maili koopiat dokumendi suhtluse ajaloos (massimeilide "
"puhul)"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_invoice_layout_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sale_tax_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sample_invoice_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_bank_data_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_coa_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_fy_data_state__done
msgid "Done"
msgstr "Tehtud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr "Lae alla"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__state__draft
#: model:ir.model.fields.selection,name:account.selection__account_move__state__draft
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__draft
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr "Mustand"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Bill"
msgstr "Arve mustand"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Credit Note"
msgstr "Kreeditarve mustand"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Entry"
msgstr "Mustandkanne"

#. module: account
#: code:addons/account/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr "Arve mustand"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr "Arvete mustandid"

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Draft Payment"
msgstr "Makse mustand"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Purchase Receipt"
msgstr "Ostutšeki mustand"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Sales Receipt"
msgstr "Müügitšeki mustand"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Vendor Credit Note"
msgstr "Tarnija kreeditarve mustan"

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr "Väljavõtte mustandid"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
#, python-format
msgid "Due"
msgstr "Tähtaeg"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__residual
msgid "Due Amount"
msgstr "Võlgnetav summa"

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_move__invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_move_line__date_maturity
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Due Date"
msgstr "Tähtaeg"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr "Tähtaja arvutus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr "Tähtaja liik"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due the"
msgstr "Tähtajaga"

#. module: account
#: model:ir.actions.server,name:account.action_duplicate_account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Duplicate"
msgstr "Tee koopia"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same vendor bill/credit note:\n"
"%s"
msgstr ""
"Tarnija viite duplikaat leitud. Ilmselt sisestasite ühte tarnija arvet/kreeditarvet topelt\n"
"%s"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_reports
msgid "Dynamic Reports"
msgstr "Dünaamilised aruanded"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "EL digi-toodete käibemaks"

#. module: account
#: model:account.incoterms,name:account.incoterm_EXW
msgid "EX WORKS"
msgstr "VEO KORRALDAMINE"

#. module: account
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "EXCH"
msgstr "EXCH"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Edit"
msgstr "Muuda"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Kas nii deebet kui kreedit, või mitte kumbki."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_email
msgid "Email"
msgstr "E-post"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email Alias"
msgstr "E-posti alias"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Saatja e-maili aadress. Antud välja rakendatakse kui vastavat partnerit ei "
"leita ja asendatakse autor_id väli jutu sees."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_email
msgid "Email by default"
msgstr "Vaikimisi e-mail"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email your Invoices/Bills"
msgstr "Saatke oma arved meili teel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__end_bank_stmt_ids
msgid "End Bank Stmt"
msgstr "Lõplik panga väljavõte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_to
#: model:ir.model.fields,field_description:account.field_account_common_report__date_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_to
msgid "End Date"
msgstr "Lõpukuupäev"

#. module: account
#: model:account.payment.term,name:account.account_payment_term_end_following_month
msgid "End of Following Month"
msgstr "Järgneva kuu lõpp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end_real
msgid "Ending Balance"
msgstr "Lõppsaldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_end_id
msgid "Ending Cashbox"
msgstr "Kassa lõpetamine"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Lõpu kuupäev, jäädes majandusaasta sisse."

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr "Kanded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr "Kanded sorteeritud"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Entries are hashed from %s (%s)"
msgstr "Väljad hakitakse kust %s (%s)"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Entries are not from the same account."
msgstr "Kanded pole samalt kontolt."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr "Kontrollimist vajavad kanded"

#. module: account
#: code:addons/account/models/account_analytic_line.py:0
#, python-format
msgid "Entries: "
msgstr "Kanded:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_id
msgid "Entry Sequence"
msgstr "Kirje järjekord"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_ids
msgid "Entry lines"
msgstr "Kanderead"

#. module: account
#. openerp-web
#: model:account.account.type,name:account.data_account_type_equity
#: code:addons/account/static/src/js/account_selection.js:0
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__equity
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Equity"
msgstr "Omakapital"

#. module: account
#: code:addons/account/models/res_config_settings.py:0
#, python-format
msgid "Error!"
msgstr "Viga!"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_model__euro
msgid "European"
msgstr "Euroopa"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__decimal_separator
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__decimal_separator
msgid ""
"Every character that is nor a digit nor this separator will be removed from "
"the matching string"
msgstr ""
"Iga trükimärk mis pole ei number ega tühik eemaldatakse vastavast jadast"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Exchange Difference"
msgstr "Valuutakursi erinevus"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Valuutakursi muutustest tekkiva kasumi/kahjumi andmik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__exchange_move_id
msgid "Exchange Move"
msgstr "Valuutakursi kanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__exclude_from_invoice_tab
msgid "Exclude From Invoice Tab"
msgstr "Välistatud arve vahekaardilt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group__excluded_journal_ids
msgid "Excluded Journals"
msgstr "Välistatud andmikud"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr "Eeldab kontoplaani"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__expense
#: model:ir.model.fields.selection,name:account.selection__account_accrual_accounting_wizard__account_type__expense
#, python-format
msgid "Expense"
msgstr "Kulu"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product__property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_expense_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr "Kulukonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_id
msgid "Expense Account on Product Template"
msgstr "Tootemalli kulukonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__expense_accrual_account
#: model:ir.model.fields,field_description:account.field_res_company__expense_accrual_account_id
msgid "Expense Accrual Account"
msgstr "Kulude juurdekasvu konto"

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Expenses"
msgstr "Kulud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__reference
msgid "External Reference"
msgstr "Väline viide"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Välislink"

#. module: account
#: model:account.incoterms,name:account.incoterm_FAS
msgid "FREE ALONGSIDE SHIP"
msgstr "FRANKO LAEVA KÕRVAL"

#. module: account
#: model:account.incoterms,name:account.incoterm_FCA
msgid "FREE CARRIER"
msgstr "TASUTA TARNE"

#. module: account
#: model:account.incoterms,name:account.incoterm_FOB
msgid "FREE ON BOARD"
msgstr "FRANKOSIHTSADAM"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__factor
msgid "Factor Ratio"
msgstr "Teguri suhe"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__factor
msgid ""
"Factor to apply on the account move lines generated from this repartition "
"line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__factor_percent
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__factor_percent
msgid ""
"Factor to apply on the account move lines generated from this repartition "
"line, in percents"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr "Lemmikud"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__2
msgid "February"
msgstr "Veebruar"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__state_ids
msgid "Federal States"
msgstr "Maakond"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "Filtreeri konto, sildi, partneri, koguse jm põjal"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__general_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr "Finantskonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__tag_ids
msgid "Financial Tags"
msgstr "Finantside märked"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "First Entry"
msgstr "Esmane kanne"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "First Hash"
msgstr "Esmane räsi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr "Finantsinformatsioon"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr "Maksukeskkond"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__position_id
msgid "Fiscal Mapping"
msgstr "Maksukeskkonna kaardistamine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr "Eelarveperioodid"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_move__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_position_id
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr "Finantspositsioon"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__name
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr "Finantspositsiooni mall"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr "Finantspositsioonid"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "Majandusaasta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Majandusaasta 2018"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Fiscal Year End"
msgstr "Majandusaasta lõpp"

#. module: account
#: model:ir.actions.act_window,name:account.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_fiscal_year
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Fiscal Years"
msgstr "Majandusaasta"

#. module: account
#: model:ir.model.fields,help:account.field_account_move__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices. The default value comes from the "
"customer."
msgstr ""
"Eelarvepositsioone kasutatakse et kohandada maksud ja kontod antud klientide"
" või müügi orderite/arvete tarvis. Vaikimisi väärtus tuleb kliendilt."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Aruandeaasta viimane päev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Aruandeaasta viimane kuu"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__second_amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__second_amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__fixed
msgid "Fixed"
msgstr "Fikseeritud"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__value__fixed
msgid "Fixed Amount"
msgstr "Kindel summa"

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr "Põhivara"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""
"Fikseeritud negatiivne summa arvestatakse kui deebet ja positiivne summa kui"
" kreedit."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_channel_ids
msgid "Followers (Channels)"
msgstr "Jälgijad (kanalid)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad (partnerid)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr "Laekuvate maksete jaoks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__outbound_payment_method_ids
msgid "For Outgoing Payments"
msgstr "Väljuvate maksete jaoks"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value_amount
msgid "For percent enter a ratio between 0-100."
msgstr "Protsendi jaoks sisestage väärtus vahemikus 0-100."

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_non_accountable_fields_null
msgid ""
"Forbidden unit price, account and quantity on non-accountable invoice line"
msgstr "Keelatud ühiku hind, konto ja kogus mittevastaval  arve real"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Force the second tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_tax_included
msgid "Force the tax to be managed as a price included tax."
msgstr "Sunni maksu haldama kui hinnas sisaldub maks."

#. module: account
#: model:ir.model.fields,help:account.field_account_account__currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr "Kõik selle konto kanded on sunnitud kasutama konto valuutat."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr "Kõik selle konto kanded on sunnitud kasutama seda teist valuutat."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__always_set_currency_id
msgid "Foreign Currency"
msgstr "Välisvaluuta"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:0
#: code:addons/account/report/account_journal.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Vormi sisu on puudu, ei saa trükkida aruannet."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__formula
msgid "Formula"
msgstr "Valem"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_type__none
msgid "Free"
msgstr "Vaba"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__email_from
msgid "From"
msgstr "Lähtekoht"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr "Võlgade kontolt"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr "Nõuete kontolt"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__amount_type__regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__second_amount_type__regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__amount_type__regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__second_amount_type__regex
msgid "From label"
msgstr "Sildilt"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "Nüüdsest võiksite:"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"See aruanne annab ülevaate tarnijate poolt arveldatavatest summadest. "
"Otsingu tööriistaga saab aruande personaliseerida vastavalt vajadusele."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"See aruanne annab ülevaate teie poolt arveldatavatest summadest. Otsingu "
"tööriistaga saab aruande personaliseerida vastavalt vajadusele."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "From: "
msgstr "Lähtekoht"

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__full_reconcile_id
msgid "Full Reconcile"
msgstr "Täielik sobitamine"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_reversal__refund_method__cancel
msgid "Full Refund"
msgstr "Täies ulatuses tagasimakse"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_reversal__refund_method__modify
msgid "Full refund and new draft invoice"
msgstr "Täies ulatuses tagasimakse ja uus arve mustand"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Future Activities"
msgstr "Tulevased tegevused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr "Vahetuskursi muutuste kasumi konto"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_ledger_general
#: model:ir.ui.menu,name:account.menu_action_account_moves_ledger_general
msgid "General Ledger"
msgstr "Pearaamat"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr "Loo kanded"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Generated Documents"
msgstr "Moodustatud dokumendid"

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Generated Entries"
msgstr "Loodud kirjed"

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr "Üldised väljavõtted"

#. module: account
#: model:ir.model,name:account.model_report_account_report_hash_integrity
msgid "Get hash integrity result as PDF."
msgstr "Loo räsi terviklikkuse tulemused PDF-ina."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr "Hoiata, kui teed kindlatele klientidele arveid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""
"Saa pangaväljavõtted automatselt imporditud süsteemi iga 4 tunni järel "
"kasutades selleks Yodlee või Plaid teenust. Kui teenus paigaldatud, seadista"
" pangakontol 'Pangaliides' pangaga sünkroniseeritavaks. Seejärel sisesta "
"online kontol oma pangakonto atribuudid."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr "Annab järjekorra kui kuvada nimekiri pangaväljavõtte ridadest."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr "Annab järjekorra kui kuvada nimekiri maksetingimuse ridadest."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Mine pangaväljavõtetesse"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/company.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Mine seadistuste paneelile"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Go to the journal configuration"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__trust__good
msgid "Good Debtor"
msgstr "Hea võlgnik"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Hea töö!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__group_id
#: model:ir.model.fields,field_description:account.field_account_account_template__group_id
msgid "Group"
msgstr "Grupp"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_report_line_search
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr "Rühmitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_register__group_payment
msgid "Group Payment"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__group
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__group
msgid "Group of Taxes"
msgstr "Käibemaksu grupp"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group payments into a single batch to ease the reconciliation process"
msgstr "Grupeerige maksed ühte partiisse lihtsustamaks sobitamist"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__rounding_method__half-up
msgid "HALF-UP"
msgstr "Alates viiest üles"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Sisaldab raamatupidamise kandeid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__has_invoices
msgid "Has Invoices"
msgstr "On arveid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__has_reconciled_entries
msgid "Has Reconciled Entries"
msgstr "Omab kooskõlastatud kirjeid"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "On mittesobitatud kandeid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__qr_code_valid
msgid "Has all required arguments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Hash Integrity Result -"
msgstr "Räsi terviklikkuse tulemus - "

#. module: account
#: model:ir.actions.report,name:account.action_report_account_hash_integrity
msgid "Hash integrity result PDF"
msgstr "Räsi terviklikkuse tulemuse PDF"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__hide_payment_method
msgid "Hide Payment Method"
msgstr "Peida maksemeetod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr "Peida kassapõhine võimalus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "History"
msgstr "Ajalugu"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr "Kuidas on maksud tellimustel ja arvetel kokku arvutatud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__id
#: model:ir.model.fields,field_description:account.field_account_account_tag__id
#: model:ir.model.fields,field_description:account.field_account_account_template__id
#: model:ir.model.fields,field_description:account.field_account_account_type__id
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__id
#: model:ir.model.fields,field_description:account.field_account_chart_template__id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__id
#: model:ir.model.fields,field_description:account.field_account_common_report__id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_group__id
#: model:ir.model.fields,field_description:account.field_account_incoterms__id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__id
#: model:ir.model.fields,field_description:account.field_account_invoice_send__id
#: model:ir.model.fields,field_description:account.field_account_journal__id
#: model:ir.model.fields,field_description:account.field_account_journal_group__id
#: model:ir.model.fields,field_description:account.field_account_move__id
#: model:ir.model.fields,field_description:account.field_account_move_line__id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_payment__id
#: model:ir.model.fields,field_description:account.field_account_payment_method__id
#: model:ir.model.fields,field_description:account.field_account_payment_register__id
#: model:ir.model.fields,field_description:account.field_account_payment_term__id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__id
#: model:ir.model.fields,field_description:account.field_account_print_journal__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__id
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__id
#: model:ir.model.fields,field_description:account.field_account_root__id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__id
#: model:ir.model.fields,field_description:account.field_account_tax__id
#: model:ir.model.fields,field_description:account.field_account_tax_group__id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__id
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__id
#: model:ir.model.fields,field_description:account.field_account_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_unreconcile__id
#: model:ir.model.fields,field_description:account.field_cash_box_out__id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:account.field_report_account_report_hash_integrity__id
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__id
#: model:ir.model.fields,field_description:account.field_validate_account_move__id
msgid "ID"
msgstr "ID"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "INV"
msgstr "INV"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_exception_icon
#: model:ir.model.fields,field_description:account.field_account_move__activity_exception_icon
#: model:ir.model.fields,field_description:account.field_account_payment__activity_exception_icon
msgid "Icon"
msgstr "Ikoon"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__activity_exception_icon
#: model:ir.model.fields,help:account.field_account_move__activity_exception_icon
#: model:ir.model.fields,help:account.field_account_payment__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr ""
"Kui summa valuuta on määratletud, siis summa peab samuti olema määratletud."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,help:account.field_account_journal__message_needaction
#: model:ir.model.fields,help:account.field_account_journal__message_unread
#: model:ir.model.fields,help:account.field_account_move__message_needaction
#: model:ir.model.fields,help:account.field_account_move__message_unread
#: model:ir.model.fields,help:account.field_account_payment__message_needaction
#: model:ir.model.fields,help:account.field_account_payment__message_unread
msgid "If checked, new messages require your attention."
msgstr "Kui kontrollitud, siis uued sõnumid nõuavad Su tähelepanu."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_sms_error
#: model:ir.model.fields,help:account.field_account_journal__message_has_error
#: model:ir.model.fields,help:account.field_account_journal__message_has_sms_error
#: model:ir.model.fields,help:account.field_account_move__message_has_error
#: model:ir.model.fields,help:account.field_account_move__message_has_sms_error
#: model:ir.model.fields,help:account.field_account_payment__message_has_error
#: model:ir.model.fields,help:account.field_account_payment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningate sõnumitel saatmiserror."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr "Kui märgitud, siis uus kontoplaan ei sisalda seda vaikimisi."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal__journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr "Kui tühi, kasutab andmikku andmiku kande tagasipööramiseks."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template__include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""
"Kui valitud, siis maksud, mis on arvutatud peale seda maksu, arvutatakse "
"maksud hinna sees põhimõttel."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally be booked is already closed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__analytic
#: model:ir.model.fields,help:account.field_account_tax_template__analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""
"Kui valitud, siis sellelt maksult arvutatud summa määratakse samale "
"analüütilisele kontole nagu on arve real (kui neid on)."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"If tags are defined for a tax report line, only two are allowed on it: a "
"positive and a negative one."
msgstr ""
"Kui märked seatakse maksu raporti reale, siis on lubatud vaid positiivsel ja"
" negatiivsel kujul."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term__active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""
"Kui aktiivne väli on väär, siis see võimaldab teil peita maksetähtaega seda "
"kustutamata."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__to_check
msgid ""
"If this checkbox is ticked, it means that the user was not sure of all the "
"related informations at the time of the creation of the move and that the "
"move needs to be checked again."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__auto_post
msgid ""
"If this checkbox is ticked, this entry will be automatically posted at its "
"date."
msgstr ""
"Kui siia on märgitud linnuke, siis kanne konteeritakse automaatselt oma "
"kuupäevaga."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__restrict_mode_hash_table
#: model:ir.model.fields,help:account.field_account_move__restrict_mode_hash_table
msgid ""
"If ticked, the accounting entry or invoice receives a hash as soon as it is "
"posted and cannot be modified anymore."
msgstr ""
"Märgituna saab raamatupidamiskanne või arve märke kohe konteerimisel ning "
"pole seejärel enam muudetav."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""
"Kui sa valid selle, siis saad makseid vastu võtta SEPA Direct Debit mandaadi"
" alusel."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr "Kui sa valid selle, siis saad registreerida maksed SEPA formaadis."

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"If you have not installed a chart of account, please install one first.<br>"
msgstr "Kui sul pole paigaldatud kontoskeemi, siis palun paigalda see esmalt."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""
"Kui sa võtad tehingud sobitamisest välja, pead sa üle vaatama ka kõik "
"tegevused, mis on nende tehingutega seotud, sest need ei ole tühistatud."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"If you want to use \"Off-Balance Sheet\" accounts, all the accounts of the "
"journal entry must be of this type"
msgstr ""
"Kui soovid kasutada bilansiväliseid kontosid, siis peavad kõik registri "
"kannete kontod olema sama tüüpi."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""
"Kui te müüte digitaalseid tooteid klientidele EL-s, peate käibemaksu "
"rakendama vastavalt kliendi asukohariigile. See reegel kehtib vaatamata teie"
" asukohale. Digitaalsed tooted on defineeritud kui raadiosaate, "
"telekommunikatsioon ja teenused, mis on elektrooniliselt edastatud. Veebi "
"teel saadetud kinkekaardid ei kuulu selle definitsiooni alla."

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr "Kohene makse"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr "Impordi .qif failid"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr "Impordi  .csv formaadis"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr "Impordi .ofx formaadis"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "Impordi CAMT.053 formaadis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr "Impordi pangaväljavõtted automaatselt"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr "Impordi pangaväljavõtted CAMT.053 formaadis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr "Impordi pangaväljavõtted CSV formaadis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr "Impordi pangaväljavõtted OFX formaadis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr "Impordi pangaväljavõtted QIF formaadis"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__invoice_payment_state__in_payment
#: model:ir.model.fields.selection,name:account.selection__account_move__invoice_payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "In Payment"
msgstr "Makses"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""
"Selleks, et pangaväljavõtte rida kustutada, pead selle esmalt tühistama, et "
"seotud andmiku kanderead kustutada."

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""
"Selleks, et pangaväljavõtet kustutada, pead selle esmalt tühistama, et "
"seotud andmiku kanderead kustutada."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr "Mitteaktiivne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__inalterable_hash
msgid "Inalterability Hash"
msgstr "Muutmatuse räsi"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Inalterability check"
msgstr "Mittemuudetavuse kontroll"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__secure_sequence_number
msgid "Inalteralbility No Gap Sequence #"
msgstr "Muutmatuse tühimiketa järjestus #"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_method__payment_type__inbound
msgid "Inbound"
msgstr "Sissetulev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__analytic
msgid "Include in Analytic Cost"
msgstr "Arvesta analüütilises kulus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template__price_include
msgid "Included in Price"
msgstr "Sisaldub hinnas"

#. module: account
#. openerp-web
#: model:account.account.type,name:account.data_account_type_revenue
#: code:addons/account/static/src/js/account_selection.js:0
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__income
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Income"
msgstr "Tulu"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product__property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_income_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr "Tulukonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_id
msgid "Income Account on Product Template"
msgstr "Toote malli tulukonto"

#. module: account
#: code:addons/account/wizard/setup_wizards.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"Väär majandusaasta kuupäev: kuupäev jääb antud kuu vahemikust väljapoole. "
"Kuu: %s; Kuupäev: %s"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_incoterm_id
msgid "Incoterm"
msgstr "Tarnetingimus"

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__code
msgid "Incoterm Standard Code"
msgstr "Rahvusvaheliste tarnetingimuste standardkood"

#. module: account
#: model:ir.actions.act_window,name:account.action_incoterms_tree
#: model:ir.model,name:account.model_account_incoterms
#: model:ir.ui.menu,name:account.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_form
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.view_incoterms_tree
msgid "Incoterms"
msgstr "Rahvusvahelised tarnetingimused"

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-"
"the-art transportation practices."
msgstr ""
"Rahvusvahelised tarnetingimused on müügi tingimused. Neid kasutatakse "
"transaktsiooni kulude ja vastutuse jagamiseks ostja ja müüja vahel ning "
"näitab, et ettevõtted rakendavad kõrgetasemelist transpordi praktikaid."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid ""
"Incoterms are used to divide transaction costs and responsibilities between "
"buyer and seller."
msgstr ""
"Rahvusvahelised Kaubandustingimused on kasutusel jagamaks kulusid ja "
"vastutusi ostja ning müüja vahel."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_line_id
msgid "Indicates that this journal item is a tax line"
msgstr "Näitab et see registri kanne on maksu rida"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Info"
msgstr "Info"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr "Informatsioon"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__parent_id
msgid "Initial thread message."
msgstr "Algse seose sõnum."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr "Sisendkonto varude hindamiseks"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr "Sisesta oma tingimused siia"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr "Paigalda rohkem mooduleid"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr "Pankadevaheliste ülekannete konto"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""
"Vahekonto, mida kasutatakse kui maksed on teel ühelt rahakontolt teisele"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_group
#: model:ir.model.fields,field_description:account.field_account_account_type__internal_group
msgid "Internal Group"
msgstr "Sisemine Grupp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__note
msgid "Internal Notes"
msgstr "Ettevõttesisesed märkmed"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_type__transfer
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr "Ühendusesisene ülekanne"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_payments_transfer
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfers"
msgstr "Sisemised laosiirded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_type
#: model:ir.model.fields,field_description:account.field_account_move_line__account_internal_type
msgid "Internal Type"
msgstr "Sisemine laosiire"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr "Ettevõttesisesed märkmed..."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_incoterm_id
#: model:ir.model.fields,help:account.field_res_company__incoterm_id
#: model:ir.model.fields,help:account.field_res_config_settings__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Rahvusvahelised kaubanduslikud tingimused on rahvusvaheliste tehingutega "
"seotud eelnevalt määratletud ärimõisted."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_intrastat
msgid "Intrastat"
msgstr "Intrastat"

#. module: account
#: code:addons/account/models/partner.py:0
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr "Mittekorrektne \"Postiindeksi vahemik\", palun seadista korralikult."

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Invalid fiscal year last day"
msgstr "Kehtetu majandusaasta viimane päev"

#. module: account
#: code:addons/account/models/account_move.py:0
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users__invoice_warn
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Invoice"
msgstr "Arve"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__name
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr "Arve nr"

#. module: account
#: code:addons/account/models/account_move.py:0
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
#, python-format
msgid "Invoice Created"
msgstr "Arve on loodud"

#. module: account
#: code:addons/account/controllers/portal.py:0
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_date
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Invoice Date"
msgstr "Arve kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_filter_type_domain
msgid "Invoice Filter Type Domain"
msgstr "Arve filtri tüübi valdkond"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_has_matching_suspense_amount
msgid "Invoice Has Matching Suspense Amount"
msgstr "Arve vastab vahesummale"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_has_outstanding
msgid "Invoice Has Outstanding"
msgstr "Arvel on maksmata"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Invoice Layout"
msgstr "Arve kujundus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Invoice Lines"
msgstr "Arve read"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_payment
msgid "Invoice Online Payment"
msgstr "Arve veebimakse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_outstanding_credits_debits_widget
msgid "Invoice Outstanding Credits Debits Widget"
msgstr "Arve tasumata kreedit-deebet vidin"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_partner_display_name
msgid "Invoice Partner Display Name"
msgstr "Arve partneri kuvanimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_partner_icon
msgid "Invoice Partner Icon"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payments_widget
msgid "Invoice Payments Widget"
msgstr "Arve tasumise vidin"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_sent
msgid "Invoice Sent"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__state
msgid "Invoice Status"
msgstr "Arve staatus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__invoice_tax_id
msgid "Invoice Tax"
msgstr "Arve käibemaks"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartition should each contain exactly one line for"
" the base."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartition should have at least one tax repartition"
" line."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartition should have the same number of lines."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartitions should match (same percentages, in the "
"same order)."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_line_ids
msgid "Invoice lines"
msgstr "Arverida"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr "Arve on makstud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Invoice send & Print"
msgstr "Arve saadetud ja prinditud"

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr "Kinnitatud arve"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_date
msgid "Invoice/Bill Date"
msgstr "Arve kuupäev"

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.name or '').replace('/','_')}${object.state == 'draft' and"
" '_draft' or ''}"
msgstr ""
"Arve_${(object.name või '').asenda('/','_')}${object.state == 'draft' ja "
"'_draft' või ''}"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr "Arveldatud"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_out_invoice_type
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_invoice_send__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_payment__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_payment_register__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users__invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.menu,name:account.menu_action_move_out_invoice_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_graph
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Invoices"
msgstr "Arved"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
msgid "Invoices &amp; Bills"
msgstr "Arved &amp; "

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr "Arvete analüüs"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Invoices Matching Rule"
msgstr "Arvete sobitamise reegel"

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Arvete statistika"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Invoices owed to you"
msgstr "Tasumata müügiarved"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to Validate"
msgstr "Arved kinnitamiseks"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__reconciled_invoice_ids
msgid "Invoices whose journal items have been reconciled with these payments."
msgstr "Arved millede registri kanded on kinnitatud nende maksetega."

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr "Makseta müügiarved"

#. module: account
#: model:ir.ui.menu,name:account.account_invoicing_menu
#: model:ir.ui.menu,name:account.menu_finance
#: model_terms:ir.ui.view,arch_db:account.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Raamatupidamine"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_amount__between
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_amount__between
msgid "Is Between"
msgstr "on vahemikus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_journal__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_move__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_payment__message_is_follower
msgid "Is Follower"
msgstr "Jälgija"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_amount__greater
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_amount__greater
msgid "Is Greater Than"
msgstr "on suurem kui"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_amount__lower
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_amount__lower
msgid "Is Lower Than"
msgstr "on vähem kui"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__printed
msgid "Is Printed"
msgstr "Prinditud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__is_rounding_line
msgid "Is Rounding Line"
msgstr "On ümardamise rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__is_difference_zero
msgid "Is zero"
msgstr "On null"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company__income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr "See toimib kui vaikimisi konto kreeditsummale"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company__expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr "See toimib kui vaikimisi konto deebetsummale"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__alias_name
msgid "It creates draft invoices and bills by sending an email."
msgstr "Kui saadate e-kirja, loob see arvete mustandid. "

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr "See näitab, et arve on saadetud. "

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr "On kohustuslik määrata konto ja andmik, et koostada mahakandmine."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""
"Ei ole lubatud kustutada makset, mis on loonud andmiku kande, sest see "
"tekitaks tühimiku numeratsiooni. Tuleks taasluua andmiku kanne ja siis see "
"tühistada tagasipööramise kaudu."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"It seems that the taxes have been modified since the creation of the journal"
" entry. You should create the credit note manually instead."
msgstr ""
"Paistab et maksusid on muudetud peale registrisse sisestust. Sul tuleks nüüd"
" luua kreedit-arve käsitsi."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "rida"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__1
msgid "January"
msgstr "Jaanuar"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__journal_id
#: model:ir.model.fields,field_description:account.field_account_move__journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_payment__journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_register__journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__linked_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__journal_id
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr "Andmik"

#. module: account
#. openerp-web
#: code:addons/account/models/account_bank_statement.py:0
#: code:addons/account/static/src/js/mail_activity.js:0
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr "Andmike kanded"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Date"
msgstr "Registri kanded kuupäevaliselt"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/mail_activity.js:0
#: model:ir.model.fields,field_description:account.field_account_move_line__move_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__move_id
#: model:ir.model.fields.selection,name:account.selection__account_move__type__entry
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,field_description:account.field_account_payment__move_name
msgid "Journal Entry Name"
msgstr "Andmiku kande nimetus"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr "Andmiku kande number"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group__name
msgid "Journal Group"
msgstr "Andmiku grupp"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_group_list
#: model:ir.model.fields,field_description:account.field_account_journal__journal_group_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_group_list
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_group_form
msgid "Journal Groups"
msgstr "Andmike Grupid"

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.model.fields,field_description:account.field_account_analytic_line__move_id
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__label
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr "Andmiku kanderea silt"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: code:addons/account/models/reconciliation_widget.py:0
#: model:ir.actions.act_window,name:account.action_account_moves_all
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move__line_ids
#: model:ir.model.fields,field_description:account.field_res_partner__journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users__journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_pivot
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
#, python-format
msgid "Journal Items"
msgstr "Andmike kanderead"

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
msgid "Journal Items for Tax Audit"
msgstr "Andmiku kanderead maksuauditi jaoks"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Sobitamist vajavad andmike kanderead"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__name
msgid "Journal Name"
msgstr "Andmiku nimi"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_group_list
msgid "Journal group are used in reporting to display relevant data together."
msgstr ""
"Andmike gruppe kasutatakse aruandluses asjakohaste andmete koos kuvamiseks."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr "Andmike kanderead, kus vastavuse number on määramata"

#. module: account
#: model:ir.model.fields,help:account.field_account_accrual_accounting_wizard__journal_id
#: model:ir.model.fields,help:account.field_res_company__accrual_default_journal_id
msgid "Journal used by default for moving the period of an entry"
msgstr "Register vaikimisi kasutamiseks kannete perioodi nihutamisel "

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr "Andmik, kuhu selle ettevõte avamiskanded on postitatud."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_currency_id
msgid "Journal's Currency"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_journals
msgid "Journals"
msgstr "Andmikud"

#. module: account
#: model:ir.actions.report,name:account.action_report_journal
msgid "Journals Audit"
msgstr "Andmike audit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__json_activity_data
msgid "Json Activity Data"
msgstr "Json tegevuste andmed"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__7
msgid "July"
msgstr "Juuli"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__6
msgid "June"
msgstr "Juuni"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_invoice_layout_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sale_tax_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sample_invoice_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_bank_data_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_coa_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_fy_data_state__just_done
msgid "Just done"
msgstr "Just tehtud"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__reason
msgid "Justification"
msgstr "Põhjendus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Kanban töölaud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Kanban Töölaua graafik"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr "Jäta tühjaks, kui sa ei soovi kontrolli"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_difference_handling__open
msgid "Keep open"
msgstr "Hoia avatuna"

#. module: account
#: model:ir.model.fields,help:account.field_product_product__property_account_income_id
#: model:ir.model.fields,help:account.field_product_template__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""
"Jäta see väli tühjaks, et kasutada vaikimisi väärtust tootekategoorias."

#. module: account
#: model:ir.model.fields,help:account.field_product_product__property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""
"Hoia see väli tühjana et kasutada vaikeväärtust toodete kategooriast. Kui "
"anglosaksi raamatupidamine automaatse takseerimise meetod on määratud, siis "
"kasutatakse kulude kontot toodete kategoorias."

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue_value
msgid "Kpi Account Total Revenue Value"
msgstr "KPI konto kogutulu väärtus"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__name
#: model:ir.model.fields,field_description:account.field_account_move_line__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#, python-format
msgid "Label"
msgstr "Silt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label_param
msgid "Label Parameter"
msgstr "Sildi väärtus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__description
msgid "Label on Invoices"
msgstr "Arve sildid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Last Entry"
msgstr "Viimane kanne"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Last Hash"
msgstr "Viimane räsi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account____last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag____last_update
#: model:ir.model.fields,field_description:account.field_account_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_account_type____last_update
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding____last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line____last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report____last_update
#: model:ir.model.fields,field_description:account.field_account_common_report____last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_group____last_update
#: model:ir.model.fields,field_description:account.field_account_incoterms____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_send____last_update
#: model:ir.model.fields,field_description:account.field_account_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_journal_group____last_update
#: model:ir.model.fields,field_description:account.field_account_move____last_update
#: model:ir.model.fields,field_description:account.field_account_move_line____last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal____last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_payment____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_register____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line____last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template____last_update
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget____last_update
#: model:ir.model.fields,field_description:account.field_account_root____last_update
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config____last_update
#: model:ir.model.fields,field_description:account.field_account_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report_line____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile____last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_hash_integrity____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal____last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard____last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud (millal)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Viimane sobitamine:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__write_uid
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_move__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_register__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud (kelle poolt)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_date
#: model:ir.model.fields,field_description:account.field_account_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_account_type__write_date
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_date
#: model:ir.model.fields,field_description:account.field_account_common_report__write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_group__write_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_date
#: model:ir.model.fields,field_description:account.field_account_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_journal_group__write_date
#: model:ir.model.fields,field_description:account.field_account_move__write_date
#: model:ir.model.fields,field_description:account.field_account_move_line__write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_payment__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_register__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_date
#: model:ir.model.fields,field_description:account.field_account_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud (millal)"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"Viimane arvete ja maksete sobitamise aeg selle partneriga. See on määratud "
"siis kui, ei ole mittesobitatud deebet- ja kreeditkandeid või kui vajutad "
"\"Tehtud\" nuppu."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Late Activities"
msgstr "Hilinenud tegevused"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Viimane arvete ja maksete sobitamise kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__layout
msgid "Layout"
msgstr "Paigutus"

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_ledgers
msgid "Ledgers"
msgstr "Kontoaruanded"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr "Ametlikud märkmed..."

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Ametlikud märkmed, mis tuleb printida arvetele."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Less Payment"
msgstr "Vähem makse"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr "Luba klientidel maksta arveid veebis"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#, python-format
msgid "Liabilities"
msgstr "Kohustused"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__liability
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Liability"
msgstr "Kohustus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__nbr_lines
msgid "Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "Maksude kuvamine ridadel"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Line subtotals tax display"
msgstr "Rea vahekokkuvõtete maksu näit"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Lines from \"Off-Balance Sheet\" accounts cannot be reconciled"
msgstr "Ridu \"bilansivälistelt\" kontodelt ei saa kinnitada."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__children_line_ids
msgid "Lines that should be rendered as children of this one"
msgstr "Read mis peaks olema renderdatud käesoleva tütardena"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__liquidity
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr "Likviidsus"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Liquidity Transfer"
msgstr "Likviidsuse kanne"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr "Nimekiri kõigist maksudest, mis tuleb viisardi poolt paigaldada"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more"
msgstr "Lae rohkem"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Laadi rohkem ... ("

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_lock_date
msgid "Lock Date"
msgstr "Lukustamise kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Sulgemise kuupäev kõikidele kasutajatele v.a. Nõustajale"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__restrict_mode_hash_table
#: model:ir.model.fields,field_description:account.field_account_move__restrict_mode_hash_table
msgid "Lock Posted Entries with Hash"
msgstr "Lukusta kinnitatud kanded räsina"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_log
msgid "Log an Internal Note"
msgstr "Lisa märkus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Looks great!"
msgstr "Tundub hea!"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Loss"
msgstr "Kahjum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__loss_account_id
msgid "Loss Account"
msgstr "Kahjukonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr "Vahetuskursi kahjumi konto"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "MISC"
msgstr "MUU"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Postiteenuste tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mailing_list_ids
msgid "Mailing List"
msgstr "Meililist"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_journal__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_move__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_payment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Peamine manus"

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Ettevõtte põhivaluuta. "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr "Ettevõtte põhivaluuta"

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr "Juhtkond"

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
msgid "Manual"
msgstr "Käsitsi"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Käsitsi tegevused"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"Manuaalne: Võta vastu sularaha makseid.\n"
"Elektrooniline: Võta vastu makseid läbi maksete vahendajate, kasutades selleks kliendi poolt salvestatud krediitkaarti andmeid."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_register__payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit, module account_batch_payment must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""
"Käsitsi: Saa tasutud sularahas, tšekiga või mõnel muul viisil väljaspool Odoo'd\n"
"Elektroonselt: Saa tasutud automaatselt läbi makse nõudja, küsides ülekannet kliendi salvestatud kaardile, kui ostad või registreerid võrgus (makse žetoon).\n"
"Tšekk: Tasu arve tšekiga ja trüki see Odoo's välja.\n"
"Partiihoius: Kogu kimpu mitmed kliendi tšekid korraga, luues deposiidi kimbu edastamaks see oma panka. tehes pangakonto väljavõtet Odoo's, soovitatakse sul kinnitada ülekanne deposiidi kimbuga. Lubamaks deposiidi kimpu tuleb paigaldada moodul account_batch_payment.\n"
"SEPA krediidimakse: tasu arve SEPA krediidimakse toimikus mille pangale oled esitanud. Võimaldamaks SEPA krediidimakset tuleb paigaldada moodul account_sepa."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"Manuaalne: Maksa arveid sularahas või muudel meetoditel väljaspool Odoo'd.\n"
"Tšekk: Maksa arveid tšekkidega ja prindi need Odoo'st.\n"
"SEPA Credit Transfer: Maksa arved SEPA maksefailiga laadides selle üles panka."

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__rule_type__writeoff_button
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__rule_type__writeoff_button
msgid "Manually create a write-off on clicked button."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__3
msgid "March"
msgstr "Märts"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr "Kasumlikkuse analüüs"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_difference_handling__reconcile
msgid "Mark invoice as fully paid"
msgstr "Märgi arve täielikult makstuks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_id
msgid "Mass Mailing"
msgstr "E-posti turundus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__campaign_id
msgid "Mass Mailing Campaign"
msgstr "Masspostituste kampaania"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_label__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_note__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_transaction_type__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_label__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_note__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_transaction_type__match_regex
msgid "Match Regex"
msgstr "Sobita regulaarne väljend"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__rule_type__invoice_matching
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__rule_type__invoice_matching
msgid "Match existing invoices/bills."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr "Sobitage kannetega, mis pole pärit laekuvatelt/makstavatelt kontodelt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_credit_ids
msgid "Matched Credits"
msgstr "Sobitatud kreeditid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_debit_ids
msgid "Matched Debits"
msgstr "Sobitatud deebetid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__reconciled_line_ids
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr "Sobitatud andmike kanderead"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Matching"
msgstr "Sobitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__full_reconcile_id
msgid "Matching #"
msgstr "Sobitamine #"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__max_date
msgid "Max Date of Matched Lines"
msgstr "Maksimaalne kuupäev sobitatud ridadel"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__5
msgid "May"
msgstr "Mai"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__communication
msgid "Memo"
msgstr "Märge"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Memo:"
msgstr "Märge:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,field_description:account.field_account_journal__message_has_error
#: model:ir.model.fields,field_description:account.field_account_move__message_has_error
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi edastamise viga"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__record_name
msgid "Message Record Name"
msgstr "Sõnumi salvestusnimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users__invoice_warn_msg
msgid "Message for Invoice"
msgstr "Sõnum arvele"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Sõnumi viis: email sõnumiks, teade süsteemi sõnumiks, kommentaar muudeks "
"sõnumiteks nagu kasutaja vastused."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_ids
msgid "Messages"
msgstr "Sõnumid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__minus_report_line_ids
msgid "Minus Report Lines"
msgstr "Miinusraporti read"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_journal_misc
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__general
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_misc
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_miscellaneous
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Miscellaneous"
msgstr "Muu"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "Muu sobitamine"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Miscellaneous Operations"
msgstr "Mitmesugused tegevused"

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_accountable_required_fields
msgid "Missing required account on accountable invoice line."
msgstr "Puudub nõutav konto konteeritava arve real."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Muuda mudeleid"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__strategy__biggest_tax
msgid "Modify tax amount"
msgstr "Muuda maksu summat"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr "Jälgi toodete marginaali arvetel"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__move_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Move"
msgstr "Suuna"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_line_ids
msgid "Move Line"
msgstr "Kande rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_count
msgid "Move Line Count"
msgstr "Kande ridade arv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_reconciled
msgid "Move Reconciled"
msgstr "Kanne sobitatud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__move_type
msgid "Move Type"
msgstr "Liigutamise viis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr "Mitu valuutat"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr "Minu arved"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__name
#: model:ir.model.fields,field_description:account.field_account_account_template__name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__name
#: model:ir.model.fields,field_description:account.field_account_chart_template__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account.field_account_group__name
#: model:ir.model.fields,field_description:account.field_account_incoterms__name
#: model:ir.model.fields,field_description:account.field_account_payment__name
#: model:ir.model.fields,field_description:account.field_account_payment_method__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__name
#: model:ir.model.fields,field_description:account.field_account_root__name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__name
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__name
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr "Nimi"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__record_name
msgid "Name get of the related document."
msgstr "Nime saamine puutuvast dokumendist."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""
"Aruanded on dünaamilised ning võimaldavad kergesti näha,  mis on  numbrite "
"taga"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__tax_negate
msgid "Negate Tax Balance"
msgstr "Tühista maksujääk"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields.selection,name:account.selection__account_bank_statement__state__open
#, python-format
msgid "New"
msgstr "Uus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_name
msgid "New Journal Name"
msgstr "Uue kande nimetus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr "Lisa tehinguid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_date_deadline
#: model:ir.model.fields,field_description:account.field_account_move__activity_date_deadline
#: model:ir.model.fields,field_description:account.field_account_payment__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_summary
#: model:ir.model.fields,field_description:account.field_account_move__activity_summary
#: model:ir.model.fields,field_description:account.field_account_payment__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_type_id
#: model:ir.model.fields,field_description:account.field_account_move__activity_type_id
#: model:ir.model.fields,field_description:account.field_account_payment__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_move__invoice_sequence_number_next
msgid "Next Number"
msgstr "Järgmine number"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_sequence_number_next_prefix
msgid "Next Number Prefix"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__blocked
msgid "No Follow-up"
msgstr "Järelkontroll puudub"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__invoice_warn__no-message
msgid "No Message"
msgstr "Sõnum puudub"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "No attachment was provided"
msgstr "Ei antud manust"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "No default debit and credit account defined on journal %s (ids: %s)."
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid "No move from this reconciliation model"
msgstr ""

#. module: account
#: code:addons/account/models/ir_actions_report.py:0
#, python-format
msgid ""
"No original vendor bills could be found for any of the selected vendor "
"bills."
msgstr "Ühegi valitud tarnija arvetele ei leitud ühtegi algset tarnija arvet."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__no_auto_thread
msgid "No threading for answers"
msgstr "Mitte ühtegi vastuse lõimet"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"Ükski kasutaja ei saa muuta kandeid seoses antud kuupäevale eelneva maksuga"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Mitte ükski kasutaja, kaasaarvatud Nõustaja õigustes, ei saa muuta kontosid "
"peale seda kuupäeva. Kasuta seda näiteks aruandeaasta sulgemiseks."

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__code_digits
msgid "No. of Digits to use for account code"
msgstr "Konto koodi numbrite arv"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr "Põhivara"

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr "Pikaajalised kohustused"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__type_tax_use__none
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__type_tax_use__none
msgid "None"
msgstr "Pole"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__trust__normal
msgid "Normal Debtor"
msgstr "Tavaline võlgnik"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_label__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_note__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_transaction_type__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_label__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_note__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_transaction_type__not_contains
msgid "Not Contains"
msgstr "Ei sisalda"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Not Due"
msgstr "Ei ole võlgnevusi"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__invoice_payment_state__not_paid
#: model:ir.model.fields.selection,name:account.selection__account_move__invoice_payment_state__not_paid
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Not Paid"
msgstr "Maksmata"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_invoice_layout_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sale_tax_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sample_invoice_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_bank_data_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_coa_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_fy_data_state__not_done
msgid "Not done"
msgstr "Ei ole valmis"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_account_template__note
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_note
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_note
#: model:ir.model.fields.selection,name:account.selection__account_move_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Note"
msgstr "Märkused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_note_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_note_param
msgid "Note Parameter"
msgstr "Märkuse omadus"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_refund_type
msgid ""
"Note that the easiest way to create a credit note is to do it directly\n"
"                from the customer invoice."
msgstr ""
"Lihtsaim viis tarnija kreeditarve loomiseks on seda teha otse ostuarvelt."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_refund_type
msgid ""
"Note that the easiest way to create a vendor credit note it to do it "
"directly from the vendor bill."
msgstr ""
"Lihtsaim viis tarnija kreeditarve loomiseks on seda teha otse ostuarvelt."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__note
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr "Märkused"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Ei ole midagi teha!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__notify
msgid "Notify followers"
msgstr "Teavita jälgijaid"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Teavita antud dokumendi jälgijaid (ainult massipostituses)"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__11
msgid "November"
msgstr "November"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__num_journals_without_account
msgid "Num Journals Without Account"
msgstr "Konteerimata kirjete arv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__name
#: model:ir.model.fields,field_description:account.field_account_move__name
#: model:ir.model.fields,field_description:account.field_account_move_line__move_name
msgid "Number"
msgstr "Number"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_journal__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_move__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimingute arv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__days
msgid "Number of Days"
msgstr "Päevade arv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__number_entries
msgid "Number of entries related to this model"
msgstr "Kirjete arv seoses antud mudeliga"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_journal__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_move__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error_counter
msgid "Number of errors"
msgstr "Vigade arv"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_journal__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_move__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_payment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Tegutsemist nõudvate sõnumite arv"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_journal__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_move__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kohaletoimetamise veateatega sõnumite arv"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,help:account.field_account_journal__message_unread_counter
#: model:ir.model.fields,help:account.field_account_move__message_unread_counter
#: model:ir.model.fields,help:account.field_account_payment__message_unread_counter
msgid "Number of unread messages"
msgstr "Lugemata sõnumite arv"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr "OFX import"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__10
msgid "October"
msgstr "Oktoober"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_model__odoo
msgid "Odoo"
msgstr "Odoo"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""
"Odoo lubab sobitada väljavõtte rea sellega seotud müügi- või ostuarve(te)ga."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""
"Odoo lubab sobitada väljavõtte rea sellega seotud müügi- või ostuarvetega."

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_customer
msgid "Odoo helps you easily track all activities related to a customer."
msgstr "Odoo aitab teil hõlpsalt jälgida kõiki klientidega seotud tegevusi."

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_supplier
msgid "Odoo helps you easily track all activities related to a supplier."
msgstr "Odoo aitab teil hõlpsalt jälgida kõiki tarnijatega seotud tegevusi."

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__off_balance
msgid "Off Balance"
msgstr "Bilansiväline"

#. module: account
#: model:account.account.type,name:account.data_account_off_sheet
msgid "Off-Balance Sheet"
msgstr "Bilansiväline"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "On the"
msgstr "Kohta"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""
"Kui paigaldatud, sea 'Pangaliides' pangakonto seadistuses kui 'Faili "
"import'. See lisab impordi teostamiseks nupu raamatupidamise töölauale."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Only a draft payment can be posted."
msgstr "Ainult mustandina makseid saab postitada."

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Only administrators can load a chart of accounts"
msgstr "Ainult administraatorid võivad laadida kontode kaarti."

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Only invoices could be printed."
msgstr "Printida saab vaid arveid"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_register__group_payment
msgid "Only one payment will be created by partner (bank)/ currency."
msgstr "Vaid üks makse luuakse partneri (panga)/valuutas."

#. module: account
#: model:ir.model.fields,help:account.field_res_company__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Ainult Nõustaja õigustes kasutaja saab muuta kontosid peale seda kuupäeva. "
"Kasuta seda näiteks aruandeaasta sees perioodi sulgemiseks."

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__state__posted
#: model_terms:ir.ui.view,arch_db:account.init_accounts_tree
msgid "Open"
msgstr "Avatud"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid "Open Balance"
msgstr "Algseis"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Algsaldo"

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Majandusaasta Algsaldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_date
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_date
msgid "Opening Date"
msgstr "Algsaldode kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_journal_id
msgid "Opening Journal"
msgstr "Algsaldode andmik"

#. module: account
#: code:addons/account/models/company.py:0
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr "Algsaldode kanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_move_posted
msgid "Opening Move Posted"
msgstr "Algsaldo kanded postitatud"

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line__number
msgid "Opening Unit Numbers"
msgstr "Avan üksuse numbrid"

#. module: account
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#, python-format
msgid "Opening balance"
msgstr "Algsaldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_credit
msgid "Opening credit"
msgstr "Algsaldo krediit"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_credit
msgid "Opening credit value for this account."
msgstr "Kreedit-algsaldo väärtus sellel kontol."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_debit
msgid "Opening debit"
msgstr "Algsaldo deebet"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_debit
msgid "Opening debit value for this account."
msgstr "Deebet-algsaldo väärtus sellel kontol."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr "Tegevuste mallid"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number "
"(%s), you cannot reconcile it entirely with existing journal entries "
"otherwise it would make a gap in the numbering. You should book an entry and"
" make a regular revert of it in case you want to cancel it."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Operation not supported"
msgstr "Toiming pole toetatud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Operations"
msgstr "Tegevused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__nocreate
msgid "Optional Create"
msgstr "Valikuline loomine"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__tag_ids
#: model:ir.model.fields,help:account.field_account_account_template__tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr "Valikulised sildid mida võite kasutada kohandatud aruannetes"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__code
msgid "Optional unique code to refer to this line in total formulas"
msgstr "Valikuline ainuomane kood viitamaks sellele reale kõikides valemites"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__option
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr "Seaded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_origin
msgid "Origin"
msgstr "Päritolu"

#. module: account
#: model:ir.actions.report,name:account.action_account_original_vendor_bill
msgid "Original Bills"
msgstr "Algsed arved"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Original Currency"
msgstr "Algne valuuta"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__payment_id
msgid "Originator Payment"
msgstr "Algataja makse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_line_id
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Originator Tax"
msgstr "Algataja käibemaks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_repartition_line_id
msgid "Originator Tax Repartition Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_group_id
msgid "Originator tax group"
msgstr "Algataja maksugrupp"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#, python-format
msgid "Other"
msgstr "Meeldetuletuse lisamine"

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr "Muud tulud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Other Info"
msgstr "Muu info"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_method__payment_type__outbound
msgid "Outbound"
msgstr "Väljuv"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_server_id
msgid "Outgoing mail server"
msgstr "Väljuvate kirjade server"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr "Väljundkonto varude hindamiseks"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Outstanding credits"
msgstr "Sidumata summa"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Outstanding debits"
msgstr "Maksmata deebet"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr "Hilinenud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr "Tasumata arved, mille tähtaeg on möödas"

#. module: account
#: model:ir.ui.menu,name:account.menu_board_journal_1
msgid "Overview"
msgstr "Ülevaade"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr "Pakend"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__invoice_payment_state__paid
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr "Makstud"

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Paid Invoices"
msgstr "Tasutud müügiarved"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr "Makstud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_id
#: model:ir.model.fields,field_description:account.field_account_root__parent_id
msgid "Parent"
msgstr "Ülem"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__parent_id
msgid "Parent Chart Template"
msgstr "Ülemgraafiku mall"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__parent_id
msgid "Parent Line"
msgstr "valdkond"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__parent_id
msgid "Parent Message"
msgstr "Peamine sõnum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_path
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__parent_path
msgid "Parent Path"
msgstr "Emaliin"

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr "Osaline sidumine"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_reversal__refund_method__refund
msgid "Partial Refund"
msgstr "Osaline tagastus"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_id
#: model:ir.model.fields,field_description:account.field_account_move__partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_payment__partner_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__commercial_partner_id
msgid "Partner Company"
msgstr "Partnerettevõte"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__contract_ids
#: model:ir.model.fields,field_description:account.field_res_users__contract_ids
msgid "Partner Contracts"
msgstr "Partnerlepingud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner
msgid "Partner Is Set"
msgstr "Partner on määratud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Partner Is Set & Matches"
msgstr "Partner on seatud & sobib"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_ledger_partner
#: model:ir.ui.menu,name:account.menu_action_account_moves_ledger_partner
msgid "Partner Ledger"
msgstr "Ostu- ja müügireskontro"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_name
msgid "Partner Name"
msgstr "Partneri nimi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__partner_type
msgid "Partner Type"
msgstr "Partneri tüüp"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "Maksta enda"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA Service"
msgstr "Maksa arved ühe vajutusega kasutades Euro SEPA teenust"

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__payable
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Payable"
msgstr "Võlad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_payable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr "Võlgade konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr "Võlgade kontod"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit_limit
#: model:ir.model.fields,field_description:account.field_res_users__debit_limit
msgid "Payable Limit"
msgstr "Võla limiit"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Payables"
msgstr "Võlad ja ettemaksed"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payment_state
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment"
msgstr "Makse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_register__payment_date
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Date"
msgstr "Makse kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference
msgid "Payment Difference"
msgstr "Makse erinevus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference_handling
msgid "Payment Difference Handling"
msgstr "Maksete erinevuste käsitsus"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Payment Info"
msgstr "Makseinfo"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Payment Journal:"
msgstr "Makseandmik"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr "Makse sobitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr "Maksemeetod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_register__payment_method_id
msgid "Payment Method Type"
msgstr "Makseviis"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Payment Method Types"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_payment_method
msgid "Payment Methods"
msgstr "Maksmise meetodid"

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr "Maksekviitung"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "Payment Receipt:"
msgstr "Maksekviitung:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payment_ref
#: model:ir.model.fields,field_description:account.field_account_payment__payment_reference
msgid "Payment Reference"
msgstr "Makse viide"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Payment References"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_payment_state
msgid "Payment Status"
msgstr "Maksestaatus"

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__payment_id
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_search
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr "Maksetingimused"

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr "Maksetingimuse rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method__payment_type
msgid "Payment Type"
msgstr "Makse tüüp"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__post_at__pay_val
msgid "Payment Validation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment term explanation for the customer..."
msgstr "Maksetingimuste selgitus kliendile..."

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr "Maksetingimus: 15 päeva"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_2months
msgid "Payment terms: 2 Months"
msgstr "Maksetingimus: 2 kuud"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_21days
msgid "Payment terms: 21 Days"
msgstr "Maksetingimus: 21 päeva"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_30days
msgid "Payment terms: 30 Days"
msgstr "Maksetingimus 30 päeva"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr "Maksetingimus: 30% ettemaks ja järgmise kuu lõpp"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance_60days
msgid "Payment terms: 30% Now, Balance 60 Days"
msgstr "Makse tingimused: 30% kohe, saldo 60 päeva"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_45days
msgid "Payment terms: 45 Days"
msgstr "Maksetingimus: 45 päeva"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_end_following_month
msgid "Payment terms: End of Following Month"
msgstr "Maksetingimus: Järgmise kuu lõpp"

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr "Maksetingimus: Kohene  makse"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__payment_id
msgid "Payment that created this entry"
msgstr "Makse, mis lõi selle kande"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.menu,name:account.root_payment_menu
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Payments"
msgstr "Maksed"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Maksete sobitamine"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""
"Makseid kasutatakse likviidsuse liikumise registreerimiseks. Te saate neid "
"makseid töödelda omal valikul või paigaldatud lahenduste abil."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "Ilma partnerit valimata ei saa makset sobitada"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__value__percent
msgid "Percent"
msgstr "Protsent"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Percent (%)"
msgstr "Protsent (%)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__percentage
msgid "Percentage"
msgstr "Protsent"

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Percentage must be between 0 and 100"
msgstr "Protsent peab jääma 0 ja 100 vahele"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__percent
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__percent
msgid "Percentage of Price"
msgstr "Protsenti hinnast"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__division
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__division
msgid "Percentage of Price Tax Included"
msgstr "Protsenti koos maksudega hinnast"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__second_amount_type__percentage
msgid "Percentage of amount"
msgstr "Protsenti summast"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__amount_type__percentage
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__second_amount_type__percentage
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__amount_type__percentage
msgid "Percentage of balance"
msgstr "Protsenti saldost"

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "Percentages on the Payment Terms lines must be between 0 and 100."
msgstr "Protsendid maksetingimustes peavad jääma 0 ja 100 vahel."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr "Periood"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_plaid
msgid "Plaid Connector"
msgstr "Plaid ühendus"

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr "Palun veendu, et väli 'Andmik' on seatud pangaväljavõttel"

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr "Palun veendu, et 'Ülekande konto' on seadistatud ettevõttel."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define a sequence on your journal."
msgstr "Palun määrake järjestus oma andmikus."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define an accounting miscellaneous journal in your company"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define an accounting purchase journal in your company"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define an accounting sale journal in your company"
msgstr "Palun määrake oma ettevõtte raamatupidamise müügiandmik "

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a %s Account. This account will be "
"used to record cash difference."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""
"Enne jätkamist paigalda palun kontoplaan või loo muude kannete andmik."

#. module: account
#: code:addons/account/models/account_cash_rounding.py:0
#, python-format
msgid "Please set a positive rounding value."
msgstr ""

#. module: account
#: code:addons/account/models/account_cash_rounding.py:33
#, python-format
msgid "Please set a strictly positive rounding value."
msgstr "Palun määra rangelt positiivne ümardusväärtus."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Please use the following communication for your payment :"
msgstr "Kasutage makse saamiseks järgmist teatist:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__plus_report_line_ids
msgid "Plus Tax Report Lines"
msgstr "Lisamaksu aruande read"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__default_pos_receivable_account_id
msgid "PoS receivable account"
msgstr "PoS deebetkonto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__access_url
msgid "Portal Access URL"
msgstr "Portaali ligipääsu URL"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Post"
msgstr "Postita"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr "Postita kõik kirjed"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__post_at
msgid "Post At"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__auto_post
msgid "Post Automatically"
msgstr "Postita automaatselt"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Post Difference In"
msgstr "Postitatud erinevus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr "Postita andmike kanded"

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Post Payments"
msgstr "Kinnita maksed"

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
msgid "Post entries"
msgstr "Kinnita kanded"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__state__posted
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr "Postitatud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr "Postitatud andmike kanded"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr "Postitatud andmike kanderead"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Posted journal entry must have an unique sequence number per company."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Posting"
msgstr "Postitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company__bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr "Pangakontode prefiks"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr "Sularaha kontode prefiks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr "Pea sularaha kontode prefiks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__transfer_account_code_prefix
msgid "Prefix of the main transfer accounts"
msgstr "Pea ülekannete kontode eesliide"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_code_prefix
msgid "Prefix of the transfer accounts"
msgstr "Ülekannete kontode eesliide"

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
msgid "Prepayments"
msgstr "Ettemaksud"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr "Eelseadistus, et luua andmike kandeid arvete ja maksete sobitamises"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "Eelseadistuse konfigureerimine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Preview"
msgstr "Eelvaade"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Preview as a PDF"
msgstr "PDF eelvaade"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Price"
msgstr "Hind"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_print
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_print
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Print"
msgstr "Prindi"

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr "Prindi raport valuutatulbaga kui valuuta erineb ettevõtte valuutast."

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_print
msgid "Print by default"
msgstr "Vaikimisi printimine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr "Trüki tšekid ja maksa tarnijatele"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Product"
msgstr "Toode"

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_categ_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr "Toote kategooria"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__quantity
msgid "Product Quantity"
msgstr "Toote kogus"

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr "Toote mall"

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
#: model_terms:ir.ui.view,arch_db:account.product_template_view_tree
msgid "Products"
msgstr "Tooted"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Profit"
msgstr "Kasum"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#, python-format
msgid "Profit & Loss"
msgstr "Kasum ja Kahjum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__profit_account_id
msgid "Profit Account"
msgstr "Kasumi konto"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid ""
"Programmation Error: Can't call _get_invoice_matching_query() for different "
"rules than 'invoice_matching'"
msgstr ""
"Programmeerimisviga: ei suuda leida _get_invoice_matching_query() muudeks "
"reegliteks kui 'invoice_matching'"

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid ""
"Programmation Error: Can't call _get_wo_suggestion_query() for different "
"rules than 'writeoff_suggestion'"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr "Seaded"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__purchase
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Purchase"
msgstr "Ost"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__type__in_receipt
msgid "Purchase Receipt"
msgstr "Ostutšekk"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Purchase Receipt Created"
msgstr "Ostutšekk loodud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Purchase Tax"
msgstr "Ostu maks"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_journal_purchase
#: model:ir.model.fields.selection,name:account.selection__account_tax__type_tax_use__purchase
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__type_tax_use__purchase
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_purchase
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Purchases"
msgstr "Ostud"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__formula
msgid ""
"Python expression used to compute the value of a total line. This field is "
"mutually exclusive with tag_name, setting it turns the line to a total line."
" Tax report line codes can be used as variables in this expression to refer "
"to the balance of the corresponding lines in the report. A formula cannot "
"refer to another line using a formula."
msgstr ""
"Pythoni valem arvutamaks kokku-rea väärtust. Antud väli on tag_name-iga "
"vastastikku välistav. Selle määramisel muutub kokku-rida. Maksuraporti rea "
"koode saab kasutada muutujatena käesolevas valemis viitamaks ridade "
"vastavusele raportis. Valem ei saa viidata muule reale mis kasutab valemit."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr "QIF import"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__quantity
msgid "Quantity"
msgstr "Kogus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Quantity:"
msgstr "Kogus:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__reason
#: model:ir.model.fields,field_description:account.field_cash_box_out__name
msgid "Reason"
msgstr "Põhjus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr "Põhjus..."

#. module: account
#: model:ir.actions.act_window,name:account.action_move_in_receipt_type
#: model:ir.actions.act_window,name:account.action_move_out_receipt_type
#: model:ir.ui.menu,name:account.menu_action_move_in_receipt_type
#: model:ir.ui.menu,name:account.menu_action_move_out_receipt_type
msgid "Receipts"
msgstr "Kaup lattu"

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__receivable
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Receivable"
msgstr "Nõuded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_receivable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr "Nõuete konto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr "Nõuete kontod"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Receivables"
msgstr "Nõuded"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_type__inbound
msgid "Receive Money"
msgstr "Võta raha vastu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr "Saaja pangakonto"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Recipients"
msgstr "Saajad"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Recognize on"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__recompute_tax_line
msgid "Recompute Tax Line"
msgstr "Maksurea ümberarvutamine"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.client,name:account.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Sobita"

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "Reconcile Model Template"
msgstr "Kooskõlastamisviisi mall"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__reconciled
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__reconciled
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr "Sobitatud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled Entries"
msgstr "Sobitatud kanded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__reconciled_invoice_ids
msgid "Reconciled Invoices"
msgstr "Kooskõlastatud arved"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__reconciled_invoices_count
msgid "Reconciled Invoices Count"
msgstr ""

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Sobitamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__reconcile_model_id
msgid "Reconciliation Model"
msgstr "Sobitamismudel"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.menu,name:account.action_account_reconcile_model_menu
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr "Sobitamise mudelid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr "Sobitamise  osad"

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "Sobita pangaväljavõtteid"

#. module: account
#: code:addons/account/models/partner.py:0
#, python-format
msgid "Record cannot be deleted. Partner used in Accounting"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr "Salvesta tehinguid välisvaluutas"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Recursion found for tax '%s'."
msgstr "Lähtekoht leitud maksule '%s'"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Viide"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__ref
msgid "Ref."
msgstr "Viide"

#. module: account
#: code:addons/account/controllers/portal.py:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement__name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__ref
#: model:ir.model.fields,field_description:account.field_account_move__ref
#: model:ir.model.fields,field_description:account.field_account_move_line__ref
#, python-format
msgid "Reference"
msgstr "Viide"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr "Viide maksedokumendile. Näiteks tšeki number, faili nimi,  jne."

#. module: account
#: model:ir.actions.act_window,name:account.action_move_in_refund_type
#: model:ir.ui.menu,name:account.menu_action_move_in_refund_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Refund"
msgstr "Tagasimaksmine"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Refund Created"
msgstr "Tagasimakse loodud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Refund Date"
msgstr "Tagasimakse kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__refund_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__refund_tax_id
msgid "Refund Tax"
msgstr "Hüvitise maks"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.actions.server,name:account.action_account_invoice_from_list
#: model:ir.model,name:account.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form_multi
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Register Payment"
msgstr "Lisa makse"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Register a bank statement"
msgstr "Lisa pangaväljavõte"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_receipt_type
msgid "Register a new purchase receipt"
msgstr "Registreeri uue ostu tšekk"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid "Register a payment"
msgstr "Lisa makse"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__other
msgid "Regular"
msgstr "Normaalne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__res_id
msgid "Related Document ID"
msgstr "Seotud dokumendi ID"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__model
msgid "Related Document Model"
msgstr "Seotud dokumendi mudel"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Repartition for Credit Notes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__invoice_repartition_line_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__invoice_repartition_line_ids
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Repartition for Invoices"
msgstr "Arvete ümberjaotamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__refund_repartition_line_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__refund_repartition_line_ids
msgid "Repartition for Refund Invoices"
msgstr " Tagasimakse arvete ümberjaotamine"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__refund_repartition_line_ids
#: model:ir.model.fields,help:account.field_account_tax_template__refund_repartition_line_ids
msgid "Repartition when the tax is used on a refund"
msgstr "Ümberjaotamine kui maksu on rakendatud tagasimaksele"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__invoice_repartition_line_ids
#: model:ir.model.fields,help:account.field_account_tax_template__invoice_repartition_line_ids
msgid "Repartition when the tax is used on an invoice"
msgstr "Ümbertegemine kui maks on rakendatud arvele"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_dest_id
msgid "Replacement Tax"
msgstr "Vahetus maks"

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Vastusmeili aadress. Määrates reply_to pääsed ümber automaatsest "
"lõimeloomest."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__reply_to
msgid "Reply-To"
msgstr "Vastamise aadress"

#. module: account
#: model:ir.model,name:account.model_ir_actions_report
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__report_action_id
msgid "Report Action"
msgstr "Tegevusest teatamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__tax_report_line_id
msgid "Report Line"
msgstr "Aruande rida"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr "Aruande valikud"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Aruandlus"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr "Kõige väiksem kasutusel olev sent."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__require_partner_bank_account
msgid "Require Partner Bank Account"
msgstr "Nõua partneri pangakontot"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__res_partner_bank_id
msgid "Res Partner Bank"
msgstr "Res partnerpank"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Reset To Draft"
msgstr "Pöördu tagasi mustandi juurde"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reset to Draft"
msgstr "Lähtesta mustandiks"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Reset to New"
msgstr "Lähtesta Uueks"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_move_reversal__residual
#, python-format
msgid "Residual"
msgstr "Jääk"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual
msgid "Residual Amount"
msgstr "Järelejäänud summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual_currency
msgid "Residual Amount in Currency"
msgstr "Järelejäänud summa valuutas"

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Residual amount"
msgstr "Jääksumma"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__user_id
msgid "Responsible"
msgstr "Vastutaja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_user_id
#: model:ir.model.fields,field_description:account.field_account_move__activity_user_id
#: model:ir.model.fields,field_description:account.field_account_payment__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_category_ids
msgid "Restrict Partner Categories to"
msgstr "Piira partnerite kategooriad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_ids
msgid "Restrict Partners to"
msgstr "Piira partnerid"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_same_currency
msgid ""
"Restrict to propositions having the same currency as the statement line."
msgstr "Piira soovitused väljavõtte real oleva sama valuutaga."

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue
#: model:ir.model.fields.selection,name:account.selection__account_accrual_accounting_wizard__account_type__income
msgid "Revenue"
msgstr "Tulu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__revenue_accrual_account
#: model:ir.model.fields,field_description:account.field_res_company__revenue_accrual_account_id
msgid "Revenue Accrual Account"
msgstr "Tulude tekkepõhine konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_id
msgid "Revenue/Expense Account"
msgstr "Tulude/Kulude konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reversal_move_id
msgid "Reversal Move"
msgstr "Tagasikäik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__date
msgid "Reversal date"
msgstr "Tagasipööramise kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reversed_entry_id
msgid "Reversal of"
msgstr "Tühistamine"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Reversal of %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/wizard/account_move_reversal.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Tühistamine: %s"

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:0
#, python-format
msgid "Reversal of: %s, %s"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Reverse"
msgstr "Loo kreedit"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr "Tagasipööramise kanne"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Reverse Journal Entry"
msgstr "Vastupidine registri kanne"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/wizard/account_move_reversal.py:0
#, python-format
msgid "Reverse Moves"
msgstr "Tagasipööramise kanded"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Revert reconciliation"
msgstr "Taasta ühendamine/sobitamine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Review"
msgstr "Ülevaade"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__root_id
#: model:ir.model.fields,field_description:account.field_account_account_template__root_id
msgid "Root"
msgstr "Põhjus"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__tax_calculation_rounding_method__round_globally
msgid "Round Globally"
msgstr "Ümarda dokumendipõhiselt"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__tax_calculation_rounding_method__round_per_line
msgid "Round per Line"
msgstr "Ümarda reapõhiselt"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr "Ümardamise vorm"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding_method
msgid "Rounding Method"
msgstr "Ümardamise meetod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding
msgid "Rounding Precision"
msgstr "Ümardamise täpsus"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__strategy
msgid "Rounding Strategy"
msgstr "Ümardamise strateegia"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr "Ümardamise puu"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "SEPA Credit Transfer (SCT)"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr "SEPA Direct Debit (SDD)"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA QR Code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_sms_error
#: model:ir.model.fields,field_description:account.field_account_journal__message_has_sms_error
#: model:ir.model.fields,field_description:account.field_account_move__message_has_sms_error
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise viga"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Sale"
msgstr "Müük"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_journal_sales
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__sale
#: model:ir.model.fields.selection,name:account.selection__account_tax__type_tax_use__sale
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__type_tax_use__sale
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_sales
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Sales"
msgstr "Müük"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Sales Person"
msgstr "Müügiesindaja"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__type__out_receipt
msgid "Sales Receipt"
msgstr "Müügitšekk"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Sales Receipt Created"
msgstr "Müügi kviitung tehtud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr "Müügimaks"

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sale_tax
msgid "Sales tax"
msgstr "Müügi maks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_user_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_user_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Müügiesindaja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_same_currency
msgid "Same Currency Matching"
msgstr "Sama valuuta sobitamine"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Sample data"
msgstr "Näidisandmed"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Sample invoice"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Sample invoice line name"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Sample invoice line name 2"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample invoice sent!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sanitized_acc_number
msgid "Sanitized Account Number"
msgstr "Puhastatud Konto Number"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Salvesta ja loo uus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as a new template"
msgstr "Salvesta uue mallina"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as new template"
msgstr "Salvesta uue mallina"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Salvesta see leht ja tulge tagasi, et funktsiooni seadistada."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr "Otsi konto andmiku"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr "Otsi konto malle"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr "Otsi pangaväljavõtteid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr "Otsi pangaväljavõtte rida"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr "Otsi kontoplaani malli"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
msgid "Search Fiscal Positions"
msgstr "Otsi eelarvepositsioone"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr "Otsi arvet"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr "Otsi andmiku kanderidu"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr "Otsi kannet"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr "Otsi maksu malle"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr "Otsi maksu"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_account_id
msgid "Second Account"
msgstr "Teine konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount_from_label_regex
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount_from_label_regex
msgid "Second Amount from Label (regex)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount_type
msgid "Second Amount type"
msgstr "Teine konto tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_account_id
msgid "Second Analytic Account"
msgstr "Teine analüütiline konto"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_tag_ids
msgid "Second Analytic Tags"
msgstr "Teine analüütiline silt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_journal_id
msgid "Second Journal"
msgstr "Teine andmik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_label
msgid "Second Journal Item Label"
msgstr "Teine andmiku kanderea silt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Second Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_tax_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_tax_ids
msgid "Second Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount
msgid "Second Write-off Amount"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Section"
msgstr "Sektsioon"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__secure_sequence_id
msgid "Secure Sequence"
msgstr "Turvaline järjestus"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Securisation of %s - %s"
msgstr "Väärtpaberid %s - %s"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__access_token
msgid "Security Token"
msgstr "Turvamärgis"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_journal_activity.xml:0
#, python-format
msgid "See all activities"
msgstr "Vaata kõiki tegevusi"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Vali 'Müük' kliendi arvete andmikuks.\n"
"Vali 'Ost' tarniajte arvete andmikuks.\n"
"Vali 'Sularaha' või 'Pank' andmikuks, mis on kasutusel maksetes.\n"
"Vali 'Üldine' mitmesuguste tegevuste andmikuks."

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Märgi partner"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Vali partner või vali vastaspool"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Select an old vendor bill"
msgstr "Vali vana tarnija arve"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr "Vali siin hindamise tüüp seotud selle maksetingimuse reaga."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""
"Vali see, kui maksud arvestatakse raha alusel, mis loob kande maksu kohta "
"sobitamise ajal."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__invoice_warn
#: model:ir.model.fields,help:account.field_res_users__invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Valides 'Hoiatus' teavitab see kasutaja teatega, valides 'Blokeerimise "
"teade' teavitab kasutajat ja peatab tegevuse. Teade tuleb kirjutada "
"järgmisse lahtrisse."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Send"
msgstr "Saada"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Send & Print"
msgstr "Saada ja prindi"

#. module: account
#: model:ir.actions.act_window,name:account.invoice_send
msgid "Send & print"
msgstr "Saada & trüki"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_email
msgid "Send Email"
msgstr "Saada kiri"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/wizard/account_invoice_send.py:0
#, python-format
msgid "Send Invoice"
msgstr "Saada arve"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_type__outbound
msgid "Send Money"
msgstr "Saada raha"

#. module: account
#: code:addons/account/wizard/account_payment_send.py:0
#, python-format
msgid "Send Payment"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sample_invoice
msgid "Send a sample invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send an invoice to test the customer portal."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Send invoices and payment follow-ups by post"
msgstr "Saatke arveid ja maksete meeldetuletusi postiga"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send receipt by email"
msgstr "Saada arve e-kirjaga"

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action_multi
msgid "Send receipts by email"
msgstr "Saada arved e-kirjaga"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send sample"
msgstr "Saada näidis"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__sent
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr "Saadetud"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__9
msgid "September"
msgstr "September"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__sequence
#: model:ir.model.fields,field_description:account.field_account_journal__sequence
#: model:ir.model.fields,field_description:account.field_account_journal_group__sequence
#: model:ir.model.fields,field_description:account.field_account_move_line__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__sequence
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sequence
#: model:ir.model.fields,field_description:account.field_account_tax__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template__sequence
msgid "Sequence"
msgstr "Järjestus"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__sequence
msgid ""
"Sequence determining the order of the lines in the report (smaller ones come"
" first). This order is applied locally per section (so, children of the same"
" line are always rendered one after the other)."
msgstr ""
"Järjestus mis määrab reastuse raportis (väiksemad esmalt). Rakendub "
"lokaalselt, sektsiooniti (rea alamread esitatakse üksteise järel)."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__secure_sequence_id
msgid "Sequence to use to ensure the securisation of data"
msgstr "Järjestus andmete turvamise tagamiseks."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr "Märgi aktiivseks, et peita konto sildid ilma neid eemaldatamata."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__active
msgid "Set active to false to hide the Journal without removing it."
msgstr "Märgi aktiivseks, et peita andmik ilma seda kustutamata."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__active
#: model:ir.model.fields,help:account.field_account_tax_template__active
msgid "Set active to false to hide the tax without removing it."
msgstr "Märgi aktiivseks, et peita maks ilma seda kustutamata."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Set taxes"
msgstr "Määra maksud"

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""
"Määra see valeks, kui sa ei taha, et seda malli kasutatakse aktiivselt "
"viisardis, mis loob kontoplaani mallidest. See on kasulik kui sa tahad luua "
"kontosid selle malliga ainult selle alammalli laadides. "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.actions.act_window,name:account.action_open_settings
#: model:ir.ui.menu,name:account.menu_account_config
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Settings"
msgstr "Seaded"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Setup your bank account to sync bank feeds."
msgstr "Seadistage oma pangakontod ja sünkroniseerige panga kanalid."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Setup your chart of accounts and record initial balances."
msgstr "Seadistage oma kontoplaan ja algsaldod."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.dashboard_onboarding_company_step
msgid "Setup your company's data for reports headers."
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.model_account_move_action_share
msgid "Share"
msgstr "Jaga"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__code
msgid "Short Code"
msgstr "Lühikood"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__tag_name
msgid ""
"Short name for the tax grid corresponding to this report line. Leave empty "
"if this report line should not correspond to any such grid."
msgstr ""
"Lühinimi maksuvõrgustikule vastavuses selle raporti reaga. Kui ei pea "
"vastama ühegagi, jäta tühjaks."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__show_force_tax_included
msgid "Show Force Tax Included"
msgstr "Näita jõuga maksusisaldust"

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr "Näita kõiki raamatupidamise võimalusi"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__show_partner_bank_account
msgid "Show Partner Bank Account"
msgstr "Näita partneri panga arvenumbrit"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__show_second_force_tax_included
msgid "Show Second Force Tax Included"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr "Näita aktiivseid makse "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Näita kõiki andmeid, millel on järgmise tegevuse kuupäev on ennem tänast "
"kuupäeva"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr "Näita mitteaktiivseid makse"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Näita andmikku töölaual"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes (B2C)"
msgstr "KUva rea ​​vahesummad koos maksudega (B2C)"

#. module: account
#: model:res.groups,comment:account.group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes included (B2C)"
msgstr "Show line subtotals with taxes included (B2C)"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_excluded
#: model:res.groups,comment:account.group_show_line_subtotals_tax_excluded
msgid "Show line subtotals without taxes (B2B)"
msgstr "Show line subtotals without taxes (B2B)"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Show standard terms &amp; conditions on invoices/orders"
msgstr "Näita arvetel/tellimustel standardseid tingimusi"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Jäta vahele"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_snailmail_account
msgid "Snailmail"
msgstr "Tavaline postiteenus"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Mõned väljad on määramata"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"Some selected statement line were not already reconciled with an account "
"move."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__bank_bic
msgid "Sometimes called BIC or Swift."
msgstr "Tihti kutsutakse BIC või Swift."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Source Document"
msgstr "Alusdokument"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_source_email
msgid "Source Email"
msgstr "Algne email"

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""
"Määra, mis moodi soovid kasutada ümardamist arvetel ning ümardamise täpsust"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__start_bank_stmt_ids
msgid "Start Bank Stmt"
msgstr "Alusta pangaväljavõttega"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_from
#: model:ir.model.fields,field_description:account.field_account_common_report__date_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_from
msgid "Start Date"
msgstr "Alguskuupäev"

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Alguskuupäev, jäädes majandusaasta sisse."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_start
msgid "Starting Balance"
msgstr "Algsaldo"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_start_id
msgid "Starting Cashbox"
msgstr "Kassa alusatmine"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_dashboard_onboarding_state
msgid "State of the account dashboard onboarding panel"
msgstr "Konto juhtpaneeli kasutuselevõtu seis"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_invoice_onboarding_state
msgid "State of the account invoice onboarding panel"
msgstr "Raamatupidamise arve kasutuselevõtu paneeli seisund "

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_bank_data_state
msgid "State of the onboarding bank data step"
msgstr "Kasutuselevõtu pangaandmete etapi olek"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_coa_state
msgid "State of the onboarding charts of account step"
msgstr "Graafikute konto juurutamise astme seisund"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_fy_data_state
msgid "State of the onboarding fiscal year step"
msgstr "Eelarveaasta sammu seis"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_invoice_layout_state
msgid "State of the onboarding invoice layout step"
msgstr "Arvete plaani juurutamise sammu seis"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sale_tax_state
msgid "State of the onboarding sale tax step"
msgstr "Müügimaksu juurutamise sammu seis"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sample_invoice_state
msgid "State of the onboarding sample invoice step"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_account_statement
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_id
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Statement"
msgstr "Väljevõte"

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr "Avaldus %s on kinnitatud, loodi andmiku kanderead."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr "Väljavõtte rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr "Väljavõtte read"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr "Väljavõtted"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr "Maakonnad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__states_count
msgid "States Count"
msgstr "Maakond"

#. module: account
#: code:addons/account/controllers/portal.py:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement__state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__state
#: model:ir.model.fields,field_description:account.field_account_move__state
#: model:ir.model.fields,field_description:account.field_account_move_line__parent_state
#: model:ir.model.fields,field_description:account.field_account_payment__state
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr "Staatus"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__activity_state
#: model:ir.model.fields,help:account.field_account_move__activity_state
#: model:ir.model.fields,help:account.field_account_payment__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Step Completed!"
msgstr "Aste lõpetatud!"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__string_to_hash
msgid "String To Hash"
msgstr "Kiud kimpu siduda"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subject
msgid "Subject"
msgstr "Teema"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Subject..."
msgstr "Teema..."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_report_line_form
msgid "Sublines"
msgstr "Alamjooned"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__subtotal
#: model:ir.model.fields,field_description:account.field_account_move_line__price_subtotal
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Subtotal"
msgstr "Vahesumma"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subtype_id
msgid "Subtype"
msgstr "Alltüüp"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__rule_type__writeoff_suggestion
msgid "Suggest a write-off."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__rule_type__writeoff_suggestion
msgid "Suggest counterpart values."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__supplier_rank
#: model:ir.model.fields,field_description:account.field_res_users__supplier_rank
msgid "Supplier Rank"
msgstr "Tarnija tiitel"

#. module: account
#: model:ir.actions.server,name:account.action_move_switch_invoice_to_credit_note
msgid "Switch into refund/credit note"
msgstr "Lülita tagasimakse/kreedit arvele"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__name
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__tag_name
msgid "Tag Name"
msgstr "Sildi nimi"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Tag name %(tag)s is used by more than one tax report line in %(country)s. "
"Each tag name should only be used once per country."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Tag name and formula are mutually exclusive, they should not be set together"
" on the same tax report line."
msgstr ""
"Märgise nimi ja valem on vastastikku välistavad, neid ei tohiks seada "
"samaaegselt samale maksuraporti reale."

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tag_ids
#: model:ir.model.fields,field_description:account.field_account_move_line__tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__tag_ids
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_form
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_tree
msgid "Tags"
msgstr "Sildid"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tag_ids
msgid ""
"Tags assigned to this line by the tax creating it, if any. It determines its"
" impact on financial reports."
msgstr ""
"Märgised mis osundavad antud reale neid loova maksu poolt, kui üldse. Määrab"
" selle mõju finantsraportites."

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Take Money In/Out"
msgstr "Raha sisse/välja"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__target_move
#: model:ir.model.fields,field_description:account.field_account_common_report__target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal__target_move
msgid "Target Moves"
msgstr "Kanded"

#. module: account
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_move__amount_tax
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__tax_id
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tax_audit_tree
msgid "Tax"
msgstr "Tulumaks"

#. module: account
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Tax %.2f%%"
msgstr "Maks %.2f%%"

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr "Käibemaksu korrigeermised"

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Tax Adjustments Wizard"
msgstr "Maksude korrigeerimise abimees"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr "Maksu summa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr "Maksu kohaldamine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_audit
msgid "Tax Audit String"
msgstr "Maksuauditi rida"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Maksuarvestuse ümardamise meetod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr "Maks sularaha kandel"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "Maks sularaha andmikul"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Maksuarvutus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr "Maksudeklaratsioon"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_exigibility
msgid "Tax Due"
msgstr "Maksu tähtaeg"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Tax Excluded"
msgstr "Maksudeta summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__tag_ids
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Tax Grids"
msgstr "Maksude võrgustik"

#. module: account
#: model:ir.model,name:account.model_account_tax_group
#: model:ir.model.fields,field_description:account.field_account_tax__tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_group_id
msgid "Tax Group"
msgstr "Maksu grupp"

#. module: account
#: model:ir.actions.act_window,name:account.action_tax_group
msgid "Tax Groups"
msgstr "Maksu grupid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Tax ID"
msgstr "KMKR nr"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_tax_included
#, python-format
msgid "Tax Included in Price"
msgstr "Maks sisaldub hinnas"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_lock_date
msgid "Tax Lock Date"
msgstr "Maksude lukustamise kuupäev"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_lock_date_message
msgid "Tax Lock Date Message"
msgstr "Maksude lukustamise kuupäeva teade"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr "Maksude kaardistamine"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Tax Mapping Template of Fiscal Position"
msgstr "Maksude kaardistuse mall finantspositsioonis"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Tax Mapping of Fiscal Position"
msgstr "Eelarvepositsiooni maksuskeem"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__name
#: model:ir.model.fields,field_description:account.field_account_tax_template__name
msgid "Tax Name"
msgstr "Maksu Nimi"

#. module: account
#: model:ir.model,name:account.model_account_tax_repartition_line
msgid "Tax Repartition Line"
msgstr "Maksude jaotuse rida"

#. module: account
#: model:ir.model,name:account.model_account_tax_repartition_line_template
msgid "Tax Repartition Line Template"
msgstr "Maksu ümberjagamise rea mustand"

#. module: account
#: model:ir.ui.menu,name:account.menu_configure_tax_report
msgid "Tax Report"
msgstr "Maksuaruanne"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__tax_report_line_ids
msgid "Tax Report Lines"
msgstr "Maksuraporti read"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_template__type_tax_use
msgid "Tax Scope"
msgstr "Maksu kohaldamisala"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_tax_signed
msgid "Tax Signed"
msgstr "Maks allakirjutatud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_src_id
msgid "Tax Source"
msgstr "Maksu allikas"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr "Maksu mall"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__tax_template_ids
msgid "Tax Template List"
msgstr "Maksu mallide nimekiri"

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr "Maksu mallid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_by_group
msgid "Tax amount by group"
msgstr "Maksumäär grupiviisiliselt"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Maksuarvestuse ümardamise meetod"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_tax_payable_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_group__property_tax_payable_account_id
msgid "Tax current account (payable)"
msgstr "Maksu arvelduskonto (maksmisele kuuluv)"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_tax_receivable_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_group__property_tax_receivable_account_id
msgid "Tax current account (receivable)"
msgstr "Maksude arvelduskonto (nõuded)"

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_excluded
msgid "Tax display B2B"
msgstr "Maksete kuvamine B2B"

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_included
msgid "Tax display B2C"
msgstr "Maksete kuvamine B2C"

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_tax_name_company_uniq
#: model:ir.model.constraint,message:account.constraint_account_tax_template_name_company_uniq
msgid "Tax names must be unique !"
msgstr "Maksu nimi peab olema unikaalne !"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_src_id
msgid "Tax on Product"
msgstr "Maks tootel"

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid ""
"Tax repartition line templates should apply to either invoices or refunds, "
"not both at the same time. invoice_tax_id and refund_tax_id should not be "
"set together."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_repartition_line_id
msgid ""
"Tax repartition line that caused the creation of this move line, if any"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Tax repartition lines should apply to either invoices or refunds, not both "
"at the same time. invoice_tax_id and refund_tax_id should not be set "
"together."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__plus_report_line_ids
msgid ""
"Tax report lines whose '+' tag will be assigned to move lines by this "
"repartition line"
msgstr ""
"Antud ümberjaotuse rea poolt määratakse siirderidadele maksuraporti read "
"millel '+' märk  "

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__minus_report_line_ids
msgid ""
"Tax report lines whose '-' tag will be assigned to move lines by this "
"repartition line"
msgstr ""
"Antud ümberjaotuse rea poolt määratakse '-' märgiga maksuraporti read ridu "
"nihutama"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__tag_ids
msgid "Tax tags populating this line"
msgstr "Maksumärgid  paiknevad sellel real"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_dest_id
msgid "Tax to Apply"
msgstr "Maks rakendamiseks"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_config_settings__show_line_subtotals_tax_selection__tax_excluded
msgid "Tax-Excluded"
msgstr "Maksudeta summa"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_config_settings__show_line_subtotals_tax_selection__tax_included
msgid "Tax-Included"
msgstr "Maksudega"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account
#. openerp-web
#: model:account.tax.group,name:account.tax_group_taxes
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__tax_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__tax_ids
#: model:ir.model.fields.selection,name:account.selection__account_account_tag__applicability__taxes
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Taxes"
msgstr "Maksud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Taxes Applied"
msgstr "Maksud rakendatud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr "Maksude kaardistamine"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_ids
msgid "Taxes that apply on the base amount"
msgstr "Maksud mis rakenduvad baasmäärale"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr "Ostudel kasutatavad maksud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr "Müügis kasutatavad maksud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr "Maksud, finantspositsioon, kontoplaan ja aruanded teie riigi jaoks"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__invoice_ids
msgid ""
"Technical field containing the invoice for which the payment has been generated.\n"
"                                   This does not especially correspond to the invoices reconciled with the payment,\n"
"                                   as it can have been generated first, and reconciled later"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__display_type
msgid "Technical field for UX purpose."
msgstr "Tehniline väli UX tarvis."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""
"Tehniline väli, mis hoiab deebet - kreedit infot, et näidata mõtestatud "
"graafikuid aruannetes"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,help:account.field_account_payment__move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""
"Tehniline väli, mis hoiab andmiku kandele antud numbrit. Number määratakse "
"automaatlselt, kui väljavõtte sobitatakse. Kui kanne tühistatakse ja "
"määratakse mustand staatusess siis uuesti kinnitamisel saab ta sama numbri "
"tagasi."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__bank_partner_id
msgid "Technical field to get the domain on the bank"
msgstr "Tehniline väli panga valdkonna saamiseks"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,help:account.field_account_payment__has_invoices
msgid "Technical field used for usability purposes"
msgstr "Tehniline väil, mida kasutatakse kasutusmugavuse eesmärgil"

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
"Tehniline väli, mida kasutatakse kasutajaliidese muutmiseks kui makse on "
"valitud."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__always_set_currency_id
msgid ""
"Technical field used to compute the monetary field. As currency_id is not a "
"required field, we need to use either the foreign currency, either the "
"company one."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""
"Tehniline väli, mida kasutatakse selleks, et näidata millisel kuupäeval "
"sobitamine tuleb näidata aegunud nõuete/kohustuste aruannetes."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_lock_date_message
msgid ""
"Technical field used to display a message when the invoice's accounting date"
" is prior of the tax lock date."
msgstr ""
"Tehniline väli teate kuvamiseks kui arve arvelduskuupäev on varasem "
"maksupäevast."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_has_matching_suspense_amount
msgid ""
"Technical field used to display an alert on invoices if there is at least a "
"matching amount in any supsense account."
msgstr ""
"Tehniline väli kus kuvatakse hoiatus arvetele kui leidub mõnele kahtlasele "
"kontole vastav summa."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__exclude_from_invoice_tab
msgid ""
"Technical field used to exclude some lines from the invoice_line_ids tab in "
"the form view."
msgstr ""
"Tehniline väli mõne rea väljajätmiseks invoice_line_ids vahelehel "
"vormivaates."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__user_id
msgid "Technical field used to fit the generic behavior in mail templates."
msgstr "Tehniline väli kirjeldamaks meili mallides üldist käitumist."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_filter_type_domain
msgid ""
"Technical field used to have a dynamic domain on journal / taxes in the form"
" view."
msgstr ""
"Tehniline väli et saaks kasutada dünaamilist valdkonda registris / maksudes "
"vormivaates."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""
"Tehniline väli, mida kasutatakse maksemeetodi peitmiseks, kui andmikul on "
"valitud manuaalne meetod."

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""
"Tehniline väli mida kasutatakse maksude kassapõhiseks sobitamise "
"jälgimiseks. Seda on vaja aluseks oleva kande tühistamiseks: see postitab "
"pöördkande, et tühistada ka see osa kandest. "

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__recompute_tax_line
msgid ""
"Technical field used to know on which lines the taxes must be recomputed."
msgstr "Tehniline väli osundamaks millistes ridades tuleb teha ümberarvutusi."

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__show_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be displayed or not in the payments form views"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__require_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be required or not in the payments form views"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""
"Tehniline väli. mis määrab kas maks on kuulub KMKR aruandesse või mitte."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__country_id
msgid "Technical field used to restrict tags domain in form view."
msgstr "Tehniline väli mille abil eirata märgete valdkonda vormi vaates"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__country_id
msgid ""
"Technical field used to restrict the domain of account tags for tax "
"repartition lines created for this tax."
msgstr ""
"Tehniline väli mille abil piirata konto märgete valdkonda maksude jaotuse "
"ridadel mis loodud antud maksu jaoks."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__is_rounding_line
msgid "Technical field used to retrieve the cash rounding line."
msgstr "Tehniline väli kasutamiseks sularaha ümardamise rea taastamisel."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__show_force_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model__show_second_force_tax_included
msgid "Technical field used to show the force tax included button"
msgstr "Tehniline väli mille abil osundada maksu jõuga lisamise nupule"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_number
msgid ""
"Technical field used to store the bank account number before its creation, "
"upon the line's processing"
msgstr ""
"Tehniline väli panga kontonumbri salvestamiseks enne selle loomist, rea "
"töötlemisel"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__chart_template_id
msgid "Template"
msgstr "Mall"

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Finantspositsiooni mall"

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr "Konto mall"

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Maksude mall"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr "Tingimuse tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr "Tingimused"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_terms
msgid "Terms & Conditions"
msgstr "Tingimused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__narration
msgid "Terms and Conditions"
msgstr "Tingimused"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "See on keskmine"

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_group
#: model:ir.model.fields,help:account.field_account_account_type__internal_group
msgid ""
"The 'Internal Group' is used to filter accounts based on the internal group "
"set on the account type."
msgstr ""
"'Sisemine grupp' on kasutusel kontode kontotüübi järgi filtreerimisel "
"sisemise grupi sätete alusel."

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_type
#: model:ir.model.fields,help:account.field_account_account_type__type
#: model:ir.model.fields,help:account.field_account_move_line__account_internal_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""
"\"Sisemine tüüp\" on kasutusel mitmete võimaluste kasutamiseks kontodel: "
"likviidsuse tüüpi kasutatakse sularaha ja panga kontodel, nõuded/kohustused "
"on kasutusel tarnijate/klientide kontodel."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "The account %s (%s) is deprecated."
msgstr "Konto %s (%s) on aegunud."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The account is already in use in a 'sale' or 'purchase' journal. This means "
"that the account's type couldn't be 'receivable' or 'payable'."
msgstr ""
"Konto on juba kasutusel 'müügi' või 'ostude' registris. Tähendab, konto tüüp"
" ei või olla 'laekumine' või 'maksmine'."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The account selected on your journal entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""
"Sinu registris valitud konto sunnib määrama teisejärgulise valuuta. Peksid "
"teisejärgulise valuuta eemaldama oma kontolt."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The accounting date is prior to the tax lock date which is set on %s. Then, "
"this will be moved to the next available one during the invoice validation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,help:account.field_res_partner_bank__journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr "Raamatupidamise andmik, mis vastab sellele pangakontole."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr "Konto, kuhu valuutakursi erinevus registreeritakse"

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__amount_currency
#: model:ir.model.fields,help:account.field_account_move_line__amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Summa väljendatud valikulises teises valuutas kui tegemist on mitme "
"valuutaga sissekandega."

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_amount_currency_balance_sign
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited. Moreover, the currency "
"field has to be left empty when the amount is expressed in the company "
"currency."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "The amount of a cash transaction cannot be 0."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or left empty."
msgstr ""
"Rakenduse maksude määr grupis peab olema kas grupiga sama suur või tühi."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr "Pangakonto ja pangaandmik peavad kuuluma samale ettevõttele (%s)."

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"                                                                                                       This is useful if you install accounting after having used invoicing for some time and\n"
"                                                                                                       don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"               This is useful if you install accounting after having used invoicing for some time and\n"
"               don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__statement_id
msgid "The bank statement used for bank reconciliation"
msgstr "Pangaväljavõtte, mida kasutatakse sobitamiseks"

#. module: account
#: model:ir.model.fields,help:account.field_res_company__chart_template_id
msgid "The chart template for the company (if any)"
msgstr "Ettevõtte kontoplaan (kui on olmemas)"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr "Sulgemise saldo on erinev kui arvutatud saldo!"

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_journal_code_company_uniq
msgid "The code and name of the journal must be unique per company !"
msgstr "Andmiku koos ja nimi peavad olema unikaalsed ettevõttes!"

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_account_code_company_uniq
msgid "The code of the account must be unique per company !"
msgstr "Ettevõtte kontonumber peab olema kordumatu!"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The combination of reference model and reference type on the journal is not "
"implemented"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__company_id
msgid "The company this repartition line belongs to."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "The credit note is auto-validated and reconciled with the invoice."
msgstr "Kreeditarve on automaatselt-kinnitatud ja sobitatakse arvega."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid ""
"The credit note is auto-validated and reconciled with the invoice.\n"
"                               The original invoice is duplicated as a new draft."
msgstr ""
"Kreeditarve on automaatselt-kinnitatud ja sobitatakse arvega.\n"
"                               Originaalarvest tehakse koopia uue mustandina. "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid ""
"The credit note is created in draft and can be edited before being issued."
msgstr "Kreeditarve on loodud mustandina ja seda saab muuta enne esitamist."

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"The currency of the bank statement line must be different than the statement"
" currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The currency of the journal should be the same than the default credit "
"account."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The currency of the journal should be the same than the default debit "
"account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__currency_id
msgid "The currency used to enter statement"
msgstr "Pangakonto väljavõttel kasutusel olev valuute"

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "The day of the month used for this term must be strictly positive."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr "Dokumendid, millest koostati arve."

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""
"Lõppsaldo ei ole korrektne! \n"
"Eeldatav saldo (%s) on erinev kui arvutatud saldo. (%s)"

#. module: account
#: code:addons/account/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "Lõppkuupäev ei saa olla enne alguskuupäeva"

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""
"Kulu kajastatakse siis, kui tarnija arve on kinnitatud, välja arvatud anglo-"
" saksi arvestus koos püsiva laoarvestusega, millisel juhul kajastatakse kulu"
" (müüdud kaupade kulu konto) müügiarve kinnitamisel."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The field 'Customer' is required, please complete it to validate the "
"Customer Invoice."
msgstr ""
"Väli 'Klient' on kohustuslik, palun täitke ära, et kinnitada müügiarve. "

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The field 'Vendor' is required, please complete it to validate the Vendor "
"Bill."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_position_id
#: model:ir.model.fields,help:account.field_res_users__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_invoice_send.py:0
#, python-format
msgid ""
"The following invoice(s) will not be sent by email, because the customers "
"don't have email address."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_payment_send.py:0
#, python-format
msgid ""
"The following payment(s) will not be sent by email, because the customers "
"don't have email address."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid ""
"The following tax report lines are used in some tax repartition template "
"though they don't generate any tag: %s . This probably means you forgot to "
"set a tag_name on these lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid ""
"The hash chain is compliant: it is not possible to alter the\n"
"                                            data without breaking the hash chain for subsequent parts."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The holder of the bank account of a \"Bank\" type journal must be the company (%s).\n"
"However, the holder of \"%s\" is \"%s\".\n"
"Please select another bank account or change the holder of \"%s\"."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "Selles andmikus kasutatakse kannetel seda prefiksit."

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""
"Andmiku kanne, mis sisaldab selle ettevõtte kõikide kontode algsaldosid."

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_month
msgid ""
"The last day of the month will be used if the chosen day doesn't exist."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "The last line of a Payment Term should have the Balance type."
msgstr "Makse tähtaja viimasel real peaks olema saldo tüüp."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""
"Viimane rida arvutuses peaks olema 'Saldo/Balance', et  tagada et kogusumma "
"on eraldatud."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__move_id
msgid "The move of this entry line."
msgstr "Selle rea liigutus."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr ""
"Järgmine järjestuse number võetakse kasutusele järgmise kreeditarve puhul."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "Järgmine järjestuse number võetakse kasutusele järgmise arve puhul."

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "The number of days used for a payment term cannot be negative."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The operation is refused as it would impact an already issued tax statement."
" Please change the journal entry date or the tax lock date set in the "
"settings (%s) to proceed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__report_action_id
msgid ""
"The optional action to call when clicking on this line in accounting "
"reports."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Valikuline teine valuuta kui see on mitme valuutaga sissekanne."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""
"Valikuline kogus toodud sellel real, nt. mitu toodet müüdi. Seaduse järgi ei"
" ole see kohustuslik aga väga kasulik aruannetes."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__sequence
msgid ""
"The order in which display and match repartition lines. For refunds to work "
"properly, invoice repartition lines should be arranged in the same order as "
"the credit note repartition lines they correspond to."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"Partneril on pärast viimast arvete ja maksete sobitamist tehtud vähemalt üks"
" kanne."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr "Andmiku ettevõtte ja seotud pangakonto partnerid ei sobi omavahel."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "The payment amount cannot be negative."
msgstr "Makse summa ei saa olla negatiivne."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr "Makset ei saa töödelda, sest arve ei ole avatud!"

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_payment_ref
msgid "The payment reference to set on journal items."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"The payments which have not been matched with a bank statement will not be "
"shown in bank reconciliation data if they were made before this date"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_category_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customer/vendor categories."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customers/vendors."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_nature
msgid ""
"The reconciliation model will only be applied to the selected transaction type:\n"
"        * Amount Received: Only applied when receiving an amount.\n"
"        * Amount Paid: Only applied when paying an amount.\n"
"        * Amount Paid/Received: Applied in both cases."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner
msgid ""
"The reconciliation model will only be applied when a customer/vendor is set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_amount
msgid ""
"The reconciliation model will only be applied when the amount being lower "
"than, greater than or between specified amount(s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_label
msgid ""
"The reconciliation model will only be applied when the label:\n"
"        * Contains: The proposition label must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_note
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_note
msgid ""
"The reconciliation model will only be applied when the note:\n"
"        * Contains: The proposition note must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_transaction_type
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_transaction_type
msgid ""
"The reconciliation model will only be applied when the transaction type:\n"
"        * Contains: The proposition transaction type must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_journal_ids
msgid ""
"The reconciliation model will only be available from the selected journals."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_tax_adjustments_wizard__tax_report_line_id
msgid "The report line to make an adjustment for."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr "Järelejäänud summa andmiku kandereal kande valuutas."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr "Järelejäänud summa andmiku kandereal ettevõtte valuutas."

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__sequence
#: model:ir.model.fields,help:account.field_account_tax_template__sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""
"Järjestuse välja kasutatakse maksuridade reastamiseks soovitud järjekorda."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount_param
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount under this percentage."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__tax_report_line_ids
msgid "The tax report lines using this tag"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__invoice_tax_id
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__invoice_tax_id
msgid ""
"The tax set to apply this repartition on invoices. Mutually exclusive with "
"refund_tax_id"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__refund_tax_id
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__refund_tax_id
msgid ""
"The tax set to apply this repartition on refund invoices. Mutually exclusive"
" with invoice_tax_id"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr "Lüüsi reegel, mida kasutatakse ümardamise toimingutes"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The type of the journal's default credit/debit account shouldn't be "
"'receivable' or 'payable'."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr "Hetke lei ole ühtegi arvet ega maksed selle konto kohta."

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:0
#, python-format
msgid "There are no journal items in the draft state to post."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""
"Lukustatavas perioodis on endiselt postitamata kandeid. Need tuleb kinnitada"
" või kustutada."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"There is more than one receivable/payable account in the concerned invoices."
" You cannot group payments in that case."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"There is no Transfer Account defined in the accounting settings. Please "
"define one to be able to confirm this transfer."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__amount_from_label_regex
msgid ""
"There is no need for regex delimiter, only the regex is needed. For instance if you want to extract the amount from\n"
"R:9672938 10/07 AX ********** T:5L:NA BRT: 3358,07 C:\n"
"You could enter\n"
"BRT: ([\\d,]+)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr "Sularaha andmikus ei ole maksu sätestatud selles ettevõttes: \"%s\" "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Ei ole midagi sobitada."

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"There isn't any journal entry flagged for data inalterability yet for this "
"journal."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr "Lehe töötlemisel tekkis viga."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr "Need maksud määratakse iga uue tootega."

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""
"Need liigid määratakse vastavalt riigile. Liik sisaldab rohkem infot konto "
"ning selle eripärade kohta. "

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "This Week"
msgstr "See nädal"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Käesoleva partneri väljaminekuteks kasutatakse seda kontot vaikimisi konto "
"asemel. "

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Käesoleva partneri sissetulekuteks kasutatakse seda kontot vaikimisi konto "
"asemel. "

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "Seda kontot kasutatakse kliendiarve kinnitamisel."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "This action isn't available for this document."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""
"See võimaldab raamatupidajatel hallata analüütilisi ja ristuvaid eelarveid. "
"Kui peamised eelarved ja eelarve on määratletud, saavad projektijuhid "
"määrata iga analüütilise konto jaoks kavandatava summa."

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__module_account_batch_payment
msgid ""
"This allows you grouping payments into a single batch and eases the reconciliation process.\n"
"-This installs the account_batch_payment module."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""
"See Boolean aitab sul valida, kas sa tahad kasutajale ostu- ja müügimäärade "
"kodeerimise võimalust pakkuda või valida maksude nimekirjast. See viimane "
"valik eeldab, et sellel mallil määratud maksude kogum on täielik"

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "This can only be used on journal items"
msgstr "Seda saab kasutada ainult andmike kanderidades. "

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr "See väli sisaldab informatsiooni kreeditarvete andmikukannete kohta."

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""
"See väli sisaldab nummerdamise informatsiooni andmiku kannete kohta selles "
"andmikus."

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr "See väli jäetakse välja pangaväljavõtte sobitamisel."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Seda välja kasutatakse väljaminekute ja sissetulekute andmike kannete jaoks."
" Saad seada piirkuupäeva selle rea maksmisele. "

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""
"Seda välja kasutatakse kolmanda osapoole nime salvestamiseks elektroonilises"
" formaadis pangaväljavõtte importimisel kui partnerit ei ole veel "
"andmebaasis olemas (või teda ei leita). "

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "This is the accounting dashboard"
msgstr "See on raamatupidamise töölaud"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""
"Andmik sisaldab juba kanderidasid, seega  ei saa muuta enam seotud "
"ettevõtet."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr "Andmik sisaldab juba kanderidasid, seega ei saa muuta enam lühikoodi."

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "This journal is not in strict mode."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__to_check
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__to_check
msgid ""
"This matching rule is used when the user is not certain of all the "
"informations of the counterpart."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "This move is configured to be auto-posted on %s"
msgstr "See kanne on loodud ja kinnitatakse automaatselt %s"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "This move is configured to be auto-posted on {}"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""
"See valikuline väli lubab sul siduda konto malli spetsiifilise tabeli "
"malliga, mis võib olla erinev sellest, mille juurde tema root ülem kuulub. "
"See lubab sul määrata tabeli mallid, mis laiendavad teist ning täidavad "
"selle mõne uue kontoga (Sa ei pea määrama kogu struktuuri, mis on ühine "
"mõlemale mitmel korral). "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"See lehekülg kuvab pangatehinguid, mida tuleb võrrelda ning annab selle "
"tarvis puhta liidese. "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"This parameter will be bypassed in case of a statement line communication "
"matching exactly existing entries"
msgstr ""
"Parameeter loetakse läbituks juhul, kui arveldusrida vastab täpselt "
"olemasolevatele kannetele"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Makse on registreeritud aga sobitamata."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"Seda maksetingimuste kasutatakse vaikimisi maksetingimuse asemel "
"ostutellimustel ja tarnijate arvetel."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"Seda maksetingimuste kasutatakse vaikimisi maksetingimuse asemel "
"müügitellimustel ja müügiarvetel."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""
"Seda tehnilist välja saab kasutada aruanderea loomise/importimise ajal, et "
"vältida selle hilisemat sobitamist. Aruanderida loob sellel kontol lihtsalt "
"vastaspoole"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""
"See viisard kinnitab kõik valitud andmike kanded. Kui kanded on kinnitatud, "
"siis ei saa neid enam muuta."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""
"Neid saab kasutada andmiku kanderidade kiireks loomiseks, kui sobitad\n"
"                 pangaväljavõtet või kontosid."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Those options will be selected by default when clicking \"Send &amp; Print\""
" on invoices"
msgstr "Need valikud rakenduvad vaikimisi kui valida arvetel \"Saada & Prindi\" "

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_move__to_check
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__to_check
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__to_check
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "To Check"
msgstr "Kontrolli üle"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr "Arveks teha"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "To reconcile the entries company should be the same for all entries."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "Sobitamise kiirendamiseks määrake "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Today Activities"
msgstr "Tänased tegevused"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__total
#: model:ir.model.fields,field_description:account.field_account_invoice_report__amount_total
#: model:ir.model.fields,field_description:account.field_account_move__amount_total
#: model:ir.model.fields,field_description:account.field_account_move_line__price_total
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
msgid "Total"
msgstr "Kokku"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__total_amount
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr "Kogu summa"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tax_audit_tree
msgid "Total Base Amount"
msgstr "Baassumma kokku"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Total Credit"
msgstr "Kogukreedit"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Total Debit"
msgstr "Kogu deebet"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users__total_invoiced
msgid "Total Invoiced"
msgstr "Kokku arveks tehtud"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit
#: model:ir.model.fields,field_description:account.field_res_users__debit
msgid "Total Payable"
msgstr "Võlg kokku"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__credit
#: model:ir.model.fields,field_description:account.field_res_users__credit
msgid "Total Receivable"
msgstr "Nõuete summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_total_signed
msgid "Total Signed"
msgstr "Allkirjastatud kokku"

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__credit
#: model:ir.model.fields,help:account.field_res_users__credit
msgid "Total amount this customer owes you."
msgstr "Kogu summa, mis see klient sulle võlgneb."

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__debit
#: model:ir.model.fields,help:account.field_res_users__debit
msgid "Total amount you have to pay to this vendor."
msgstr "Kogu summa, mida peate maksma sellele tarnijale."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__total_entry_encoding
msgid "Total of transaction lines."
msgstr "Kokku tehingute ridu."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc"
msgstr "Jälgi projekti, osakonna jne kulusid &amp; tulusid"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr "Tehing"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__transaction_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_transaction_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_transaction_type
msgid "Transaction Type"
msgstr "Tehingu tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_transaction_type_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_transaction_type_param
msgid "Transaction Type Parameter"
msgstr "Tehingu tüübi parameeter"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr "Tehingud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__total_entry_encoding
msgid "Transactions Subtotal"
msgstr "Tehingute vahesumma"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_journal_id
msgid "Transfer To"
msgstr "Ülekanne"

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Transfer from %s"
msgstr "Ülekanne alates %s"

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Transfer to %s"
msgstr "Ülekanne kuni %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr "Siirded"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_template__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type__type
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report__type
#: model:ir.model.fields,field_description:account.field_account_invoice_send__message_type
#: model:ir.model.fields,field_description:account.field_account_journal__type
#: model:ir.model.fields,field_description:account.field_account_move__type
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__rule_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__rule_type
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_type
msgid "Type"
msgstr "Tüüp"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__type_name
msgid "Type Name"
msgstr "Tüübi nimi"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__activity_exception_decoration
#: model:ir.model.fields,help:account.field_account_move__activity_exception_decoration
#: model:ir.model.fields,help:account.field_account_payment__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__rounding_method__up
msgid "UP"
msgstr "Üles"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Unable to mix any taxes being price included with taxes affecting the base "
"amount but not included in price."
msgstr ""
"Ei suuda segada makse, mis sisalduvad hinnas ning mis mõjutavad baassummat, "
"aga ei sisaldu hinnas."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Undefined Yet"
msgstr "Määratlemata"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Undistributed Profits/Losses"
msgstr "Kajastamata kasum/kahjum"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__price_unit
msgid "Unit Price"
msgstr "Ühiku hind"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Unit Price:"
msgstr "Ühiku hind:"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_uom_id
msgid "Unit of Measure"
msgstr "Mõõtühik"

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Tundmatu partner"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Unpaid Invoices"
msgstr "Tasumata müügiarved"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr "Sisestamata"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr "Postitamata andmike kanded"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr "Postitamata andmiku kanderead"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,field_description:account.field_account_journal__message_unread
#: model:ir.model.fields,field_description:account.field_account_move__message_unread
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread
msgid "Unread Messages"
msgstr "Lugemata sõnumid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_journal__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_move__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lugemata sõnumite loendur"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#: model:ir.actions.act_window,name:account.action_account_unreconcile
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr "Ühenda lahti"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr "Ühenda tehingud lahti "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr "Lahti ühendatud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_untaxed
msgid "Untaxed Amount"
msgstr "Maksudeta summa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_untaxed_signed
msgid "Untaxed Amount Signed"
msgstr "Maksudeta summa allkirjastatud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_subtotal
msgid "Untaxed Total"
msgstr "Ilma maksudeta kokku"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "UoM"
msgstr "Mõõtühikud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr "Uuenda vahetuskursse automaatselt"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/bills_tree_upload_views.xml:0
#, python-format
msgid "Upload"
msgstr "Üleslaadimine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr "Kasuta anglosaksi raamatupidamist"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_exigibility
msgid "Use Cash Basis"
msgstr "Kasuta kassapõhist arvestust"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "Kasuta SEPA otsekorraldust"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__journal_id
msgid "Use Specific Journal"
msgstr "Kasuta kindlat andmikku"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__use_active_domain
msgid "Use active domain"
msgstr "Kasutage aktiivset domeenit"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr "Kasuta anglosaksi raamatupidamist"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_batch_payment
msgid "Use batch payments"
msgstr "Kasuta koondmakseid"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"Kasutage eelarveid, et võrrelda tegelikke ja eeldatud tulusid ja kulusid"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__template_id
msgid "Use template"
msgstr "Kasuta malli"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__used
msgid "Used"
msgstr "Kasutatud"

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type__include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""
"Kasutatakse aruannetes, saamaks teada, kas peaksime arvesse võtma andmiku "
"kanderead täiesti algusest või ainult aruandeaastast.  Kontoliigid, mis "
"tuleks igal aruandeaastal nullida (nagu kulud, tulud ...), ei peaks seda "
"võimalust omama."

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""
"Kasutatakse hoidmaks välise tähendusega viidet, mis lõi selle aruande "
"(imporditud faili nimi, internetis sünkroniseerimise viide ...)"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Kasutusel andmike reastamiseks töölaual"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"Kasutatakse kassa lugemise ja süsteemi järgse kassa võrdlemisel tekkiva "
"kahju registreerimiseks"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Kasutatakse kassa lugemise ja süsteemi järgse kassa võrdlemisel tekkiva kasu"
" registreerimiseks"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__user_id
msgid "User"
msgstr "Kasutaja"

#. module: account
#: model:ir.model,name:account.model_res_users
msgid "Users"
msgstr "Kasutajad"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,help:account.field_res_partner__currency_id
#: model:ir.model.fields,help:account.field_res_users__currency_id
msgid "Utility field to express amount currency"
msgstr "Kasulik väli väljendamaks valuuta summat"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__vat_required
msgid "VAT required"
msgstr "Käibemaks vajalik"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr "Kinnita"

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr "Kinnita konto liikumine"

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__auto_reconcile
msgid ""
"Validate the statement line automatically (reconciliation based on your "
"rule)."
msgstr "Kinnita väljavõtte rida automaatselt (sobitamine vastavalt reeglile)"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_bank_statement__state__confirm
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__posted
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr "Kinnitatud"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value_amount
msgid "Value"
msgstr "Väärtus"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__partner_type__supplier
#: model_terms:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Vendor"
msgstr "Tarnija"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_vendor_bill_id
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__in_invoice
#: model:ir.model.fields.selection,name:account.selection__account_move__type__in_invoice
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Vendor Bill"
msgstr "Tarnija arve"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Vendor Bill Created"
msgstr "Ostuarve loodud"

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#, python-format
msgid "Vendor Bills"
msgstr "Ostuarved"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__in_refund
#: model:ir.model.fields.selection,name:account.selection__account_move__type__in_refund
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr "Tarnija kreeditarve"

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Vendor Payment"
msgstr "Tarnija makse"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Tarnija maksetingimused"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Vendor Payments"
msgstr "Tarnija maksed"

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product__supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template__supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Ostumaksud"

#. module: account
#: model:ir.actions.act_window,name:account.res_partner_action_supplier
#: model:ir.ui.menu,name:account.menu_account_supplier
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model_terms:ir.ui.view,arch_db:account.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Vendors"
msgstr "Tarnijad"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "Kinnita"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "View"
msgstr "Vaade"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "View accounts detail"
msgstr "Vaadake kontode üksikasju"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__invoice_warn__warning
msgid "Warning"
msgstr "Hoiatus"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Warning for %s"
msgstr "Hoiatus %s"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr "Hoiatus arvel"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Warnings"
msgstr "Hoiatused"

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_warning_account
msgid "Warnings in Invoices"
msgstr "Hoiatused müügiarvetel"

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"We cannot find a chart of accounts for this company, you should configure it. \n"
"Please go to Account Configuration and select or install a fiscal localization."
msgstr ""
"Me ei leia selle ettevõtte kontoplaani, peaksite selle looma.\n"
"Minge konto konfiguratsiooni ja valige või installige fiskaalne lokaliseerimine."

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"We cannot find any journal for this company. You should create one.\n"
"Please go to Configuration > Journals."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_journal__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_move__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,help:account.field_account_journal__website_message_ids
#: model:ir.model.fields,help:account.field_account_move__website_message_ids
#: model:ir.model.fields,help:account.field_account_payment__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_receipt_type
msgid ""
"When the purchase receipt is confirmed, you can record the\n"
"                vendor payment related to this purchase receipt."
msgstr ""
"Kui ostuarve on kinnitatud, siis on võimalik\n"
"                salvestada makse tarnijale, mis on seotud selle ostuarvega."

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_receipt_type
msgid ""
"When the sale receipt is confirmed, you can record the customer\n"
"                payment related to this sales receipt."
msgstr ""
"Kui müügiarve on kinnitatud, siis on võimalik\n"
"                salvestada kliendi makse, mis on seotud selle müügiarvega."

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Kas näidata seda andmiku töölaual või mitte"

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__new_journal_name
msgid "Will be used to name the Journal related to this bank account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal__amount_currency
msgid "With Currency"
msgstr "Valuutaga"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr "Maksudega"

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Write-Off"
msgstr "Mahakandmine"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount
msgid "Write-off Amount"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Mahakandmise kuupäev"

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_credit_debit
msgid "Wrong credit or debit value in accounting entry !"
msgstr "Vale kreedit- või deebetväärtus raamatupidamiskirjendil!"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled."
msgstr "Te üritate sobitada kirjeid, mis on juba omavahel ühendatud."

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""
"Sa saad märkida selle kasti, et märgistada andmiku kanderea kui õigustüli "
"seotud partneriga"

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid ""
"You can not delete payment terms as other records still reference it. "
"However, you can archive it."
msgstr ""
"Maksetingimusi ei saa kustutada, kuna muud kirjed viitavad sellele ikkagi. "
"Kuid saate selle arhiivida."

#. module: account
#: code:addons/account/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "You can only change the period for items in these types of accounts: "
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "You can only change the period for items that are not yet reconciled."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "You can only change the period for posted journal items."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You can only register at the same time for payment that are all from the "
"same company"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You can only register at the same time for payment that are all inbound or "
"all outbound"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "You can only register payments for open invoices"
msgstr "Saate registreerida ainult avatud arvete makseid"

#. module: account
#: code:addons/account/wizard/account_invoice_send.py:0
#, python-format
msgid "You can only send invoices."
msgstr "Te saate ainult saata arveid."

#. module: account
#: code:addons/account/wizard/account_payment_send.py:0
#, python-format
msgid "You can only send payments."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You can only set an account having the payable type on payment terms lines "
"for vendor bill."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You can only set an account having the receivable type on payment terms "
"lines for customer invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You can't change the company of your account since there are some journal "
"items linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You can't change the company of your journal since there are some journal "
"entries linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You can't change the company of your tax since there are some journal items "
"linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account_analytic_line.py:0
#, python-format
msgid ""
"You can't set a different company on your analytic account since there are "
"some journal items linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account_analytic_line.py:0
#, python-format
msgid ""
"You can't set a different company on your analytic tags since there are some"
" journal items linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""
"Te ei saa lisada/muuta kandeid enne ja kaasa arvatud lukustamise kuupäeval "
"%s. Vaata ettevütte seadeud või küsi kelleltki, kellel on \"Nõustaja\" roll."

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr ""
"Ettevõtte valuutat ei saa enam muuta, sest juba on tehtud andmike kanderead."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr ""
"Sa ei saa muuta omanikettevõtet kontol, mis juba sisaldab andmike "
"kanderidasid."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot create a move already in the posted state. Please create a draft "
"move and post it after."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "You cannot delete a payment that is already posted."
msgstr "Te ei saa kustutada juba sisestatud makset."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot delete an entry which has been posted once."
msgstr "Te ei saa kustutada juba sisestatud kannet."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot delete an item linked to a posted entry."
msgstr ""

#. module: account
#: code:addons/account/models/res_config_settings.py:0
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""
"Te ei saa seda seadistust keelata, sest mõned teie maksud on kassapõhised. "
"Enne selle seadistuse keelamist muutke oma makse."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot do this modification on a reconciled journal entry. You can just change some non legal fields or you must unreconcile first.\n"
"Journal Entry (id): %s (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot edit the following fields due to restrict mode being activated on"
" the journal: %s."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot edit the journal of an account move if it has been posted once."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""
"Teil ei saa olla laekumiste/maksmiste kontot, mida ei saa ühendada. (konto "
"kood: %s)"

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot have more than one account with \"Current Year Earnings\" as "
"type. (accounts: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot modify a journal entry linked to a posted payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot modify a posted entry of this journal because it is in strict "
"mode."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot modify the field %s of a journal that already has accounting "
"entries."
msgstr ""
"Te ei saa muuta %s välja andmikul, mis sisaldab juba raamatupidamiskandeid."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot modify the taxes related to a posted journal item, you should "
"reset the journal entry to draft to do so."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot overwrite the values ensuring the inalterability of the "
"accounting."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot perform this action on an account that contains journal items."
msgstr ""
"Te ei saa viia läbi seda toimingut kontol, mis sisaldab andmike kanderidu"

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""
"Te ei saa lisada/võtta raha sisse/välja suletud pangakontole/pangakontolt."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You cannot register payments for customer invoices and credit notes at the "
"same time."
msgstr ""
"Sa ei saa registreerida korraga müügiarvete ja krediidiarvete makseid."

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You cannot register payments for vendor bills and supplier refunds at the "
"same time."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "You cannot remove the bank account from the journal once set."
msgstr "Te ei saa eemaldada pangakontot, mis on kord juba andmikusse seatud. "

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""
"Te ei saa eemaldada/deaktiveerida kontot, mis on määratud kliendile või "
"tarnijale."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft a tax cash basis journal entry."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft an exchange difference journal entry."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot switch an account to prevent the reconciliation if some partial "
"reconciliations are still pending."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot use a deprecated account."
msgstr "Te ei saa kasutada mittesoovitatavat kontot."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot use taxes on lines with an Off-Balance account"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""
"Te ei saa kasutada seda üldkontot selles andmikus, vaadake vahelehte "
"\"Kannete reguleerimine\" seotud andmikus."

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead. Use the action menu to transform it into a "
"credit note or refund."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You don't have the access rights to post an invoice."
msgstr "Teil puuduvad arve postitamiseks vajalikud juurdepääsuõigused."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "You have"
msgstr "Teil on"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "You have suspense account moves that match this invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr "Te peate määratlema järjestuse %s oma ettevõttes."

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid ""
"You have to define an 'Internal Transfer Account' in your cash register's "
"journal."
msgstr "Pead määrama 'Sisemise Siirde Konto' oma sularaha kassa registris."

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "You must first define an opening move."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "The entry %s (id %s) is already posted."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You need to add a line before posting."
msgstr "Enne postitamist on vaja lisada rida"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Te sobitasite"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""
"Peaksid raamatupidamisseadetes \"Vahetuskursi andmik\" seadistama, et "
"automaatselt vahetuskursi erinevustega seotud raamatupidamiskannete "
"broneerimist hallata. "

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Peaksid raamatupidamisseadetes \"Vahetuskursi võidu konto\" seadistama, et "
"automaatselt vahetuskursi erinevustega seotud raamatupidamiskannete "
"broneerimist hallata. "

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""
"Peaksid raamatupidamisseadetes 'Vahetuskursi kaotuse konto' seadistama, et "
"automaatselt vahetuskursi erinevustega seotud raamatupidamiskannete "
"broneerimist hallata. "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr "Sihtnumbri vahemik"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_from
msgid "Zip Range From"
msgstr "Sihtnumber alates"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_to
msgid "Zip Range To"
msgstr "Sihtnumber kuni"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "ja teha klientidele järelpärimisi"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "assign to invoice"
msgstr "määra arvele"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr "sulge"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr "kood"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__option__day_after_invoice_date
msgid "days after the invoice date"
msgstr "päevi peale arve kuupäeva"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g ****************"
msgstr "nt. ****************"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Bank of America"
msgstr "nt. Bank of America"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Checking account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr "nt pangateenustasud"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_group_form
msgid "e.g. GAAP, IFRS, ..."
msgstr "nt. GAAP, IFRS,..."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""
"sellelt kliendilt. Teil on võimalik lisada makse, et märkida arve makstuks."

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""
"selle tarnija jaoks. Saate neid jaotada selle arve makstuks märkimiseks."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "on sobitatud automaatselt"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__invoice_without_email
msgid "invoice(s) that will not be sent"
msgstr "arve(d) mis on saatmata"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_tree
msgid "name"
msgstr "Nimi"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line__repartition_type__tax
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line_template__repartition_type__tax
msgid "of tax"
msgstr "maksust"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__option__day_current_month
msgid "of the current month"
msgstr "käesolevas kuus"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__option__day_following_month
msgid "of the following month"
msgstr "eelolevas kuus"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "on"
msgstr " "

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "outstanding debits"
msgstr "tasumata võlad"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "outstanding payments"
msgstr "saamata makseid"

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__invoice_payment_state__paid
msgid "paid"
msgstr "Tasutud"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr "sobitada"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "sobitamise mudelid"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "ülejäänud)"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "sekundit tehingu kohta."

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr "väljavõtte read"

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_group_id
msgid "technical field for widget tax-group-custom-field"
msgstr "tehniline väli maksu-grupi-kohandatud-välja vidinale"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr "ülem ettevõte"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "kontrolli üle"

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The chosen journal has a type that is not compatible with your invoice type."
" Sales operations should go to 'sale' journals, and purchase operations to "
"'purchase' ones."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "to mark this invoice as paid."
msgstr "märgi see arve makstuks"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "tehingud"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "tasumata müügiarveid"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "sobitamata kandeid"

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "ostuarved"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr "→ Loenda"
