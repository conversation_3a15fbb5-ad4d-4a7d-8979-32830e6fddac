<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- QWeb Reports -->
        <report
            id="account_invoices"
            model="account.move"
            string="Invoices"
            report_type="qweb-pdf"
            name="account.report_invoice_with_payments"
            file="account.report_invoice_with_payments"
            attachment="(object.state == 'posted') and ((object.name or 'INV').replace('/','_')+'.pdf')"
            print_report_name="(object._get_report_base_filename())"
            groups="account.group_account_invoice"
        />

        <record id="action_account_original_vendor_bill" model="ir.actions.report">
            <field name="name">Original Bills</field>
            <field name="model">account.move</field>
            <field name="binding_model_id" ref="model_account_move"/>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">account.report_original_vendor_bill</field>
            <field name="report_file">account.report_original_vendor_bill</field>
            <field name="attachment">'original_vendor_bill.pdf'</field>
            <field name="attachment_use">True</field>
            <field name="binding_view_types">list</field>
        </record>

        <report
            id="account_invoices_without_payment"
            model="account.move"
            string="Invoices without Payment"
            report_type="qweb-pdf"
            name="account.report_invoice"
            file="account.report_invoice"
            attachment="(object.state == 'posted') and ((object.name or 'INV').replace('/','_')+'.pdf')"
            print_report_name="(object._get_report_base_filename())"
        />

        <report
            id="action_report_aged_partner_balance"
            model="res.partner"
            string="Aged Partner Balance"
            menu="False"
            report_type="qweb-pdf"
            name="account.report_agedpartnerbalance"
            file="account.report_agedpartnerbalance"
        />

        <report
            id="action_report_journal"
            model="account.common.journal.report"
            string="Journals Audit"
            report_type="qweb-pdf"
            name="account.report_journal"
            file="account.report_journal"
            />

        <report
            id="action_report_payment_receipt"
            model="account.payment"
            string="Payment Receipt"
            report_type="qweb-pdf"
            name="account.report_payment_receipt"
            file="account.report_payment_receipt"
            menu="True"
        />

        <report
            id="action_report_account_statement" 
            model="account.bank.statement"
            string="Statement"
            name="account.report_statement"
            file="account.report_statement"
            report_type="qweb-pdf"
        />

        <report
            id="action_report_account_hash_integrity"
            model="res.company"
            string="Hash integrity result PDF"
            report_type="qweb-pdf"
            name="account.report_hash_integrity"
            file="account.report_hash_integrity"
            menu="False"
        />

    </data>
</odoo>
