# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product
# 
# Translators:
# 8bbc36517816c0507c9ca350a0e523fd_9e68207 <90a3963e59612d1681dec64fbc906ae0_331927>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>ban <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>gaux, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# Yedigen, 2019
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: Tugay Hatıl <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__2_product_category
msgid " Product Category"
msgstr "Ürün Kategorisi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# Ürün Varyantları"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# Ürünler"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "%s %% discount"
msgstr "%s indirim"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "%s %% discount and %s surcharge"
msgstr "%s %% indirim ve %s ek ücret"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopya)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_label
#: model:ir.actions.report,print_report_name:product.report_product_template_label
msgid "'Products Labels - %s' % (object.name)"
msgstr "'Ürün Etiketleri - %s' % (object.name)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_product_barcode
#: model:ir.actions.report,print_report_name:product.report_product_template_barcode
msgid "'Products barcode - %s' % (object.name)"
msgstr "'Ürün barkodları - %s' % (object.name)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_packaging
msgid "'Products packaging - %s' % (object.name)"
msgstr "'Ürün paketleme- %s' % (object.name)"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid ""
"- Instantly: All possible variants are created as soon as the attribute and its values are added to a product.\n"
"        - Dynamically: Each variant is created only when its corresponding attributes and values are added to a sales order.\n"
"        - Never: Variants are never created for the attribute.\n"
"        Note: the variants creation mode cannot be changed once the attribute is used on at least one product."
msgstr ""
"- Anında: Tüm olası değişkenler, özellik ve değerleri bir ürüne eklenir eklenmez oluşturulur.\n"
"- Dinamik olarak: Her değişken yalnızca bir müşteri siparişine karşılık gelen özellikler ve değerler eklendiğinde oluşturulur.\n"
"- Asla: Özellik için asla varyantlar oluşturulmaz.\n"
"Not: özellik en az bir üründe kullanıldıktan sonra varyant oluşturma modu değiştirilemez."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr "1 yıl"

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4b_product_template
msgid "160x80cm, with large legs."
msgstr "160x80cm, geniş bacak."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr "2 yıl"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span attrs=\"{'invisible': [('pricelist_item_count', '=', 1)]}\">\n"
"                                          Extra Prices\n"
"                                        </span>\n"
"                                        <span attrs=\"{'invisible': [('pricelist_item_count', '!=', 1)]}\">\n"
"                                          Extra Price\n"
"                                        </span>"
msgstr ""
"<span attrs=\"{'invisible': [('pricelist_item_count', '=', 1)]}\">\n"
"                                          Ekstra Fiyatlar\n"
"                                        </span>\n"
"                                        <span attrs=\"{'invisible': [('pricelist_item_count', '!=', 1)]}\">\n"
"                                          Ekstra Fiyat\n"
"                                        </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Sales Price  -  </span>\n"
"                              <span attrs=\"{'invisible':[('base', '!=', 'standard_price')]}\">Cost  -  </span>\n"
"                              <span attrs=\"{'invisible':[('base', '!=', 'pricelist')]}\">Other Pricelist  -  </span>"
msgstr ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Satış Fiyatı  -  </span>\n"
"                              <span attrs=\"{'invisible':[('base', '!=', 'standard_price')]}\">Maliyet  -  </span>\n"
"                              <span attrs=\"{'invisible':[('base', '!=', 'pricelist')]}\">Diğer Fiyat Listesi  -  </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> Ürünler</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_simple_barcode
#: model_terms:ir.ui.view,arch_db:product.report_simple_label
msgid "<span class=\"text-muted\">No barcode available</span>"
msgstr "<span class=\"text-muted\">Barkod yok</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>All general settings about this product are managed on</span>"
msgstr "<span>Bu ürün hakkında genel ayarları yönet</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Currency</strong>:<br/>"
msgstr "<strong>Para Birimi</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Description</strong>"
msgstr "<strong>Açıklama:</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Price List Name</strong>:<br/>"
msgstr "<strong>Fiyat Listesi Adı</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_simple_label
msgid "<strong>Price:</strong>"
msgstr "<strong>Fiyat:</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Print date</strong>:<br/>"
msgstr "<strong>Yazdırma tarihi:</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr "<strong>Miktar: </strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""
"<strong>Dikkat !</strong>: Nitelik eklemek veya silmek\n"
"                        mecvut varyantların silinmesine ve yeniden oluşturulmasına neden olur,\n"
"                        muhtemel özelleştirmelerinizi kaybetmemek için dikkatli olun."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_product_barcode_uniq
msgid "A barcode can only be assigned to one product !"
msgstr "Bir barkod sadece bir ürüne atanabilir!"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Müşterilerinizle iletişim kurarken göstermek isteyeceğiniz bir ürün "
"açıklaması. Bu açıklama, her Satış Siparişi, Teslimat Siparişi ve Müşteri "
"Fatura, İade ve Fiyat Farklarına getirilecektir."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"                This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""
"Bir fiyat, ürünlere, ürün kategorilerine, tarihlere ve sipariş edilen miktarlara dayalı olarak satış siparişi satırlarının fiyatını hesaplamak için bir satış fiyatları veya kuralları kümesidir.\n"
"Bu, birkaç fiyatlandırma, mevsimsel indirimler vb. için mükemmel bir araçtır."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__type
#: model:ir.model.fields,help:product.field_product_template__type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Acoustic Bloc Screens"
msgstr "Akustik Blok Ekranları"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Eylem Gerekiyor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_active
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Active"
msgstr "Etkin"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "Aktiviteler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Etkinlik İstisna Donatımı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "Aktivite Durumu"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_sale_pricelist
#: model:res.groups,name:product.group_sale_pricelist
msgid "Advanced Pricelists"
msgstr "Gelişmiş Fiyat Listeleri"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__advanced
msgid "Advanced price rules (discounts, formulas)"
msgstr "Gelişmiş fiyat kuralları (indirimler, formüller)"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__3_global
#, python-format
msgid "All Products"
msgstr "Tüm Ürünler"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""
"Müşteri kategorilerine göre değişik fiyat kuralları uygulanmasını sağlar.\n"
"Örnek: Perakendeciler için %10, bu ürün için 5 TL indirim gibi."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
#: model:product.template.attribute.value,name:product.product_11_attribute_1_value_2
#: model:product.template.attribute.value,name:product.product_4_attribute_1_value_2
msgid "Aluminium"
msgstr "Alüminyum"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_inherit
msgid "Applicable On"
msgstr "Uygulanabilir"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
msgid "Applied On"
msgstr "Uygulanan"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "Şuna Uygula"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Archived"
msgstr "Arşivlendi"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Ürün tedarikçi listesine öncelik atar."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "Nitelik"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_line_id
msgid "Attribute Line"
msgstr "Nitelik Satırı"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "Nitelik Adı"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
msgid "Attribute Value"
msgstr "Nitelik Değerleri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Nitelik Değerleri"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Attributes"
msgstr "Nitelikler"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Availability"
msgstr "Başlayabileceği Tarih"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
#: model_terms:ir.ui.view,arch_db:product.report_simple_barcode
#: model_terms:ir.ui.view,arch_db:product.report_simple_label
msgid "Barcode"
msgstr "Barkod"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid ""
"Barcode used for packaging identification. Scan this packaging barcode from "
"a transfer in the Barcode app to move all the contained units"
msgstr ""
"Ambalaj tanımlaması için kullanılan barkod. İçindeki tüm birimleri taşımak "
"için bu ambalaj barkodunu Barkod uygulamasındaki bir transferden tarayın"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Sales Price: The base price will be the Sales Price.\n"
"Cost Price : The base price will be the cost price.\n"
"Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""
"Hesaplama için temel fiyat.\n"
"Perakende Fiyatı: Temel fiyat Satış/Perakende Fiyatı olacak.\n"
"Maliyet Fiyatı: Temel fiyat maliyet fiyatı olacak.\n"
"Diğer Fiyat Listeleri: Başka Fiyat Listesini baz alan temel fiyatın hesaplanması."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "Buna göre"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Basic Pricelists"
msgstr "Temel Fiyat Listeleri"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
#: model:product.template.attribute.value,name:product.product_4_attribute_2_value_2
msgid "Black"
msgstr "Siyah"

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr "Kapılı Dolap"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Calculate Product Price per Unit Based on Pricelist Version."
msgstr "Fiyat listesi sürümüne bağlı birim için Ürün Fiyatını hesapla"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:product.field_product_template__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Görüntü 1024 yakınlaştırılabilir"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr "Varyant Görüntüsü 1024 yakınlaştırılabilir"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Purchased"
msgstr "Satın Alınabilir"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__rental
#: model:ir.model.fields,field_description:product.field_product_template__rental
msgid "Can be Rent"
msgstr "Kiralanabilir"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Satılabilir"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Cancel"
msgstr "İptal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category name"
msgstr "Kategori Adı"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Category: %s"
msgstr "Kategori: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "Alt Kategoriler"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Choose the unit to measure weight"
msgstr "Ağırlığı ölçmek için birimi seçin"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Kodlar"

#. module: product
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Renk"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "Renk"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__combination_indices
msgid "Combination Indices"
msgstr "Kombinasyon Endeksleri"

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "Şirket"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "Tam Adı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Compute Price"
msgstr "Fiyat Hesaplama"

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11b_product_template
msgid "Conference Chair (CONFIG)"
msgstr "Konferans Koltuğu (CONFIG)"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "Conference room table"
msgstr "Konferans masası"

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigürasyon Ayarları"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Configuration"
msgstr "Yapılandırma"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Configure Variants"
msgstr "Varyantları Yapılandır"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__type__consu
msgid "Consumable"
msgstr "Sarf Malzeme"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr "İçerdiği Miktar"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr "İçerdiği Miktar"

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Black"
msgstr "Siyah Köşe Çalışma Masası"

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr "Sağ Köşe Çalışma Masası"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__standard_price
msgid "Cost"
msgstr "Maliyet"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__cost_currency_id
#: model:ir.model.fields,field_description:product.field_product_template__cost_currency_id
msgid "Cost Currency"
msgstr "Maliyet Para Birimi"

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Ülke Grubu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "Ülke Grupları"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr "Yeni Satış Listesi Oluştur"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "Yeni bir ürün oluştur"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr "Yeni bir ürün varyantı oluştur"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_price_list__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_price_list__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__1
msgid "Cubic Feet"
msgstr "Fit Küp"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__0
msgid "Cubic Meters"
msgstr "Metre Küp"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
msgid "Currency"
msgstr "Para Birimi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "Müşteri Ref"

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4b_product_template
msgid "Customizable Desk (CONFIG)"
msgstr "Özelleştirilebilir Masa (CONFIG)"

#. module: product
#: model:product.product,uom_name:product.expense_hotel
#: model:product.template,uom_name:product.expense_hotel_product_template
msgid "Days"
msgstr "Gün"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "Ondalık Hassasiyeti"

#. module: product
#: code:addons/product/models/res_company.py:0
#: code:addons/product/models/res_company.py:0
#, python-format
msgid "Default %(currency)s pricelist"
msgstr "Varsayılan fiyat listesi %(currency)s"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Tüm stok işlemleri için varsayılan ölçü birimi."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""
"Satınalma siparişleri için kullanılan varsayılan ölçü birimi. Varsayılan "
"ölçü birimiyle aynı kategoride olmalıdır."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__seller_ids
#: model:ir.model.fields,help:product.field_product_template__seller_ids
msgid "Define vendor pricelists."
msgstr "Tedarikçi fiyat listelerini tanımlayın."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "Teslim Süresi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "Açıklama"

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr "Masa Kombinasyonu"

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr "Ekranlı Masa Standı"

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr "Masa kombinasyonu, siyah-kahverengi: sandalye + masa + çekmece."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
msgid "Determine the display order"
msgstr "Görünüm sırasını belirle"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__discount_policy
msgid "Discount Policy"
msgstr "İndirim Kuralı"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__with_discount
msgid "Discount included in the price"
msgstr "İndirim fiyata dahil edildi"

#. module: product
#: model:res.groups,name:product.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Satış Satırında İndirim"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_discount_per_so_line
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Discounts"
msgstr "İndirimler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_price_list__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr "Çekmece"

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr "Siyah Çekmece"

#. module: product
#: model:product.product,description:product.product_product_27
#: model:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr "İki yönlendirme imkanına sahip çekmece."

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "Süre"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__dynamic
msgid "Dynamically"
msgstr "Dinamik"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_template_attribute_value_attribute_value_unique
msgid "Each value should be defined only once per attribute per product."
msgstr "Her değer, ürün nitelik başına yalnızca bir kez tanımlanmalıdır."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "Bu tedarikçi fiyatı için son gün"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid "Ending valid for the pricelist item validation"
msgstr "Fiyat listesi öğe doğrulaması için geçerli bitiş."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr "Hariç"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "Bu fiyat listesi satırı için belirgin kural adı"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Extra price for the variant with this attribute value on sale price. eg. 200"
" price extra, 1000 + 200 = 1200."
msgstr ""
"Satış fiyatı üzerinden bu niteliklere sahip varyant için ekstra fiyat. "
"Örneğin. 200 ekstra fiyat, 1000 + 200 = 1200."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__fixed
msgid "Fixed Price"
msgstr "Sabit Fiyat"

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr "Ters çevir"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_channel_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_channel_ids
msgid "Followers (Channels)"
msgstr "Takipçiler (Kanallar)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Tekipçiler (İş ortakları)"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"Kuralın uygulanması için, satın alınan/satılan miktar, bu alanda belirlenen "
"minimum miktardan daha fazla ya da eşit olmalıdır."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__formula
msgid "Formula"
msgstr "Formül"

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr "4 Kişilik Çalışma Masası"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid "Four person modern office workstation"
msgstr "Dört kişilik modern ofis iş istasyonu"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "Sonraki Aktiviteler"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Genel Bilgi"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "Bir ürün için farklı paketleme yolları tanımlayın"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Ürün listesi gösterildiğinde sıralama emri ver."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Go to Internal Categories"
msgstr "İç Kategorilere Git"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Group By"
msgstr "Grupla"

#. module: product
#: model:product.product,name:product.expense_hotel
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "Otel Konaklama"

#. module: product
#: model:product.product,uom_name:product.product_product_1
#: model:product.product,uom_name:product.product_product_2
#: model:product.template,uom_name:product.product_product_1_product_template
#: model:product.template,uom_name:product.product_product_2_product_template
msgid "Hours"
msgstr "Saat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_price_list__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist__id
msgid "ID"
msgstr "ID"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_icon
msgid "Icon"
msgstr "İkon"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Bir istisna faaliyetini gösteren simge."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_product__message_unread
#: model:ir.model.fields,help:product.field_product_template__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_unread
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse,bazı mesajlar gönderi hatası içermektedir."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr "Ayarlanmazsa, satıcı fiyatı bu ürünün tüm varyantlarına uygulanır."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
#: model:ir.model.fields,help:product.field_product_pricelist_item__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr ""
"İşareti kaldırırsanız, fiyat listesini kaldırmadan gizlemenizi "
"sağlayacaktır."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "İşaretlerseniz, ürünü silmeden gizlemenizi sağlar."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1920
#: model:ir.model.fields,field_description:product.field_product_template__image_1920
msgid "Image"
msgstr "Görsel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1024
#: model:ir.model.fields,field_description:product.field_product_template__image_1024
msgid "Image 1024"
msgstr "Görsel 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_128
#: model:ir.model.fields,field_description:product.field_product_template__image_128
msgid "Image 128"
msgstr "Görsel 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_256
#: model:ir.model.fields,field_description:product.field_product_template__image_256
msgid "Image 256"
msgstr "Görsel 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_512
#: model:ir.model.fields,field_description:product.field_product_template__image_512
msgid "Image 512"
msgstr "Görsel 512"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Import Template for Pricelists"
msgstr "Fiyat Listeleri için İçe Aktarma Şablonu"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Ürünler İçe Aktarma Şablonu"

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "Import Template for Vendor Pricelists"
msgstr "Tedarikçi Fiyat Listeleri için İçe Aktarma Şablonu"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"In Standard Price & AVCO: value of the product (automatically computed in AVCO).\n"
"        In FIFO: value of the last unit that left the stock (automatically computed).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"Standart Maliyet ve AVCO'da: ürünün değeri (AVCO'da otomatik olarak hesaplanır).\n"
"FIFO'da: stoktan ayrılan son birimin değeri (otomatik olarak hesaplanır).\n"
"Satın alma maliyeti bilinmediğinde ürüne değer vermek için kullanılır (örn. Envanter ayarlaması).\n"
"Satış siparişlerindeki marjları hesaplamak için kullanılır."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "In which unit of measure do you manage your volumes"
msgstr "Birimlerinizi hangi ölçü biriminde yönetiyorsunuz?"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Inactive"
msgstr "Pasif"

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr "Bireysel İşyeri"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__always
msgid "Instantly"
msgstr "Hemen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "İç Notlar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "İç Referans"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
#: model:ir.model.fields,help:product.field_product_template__barcode
msgid "International Article Number used for product identification."
msgstr "Uluslararası Makale numarası ürün tanımlama için kullanılır."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Stok"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr "Ürün Varyantı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_configurable_attributes
#: model:ir.model.fields,field_description:product.field_product_template__has_configurable_attributes
msgid "Is a configurable product"
msgstr "Yapılandırılabilir bir üründür"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr "Ürün varyantı mı?"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__0
msgid "Kilogram"
msgstr "Kilogram"

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr "Büyük Dolap"

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr "Büyük Masa"

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr "Büyük Toplantı Masası"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute____last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value____last_update
#: model:ir.model.fields,field_description:product.field_product_category____last_update
#: model:ir.model.fields,field_description:product.field_product_packaging____last_update
#: model:ir.model.fields,field_description:product.field_product_price_list____last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist____last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item____last_update
#: model:ir.model.fields,field_description:product.field_product_product____last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo____last_update
#: model:ir.model.fields,field_description:product.field_product_template____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value____last_update
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_price_list__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_price_list__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_product__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "Geciken Aktiviteler"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"Satınalma siparişi ve ürünlerin deponuza girişi arasındaki hazırlık süresi "
"(gün olarak). Sistem tarafından satınalma siparişi planlamasının otomatik "
"hesaplanmasında kullanılır."

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr "Bacaklar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_attribute_value__pav_attribute_line_ids
msgid "Lines"
msgstr "Satırlar"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Logistics"
msgstr "Lojistik"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_main_attachment_id
#: model:ir.model.fields,field_description:product.field_product_template__message_main_attachment_id
msgid "Main Attachment"
msgstr "Ana Ek"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with other values of the product or"
" some attribute values of optional and accessory products."
msgstr ""
"Bu nitelik değerini ürünün diğer değerleriyle veya isteğe bağlı ve aksesuar "
"ürünlerin bazı özellik değerleriyle uyumlu değil yapın."

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Ürün Paketleme Yönetimi"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Ürün Varyantlarını Yönet"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Max. Marj (%)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "Max. Br.Fiyat Marjı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Min. Marj (%)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "Min. Fiyat Marjı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "Min. Miktar"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__basic
msgid "Multiple prices per product"
msgstr "Ürün başına birden çok fiyat"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__product_pricelist_setting
msgid ""
"Multiple prices: Pricelists with fixed price rules by product,\n"
"Advanced rules: enables advanced price rules for pricelists."
msgstr ""
"Birden çok fiyat: Ürüne göre sabit fiyat kurallarına sahip fiyat listeleri\n"
"Gelişmiş kurallar: fiyat listeleri için gelişmiş fiyat kurallarını etkinleştirir."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "Adı"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__no_variant
msgid "Never"
msgstr "Asla"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "New Price ="
msgstr "Yeni Fiyat ="

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Sonraki Aktivite Zaman Sınırı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "Sonraki Aktivite Özeti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "Sonraki Aktivite Türü"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Eylemlerin Sayısı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Number of errors"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Eylem gerektiren mesaj sayısı"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj sayısı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_count
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_item_count
msgid "Number of price rules"
msgstr "Fiyat kuralları sayısı"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_unread_counter
#: model:ir.model.fields,help:product.field_product_template__message_unread_counter
msgid "Number of unread messages"
msgstr "Okunmamış mesaj sayısı"

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr "Ofis sandalyesi"

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr "Siyah Ofis Koltuğu"

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr "Ofis Tasarım Yazılımı"

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr "Ofis Lambası"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"On the product %s you cannot associate the value %s with the attribute %s "
"because they do not match."
msgstr ""
"%sÜrününde eşleşmediği için %sdeğeri %s öznitelikle ilişkilendiremezsiniz."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"On the product %s you cannot transform the attribute %s into the attribute "
"%s."
msgstr "%s ürünü %siçindeki niteliğini %sniteliğine dönüştüremezsiniz."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Other Information"
msgstr "Diğer Bilgiler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__pricelist
msgid "Other Pricelist"
msgstr "Diğer Fiyat Listesi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__name
msgid "Package Type"
msgstr "Paket Türü"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Paketleme"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "Üst Kategori"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr "Üst Yol"

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr "Pedallı çöp kovası"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__percentage
msgid "Percentage (discount)"
msgstr "Yüzde (iskonto)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "Yüzde Fiyatı"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Please specify the category for which this rule should be applied"
msgstr "Lütfen bu kuralın uygulanması gereken kategoriyi belirtin"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Please specify the product for which this rule should be applied"
msgstr "Lütfen bu kuralın uygulanması gereken ürünü belirtin"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"Please specify the product variant for which this rule should be applied"
msgstr "Lütfen bu kuralın uygulanması gereken ürün varyantını belirtin"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__1
msgid "Pound"
msgstr "Pound"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_product__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model:ir.model.fields,field_description:product.field_product_template__price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_inherit
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Price"
msgstr "Fiyat"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Fiyat Hesaplama"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "Fiyat İndirimi"

#. module: product
#: model:ir.actions.act_window,name:product.action_product_price_list
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Price List"
msgstr "Fiyat Listesi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "Fiyat Yuvarlama"

#. module: product
#: code:addons/product/models/product.py:0
#: code:addons/product/models/product_template.py:0
#: model:ir.actions.act_window,name:product.product_pricelist_item_action
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#, python-format
msgid "Price Rules"
msgstr "Fiyat Kuralları"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Price Surcharge"
msgstr "Fiyat Artırımı"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
#: model:ir.model.fields,help:product.field_product_template__lst_price
msgid "Price at which the product is sold to customers."
msgstr "Ürünün müşterilere satıldığı fiyat."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Fiyat:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__price_list
msgid "PriceList"
msgstr "Fiyat Listesi"

#. module: product
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Pricelist"
msgstr "Fiyat Listesi"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Seçilen seçeneğe uygulanabilen Fiyat Listesi Öğeleri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_inherit
msgid "Pricelist Items"
msgstr "Fiyat Listesi Öğeleri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "Fiyat Listesi Adı"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Fiyat Listesi Kuralı"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "Fiyat Listeleri"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_pricelist_setting
msgid "Pricelists Method"
msgstr "Fiyat Listeleri Yöntemi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Fiyat listeleri bundan yönetilir"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Fiyatlandırma"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Print"
msgstr "Yazdır"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__1_product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Product"
msgstr "Ürün"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "Ürün Niteliği"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr "Ürün Nitelik Değerleri"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "Ürün Nitelikleri ve Değerleri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "Ürün Nitelikleri"

#. module: product
#: model:ir.actions.report,name:product.report_product_product_barcode
#: model:ir.actions.report,name:product.report_product_template_barcode
msgid "Product Barcode (PDF)"
msgstr "Ürün Barkodu (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Ürün Kategorileri"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Product Category"
msgstr "Ürün Kategorisi"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_sale_product_configurator
msgid "Product Configurator"
msgstr "Ürün Yapılandırma"

#. module: product
#: model:ir.actions.report,name:product.report_product_label
#: model:ir.actions.report,name:product.report_product_template_label
msgid "Product Label (PDF)"
msgstr "Ürün Etiketi (PDF)"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Ürün Adı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "Ürün Paketleri"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr "Ürün Paketleme"

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr "Ürün Ambalajı (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr "Ürün Paketlemeleri"

#. module: product
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "Product Price List Report"
msgstr "Ürün Fiyat Listesi Raporu"

#. module: product
#: model:ir.model,name:product.model_product_price_list
msgid "Product Price per Unit Based on Pricelist Version"
msgstr "Fiyat listesi sürümüne bağlı birim için Ürün Fiyatını hesapla"

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Ürün Şablonu"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr "Ürün Şablonu Niteliği Hariç"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Ürün Şablon Nitelik Satırı"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Ürün Şablonu Özellik Değeri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Product Type"
msgstr "Ürün Türü"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__0_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
msgid "Product Variant"
msgstr "Ürün Varyantı"

#. module: product
#: model:ir.actions.act_window,name:product.product_attribute_value_action
msgid "Product Variant Values"
msgstr "Ürün Varyant Değerleri"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_product_view_activity
msgid "Product Variants"
msgstr "Ürün Varyantları"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Product: %s"
msgstr "Ürün: %s"

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_activity
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Products"
msgstr "Ürünler"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Ürünlerin Fiyatı"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Ürün Fiyat Listesi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Products Price Rules Search"
msgstr "Ürün Fiyat Kuralları Ara"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Ürün Fiyatı Ara"

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "Products: "
msgstr "Ürünler: "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
#: model:ir.model.fields,field_description:product.field_product_template__lst_price
msgid "Public Price"
msgstr "Genel Liste Fiyatı"

#. module: product
#: model:product.pricelist,name:product.list0
msgid "Public Pricelist"
msgstr "Genel Fiyat Listesi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Purchase"
msgstr "Satınalma"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "Satınalma Açıklaması"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Satınalma Ölçü Birimi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Quantity"
msgstr "Miktar"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "Quantity of products contained in the packaging."
msgstr "Ambalajda bulunan ürün miktarı."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty1
msgid "Quantity-1"
msgstr "Miktar-1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty2
msgid "Quantity-2"
msgstr "Miktar-2"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty3
msgid "Quantity-3"
msgstr "Miktar-3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty4
msgid "Quantity-4"
msgstr "Miktar-4"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list__qty5
msgid "Quantity-5"
msgstr "Miktar-5"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
msgid "Reference"
msgstr "Referans"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__product_tmpl_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Related Products"
msgstr "İlgili Ürünler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_product_variant_ids
msgid "Related Variants"
msgstr "İlişkili Varyantlar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "Sorumlu Kullanıcı"

#. module: product
#: model:product.product,name:product.expense_product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr "Restoran Giderleri"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Yuvarlama Yöntemi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Satış"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales Description"
msgstr "Satış Açıklaması"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "Satış Tablo Girişi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__list_price
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Sales Price"
msgstr "Satış Fiyatı"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__categ_id
#: model:ir.model.fields,help:product.field_product_template__categ_id
msgid "Select category for the current product"
msgstr "Geçerli ürün için kategori belirtin"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
msgid "Sequence"
msgstr "Sıra"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__type__service
msgid "Service"
msgstr "Hizmet"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Services"
msgstr "Hizmetler"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Fiyatı bu değerin katı olarak ayarlar.\n"
"Yuvarlama indirimden sonra ek ücretten önce yapılır.\n"
"9.99 şeklinde bir fiyat elde etmek için yuvarlamayı 10, ek ücreti -0.01 olarak ayarlayın."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr "Bir sonraki eylem tarihi bugünden önce olan tüm kayıtları göster"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__without_discount
msgid "Show public price & discount to the customer"
msgstr "Herkese açık olan Fiyat ve İndirimleri Müşteriye göster"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Bu kural yalnızca bu kategori ya da alt kategorisine ait ürünlere "
"uygulanıyorsa bir ürün kategorisi belirleyin. Aksi durumda boş bırakın."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Bu kural yalnızca bir ürüne uygulanıyorsa bir ürün belirleyin. Aksi takdirde"
" boş bırakın."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Bu kuralın yalnızca bir ürün şablonu için geçerliyse bir şablon belirtin. "
"Aksi durumda boş bırakın"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or substract(if negative) to the amount "
"calculated with the discount."
msgstr ""
"İndirimle hesaplanan tutara eklenecek ya da çıkartılacak (eksi değerdeyse) "
"sabit tutarı belirtin."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Taban fiyat üzerinden max. marj oranını belirtin."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Taban fiyat üzerinden min. oran miktarını belirtin."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "Başlangıç Tarihi"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "Bu tedarikçi fiyatı için başlangıç tarihi"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid "Starting date for the pricelist item validation"
msgstr "Fiyat listesi öğe doğrulaması için başlangıç tarihi"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Etkinliklerdeki aşamalar\n"
"Zamanı Geçmiş: Tarihi geçmiş \n"
"Bugün: Etkinlik günü bugün\n"
"Planlanan: Gelecek etkinlikler."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
#: model:product.template.attribute.value,name:product.product_11_attribute_1_value_1
#: model:product.template.attribute.value,name:product.product_4_attribute_1_value_1
msgid "Steel"
msgstr "Çelik"

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr "Saklama kutusu"

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Tedarikçi Fiyat Listesi"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Technical compute"
msgstr "Teknik hesaplama"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__pricelist_id
#: model:ir.model.fields,help:product.field_product_template__pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""
"Teknik alan. Fiyat listelerinde arama yapmak için kullanılır, veritabanında "
"saklanmaz."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "The attribute %s must have at least one value for the product %s."
msgstr "%s niteliği %s ürünü için en az 1 niteliğe sahip olmalıdır."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__attribute_id
msgid ""
"The attribute cannot be changed once the value is used on at least one "
"product."
msgstr "Nitelik en az bir üründe kullanıldıktan sonra özellik değiştirilemez."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr "Bu hesaplanan fiyat, ürünün Varsayılan Ölçü Biriminde tanımlanır."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr ""
"Varsayılan Ölçü Birimi ve satın alınan Ölçü Birimi aynı kategoride "
"olmalıdır."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr "İlk sırada olan varsayılan bir tanedir."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "The minimum margin should be lower than the maximum margin."
msgstr "Minimum kenar boşluğu maksimum kenar boşluğundan düşük olmalıdır."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr "Bu kategori altındaki ürün sayısı (Çocuk kategorisini içermez)"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The number of variants to generate is too high. You should either not "
"generate variants for each combination or generate them on demand from the "
"sales order. To do so, open the form view of attributes and change the mode "
"of *Create Variants*."
msgstr ""
"Üretilecek varyant sayısı çok yüksek. Her kombinasyon için değişkenler "
"oluşturmamalı veya müşteri siparişinden talep üzerine bunları "
"oluşturmamalısınız. Bunu yapmak için, özelliklerin form görünümünü açın ve *"
" Varyantlar Oluştur * modunu değiştirin."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr "Bir ürün satın alma fiyatı"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The product template is archived so no combination is possible."
msgstr "Ürün şablonunun arşivlenir, böylece hiçbir kombinasyon yapılamaz."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The quantity to purchase from this vendor to benefit from the price, "
"expressed in the vendor Product Unit of Measure if not any, in the default "
"unit of measure of the product otherwise."
msgstr ""
"Tedarikçi Ürün Birim Ölçülerinde belirtilen ya da belirtilmeyen, bu "
"tedarikçiden alınacak minimum miktar, eğer belirtilmezse varsayılan ürün "
"ölçü birimi kullanılır."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"Satış fiyat ürün şablonundan yönetilir. Ekstra nitelik fiyatlarını ayarlamak"
" için 'Varyantları Yapılandır' düğmesine tıklayın."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "The value %s is not defined for the attribute %s on the product %s."
msgstr "%s değeri, %s ürünü üzerindeki %sözelliği için tanımlanmamış."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no possible combination."
msgstr "Olası bir kombinasyon yoktur."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining closest combination."
msgstr "Kalan çok yakın bir kombinasyon yok."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining possible combination."
msgstr "Olası yakın bir kombinasyon yoktur."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
msgid "This comes from the product form."
msgstr "Bu, ürün kartından geliyor."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"This configuration of product attributes, values, and exclusions would lead "
"to no possible variant. Please archive or delete your product directly if "
"intended."
msgstr ""
"Ürün niteliklerinin, değerlerinin ve hariç tutulanların bu yapılandırması, olası bir varyasyona yol açmaz.\n"
"Lütfen istenirse ürününüzü doğrudan arşivleyin veya silin."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Bu, tüm özelliklerin ekstra fiyat toplamıdır"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is added to sales orders and invoices."
msgstr "Bu not müşteri siparişlerine ve faturalara eklenir."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr "Bu not sadece dahili amaçlar içindir."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Geçerli iş ortağı için varsayılan fiyat listesi yerine bu fiyat listesi "
"kullanılacaktır."

#. module: product
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid ""
"This rounding precision is higher than the Decimal Accuracy (%s digits).\n"
"This may cause inconsistencies in computations.\n"
"Please set a precision between %s and 1."
msgstr ""
"Bu yuvarlama hassasiyeti, Ondalık Doğruluktan (%s basamak) daha yüksektir.\n"
"Bu, hesaplamalarda tutarsızlıklara neden olabilir.\n"
"Lütfen %s ve 1 arasında bir hassasiyet ayarlayın."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Bu tedarikçi ürün kodu bir teklif ihtiyacı için yazdırılırken "
"kullanılacaktır. İçini kullanmak için boş tutunuz."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Bu tedarikçi ürün adı, bir teklif ihtiyacı için yazdırılırken "
"kullanılacaktır. İçini kullanmak için boş tutunuz."

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "Three Seater Sofa with Lounger in Steel Grey Colour"
msgstr "Çelik Gri Renkli Şezlonglu Üç Kişilik Kanepe"

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr "Üçlü Koltuk"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "Bugünkü Aktiviteler"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kayıttaki istisna etkinliğinin türü."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "Ölçü Birimi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr "Ölçü Birim Adı"

#. module: product
#: model:product.product,uom_name:product.consu_delivery_01
#: model:product.product,uom_name:product.consu_delivery_02
#: model:product.product,uom_name:product.consu_delivery_03
#: model:product.product,uom_name:product.expense_product
#: model:product.product,uom_name:product.product_delivery_01
#: model:product.product,uom_name:product.product_delivery_02
#: model:product.product,uom_name:product.product_order_01
#: model:product.product,uom_name:product.product_product_10
#: model:product.product,uom_name:product.product_product_11
#: model:product.product,uom_name:product.product_product_11b
#: model:product.product,uom_name:product.product_product_12
#: model:product.product,uom_name:product.product_product_13
#: model:product.product,uom_name:product.product_product_16
#: model:product.product,uom_name:product.product_product_20
#: model:product.product,uom_name:product.product_product_22
#: model:product.product,uom_name:product.product_product_24
#: model:product.product,uom_name:product.product_product_25
#: model:product.product,uom_name:product.product_product_27
#: model:product.product,uom_name:product.product_product_3
#: model:product.product,uom_name:product.product_product_4
#: model:product.product,uom_name:product.product_product_4b
#: model:product.product,uom_name:product.product_product_4c
#: model:product.product,uom_name:product.product_product_4d
#: model:product.product,uom_name:product.product_product_5
#: model:product.product,uom_name:product.product_product_6
#: model:product.product,uom_name:product.product_product_7
#: model:product.product,uom_name:product.product_product_8
#: model:product.product,uom_name:product.product_product_9
#: model:product.template,uom_name:product.consu_delivery_01_product_template
#: model:product.template,uom_name:product.consu_delivery_02_product_template
#: model:product.template,uom_name:product.consu_delivery_03_product_template
#: model:product.template,uom_name:product.expense_product_product_template
#: model:product.template,uom_name:product.product_delivery_01_product_template
#: model:product.template,uom_name:product.product_delivery_02_product_template
#: model:product.template,uom_name:product.product_order_01_product_template
#: model:product.template,uom_name:product.product_product_10_product_template
#: model:product.template,uom_name:product.product_product_11b_product_template
#: model:product.template,uom_name:product.product_product_12_product_template
#: model:product.template,uom_name:product.product_product_13_product_template
#: model:product.template,uom_name:product.product_product_16_product_template
#: model:product.template,uom_name:product.product_product_20_product_template
#: model:product.template,uom_name:product.product_product_22_product_template
#: model:product.template,uom_name:product.product_product_24_product_template
#: model:product.template,uom_name:product.product_product_25_product_template
#: model:product.template,uom_name:product.product_product_27_product_template
#: model:product.template,uom_name:product.product_product_3_product_template
#: model:product.template,uom_name:product.product_product_4b_product_template
#: model:product.template,uom_name:product.product_product_5_product_template
#: model:product.template,uom_name:product.product_product_6_product_template
#: model:product.template,uom_name:product.product_product_7_product_template
#: model:product.template,uom_name:product.product_product_8_product_template
#: model:product.template,uom_name:product.product_product_9_product_template
msgid "Units"
msgstr "Adet"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
msgid "Units of Measure"
msgstr "Ölçü Birimi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_unread
#: model:ir.model.fields,field_description:product.field_product_template__message_unread
msgid "Unread Messages"
msgstr "Okunmamış Mesajlar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_unread_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Okunmamış Mesaj Sayacı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__is_used_on_products
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_used_on_products
msgid "Used on Products"
msgstr "Ürünlerde Kullanılan"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Doğrulanmış Ürün Nitelik Satırları"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Geçerlilik"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "Değer"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Value Price Extra"
msgstr "Ekstra Fiyat Değeri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "Değerler"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Variant"
msgstr "Varyant"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "Varyant Sayısı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1920
msgid "Variant Image"
msgstr "Varyant Resmi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1024
msgid "Variant Image 1024"
msgstr "Varyant Görüntüsü 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_128
msgid "Variant Image 128"
msgstr "Varyant Görüntüsü 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_256
msgid "Variant Image 256"
msgstr "Varyant Görüntüsü 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_512
msgid "Variant Image 512"
msgstr "Varyant Görüntüsü 512"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Varyant Bilgisi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "Varyant Ek Özellik Fiyatı"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr "Satıcı Varyantı"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
msgid "Variant Values"
msgstr "Varyant Değerleri"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Variant: %s"
msgstr "Varyant: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Varyantlar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Variants Creation Mode"
msgstr "Varyantlar Oluşturma Modu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Tedarikçi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "Tedarikçi  Faturaları"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Tedarikçi Bilgisi"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "Tedarikçi Fiyat Listeleri"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "Tedarikçi Ürün Kodu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "Tedarikçi Ürün Adı"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__name
msgid "Vendor of this product"
msgstr "Bu ürününün tedarikçisi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "Tedarikçiler"

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr "Sanal Ev Sahnesi"

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "Virtual Interior Design"
msgstr "Sanal İç Tasarım"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Volume"
msgstr "Hacim"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr "Hacim ölçü birimi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Hacim ölçü birimi etiketi"

#. module: product
#: code:addons/product/models/decimal_precision.py:0
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "Uyarı!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Warnings"
msgstr "Uyarılar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
msgid "Weight"
msgstr "Ağırlık"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight Measurement"
msgstr "Ağırlık ölçümü"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "Ağırlık ölçü birimi"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Ağırlık ölçü birimi etiketi"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
#: model:product.template.attribute.value,name:product.product_4_attribute_2_value_1
msgid "White"
msgstr "Beyaz"

#. module: product
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You are setting a Decimal Accuracy less precise than the UOMs:\n"
"%s\n"
"This may cause inconsistencies in computations.\n"
"Please increase the rounding of those units of measure, or the digits of this Decimal Accuracy."
msgstr ""
"Ölçü biriminden daha az hassas bir Ondalık Doğruluk ayarlıyorsunuz:\n"
"%s\n"
"Bu, hesaplamalarda tutarsızlıklara neden olabilir.\n"
"Lütfen bu ölçü birimlerinin yuvarlamasını veya bu Ondalık Doğruluğun basamaklarını artırın."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""
"Müşterilerinize fiyat listeleri atayabilir veya yeni bir satış teklifi "
"oluştururken bir tane seçebilirsiniz."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr ""
"Fiyat Listesi Kaleminde Ana Fiyat Listesini Diğer Fiyat Listesi olarak "
"atayamazsınız."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot change the Variants Creation Mode of the attribute %s because it is used on the following products:\n"
"%s"
msgstr ""
"Aşağıdaki ürünlerde kullanıldığı için %s niteliğinin Varyant Oluşturma Modunu değiştiremezsiniz:\n"
"%s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot change the attribute of the value %s because it is used on the "
"following products:%s"
msgstr ""
"Aşağıdaki ürünlerde kullanıldığı için %s değerin niteliğini "
"değiştiremezsiniz: %s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "You cannot change the product of the value %s set on product %s."
msgstr "%s Ürününde ayarlanan %s değerin ürününü değiştiremezsiniz."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "You cannot change the value of the value %s set on product %s."
msgstr "%s Ürününde ayarlanan %sdeğerin değerini değiştiremezsiniz."

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "You cannot create recursive categories."
msgstr "Özyinelemeli kategoriler oluşturamazsınız"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_value_value_company_uniq
msgid ""
"You cannot create two values with the same name for the same attribute."
msgstr "Aynı nitelik için aynı ada sahip iki değer oluşturamazsınız."

#. module: product
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""
"'Hesabın' ondalık kesinliğini, şirketin ana para biriminin yuvarlama "
"faktöründen büyük olarak tanımlayamazsınız."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot delete the attribute %s because it is used on the following products:\n"
"%s"
msgstr ""
"Aşağıdaki ürünlerde kullanıldığından %s özniteliğini silemezsiniz:\n"
"%s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot delete the value %s because it is used on the following products:\n"
"%s"
msgstr ""
"Aşağıdaki ürünlerde kullanıldığı için %s değerini silemezsiniz:\n"
"%s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot move the attribute %s from the product %s to the product %s."
msgstr "%s niteliğini %s ürününden %sürününe taşıyamazsınız."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot disable a pricelist rule, please delete it or archive its "
"pricelist instead."
msgstr ""
"Bir fiyat listesi kuralını devre dışı bırakamazsınız, lütfen silin veya "
"bunun yerine fiyat listesini arşivleyin."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot update related variants from the values. Please update related "
"values from the variants."
msgstr ""
"Değerlerden ilgili varyantları güncelleyemezsiniz. Lütfen değişkenlerden "
"ilgili değerleri güncelleyin."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Sattığınız veya satın aldığınız her şey için bir ürün tanımlamanız gerekir,\n"
"               ister depolanabilir bir ürün, ister sarf malzemesi veya hizmet olsun."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"İster depolanabilir bir ürün, ister sarf malzemesi veya hizmet olsun, \n"
"sattığınız veya satın aldığınız her şey için bir ürün tanımlamanız gerekir.\n"
"Ürün formu, satış sürecini basitleştiren bilgiler içerir:\n"
"Fiyat, fiyat teklifi, muhasebe verileri, tedarik yöntemleri vb."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Bir sarf malzemesi, müşterilere sunduğunuz bir hizmet veya fiziksel bir ürün olsun, \n"
"sattığınız her şey için bir ürün tanımlamanız gerekir.\n"
"Ürün formu, satış sürecini basitleştiren bilgiler içerir:\n"
"Fiyat, fiyat teklifi, muhasebe verileri, tedarik yöntemleri vb."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"You must define at least one product category in order to be able to create "
"products."
msgstr ""
"Ürünleri oluşturabilmek için en az bir ürün kategorisi tanımlamanız "
"gerekmektedir."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "gün"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "Örn. Aydınlatma"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr "Örn. Odoo Kurumsal Abonelik"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "Örn. Dolar Perakendeceliği"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "per"
msgstr "başına"

#. module: product
#: code:addons/product/models/product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "product"
msgstr "Ürün"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "ana şirket"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template."
msgstr "ürün şablonu"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "den"
