# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON>, 2021
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Japanese (https://www.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_optimize_dialog.js:0
#, python-format
msgid "%d (Original)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_optimize_dialog.js:0
#, python-format
msgid "%d (Suggested)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr "画像が表示できない場合 ()、「代替タグ」は画像に代替テキストを特定します。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr "画像の上にマウスを置くと「タイトルタグ」はツールチップとして表示されます。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr "(ALT Tag)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr "(タイトルタグ)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr "(URL又は埋め込む)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"
msgstr "(YouTube, Vimeo, Vine, Instagram, DailyMotion または Youku)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<i class=\"fa fa-th-large\"/> First Panel"
msgstr "<i class=\"fa fa-th-large\"/> 最初のパネル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr "サーバーエラーが発生しました。正しくサインインしたか、又は保存しているファイルは正しくフォーマットされているかをご確認ください"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Accepts"
msgstr "同意"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Action"
msgstr "アクション"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Add"
msgstr "追加"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Add as document"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Add as image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add document"
msgstr "書類を追加"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Align center"
msgstr "Align center"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Align left"
msgstr "左揃"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Align right"
msgstr "右揃"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "All SCSS Files"
msgstr "全てのSCSSファイル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All images have been loaded"
msgstr "全ての画像が読み込まれました"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Are you sure you want to delete this file ?"
msgstr "このファイルを本当に削除しますか？"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr "アスペクト比"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "Assets Utils"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Assign a focal point that will always be visible"
msgstr "常に表示される焦点の割当"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "添付"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "添付URL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr "相対リンクに自動変換"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoplay"
msgstr "自動再生"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Background Color"
msgstr "背景色"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Background Image Sizing"
msgstr "背景画像サイズ調整"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Background height"
msgstr "背景の高さ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Background position"
msgstr "背景位置"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Background repeat"
msgstr "背景繰返し"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Background size"
msgstr "背景サイズ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Background width"
msgstr "背景幅"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "ブロック"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Blocks"
msgstr "ブロック"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Bold"
msgstr "太字"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Careful !"
msgstr "ご注意!"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Center"
msgstr "中央"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js:0
#, python-format
msgid "Change media description and tooltip"
msgstr "メディアの説明とツールチップを変更"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Changing the quality is not supported for images of type"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/summernote.js:0
#, python-format
msgid "Checklist"
msgstr "チェックリスト"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Click in the page to customize"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/crop_dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Close"
msgstr "閉じる"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Code"
msgstr "コード"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Code View"
msgstr "コードビュー"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Color"
msgstr "色"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Column"
msgstr "列"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr "カラム色"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Contain"
msgstr "含有"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr "ここにURL又は埋め込みコードをコピーペーストしてくださ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Cover"
msgstr "カバー"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "作成者"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "作成日"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/crop_dialog.js:0
#, python-format
msgid "Crop Image"
msgstr "画像を切り抜く"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Custom"
msgstr "カスタム"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Custom Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/root.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default"
msgstr "デフォルト"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Define if/how the background image will be repeated"
msgstr "背景イメージが繰り返されるかどうか定義する"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Description"
msgstr "説明"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/crop_dialog.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/dialog.js:0
#, python-format
msgid "Discard"
msgstr "破棄"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_assets__display_name
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "表示名"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Document"
msgstr "ドキュメント"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Document Style"
msgstr "ドキュメントスタイル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Double-click to edit"
msgstr "ダブルクリックで編集"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Drag an image here"
msgstr "イメージをここにドラッグ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr "コンテナの複製"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Edit"
msgstr "編集"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Expected "
msgstr "予定"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "File / Image"
msgstr "ファイル/イメージ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Filter"
msgstr "フィルタ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr "フラット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr "左右に反転"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr "上下に反転"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Float Left"
msgstr "フロート左"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Float None"
msgstr "フロートなし"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Float Right"
msgstr "フロート右"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Font Color"
msgstr "フォント色"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Font Family"
msgstr "フォントファミリー"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Font Size"
msgstr "フォントサイズ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr "技術原因でこのブロックはここにドロップできません"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Format"
msgstr "フォーマット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/crop_dialog.js:0
#, python-format
msgid "Free"
msgstr "無料"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Full Screen"
msgstr "フルスクリーン"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr "フルスクリーン"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Give a relevant name to your file to optimize search engine results."
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "HTTPルーティング"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Header 1"
msgstr "Header 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Header 2"
msgstr "Header 2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Header 3"
msgstr "Header 3"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Header 4"
msgstr "Header 4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Header 5"
msgstr "Header 5"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Header 6"
msgstr "Header 6"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Height"
msgstr "高さ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Help"
msgstr "ヘルプ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr "Dailymotionロゴを非表示"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Youtube logo"
msgstr "Youtubeロゴを表示しない"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide fullscreen button"
msgstr "全画面表示ボタンを隠す"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide player controls"
msgstr "プレイヤーコントローラを表示しない"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide sharing button"
msgstr "共有ボタンを非表示"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Horizontal"
msgstr "水平"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/backend/field_html.js:0
#, python-format
msgid "Html"
msgstr "HTML"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_assets__id
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/editor.js:0
#, python-format
msgid ""
"If you discard the current edition, all unsaved changes will be lost. You "
"can cancel to return to the edition mode."
msgstr "現在の編集を破棄すると、保存されていない変更はすべて失われます。キャンセルすると編集モードに戻ります。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid ""
"If you edit and save this file, you will never receive updates from Odoo "
"anymore unless you reset your customizations."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr "このファイルをリセットする場合、全てのカスタマイズが失われてデフォルトファイルに戻ります"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"If you want to crop it, please first download it from the original source "
"and upload it in Odoo."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Image"
msgstr "画像"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "画像高さ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Image Preview"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "画像Src"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Image URL"
msgstr "画像URL"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "画像の幅"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_optimize_dialog.js:0
#, python-format
msgid "Improve your Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Indent"
msgstr "インデント"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Insert Horizontal Rule"
msgstr "水平ルールの挿入"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Insert Image"
msgstr "画像の挿入"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Insert Link"
msgstr "リンクの挿入"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Insert Video"
msgstr "ビデオの挿入"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "インストール"

#. module: web_editor
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr "%sに無効なフィールド値: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Italic"
msgstr "斜体"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "JS"
msgstr "JS"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "JS file: %s"
msgstr "JSファイル: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Justify full"
msgstr "正当化する"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_optimize_dialog.js:0
#, python-format
msgid "Keep Original"
msgstr "原本を保持する"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Keyboard shortcuts"
msgstr "キーボードショートカット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "大"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_assets____last_update
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Line Height"
msgstr "ラインの高さ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Link"
msgstr "リンク"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr "リンクラベル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_dialog.js:0
#, python-format
msgid "Link to"
msgstr "リンク先"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Load more..."
msgstr "さらに読み込む..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Loop"
msgstr "繰返し"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "中"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
#, python-format
msgid "Name"
msgstr "名称"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "No color"
msgstr "色なし"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No documents found."
msgstr "書類は見つかりませんでした。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No images found."
msgstr "画像は見つかりませんでした。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "No repeat"
msgstr "繰返しなし"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "None"
msgstr "なし"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Normal"
msgstr "通常"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Ok"
msgstr "OK"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr "カスタムSCSSファイルのみ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Page SCSS Files"
msgstr "ページSCSSファイルのみ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Views"
msgstr "ビューのみ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in new window"
msgstr "新しいウインドウを開く"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_optimize_dialog.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Optimize"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Options"
msgstr "オプション"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Or choose a preset:"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Ordered list"
msgstr "並び順リスト"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Outdent"
msgstr "アウトデント"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr "アウトライン"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline-Rounded"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Padding"
msgstr "パディング"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Paragraph"
msgstr "段落"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Paragraph formatting"
msgstr "段落フォーマット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Pictogram"
msgstr "ピクトグラム"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "プレビュー表示"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Quality"
msgstr "品質"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Quick Upload"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Quote"
msgstr "引用"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Qwebフィールド"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qwebフィールドコンタクト"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "Qwebフィールド日付"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "Qwebフィールド日時"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "Qwebフィールド期間"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "Qwebフィールドフロート"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "QwebフィールドHTML"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Qweb項目イメージ"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "Qwebフィールド整数"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "Qwebフィールド多対1"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "Qwebフィールドマネー"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "Qwebフィールド相対"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "Qwebフィールドの選択"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "Qwebフィールドテキスト"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "Qwebフィールドqweb"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.js:0
#, python-format
msgid "Readonly field"
msgstr "読み取り専用フィールド"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Redo"
msgstr "もとに戻す"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reduce the quality as much as possible to increase performance."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reduce the size as much as possible to increase performance."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Remove"
msgstr "削除"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr "ブロックを削除"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Remove Font Style"
msgstr "フォントスタイルを取り除く"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Remove Image"
msgstr "イメージ除去"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Repeat"
msgstr "繰返し"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Repeat both"
msgstr "両方繰り返す"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Repeat x"
msgstr "X回繰返し"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Repeat y"
msgstr "Y回繰返し"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Reset"
msgstr "リセット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr "画像をリセット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Reset to default"
msgstr "デフォルトにリセット"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr "閲覧リセットにまだ対応してません"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Resize Full"
msgstr "全画面表示"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Resize Half"
msgstr "半画面でリサイズ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Resize Quarter"
msgstr "四分の一サイズでリサイズ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Resizing is not supported for images of type"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr "左に回転"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr "右に回転"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rounded"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "SCSS (CSS)"
msgstr "SCSS (CSS)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "SCSS file: %s"
msgstr "SCSSファイル: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/crop_dialog.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Save"
msgstr "保存"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search"
msgstr "検索"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Search Contact"
msgstr "連絡先を探す"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a document"
msgstr "書類を検索"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a pictogram"
msgstr "ピクトグラムを検索"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search an image"
msgstr "画像を検索"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Select a Media"
msgstr "メディアを選択"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Select from files"
msgstr "ファイルから選択"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Server error"
msgstr "サーバーエラー"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Set the starting position of the background image."
msgstr "背景イメージの開始位置の設定"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"Sets the width and height of the background image in percent of the parent "
"element."
msgstr "背景画像の幅と高さを親要素のパーセントで設定します。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Shadow"
msgstr "シャドー"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Size"
msgstr "サイズ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "小"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Spin"
msgstr "スピン"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Strikethrough"
msgstr "取り消し線"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Style"
msgstr "スタイル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Subscript"
msgstr "添字"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Superscript"
msgstr "スーパースクリプト"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Table"
msgstr "テーブル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Template ID: %s"
msgstr "テンプレートID:%s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Text formatting"
msgstr "テキストフォーマッティング"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Text to display"
msgstr "表示するテキスト"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The URL contains an image. The file will be added in the image section."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The URL does not contain any image. The file will be added in the document "
"section."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr "URLは無効だそうです。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL seems valid."
msgstr "URLは有効だそうです。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""
"画像が削除されませんでした。\n"
"                次のページまたはビュー："

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr "提供されたURLは対応動画を参照してません"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url is not valid"
msgstr "提供されたURLは無効です"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr "テーマカラー"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/editor.js:0
#, python-format
msgid "This document is not saved!"
msgstr "このドキュメントは保存されていません！"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is a public view attachment."
msgstr "このファイルはパブリックビュー添付ファイルです。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is attached to the current record."
msgstr "このファイルは現在のレコードに添付されています。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This image is an external image"
msgstr "この画像は外部画像です"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This reduces the quality to increase performance."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This type of image is not supported for cropping."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "To what URL should this link go?"
msgstr "このリンクでどのURLに移動させますか？"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Tooltip"
msgstr "ツールチップ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "翻訳可"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "Translation"
msgstr "翻訳"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Transparent"
msgstr "透過"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr "透明色"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr "URLまたはEメール"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Underline"
msgstr "下線"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Undo"
msgstr "戻す"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Unexpected "
msgstr "予想外"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Unlink"
msgstr "リンク除去"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Unordered list"
msgstr "順序付けられていないリスト"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload a document"
msgstr "書類をアップロード"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload an image"
msgstr "画像のアップロード"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Vertical"
msgstr "垂直"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video"
msgstr "ビデオ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Video Link"
msgstr "ビデオリンク"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Video URL?"
msgstr "ビデオURL？"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video code"
msgstr "ビデオコード"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr "自動再生を有効化するとビデオが消音で再生されます。"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "照会"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Views and Assets bundles"
msgstr "閲覧及びアセットバンドル"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "Webエディターコンバーターサブテスト"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "Webエディターコンバーターテスト"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Width"
msgstr "幅"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "XML (HTML)"
msgstr "XML (HTML)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:0
#, python-format
msgid "Xl"
msgstr "Xl"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr "画面の左上にあるボタンで書類をアップロードすることができます。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload images with the button located in the top left of the screen."
msgstr "画面の左上にあるボタンで画像をアップロードすることができます。"

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid ""
"You cannot change the quality, the width or the name of an URL attachment."
msgstr ""

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr "添付ファイルを作成するにはデータかURLかを指定してください。"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr "ズームイン"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr "ズームアウトする"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "and"
msgstr "と"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "auto"
msgstr "自動"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr "https://www.odoo.com/logo.png"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/mydocument"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr "px"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "videos"
msgstr "動画"
