# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_membership
# 
# Translators:
# <PERSON>, 2019
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"Collegamento esterno\"/>"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Tutti"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:83
#, python-format
msgid "All Countries"
msgstr "Tutte le nazioni"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Associazioni"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "Chiudi"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Cerca un partner aziendale"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:126
#, python-format
msgid "Free Members"
msgstr "Iscritti gratuiti"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Ubicazione"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
#: model:website.menu,name:website_membership.menu_members
msgid "Members"
msgstr "Soci"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Riga iscrizione"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "Nessun risultato trovato."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Nostro elenco soci"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr "Mappa del mondo"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "float-left"
msgstr "float-left"
