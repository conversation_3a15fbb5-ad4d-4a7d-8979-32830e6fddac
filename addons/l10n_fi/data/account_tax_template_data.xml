<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- # Domestic -->
    <!-- ## Domestic:sales -->
    <!-- ### Domestic:sales:goods -->
    <record id="tax_dom_sales_goods_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 24%</field>
        <field name="description">VAT 24%</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids':  [ref('tax_report_sales_24')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids':  [ref('tax_report_sales_24')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_sales_goods_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 14%</field>
        <field name="description">VAT 14%</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids':  [ref('tax_report_sales_14')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids':  [ref('tax_report_sales_14')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_sales_goods_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 10%</field>
        <field name="description">VAT 10%</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>

        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids':  [ref('tax_report_sales_10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids':  [ref('tax_report_sales_10')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_sales_goods_0" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 0%</field>
        <field name="description">VAT 0%</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>

        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids':  [ref('tax_report_base_turnover_0_vat')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids':  [ref('tax_report_base_turnover_0_vat')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- ### Domestic:sales:service -->
    <record id="tax_dom_sales_service_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 24% Service</field>
        <field name="description">VAT 24% Service</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>

        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids':  [ref('tax_report_sales_24')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids':  [ref('tax_report_sales_24')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_sales_service_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 14% Service</field>
        <field name="description">VAT 14% Service</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>

        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids':  [ref('tax_report_sales_14')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids':  [ref('tax_report_sales_14')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_sales_service_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 10% Service</field>
        <field name="description">VAT 10% Service</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids':  [ref('tax_report_sales_10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids':  [ref('tax_report_sales_10')],
            }),
        ]"/>
    </record>

    <!-- ## Domestic:purchase -->

    <!-- ### Domestic:purchase:goods -->
    <record id="tax_dom_purchase_goods_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24%</field>
        <field name="description">Purchase 24%</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids':  [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids':  [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_purchase_goods_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 14%</field>
        <field name="description">Purchase 14%</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_purchase_goods_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 10%</field>
        <field name="description">Purchase 10%</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>

    <!-- ### Domestic:purchase:service -->
    <record id="tax_dom_purchase_service_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% Service</field>
        <field name="description">Purchase 24% Service</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_purchase_service_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 14% Service</field>
        <field name="description">Purchase 14% Service</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_purchase_service_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 10% Service</field>
        <field name="description">Purchase 10% Service</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>

    <!-- ### Domestic:purchase:brutto -->
    <record id="tax_dom_purchase_brutto_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% (brutto)</field>
        <field name="description">Purchase 24% (brutto)</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_purchase_brutto_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 14% (brutto)</field>
        <field name="description">Purchase 14% (brutto)</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_dom_purchase_brutto_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 10% (brutto)</field>
        <field name="description">Purchase 10% (brutto)</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>

    <!-- ### Domestic:purchase:no_category -->
    <record id="tax_dom_purchase_0" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 0%</field>
        <field name="description">Purchase 0%</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
    </record>

    <!-- # Europe -->
    <!-- ## Europe:sales -->
    <!-- ### Europe:sales:goods -->
    <record id="tax_eu_sales_goods_0" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 0% EU Goods</field>
        <field name="description">VAT 0% EU Goods</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids':  [ref('tax_report_base_sales_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids':  [ref('tax_report_base_sales_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- ### Europe:sales:service -->
    <record id="tax_eu_sales_service_0" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 0% EU Service</field>
        <field name="description">VAT 0% EU Service</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids':  [ref('tax_report_base_sales_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids':  [ref('tax_report_base_sales_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- ## Europe:purchase -->
    <!-- ### Europe:purchase:goods -->
    <record id="tax_eu_purchase_goods_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% EU Goods</field>
        <field name="description">Purchase 24% EU Goods</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>
    <record id="tax_eu_purchase_goods_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 14% EU Goods</field>
        <field name="description">Purchase 14% EU Goods</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>
    <record id="tax_eu_purchase_goods_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 10% EU Goods</field>
        <field name="description">Purchase 10% EU Goods</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_goods_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>

    <!-- ### Europe:purchase:service -->
    <record id="tax_eu_purchase_service_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% EU Service</field>
        <field name="description">Purchase 24% EU Service</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>
    <record id="tax_eu_purchase_service_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 14% EU Service</field>
        <field name="description">Purchase 14% EU Service</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>
    <record id="tax_eu_purchase_service_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 10% EU Service</field>
        <field name="description">Purchase 10% EU Service</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_purchase_service_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>

    <!-- # TRIANGULATION -->
    <record id="vat0triangulation" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 0% Triangulation</field>
        <field name="description">VAT 0% Triangulation</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="triangulation_purchase" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Triangulation Purchase</field>
        <field name="description">Triangulation Purchase</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
            }),
        ]"/>
    </record>

    <!-- # Construct -->
    <record id="tax_construct_sales_0" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Construct 0%</field>
        <field name="description">Construct 0%</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids':  [ref('tax_report_base_sales_construct_service')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids':  [ref('tax_report_base_sales_construct_service')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="tax_construct_purchase_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% Construct</field>
        <field name="description">Purchase 24% Construct</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>
    <record id="tax_construct_purchase_24_finland" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% FI Construct</field>
        <field name="description">Purchase 24% FI Construct</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="amount">24.0</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_purchase_construct_service')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'minus_report_line_ids': [ref('tax_report_tax_purchase_construct_service')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_purchase_construct_service')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
              'plus_report_line_ids': [ref('tax_report_tax_purchase_construct_service')],
            }),
        ]"/>
    </record>

    <!-- # Aland -->
    <!-- ## Aland:sales -->
    <record id="aland_sales_0" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Aland 0%</field>
        <field name="description">Aland 0%</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids':  [ref('tax_report_base_turnover_0_vat')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids':  [ref('tax_report_base_turnover_0_vat')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- # Non EU -->
    <!-- ## Non EU:purchase -->
    <!-- ## Non EU:purchase:goods -->
    <record id="tax_non_eu_purchase_goods_24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 24% Non EU Goods</field>
        <field name="description">Purchase 24% Non EU Goods</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>
    <record id="tax_non_eu_purchase_goods_14" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 14% Non EU Goods</field>
        <field name="description">Purchase 14% Non EU Goods</field>
        <field name="amount">14.0</field>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>
    <record id="tax_non_eu_purchase_goods_10" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Purchase 10% Non EU Goods</field>
        <field name="description">Purchase 10% Non EU Goods</field>
        <field name="amount">10.0</field>
        <field name="tax_group_id" ref="tax_group_10"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible'), ref('tax_report_tax_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': -100,
              'repartition_type': 'tax',
              'account_id': ref('account_2930'),
            }),
        ]"/>
    </record>

    <!-- ## Non EU:others -->
    <record id="vat0export" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">VAT 0% Export</field>
        <field name="description">VAT 0% Export</field>
        <field name="amount">0.0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>

        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids':  [ref('tax_report_base_turnover_0_vat')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids':  [ref('tax_report_base_turnover_0_vat')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="import_pay24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Import Pay24</field>
        <field name="description">24%</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'plus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_tax_import_goods_no_eu')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
              'minus_report_line_ids': [ref('tax_report_base_import_goods_no_eu')],
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_tax_import_goods_no_eu')],
            }),
        ]"/>
    </record>
    <record id="import_deduct24" model="account.tax.template">
        <field name="chart_template_id" ref="fi_chart_template"/>
        <field name="name">Import Deduct24</field>
        <field name="description">24%</field>
        <field name="amount">24.0</field>
        <field name="tax_group_id" ref="tax_group_24"/>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'plus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'base',
            }),
            (0,0, {
              'factor_percent': 100,
              'repartition_type': 'tax',
              'account_id': ref('account_1765'),
              'minus_report_line_ids': [ref('tax_report_deductible')],
            }),
        ]"/>
    </record>

</odoo>
