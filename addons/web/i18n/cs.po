# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON>, 2020
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# rast<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# trendspotter, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 10:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: trendspotter, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid " [Me]"
msgstr "[Já]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid " and "
msgstr "a"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#, python-format
msgid " or "
msgstr "nebo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr "záznamy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "# Editor kódu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "před %d dny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "před %d hodinami"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "před %d minutami"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "před %d měsíci"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "před %d lety"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' není platné datum"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' není platné datum, datum/čas, ani čas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' není platné datum/čas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' není platná hodnota v plovoucí desetinné čárce"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' není platné celé číslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "'%s' není správné peněžní pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' nelze převést na datum, datum/čas, ani čas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "(change)"
msgstr "(změna)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(počet)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr "(prázdný text)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(bez štítku)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr "1 záznam"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-at\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-at\" role=\"img\" aria-label=\"Email\" title=\"E-mail\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Fiscal number\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Fiscal number\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-globe\" role=\"img\" aria-label=\"Website\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe\" role=\"img\" aria-label=\"Website\" title=\"Website\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Telefon\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "<span>Copyright &amp;copy;</span>"
msgstr "<span>Copyright &amp;copy;</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "A name for your favorite is required."
msgstr "Je vyžadován název vašeho oblíbeného."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"Vyskakovací okno bylo zablokováno. Možná budete muset změnit nastavení "
"prohlížeče tak, aby pro tuto stránku povolovala vyskakovací okna."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"A popup window with your report was blocked. You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""
"Vyskakovací okno s vaším přehledem bylo zablokováno. Možná budete muset "
"změnit nastavení prohlížeče tak, aby pro tuto stránku povoloval vyskakovací "
"okna."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "VŠE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "COKOLIV"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Denied"
msgstr "Přístup zamítnut"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Error"
msgstr "Chyba přístupu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Přístup ke všem podnikovým aplikacím"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#, python-format
msgid "Action"
msgstr "Akce"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "ID akce:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Zobrazení okna akcí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "Aktivace ladění položek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "Přidat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr "Přidat %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "Přidat vlastní filtr"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "Přidat vlastní skupinu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr "Přidat sloupec"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add a condition"
msgstr "Přidat podmínku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr "Přidat řádek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "Přidat větev"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr "Přidat sloupec"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "Přidat filtr"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "Přidat novou hodnotu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "Přidat uzel"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "Přidat značku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "Přidat k oblíbeným"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add..."
msgstr "Přidat..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "Přidat "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "Přijmout svůj podpis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt and Sign"
msgstr "Přijmout a podepsat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Advanced Search..."
msgstr "Pokročilé hledání..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "Výstraha"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "All"
msgstr "Vše"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr "Celý den"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "Všichni uživatelé"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Alt"
msgstr "Alt"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr "Mezi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "An error occurred"
msgstr "Vyskytla se chyba"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin. (Opening your browser "
"console might give you a hint on the error.)"
msgstr ""
"Došlo k neznámé chybě CORS. Chyba pravděpodobně pochází z JavaScript souboru"
" poskytnutého serverem z jiného původu. (Otevření konzoly prohlížeče vám "
"může dát nápovědu k chybě.)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "And more"
msgstr "A více"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "Jakýkoliv"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Apply"
msgstr "Použít"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Archivovat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr "Archivovat vše"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "Opravdu chcete archivovat všechny záznamy z tohoto sloupce?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "Opravdu chcete archivovat všechny vybrané záznamy?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "Opravdu chcete tento záznam archivovat?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Opravdu chcete odebrat tento sloupec?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Opravdu chcete tento filtr odebrat?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Opravdu jste si jisti, že chcete smazat tento záznam ?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "Opravdu na nich chcete provést následující aktualizaci"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr "Připojit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "Příloha"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attachment :"
msgstr "Příloha:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "Automaticky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "Dostupná pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr "Odznaky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bar Chart"
msgstr "Sloupcový graf"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Základní část"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Based On"
msgstr "Na základě"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Become Superuser"
msgstr "Staňte se Superuživatelem"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""
"Binární pole nelze exportovat do aplikace Excel, pokud jejich obsah není "
"zakódován base64. Zdá se, že tomu tak není %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr "Binární soubor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Blue"
msgstr "Modrá"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "Záruka na opravy chyb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "Tlačítko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "Typ tlačítka:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Kliknutím na položku Přijmout a podepsat souhlasím s tím, že vybrané podpisy"
" / iniciály budou platné elektronické zastoupení mých ručně psaných podpisů "
"/ iniciál pro všechny účely, pokud jsou používány v dokumentech včetně "
"právně závazných smluv."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "CLEAR"
msgstr "VYMAZAT"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "Kalendář"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr "Pohled kalendáře nemá definovaný atribut 'date_start'."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Cancel"
msgstr "Zrušit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Cannot render chart with mode : "
msgstr "Nelze vykreslit graf v režimu:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "Barva karty: %s"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/change_password.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "Změnit heslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "Změnit výchozí:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change graph"
msgstr "Změnit graf"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Checkbox"
msgstr "Zaškrtávací pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr "Zaškrtávací políčka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Choose"
msgstr "Zvolit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "Vymazat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Clear Events"
msgstr "Vymazat Události"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "Vymazat podpis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/dialog.xml:0
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "Close"
msgstr "Zavřít"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "Sloupec %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr "Název sloupce"

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "Název společnosti"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Compare To"
msgstr "Porovnat s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "Podmínka:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Confirm New Password"
msgstr "Potvrdit nové heslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Potvrzení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection lost"
msgstr "Spojení ztraceno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection restored"
msgstr "Připojení obnoveno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "Kontext:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Control"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Control panel toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Copied !"
msgstr "Zkopírováno !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Copy Text"
msgstr "Kopírovat text"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "Zkopírujte celou chybu do schránky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "Zkopírovat do schránky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "Nelze se připojit k serveru"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image."
msgstr "Nelze zobrazit zvolený obrázek."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "Nemohu serializovat XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Count"
msgstr "Počet"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Create"
msgstr "Vytvořit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "Vytvořit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Vytvořit \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create a %s"
msgstr "Vytvořit %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Create a new record"
msgstr "Vytvořit nový záznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "Vytvořit a upravit..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and edit"
msgstr "Vytvořit a upravit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create: "
msgstr "Vytvořit: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Create: %s"
msgstr "Vytvořit: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Created by :"
msgstr "Vytvořeno:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation Date:"
msgstr "Datum vytvoření:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation User:"
msgstr "Vytvořil:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "Aktuální stav"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark blue"
msgstr "Tmavě modrá"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark purple"
msgstr "Tmavě fialová"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Databáze"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "Datum"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr "Datum a čas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Day"
msgstr "Den"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""
"Režim ladění je aktivován#{debug_mode_help}. Klepnutím sem ukončíte režim "
"ladění."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr "Desetinný"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "Výchozí:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Delete"
msgstr "Smazat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr "Smazat uzel"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "Smazat řádek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete this attachment"
msgstr "Smazat tuto přílohu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Description"
msgstr "Popis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Discard"
msgstr "Zrušit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Discard a record modification"
msgstr "Zrušit změnu záznamu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "Opravdu chcete odstranit tuto exportní šablonu?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr "Opravdu chcete smazat tento filter z oblíbených?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Documentation"
msgstr "Dokumentace"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "Doména"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain error"
msgstr "Chyba domény"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "Doména:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Ještě neodcházejte,<br />stále probíhá načítání..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "Stáhnout"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download xls"
msgstr "Stáhnout xls"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "Kreslit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Rozbalovací nabídka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "Duplikovat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "Upravit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit Action"
msgstr "Upravit akci"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "Upravit sloupec"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit ControlPanelView"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr "Upravit doménu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr "Upravit Fázi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit View:"
msgstr "Upravit zobrazení:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Edit a record"
msgstr "Upravit záznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr "Email:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Error"
msgstr "Chyba"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "Chyba, heslo nebylo změněno!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr "zmáčknutím Esc zahodit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "Všechny sdílené kalendáře"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "Všechno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Expand all"
msgstr "Rozbalit vše"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "Export"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr "Exportovat vše"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "Exportovat data"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr "Exportní formát:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr "Export seskupených dat do formátu CSV není podporován."

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "Externí ID"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "Externí odkaz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "FILTER"
msgstr "FILTR"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/control_panel_model.js:0
#, python-format
msgid "Failed to evaluate search criterions"
msgstr "Hodnocení kritérií vyhledávání se nezdařilo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "Neplatné"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Favorites"
msgstr "Oblíbené"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "Pole:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Fields View Get"
msgstr "Získat pole pohledu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "Pole k exportu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "Soubor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File Upload"
msgstr "Nahrát soubor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "Nahrát soubor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Filter on: %s"
msgstr "Filtrovat: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "Filtr se stejným názvem již existuje."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/filter_menu.js:0
#, python-format
msgid "Filters"
msgstr "Filtry"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Flip axis"
msgstr "Osa převrácení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "Sbalit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "Sledován od"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "Formulář"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "Celé jméno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fushia"
msgstr "Fuschia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "Získejte tuto funkci a mnohem více s Odoo Enterprise!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Global Business Error"
msgstr "Globální chyba podnikání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Got it"
msgstr "Mám to"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "Graf"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Green"
msgstr "Zelená"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/groupby_menu.js:0
#, python-format
msgid "Group By"
msgstr "Seskupit podle"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Group by: %s"
msgstr "Seskupit podle: %s"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "Skrýt v Kanbanu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr "Stisknutím tlačítka DOWN přejděte na níže uvedený seznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "Stiskněte ENTER do"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "Stiskněte ENTER pro CREATE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "Zmáčknout ENTER pro uložení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "Stiskněte ESCAPE pro ZRUŠENÍ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Hue"
msgstr "Odstín"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "Jsem si tím jistý."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "Chci aktualizovat data (import-kompatibilní export)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Image"
msgstr "Obrázek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr "Celé číslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid data"
msgstr "Neplatná data"

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""
"Neplatný název databáze. Povoleny jsou pouze alfanumerické znaky, "
"podtržítko, spojovník a tečka."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr "Neplatná doména"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid mode for chart"
msgstr "Neplatný režim pro graf"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Mobile Tests"
msgstr "JS Mobile Testy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Tests"
msgstr "JS testy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/user_menu.js:0
#, python-format
msgid "Keyboard Shortcuts"
msgstr "Klávesové zkratky"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "Jazyky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 30 Days"
msgstr "Posledních 30 dní"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 365 Days"
msgstr "Posledních 365 dní"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 5 Years"
msgstr "Posledních 5 let"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 7 Days"
msgstr "Posledních 7 dní"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Month"
msgstr "Poslední měsíc"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Quarter"
msgstr "Poslední čtvrtletí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Week"
msgstr "Minulý týden"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Year"
msgstr "Minulý rok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "Naposledy změneno:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "Naposledy změnil:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "Opustit Vývojářské Nástroje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Light blue"
msgstr "Světle modrá"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Lightness %"
msgstr "Světlost %"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Line Chart"
msgstr "Čárový graf"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "Seznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "Načíst"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr "Načíst více... ("

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading"
msgstr "načítání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading (%d)"
msgstr "Načítání (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "Načítání prosím čekejte..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "Načítání..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Přihlásit se"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "Přihlaste se jako superuser"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Log out"
msgstr "Odhlásit se"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Mac"
msgstr "Mac"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr "E-mail:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Main actions"
msgstr "Hlavní akce"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Attachments"
msgstr "Správa příloh"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Správa databází"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Filters"
msgstr "Spravovat filtry"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "Shoda se záznamy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "Shoda záznamů s následujícím pravidlem:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr "Možná byste měli zvážit znovunačtení aplikace stisknutím F5..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Measures"
msgstr "Měřítka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Medium blue"
msgstr "Střední hodnoty modré"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Metadata (%s)"
msgstr "Metadata (%s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "Metoda:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Missing Record"
msgstr "Chybějící záznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr "Podpora mobilních zařízení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modified by :"
msgstr "Změněno:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr "Upravující:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr "Měnový"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Month"
msgstr "Měsíc"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "More"
msgstr "Více"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr "Víceřádkový text"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "My Odoo.com account"
msgstr "Můj účet Odoo.com"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr "ŽÁDNÝ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Name:"
msgstr "Název:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "Nové"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "Nové heslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New design"
msgstr "Nová úprava"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New template"
msgstr "Nová šablona"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "Další"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "No"
msgstr "Ne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "No Update:"
msgstr "Žádná aktualizace:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No attachment available"
msgstr "K dispozici není žádná příloha"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "No color"
msgstr "Žádná barva"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "Žádná data"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "No data to display"
msgstr "Žádná data k zobrazení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr "Žádná shoda nenalezena."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No metadata available"
msgstr "Žádné metadata nejsou k dispozici"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "No records"
msgstr "Žádné záznamy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "Nenalezeny žádné záznamy!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "No results to show..."
msgstr "Nic nenalezeno..."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr "Žádný platný záznam k uložení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Node [%s] is not a JSONified XML node"
msgstr "Uzel [%s] není JSONifikovaný uzel XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "None"
msgstr "Nic"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "Nenastaveno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr "Stav není aktivní"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "Není aktivní stav, klepnutím ho změňte"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "Objekt:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo Apps budou brzy k dispozici"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "Chyba klienta Odoo "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Error"
msgstr "Odoo chyba"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "Platnost Odoo vypršela"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo varování"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr "Staré heslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "Při změně:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Only Integer or Float Value should be valid."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr "Do této databáze má přístup pouze zaměstnanec. Obraťte se na správce."

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""
"Pouze typy %(supported_types)s jsou podporovány pro filtr (nalezený typ "
"%(field_type)s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "Pouze vy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Opacity %"
msgstr "Neprůhlednost %"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools"
msgstr "Otevřít Developer Tools"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools#{widget.debug_mode_help}"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the next record"
msgstr "Otevřít další záznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the previous record"
msgstr "Otevřít předchozí záznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to kanban view"
msgstr "Otevřít zobrazení kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to list view"
msgstr "Otevřít pro zobrazení seznamu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "Otevřít: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Orange"
msgstr "Oranžový"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "jiný"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr "PDF Prohlížeč"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr "PDF nastavení"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"Strana:\n"
"                    <span class=\"page\"/>\n"
"                    z\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "Strana: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Heslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr "Procenta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "Procentní koláč"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr "Telefon:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Pick a color"
msgstr "Zvolit barvu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pie Chart"
msgstr "Koláčový graf"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""
"Výsečový graf nemůže zobrazit všechna nulová čísla. Zkuste zkusit změnit "
"svou doménu tak, aby zobrazovala kladné výsledky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Koláčový graf nemůže kombinovat kladná a záporná čísla. Zkuste změnit doménu"
" tak, aby zobrazovala pouze pozitivní výsledky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "Pivot"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pivot settings"
msgstr "Nastavení kontingenční tabulky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first."
msgstr "Nejprve klikněte na tlačítko \"uložit\"."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "Prosíme zadejte jméno seznamu polí k uložení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "Prosíme vyberte pole k exportu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "Prosíme vyberte pole k uložení exportovaného seznamu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "Aktualizujte prosím překlady:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr "Pomocí tlačítka Kopírovat nahláste chybu své podpoře."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "Běží na %s%s"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Běží na <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Preferences"
msgstr "Předvolby"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr "Předchozí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Period"
msgstr "Předchozí období"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Year"
msgstr "Minulý rok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#: code:addons/web/static/src/xml/report.xml:0
#, python-format
msgid "Print"
msgstr "Tisk"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "Přednost"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr "Ukazatel průběhu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Purple"
msgstr "Fialový"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q1"
msgstr "Q1"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q2"
msgstr "Q2"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q3"
msgstr "Q3"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q4"
msgstr "Q4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Quarter"
msgstr "Čtvrtletí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr "Rychlé přidání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr "Rychlé hledání: %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Obrázek pole Qweb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr "Rádio"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Range"
msgstr "Rozsah"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Red"
msgstr "Červená"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "Vztah k následování"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "Vztah:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove"
msgstr "Odebrat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr "Odstranit  úvodní obrázek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr "Odebrat pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "Odstranit z Oblíbených"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "Odstranit značku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "Odebrat tuto oblíbenou položku ze seznamu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid "Report"
msgstr "Sestava"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Request timeout"
msgstr "Zažádat o timeout"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "Spustit test Kliknout kamkoliv"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "Spustit JS Mobile testy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Tests"
msgstr "Spustit JS testy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "SEE RESULT"
msgstr "ZOBRAZIT VÝSLEDEK"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr "PODPIS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Salmon pink"
msgstr "Lososově růžová"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Saturation %"
msgstr "Sytost %"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save"
msgstr "Uložit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr "Uložit a zavřít"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "Uložit & Nový"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Save As..."
msgstr "Uložit jako..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save Current Search"
msgstr "Uložit aktuální vyhledávání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Save a record"
msgstr "Uložit záznam"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "Uložit jako :"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Save default"
msgstr "Uložit výchozí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search"
msgstr "Hledání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr "Vyhledat %(field)s v: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr "Prohledat %(field)s na výskyt: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "Vyhledat více..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search..."
msgstr "Hledat..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search: "
msgstr "Hledat: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "See details"
msgstr "viz podrobnosti"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr "Viz příklady"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "Vybrat"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""
"Vybrat <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "Vybrat styl podpisu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "Vybrat model pro přidání filtru."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Select a view"
msgstr "Vybrat pohled"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr "Vyberte pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr "Vybrané záznamy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "Výběr"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "Výběr:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Set Default"
msgstr "Nastavit výchozí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Set Defaults"
msgstr "Nastavit výchozí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "Nastavit úvodní obrázek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "Nastavte na svém uživateli časové pásmo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "Nastavení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Share with all users"
msgstr "Sdílet se všemi uživateli"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Shortcuts"
msgstr "Klávesové zkratky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "Zobrazují se lokálně dostupné moduly"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "Podpis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "Velikost:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr "Stalo se něco hrozného"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Something went wrong !"
msgstr "Něco se pokazilo !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "Speciální:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Stacked"
msgstr "Skládaný"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "Načítání stále probíhá..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Načítání stále probíhá...<br />Prosím čekejte."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "Styl"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "styly"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Summary"
msgstr "Shrnutí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Summary:"
msgstr "Shrnutí:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Support"
msgstr "Podpora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Switch to this company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "Chyba syntaxe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "Štítky"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Běžte si udělat kafe<br />protože probíhá načítání..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Technical Translation"
msgstr "Technický překlad"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr "Tel:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr "Šablona:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "Text"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "The domain you entered is not properly formed"
msgstr "Zadaná doména není správně vytvořena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or"
" followed a non-relational field?"
msgstr ""
"Řetězec pole není platný. Možná jste použili neexistující název pole nebo "
"sledovali nerelační pole?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr "Pole je prázdné, není co ukládat!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "The following field is invalid:"
msgstr "Následující pole je neplatné:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "The following fields are invalid:"
msgstr "Následující pole jsou neplatná:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "Nové heslo a jeho potvrzení musí být stejné."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"Staré heslo, které jste zadali, je neplatné. Vaše heslo nebylo změněno."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""
"Operace byla přerušena. To obvykle znamená, že aktuální operace trvá příliš "
"mnoho času."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to"
" proceed?"
msgstr "Záznam byl změněn, vaše změny budou zahozeny. Chcete pokračovat?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "Zvolený soubor překračuje maximální velikost %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr "Typ pole '%s' musí být many2many s relací k modelu 'ir.attachment'."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "Není k dispozici žádný obrázek, který má být nastaven jako úvodní."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Při nahrávání vašeho souboru nastal problém"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Month"
msgstr "Tento měsíc"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Quarter"
msgstr "Toto čtvrtletí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Week"
msgstr "Tento týden"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Year"
msgstr "Tento rok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/date_picker.js:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""
"Toto datum je o budoucnosti. Ujistěte se, že je takové, jaké jste očekávali."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "Tato doména není podporována."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "Tento soubor je neplatný. Vyberte obrázek."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""
"Tento filtr je globální, a pokud budete pokračovat, bude odebrán všem "
"uživatelům."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "Jedná se o vzorek externí zprávy."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "Jedná se o vzorek interní zprávy."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr "Čas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Time Ranges"
msgstr "Časové rozsahy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""
"Neshoda časových pásem: Toto časové pásmo se liší od časového pásma vašeho prohlížeče.\n"
"Chcete-li se vyhnout časovým nesrovnalostem v systému, nastavte stejné časové pásmo jako v prohlížeči."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "Dnes"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr "Přepnout"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Toggle Timelines"
msgstr "Přepnout časové osy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "Celkem"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Traceback:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: "
msgstr "Přeložit:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "Pravda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_renderer.js:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is at least one measure and"
" no active filter in the search bar."
msgstr ""
"Zkuste přidat nějaké záznamy nebo se ujistěte, že ve vyhledávacím řádku je "
"alespoň jedno měření a žádný aktivní filtr."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                active filter in the search bar."
msgstr ""
"Zkuste přidat nějaké záznamy, nebo se ujistěte, že není žádný\n"
"aktivní filtr ve vyhledávací liště."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Trying to reconnect..."
msgstr "Pokus o opětovné připojení ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "Typ:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr "V tomto systému nelze najít Wkhtmltopdf. Zpráva se zobrazí v html."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Zrušit archivaci"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr "Vyarchivovat vše"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_renderer_mobile.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Undefined"
msgstr "Nedefinováno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr "Rozbalit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Unknown CORS error"
msgstr "Neznámá chyba CORS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "Neznámý typ nonliterálu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr "Odpojit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:0
#, python-format
msgid "Unnamed"
msgstr "Nepojmenovaný"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "Bez názvu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr "Aktualizovat na:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "Upgradovat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "Upgrade na enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "Upgrade na budoucí verze"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upload your file"
msgstr "Nahrajte soubor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr "Nahráno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr "Nahrávání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "Chyba nahrávání"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "Nahrávání..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Use by default"
msgstr "Použít ve výchozím stavu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "User"
msgstr "Uživatel"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "User Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Users"
msgstr "Uživatelé"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Validation Error"
msgstr "Chyba ověření"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr "Variace"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Fields"
msgstr "Pole zobrazení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Metadata"
msgstr "Zobrazit metadata"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "Typ zobrazení"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Warning"
msgstr "Varování"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "Warning : You have to save first before attaching a file."
msgstr "Varování: Před připojením souboru musíte nejprve uložit."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr "Webové benchmarky"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "Testy webu pro mobilní zařízení"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr "Web:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Week"
msgstr "Týden"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:0
#, python-format
msgid "Well Done!"
msgstr "Velmi dobře!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "Widget:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Windows/Linux"
msgstr "Windows/Linux"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr "Špatný login / heslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Wrong value entered!"
msgstr "Zadali jste špatnou hodnotu!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Year"
msgstr "Rok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Yellow"
msgstr "Žlutý"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Yes"
msgstr "Ano"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Yesterday"
msgstr "Včera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "You are back online"
msgstr "Jste zpět online"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr "Vytváříte nový %s, jste si jisti, že ještě neexistuje?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr "Pro tuto konstrukci řetězového pole nemůžete sledovat vztahy"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "Všechna hesla musí být vyplněná."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "Možná tomu nebudete věřit<br />ale aplikace se stále načítá..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""
"Před úpravou překladu musíte tento nový záznam uložit. Chcete pokračovat?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""
"Potřebujete spustit Odoo s nejméně dvěma pracovníky, abyste mohli vytisknout"
" pdf verzi zpráv."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""
"Měli byste upgradovat svou verzi Wkhtmltopdf na alespoň 0.12.0, abyste "
"získali správné zobrazení záhlaví a zápatí, stejně jako podporu pro "
"rozdělení tabulek mezi stránkami."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr "Vaše sezení Odoo vypršelo. Prosím, obnovte aktuální webovou stránku."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""
"Zdá se, že vaše instalace Wkhtmltopdf je rozbitá. Zpráva se zobrazí v html."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr "Vaše jméno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "včera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "asi před minutou"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "asi před měsícem"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "asi před rokem"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "asi před hodinou"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "všechny záznamy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr "jsou pro tuto aktualizaci platné."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "podřízený"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "contains"
msgstr "obsahuje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "neobsahuje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "doesn't contain"
msgstr "neobsahuje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than"
msgstr "větší než"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than or equal to"
msgstr "větší nebo rovno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "v "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "is"
msgstr "je"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after"
msgstr "je po"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after or equal to"
msgstr "je po nebo rovno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before"
msgstr "je před"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is between"
msgstr "je mezi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is equal to"
msgstr "rovná se"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is false"
msgstr "je nepravda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not"
msgstr "není"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "není  ="

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is not equal to"
msgstr "nerovná se"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not set"
msgstr "není nastaveno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is set"
msgstr "je nastaveno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is true"
msgstr "je pravda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than"
msgstr "menší než"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "méně než před minutou"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than or equal to"
msgstr "menší nebo rovno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "not"
msgstr "není"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid integer"
msgstr "neplatné celé číslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid number"
msgstr "neplatné číslo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "není v"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "není nastaveno (false)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "následujících pravidel:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "z:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "or"
msgstr "nebo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "nadřízený "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion
msgid "portal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr "záznam(y)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "zbývajících)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "search"
msgstr "hledat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "set"
msgstr "nastavit"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "nastavit (true)"
