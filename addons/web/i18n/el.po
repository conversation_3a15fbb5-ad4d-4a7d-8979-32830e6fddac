# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2019
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 10:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: Ko<PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid " [Me]"
msgstr "[Εγώ]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid " and "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#, python-format
msgid " or "
msgstr "ή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr "εγγραφές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d ημέρες πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d ώρες πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d λεπτά πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d μήνες πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d χρόνια πριν"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""
"&lt;!--[if lt IE 10]&gt;\n"
"&lt;body class=\"ie9\"&gt;\n"
"&lt;![endif]--&gt;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' δεν είναι σωστή ημερομηνία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' δεν είναι έγκυρη ημερομηνία. ημερομηνία ή/και ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' δεν είναι μια σωστή ημερομηνία/ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' δεν είναι σωστός πραγματικός αριθμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' δεν είναι σωστός ακέραιος αριθμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' δεν μπορεί να μετατραπεί σε ημερομηνία η ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "(change)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr "(no string)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(χωρίς ετικέτα)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-at\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Fiscal number\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-globe\" role=\"img\" aria-label=\"Website\" title=\"Website\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "<span>Copyright &amp;copy;</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "A name for your favorite is required."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"Ένα αναδυόμενο παράθυρο έχει αποκλειστεί. Ίσως χρειαστεί να αλλάξετε τις "
"ρυθμίσεις του προγράμματος περιήγησής σας για να επιτρέψετε αναδυόμενα "
"παράθυρα για αυτήν τη σελίδα."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"A popup window with your report was blocked. You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "ΟΛΑ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Denied"
msgstr "Μη επιτρεπτή πρόσβαση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Error"
msgstr "Σφάλμα Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Πρόσβασης σε όλες τις Enterprise Εφαρμογές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#, python-format
msgid "Action"
msgstr "Ενέργεια"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "ID Ενέργειας:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "Ενεργοποίηση Εντοπισμού Σφαλμάτων Σταθερών Αρχείων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "Προσθήκη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "Προσθήκη Προσαρμοσμένου Φίλτρου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add a condition"
msgstr "Προσθέστε μια συνθήκη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr "Προσθήκη γραμμής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "Προσθήκη στα Αγαπημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add..."
msgstr "Προσθήκη..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "Προσθήκη: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt and Sign"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Advanced Search..."
msgstr "Προχωρημένη αναζήτηση..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "Προσοχή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "All"
msgstr "Όλα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr "Ολοήμερη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "Όλοι οι χρήστες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Alt"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin. (Opening your browser "
"console might give you a hint on the error.)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "And more"
msgstr "και περισσότερα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "Οποιοδήποτε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Apply"
msgstr "Εφαρμογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Αρχειοθετήθηκαν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Είστε σίγουροι ότι θέλετε να αφαιρέστε αυτή την στήλη;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Είστε σίγουροι ότι θέλετε να αφαιρέστε αυτό το φίλτρο;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτήν την εγγραφή;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "Συνημμένο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attachment :"
msgstr "Συνημένο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "Διαθέσιμα πεδία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr "Άβαταρ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bar Chart"
msgstr "Ραβδόγραμμα"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Βάση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Based On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Become Superuser"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr "Δυαδικό αρχείο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Blue"
msgstr "Μπλε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "Εγγύηση διορθώσεις σφαλμάτων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "Πλήκτρο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "Τύπος Κουμπιού:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "CLEAR"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "Ημερολόγιο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr ""
"Στην προβολή ημερολογίου δεν έχει ορισθεί το χαρακτηριστικό 'date_start'"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Cancel"
msgstr "Ακύρωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Cannot render chart with mode : "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/change_password.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "Αλλαγή Κωδικού Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "Αλλαγή προεπιλογής:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Checkbox"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Choose"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "Καθαρισμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Clear Events"
msgstr "Εκκαθάριση γεγονότων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/dialog.xml:0
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "Close"
msgstr "Κλείσιμο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr "Τίτλος στήλης"

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "Επωνυμία Εταιρίας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Compare To"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "Κατάσταση:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Confirm New Password"
msgstr "Επιβεβαίωση νέου Κωδικού Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Επιβεβαίωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection lost"
msgstr "Χάθηκε η σύνδεση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection restored"
msgstr "Η σύνδεση αποκαταστάθηκε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "Περιεχόμενο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Control"
msgstr "Έλεγχος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Control panel toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Copied !"
msgstr "Αντιγράφηκε !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Copy Text"
msgstr "Αντιγραφή Κειμένου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image."
msgstr "Δεν ήταν δυνατή η προβολή της συγκεκριμένης εικόνας."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "Δεν ήταν δυνατή η σειριοποίηση XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Count"
msgstr "Πλήθος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Create"
msgstr "Δημιουργία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "Δημιουργία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Δημιουργία \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create a %s"
msgstr "Δημιουργία ενός %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Create a new record"
msgstr "Δημιουργία Νέας Εγγραφής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "Δημιουργία και Επεξεργασία..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and edit"
msgstr "Δημιουργία και επεξεργασία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create: "
msgstr "Δημιουργία: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Create: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Created by :"
msgstr "Δημιουργήθηκε από:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation Date:"
msgstr "Ημερομηνία Δημιουργίας:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation User:"
msgstr "Χρήστης δημιουργίας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark purple"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Βάση Δεδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "Ημερομηνία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Day"
msgstr "Ημέρα"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "Προεπιλογή:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Delete"
msgstr "Διαγραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete this attachment"
msgstr "Διαγραφή συνημμένου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Description"
msgstr "Περιγραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Discard"
msgstr "Απόρριψη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Discard a record modification"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr "Θέλετε πραγματικά να διαγράψετε αυτό το φίλτρο από τα αγαπημένα;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Documentation"
msgstr "Τεκμηρίωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "Τομέας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "Τομέας:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Μην φύγετε ακόμη,<br />φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "Λήψη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download xls"
msgstr "Λήψη xls"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "Δημιουργία Αντίγραφου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "Επεξεργασία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit Action"
msgstr "Επεξεργασία ενέργειας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "Επεξεργασία Στήλης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit ControlPanelView"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr "Επεξεργασία Τομέα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr "Επεξεργασία Σταδίου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit View:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Edit a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "Email"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr "E-mail:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Error"
msgstr "Σφάλμα"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "Σφάλμα, ο κωδικός δεν άλλαξε!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "Το ημερολόγιο όλων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Expand all"
msgstr "Ανάπτυξη όλων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "Εξαγωγή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "Εξαγωγή δεδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "Εξωτερικό Αναγνωριστικό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "External link"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "FILTER"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/control_panel_model.js:0
#, python-format
msgid "Failed to evaluate search criterions"
msgstr "Αποτυχία εκτίμησης των κριτηρίων αναζήτησης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "Λάθος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Favorites"
msgstr "Αγαπημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "Πεδίο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Fields View Get"
msgstr "Ανάκτηση Πεδίων Προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "Πεδία για εξαγωγή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "Αρχείο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File Upload"
msgstr "Μεταφόρτωση αρχείου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "Μεταφόρτωση αρχείου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Filter on: %s"
msgstr "Ενεργό φίλτρο: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "Υπάρχει ήδη φίλτρο με το όνομα αυτό."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/filter_menu.js:0
#, python-format
msgid "Filters"
msgstr "Φίλτρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Flip axis"
msgstr "Αναστροφή άξονα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "Δίπλωμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"Για λόγους συμβατότητας με το Excel, τα δεδομένα δεν μπορούν να εξαχθούν εάν υπάρχουν περισσότερες από 256 στήλες.\n"
"\n"
"Συμβουλή: Δοκιμάστε να αναστρέψετε τους άξονες, να φιλτράρετε περαιτέρω ή να μειώσετε τον αριθμό των μέτρων."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "Φόρμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "Πλήρες Όνομα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fushia"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""
"Αποκτήστε αυτό το χαρακτηριστικό και πολύ περισσότερα με Odoo Enterprise!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Global Business Error"
msgstr "Γενικό Επιχειρησιακό Σφάλμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Got it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "Γράφημα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Green"
msgstr "Πράσινο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/groupby_menu.js:0
#, python-format
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Group by: %s"
msgstr "Ομαδοποίηση κατά: %s"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Hue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "ID:"
msgstr "Αναγνωριστικό:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Image"
msgstr "Εικόνα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr "Ακέραιος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid data"
msgstr "Μη έγκυρα δεδομένα"

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr "Μη έγκυρος τομέας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid mode for chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Tests"
msgstr "JS Τεστ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/user_menu.js:0
#, python-format
msgid "Keyboard Shortcuts"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "Γλώσσες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 30 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 365 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 5 Years"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 7 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Month"
msgstr "Τελευταίος Μήνας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Week"
msgstr "Προηγ. Εβδομάδα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Year"
msgstr "Τελευταίο Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "Τελευταία Ημερ. Τροποποίησης:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "Τελευταία τροποποίηση από:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "Έξοδος από τα Εργαλεία Προγραμματιστή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Light blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Lightness %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Line Chart"
msgstr "Γραμμικό γράφημα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "Λίστα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "Φόρτωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr "Φόρτωση περισσοτέρων ... ("

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading"
msgstr "Γίνεται φόρτωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading (%d)"
msgstr "Φόρτωση (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "Φόρτωση..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Σύνδεση"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Log out"
msgstr "Αποσύνδεση"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Λογότυπο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Mac"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Main actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Attachments"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Διαχείριση Βάσεων Δεδομένων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Filters"
msgstr "Διαχείριση Φίλτρων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "Αντιστοίχιση "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "Αντιστοίχιση εγγραφών με"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "Τα αρχεία αντιστοιχίζονται με τον ακόλουθο κανόνα:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""
"Ίσως θα πρέπει να εξετάσετε την επαναφόρτωση της εφαρμογής πατώντας F5 ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Measures"
msgstr "Μετρήσεις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Medium blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Metadata (%s)"
msgstr "Μεταδομένα (%s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "Μέθοδος:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Missing Record"
msgstr "Λείπει εγγραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr "Υποστήριξη κινητού"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modified by :"
msgstr "Τροποποιήθηκε από :"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr "Τροποποιητές:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Month"
msgstr "Μήνας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "More"
msgstr "Περισσότερα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "My Odoo.com account"
msgstr "Ο Odoo.com λογαριασμός μου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Name:"
msgstr "Όνομα:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "Νέα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "Νέος Κωδικός Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New design"
msgstr "Νέα σχεδίαση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New template"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "Επόμενο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "No"
msgstr "Όχι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "No Update:"
msgstr "Μη Ανανέωση:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No attachment available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "No color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "No data to display"
msgstr "Δεν υπάρχουν δεδομένα προς εμφάνιση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No metadata available"
msgstr "Μη διαθέσιμα μεταδομένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "No records"
msgstr "Δεν υπάρχουν αρχεία"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "No results to show..."
msgstr "Δεν υπάρχουν αποτελέσματα για προβολή..."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Node [%s] is not a JSONified XML node"
msgstr "Ο κόμβος [%s] δεν είναι συμβατός JSON κόμβος XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "None"
msgstr "Κανένα"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "Αντικείμενο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Οι Εφαρμογές Odoo θα είναι σύντομα διαθέσιμες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Ok"
msgstr "ΟΚ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr "Παλιός Κωδικός Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "Σε αλλαγή:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Only Integer or Float Value should be valid."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "Μόνο εσείς"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Opacity %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools"
msgstr "Ενεργοποίηση των Εργαλείων Προγραμματιστή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools#{widget.debug_mode_help}"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open View"
msgstr "Άνοιγμα Προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the next record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the previous record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to kanban view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to list view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "Άνοιγμα: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Orange"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "Άλλο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Κωδικός Πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr "Ποσοστό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Phone"
msgstr "Τηλέφωνο"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr "Τηλέφωνο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Pick a color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pie Chart"
msgstr "Κυκλικό Διάγραμμα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""
"Το γράφημα πίτας δεν μπορεί να εμφανιστεί όταν όλες οι τιμές είναι μηδέν. "
"Προσπαθήστε να αλλάξετε τον τομέα έτσι ώστε να εμφανίζει μόνο θετικά "
"αποτελέσματα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Το γράφημα πίτας δεν μπορεί να περιέχει και αρνητικές και θετικές τιμές. "
"Προσπαθήστε να αλλάξετε τον τομέα έτσι ώστε να εμφανίζει μόνο θετικά "
"αποτελέσματα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "Συγκεντρωτικό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pivot settings"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "Παρακαλούμε εισάγετε τον όνομα της λίστας των πεδίων για αποθήκευση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "Παρακαλώ επιλέξτε πεδία για εξαγωγή..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "Παρακαλώ επιλέξτε πεδία για να αποθηκεύσετε τη λίστα εξαγωγής..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Δημιουργήθηκε με <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Preferences"
msgstr "Προτιμήσεις"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr "Προηγούμενο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Period"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Year"
msgstr "Προηγούμενο Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#: code:addons/web/static/src/xml/report.xml:0
#, python-format
msgid "Print"
msgstr "Εκτύπωση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "Προτεραιότητα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Purple"
msgstr "Μωβ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q1"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q2"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q3"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q4"
msgstr ""

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Quarter"
msgstr "Τρίμηνο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr "Κουμπί Επιλογής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Range"
msgstr "Εύρος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Red"
msgstr "Κόκκινο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "Σχέση:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove"
msgstr "Αφαίρεση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "Κατάργηση από τα Αγαπημένα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "Απομάκρυνση του αγαπημένου από τη λίστα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid "Report"
msgstr "Αναφορά"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Request timeout"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Tests"
msgstr "Εκτέλεση JS Tests"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "SEE RESULT"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Salmon pink"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Saturation %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save"
msgstr "Αποθήκευση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "Αποθήκευση  & Δημιουργία Νέου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Save As..."
msgstr "Αποθήκευση ως…"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save Current Search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Save a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Save default"
msgstr "Αποθήκευση προεπιλογής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search"
msgstr "Αναζήτηση"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr "Αναζήτηση %(field)s στο: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr "Αναζήτηση σε %(field)s για: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "Αναζήτηση Περισσότερων..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search..."
msgstr "Αναζήτηση..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search: "
msgstr "Αναζήτηση: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "Επιλογή"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Select a view"
msgstr "Επιλογή προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr "Επιλεγμένες εγγραφές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "Επιλογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "Επιλογή:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Set Default"
msgstr "Ορισμός ως προεπιλογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Set Defaults"
msgstr "Ορισμός προεπιλογών"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Share with all users"
msgstr "Κοινό με όλους τους χρήστες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Shortcuts"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "Προβολή τοπικά διαθέσιμων εφαρμογών"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "Υπογραφή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "Μέγεθος:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr "Κάτι τρομερό συνέβη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Something went wrong !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "Ειδικά:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Stacked"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "Ακόμα φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Ακόμη Φορτώνει...<br />Παρακαλώ Περιμένετε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "Στύλ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "Στυλ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Summary"
msgstr "Περίληψη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Summary:"
msgstr "Σύνοψη:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Support"
msgstr "Υποστήριξη"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Switch to this company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "Ετικέτες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Κάνε ένα λεπτό διάλειμα,<br />γιατί ακόμη φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Technical Translation"
msgstr "Τεχνική Μετάφραση"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr "Τηλ:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "Κείμενο"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "The domain you entered is not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or"
" followed a non-relational field?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr "Το πεδίο είναι άδειο, δεν υπάρχει τίποτα για αποθήκευση!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "The following field is invalid:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "The following fields are invalid:"
msgstr "Τα επόμενα πεδία είναι μη έγκυρα"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "Ο νέος κωδικός και η επιβεβαίωση του πρέπει να είναι ακριβώς ίδιοι."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "Ο παλιός κωδικός που δώσατε είναι λάθος, ο κωδικός σας δεν άλλαξε"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to"
" proceed?"
msgstr ""
"Η εγγραφή έχει τροποποιηθεί, οι αλλαγές σας θα απορριφθούν. Θέλετε να "
"συνεχίσετε?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "Το συγκεκριμένο αρχείο ξεπερνάει το μέγιστο μέγεθως των %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""
"Ο τύπος του πεδίου '%s' πρέπει να έχει μία σχέση ΠολλάΠροςΠολλά με το "
"μοντέλο 'ir.attachment'."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Υπήρξε κάποιο πρόβλημα κατά το ανέβασμα του αρχείου σας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Month"
msgstr "Αυτόν τον μήνα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Week"
msgstr "Της Εβδομάδας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Year"
msgstr "Αυτό το Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/date_picker.js:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr "Το φίλτρο είναι καθολικό και θα αφαιρεθεί για όλους εάν συνεχίσετε."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr "Χρόνος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Time Ranges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "Σήμερα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Toggle Timelines"
msgstr "Εναλλαγή Χρονολογικών Σειρών"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "Σύνολο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Traceback:"
msgstr "Ανίχνευση προς τα πίσω:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "Αληθές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_renderer.js:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is at least one measure and"
" no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Trying to reconnect..."
msgstr "Προσπάθεια επανασύνδεσης..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "Τύπος:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Μη αρχειοθετημένο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_renderer_mobile.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Undefined"
msgstr "Μη ορισμένο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Unknown CORS error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "Άγνωστος μη γραμματικός τύπου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:0
#, python-format
msgid "Unnamed"
msgstr "Χωρίς όνομα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "Αναβάθμιση τώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "Αναβάθμιση σε μελλοντικές εκδόσεις"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upload your file"
msgstr "Μεταφόρτωση του αρχείου σας"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr "Αποστέλεται"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "Σφάλμα αποστολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "Αποστολή..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Use by default"
msgstr "Χρησιμοποίησε ως προεπιλογή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "User"
msgstr "Χρήστης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "User Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Users"
msgstr "Χρήστες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Validation Error"
msgstr "Σφάλμα Επικύρωσης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Fields"
msgstr "Προβολή Πεδίων"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Metadata"
msgstr "Προβολή Μεταδομένων"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "Τύπος Προβολής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Warning"
msgstr "Προσοχή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "Warning : You have to save first before attaching a file."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Week"
msgstr "Εβδομάδα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:0
#, python-format
msgid "Well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "Γραφικό Στοιχείο:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Windows/Linux"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr "Λανθασμένο όνομα χρήστη/κωδικός πρόσβασης"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Wrong value entered!"
msgstr "Έχει εισαχθεί λάθος τιμή!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Year"
msgstr "Έτος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Yellow"
msgstr "Κίτρινο"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Yes"
msgstr "Ναι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Yesterday"
msgstr "Χθες"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "You are back online"
msgstr "Έχετε επανασυνδεθεί"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr ""
"Θέλετε να δημιουργήσετε μία νέα %s, είσαστε σίγουροι ότι δεν υπάρχει ήδη;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""
"Δεν μπορείτε να ακολουθήσετε τις σχέσεις για αυτή την κατασκευή αλυσίδας "
"πεδίων"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "Δεν μπορείτε να αφήσετε κανένα συνθηματικό κενό"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "Μπορεί να μην το πιστεύετε,<br/>αλλά η εφαρμογή φορτώνει..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr ""
"Έληξε η Odoo συνεδρία σας. Παρακαλούμε ανανεώστε την τρέχουσα ιστοσελίδα."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr "[Μη γραφικά στοιχεία %s]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "Μια ημέρα πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "πριν ένα λεπτό περίπου"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "περίπου πριν από ένα μήνα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "περίπου ένα έτος πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "περίπου πριν από μια ώρα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "όλες οι εγγραφές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "contains"
msgstr "περιέχει"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "doesn't contain"
msgstr "δεν περιέχει"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than"
msgstr "μεγαλύτερη από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than or equal to"
msgstr "Μεγαλύτερο ή ίσο του"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "is"
msgstr "είναι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is between"
msgstr "είναι ανάμεσα"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is equal to"
msgstr "είναι ίσο με"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is false"
msgstr "είναι ψευδής"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not"
msgstr "δεν είναι"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is not equal to"
msgstr "δεν είναι ίσο με"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not set"
msgstr "δεν έχει οριστεί"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is set"
msgstr "έχει οριστεί"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is true"
msgstr "είναι αληθές"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than"
msgstr "μικρότερο από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "λιγότερο από ένα λεπτό πριν"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than or equal to"
msgstr "μικρότερο ή ίσο από"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid integer"
msgstr "δεν είναι έγκυρος ακέραιος"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid number"
msgstr "μη έγκυρος αριθμός"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "or"
msgstr "ή"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion
msgid "portal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "απομένουν)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr ""
