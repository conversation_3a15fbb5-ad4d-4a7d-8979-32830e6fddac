# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web
#
# Translators:
# <PERSON> <<EMAIL>>, 2015-2016
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-22 05:01+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  es_EC.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:866
#, python-format
msgid " & Close"
msgstr "& Cerrar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:467
#, python-format
msgid " selected records"
msgstr "registros seleccionados"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:168
#, python-format
msgid " view couldn't be loaded"
msgstr "no pueden ser cargados"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:115
#: code:addons/web/static/src/js/views/search_filters.js:290
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:116
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1587
#, python-format
msgid "%(page)d/%(page_count)d"
msgstr "%(page)d/%(page_count)d"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:66
#, python-format
msgid "%(view_type)s view"
msgstr "Vista %(view_type)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:450
#, python-format
msgid "%d / %d"
msgstr "%d / %d"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:134
#, python-format
msgid "%d days ago"
msgstr "Hace %d días"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:132
#, python-format
msgid "%d hours ago"
msgstr "hace %d horas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:130
#, python-format
msgid "%d minutes ago"
msgstr "hace %d minutos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:136
#, python-format
msgid "%d months ago"
msgstr "hace %d meses"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:138
#, python-format
msgid "%d years ago"
msgstr "hace %d años"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:440
#, python-format
msgid "%d-%d of %d"
msgstr "%d-%d de %d"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1496
#, python-format
msgid "%s (%d)"
msgstr "%s (%d)"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid "&lt;!--[if lte IE 9]&gt;"
msgstr "&lt;!--[if lte IE 9]&gt;"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid "&lt;![endif]--&gt;"
msgstr "&lt;![endif]--&gt;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:177
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' no es una fecha correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/time.js:193
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' no es una fecha, fechahora ni hora correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:169
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' no es una fecha-hora correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:146
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' no es un float correcto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:134
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' no es un entero correcto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:182
#, python-format
msgid "'%s' is not a correct time"
msgstr "'%s' no es una hora correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/time.js:205
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' no es convertible a fecha, fecha-hora ni hora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:77
#, python-format
msgid "(%d records)"
msgstr "(%d registros)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:106
#, python-format
msgid "(Community Edition)"
msgstr "(Edición de la Comunidad)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:100
#, python-format
msgid "(no string)"
msgstr "(sin cadena)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:34
#, python-format
msgid "(nolabel)"
msgstr "(sin etiqueta)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1059
#, python-format
msgid "...Upload in progress..."
msgstr "...Carga en progreso..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span class=\"oe_logo_edit\">Edit Company data</span>"
msgstr "<span class=\"oe_logo_edit\">Editar Datos de Compañía</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span>Odoo</span>"
msgstr "<span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/user_menu.js:79
#: code:addons/web/static/src/xml/base.xml:84
#, python-format
msgid "About"
msgstr "Sobre nosotros"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:25
#, python-format
msgid "Access Denied"
msgstr "Acceso Denegado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:21
#, python-format
msgid "Access Error"
msgstr "Error de Acceso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1515
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Acceda a todas las aplicaciones Enterprise."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:24
#, python-format
msgid "Action"
msgstr "Acción"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1125
#, python-format
msgid "Action Button"
msgstr "Botón de Acción"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:120
#, python-format
msgid "Action ID:"
msgstr "ID de Acción"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:685
#, python-format
msgid "Activate"
msgstr "Activar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:96
#, python-format
msgid "Activate the developer mode"
msgstr "Activar el modo de desarrollo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:682
#, python-format
msgid "Active"
msgstr "Activo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1384
#: code:addons/web/static/src/xml/base.xml:1106
#: code:addons/web/static/src/xml/base.xml:1339
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1212
#, python-format
msgid "Add Custom Filter"
msgstr "Añadir Filtro personalizado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1213
#, python-format
msgid "Add a condition"
msgstr "Agregar condición"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1024
#, python-format
msgid "Add an item"
msgstr "Añadir un elemento"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1223
#, python-format
msgid "Add custom group"
msgstr "Añadir grupo personalizado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:325
#, python-format
msgid "Add..."
msgstr "Añadir..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1325
#, python-format
msgid "Add: "
msgstr "Agregar: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1119
#, python-format
msgid "Advanced Search..."
msgstr "Búsqueda avanzada..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:169
#, python-format
msgid "Alert"
msgstr "Alerta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:562
#, python-format
msgid "All users"
msgstr "Todos los usuarios"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1520
#, python-format
msgid "And more"
msgstr "Y más"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1213
#: code:addons/web/static/src/xml/base.xml:1233
#, python-format
msgid "Apply"
msgstr "Aplicar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:689
#: code:addons/web/static/src/js/views/list_view.js:307
#, python-format
msgid "Archive"
msgstr "Archivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:690
#, python-format
msgid "Archived"
msgstr "Archivado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:243
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "¿Se encuentra seguro que desea eliminar este archivo?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:297
#, python-format
msgid "Attachment :"
msgstr "Adjunto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1323
#, python-format
msgid "Available fields"
msgstr "Campos disponibles"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:614
#, python-format
msgid "Bar Chart"
msgstr "Gráfico de Barras"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1955
#, python-format
msgid "Binary file"
msgstr "Archivo binario"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1519
#, python-format
msgid "Bugfixes guarantee"
msgstr "Garantía de corrección de errores"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:97
#, python-format
msgid "Button"
msgstr "Botón"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:112
#, python-format
msgid "Button Type:"
msgstr "Tipo de Botón"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:285
#, python-format
msgid "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"
msgstr "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_common.js:134
#, python-format
msgid "Can't convert value %s to context"
msgstr "No se puede convertir el valor %s a context"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:359
#, python-format
msgid "Can't send email to invalid e-mail address"
msgstr "No puede enviar el email a una dirección inválida"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:182
#: code:addons/web/static/src/js/framework/dialog.js:183
#: code:addons/web/static/src/js/views/form_common.js:1031
#: code:addons/web/static/src/js/views/form_relational_widgets.js:44
#: code:addons/web/static/src/js/views/form_widgets.js:1706
#: code:addons/web/static/src/xml/base.xml:60
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:748
#: code:addons/web/controllers/main.py:750
#: code:addons/web/controllers/main.py:756
#: code:addons/web/controllers/main.py:757
#: code:addons/web/static/src/js/widgets/change_password.js:15
#: code:addons/web/static/src/xml/base.xml:59
#, python-format
msgid "Change Password"
msgstr "Cambiar Contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:71
#, python-format
msgid "Change default:"
msgstr "Cambiar predeterminado:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:472
#, python-format
msgid "Change selection "
msgstr "Cambiar selección"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:963
#: code:addons/web/static/src/xml/base.xml:1017
#: code:addons/web/static/src/xml/base.xml:1019
#, python-format
msgid "Clear"
msgstr "Limpiar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:135
#: code:addons/web/static/src/js/web_client.js:132
#, python-format
msgid "Client Error"
msgstr "Error en el cliente"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:860
#: code:addons/web/static/src/js/views/form_view.js:1194
#: code:addons/web/static/src/js/widgets/data_export.js:41
#, python-format
msgid "Close"
msgstr "cerrar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:536
#, python-format
msgid "Condition:"
msgstr "Condición:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:53
#, python-format
msgid "Confirm New Password:"
msgstr "Confirmar nueva contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:194
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:59
#, python-format
msgid "Context:"
msgstr "Contexto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:116
#, python-format
msgid "Copyright © 2004-2015 Odoo S.A."
msgstr "Derechos reservados © 2004-2015 Odoo S.A."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1379
#, python-format
msgid "Could not display the selected image."
msgstr "No se pudo mostrar la imagen seleccionada."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1109
#, python-format
msgid "Could not find id in dataset"
msgstr "No se puede encontrar en los datos activos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:180
#, python-format
msgid "Could not serialize XML"
msgstr "No pudo serializar el XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_view.js:90
#: code:addons/web/static/src/js/views/graph_widget.js:21
#: code:addons/web/static/src/js/views/pivot_view.js:186
#: code:addons/web/static/src/xml/base.xml:627
#: code:addons/web/static/src/xml/base.xml:659
#, python-format
msgid "Count"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1034
#: code:addons/web/static/src/js/views/form_relational_widgets.js:30
#: code:addons/web/static/src/js/views/list_view.js:48
#: code:addons/web/static/src/js/views/list_view.js:2033
#: code:addons/web/static/src/xml/base.xml:501
#, python-format
msgid "Create"
msgstr "Crear"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:225
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Crear \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:27
#, python-format
msgid "Create a %s"
msgstr "Crear un %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:236
#, python-format
msgid "Create and Edit..."
msgstr "Crear y Editar..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:40
#, python-format
msgid "Create and edit"
msgstr "Crear y editar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:281
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1083
#, python-format
msgid "Create: "
msgstr "Crear: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:302
#, python-format
msgid "Created by :"
msgstr "Creado por:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:248
#, python-format
msgid "Creation Date:"
msgstr "Fecha de Creación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:244
#, python-format
msgid "Creation User:"
msgstr "Usuario que creó:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1067
#: code:addons/web/static/src/js/views/search_menus.js:175
#, python-format
msgid "Custom Filter"
msgstr "Filtro Personalizado"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Base de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:110
#, python-format
msgid "Database expiration:"
msgstr "Expiración de la Base de Datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:673
#, python-format
msgid "Day"
msgstr "Día"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:683
#, python-format
msgid "Deactivate"
msgstr "Desactivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:519
#, python-format
msgid "Default:"
msgstr "Predeterminado:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:214
#: code:addons/web/static/src/js/views/list_view.js:309
#: code:addons/web/static/src/xml/base.xml:1408
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:313
#, python-format
msgid "Delete this attachment"
msgstr "Eliminar este adjunto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1066
#, python-format
msgid "Delete this file"
msgstr "Eliminar este archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:860
#: code:addons/web/static/src/xml/base.xml:425
#: code:addons/web/static/src/xml/base.xml:507
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:205
#, python-format
msgid "Do you really want to delete this attachment ?"
msgstr "¿Realmente quiere borrar este adjunto?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:803
#, python-format
msgid "Do you really want to delete this record?"
msgstr "¿Realmente desea eliminar este registro?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:629
#, python-format
msgid "Do you really want to remove these records?"
msgstr "¿Está seguro que quiere eliminar estos registros?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:82
#, python-format
msgid "Documentation"
msgstr "Documentación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:63
#, python-format
msgid "Domain:"
msgstr "Dominio:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:16
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "No salir aún, <br />Aún está cargando..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1305
#: code:addons/web/static/src/js/views/list_view.js:1955
#, python-format
msgid "Download"
msgstr "Descargar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1971
#, python-format
msgid "Download \"%s\""
msgstr "Descargar \"%s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:122
#, python-format
msgid "Download xls"
msgstr "Descargar xls"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:215
#, python-format
msgid "Duplicate"
msgstr "Duplicar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:359
#, python-format
msgid "E-mail Error"
msgstr "Error en Correo electrónico"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:219
#: code:addons/web/static/src/xml/base.xml:496
#: code:addons/web/static/src/xml/base.xml:962
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:221
#, python-format
msgid "Edit Action"
msgstr "Editar Acción"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:220
#, python-format
msgid "Edit SearchView"
msgstr "Editar SearchView"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:179
#: code:addons/web/static/src/xml/base.xml:222
#, python-format
msgid "Edit Workflow"
msgstr "Editar Flujo de trabajo"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Email"
msgstr "Email"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/website.tour.xml:25
#, python-format
msgid "End This Tutorial"
msgstr "Finaliza este tutorial"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:168
#: code:addons/web/static/src/js/views/search_menus.js:83
#: code:addons/web/static/src/js/views/search_menus.js:90
#, python-format
msgid "Error"
msgstr "Error"

#. module: web
#: code:addons/web/controllers/main.py:757
#, python-format
msgid "Error, password not changed !"
msgstr "¡Error, contraseña no modificada!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:462
#, python-format
msgid "Error: Bad domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/pyeval.js:993
#, python-format
msgid "Evaluation Error"
msgstr "Error de evaluación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:648
#, python-format
msgid "Expand all"
msgstr "Expandir todo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:306
#: code:addons/web/static/src/xml/base.xml:1299
#, python-format
msgid "Export"
msgstr "Exportar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:51
#, python-format
msgid "Export Data"
msgstr "Exportar Datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1317
#, python-format
msgid "Export Formats"
msgstr "Formato para exportar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:42
#, python-format
msgid "Export To File"
msgstr "Exportar a Archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1311
#, python-format
msgid "Export Type:"
msgstr "Tipo de Exportación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1314
#, python-format
msgid "Export all Data"
msgstr "Exportar todos los datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:381
#, python-format
msgid "Failed to evaluate search criterions"
msgstr "Falla al evaluar los criterios de búsqueda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1237
#, python-format
msgid "Favorites"
msgstr "Favoritos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1358
#, python-format
msgid "Field '%s' specified in view could not be found."
msgstr "No se encontró el campo '%s' especificado en la vista."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:39
#, python-format
msgid "Field:"
msgstr "Campo:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:140
#: code:addons/web/static/src/xml/base.xml:215
#, python-format
msgid "Fields View Get"
msgstr "Obtener Campos de Vista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1325
#, python-format
msgid "Fields to export"
msgstr "Campos a exportar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1220
#, python-format
msgid "File Upload"
msgstr "Cargar Archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1200
#, python-format
msgid "File upload"
msgstr "Subir Archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:550
#, python-format
msgid "Filter"
msgstr "Filtro"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:83
#, python-format
msgid "Filter name is required."
msgstr "Nombre de filtro es requerido."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:494
#, python-format
msgid "Filter on: %s"
msgstr "Filtrar por: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:90
#, python-format
msgid "Filter with same name already exists."
msgstr "Filtro con el mismo nombre existe."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1209
#, python-format
msgid "Filters"
msgstr "Filtros"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:647
#, python-format
msgid "Flip axis"
msgstr "Cambiar eje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:808
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there is more than 256 "
"columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:119
#, python-format
msgid "For more information visit"
msgstr "Para información adicional visite"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:34
#: code:addons/web/static/src/js/views/form_view.js:361
#, python-format
msgid "Form"
msgstr "Formulario"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:118
#, python-format
msgid "GNU Lesser General Public License"
msgstr "GNU Lesser General Public License"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1512
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "¡Obtenga esta características y otras con Odoo Enterprise!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:24
#, python-format
msgid "Global Business Error"
msgstr "Error de Negocio global"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_view.js:18
#, python-format
msgid "Graph"
msgstr "Gráfico"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:464
#, python-format
msgid "Group"
msgstr "Grupo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:695
#: code:addons/web/static/src/xml/base.xml:1219
#, python-format
msgid "Group By"
msgstr "Agrupar por"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:670
#, python-format
msgid "Group by: %s"
msgstr "Agrupar por: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:50
#, python-format
msgid "High"
msgstr "Alta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:232
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1379
#, python-format
msgid "Image"
msgstr "imagen"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1313
#, python-format
msgid "Import-Compatible Export"
msgstr "Compatible con Importación-Exportación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:684
#, python-format
msgid "Inactive"
msgstr "Inactivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:198
#: code:addons/web/static/src/js/views/graph_widget.js:205
#, python-format
msgid "Invalid data"
msgstr "Datos inválidos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:111
#: code:addons/web/static/src/xml/base.xml:213
#, python-format
msgid "JS Tests"
msgstr "Tests JS"

#. module: web
#: code:addons/web/controllers/main.py:764
#, python-format
msgid "Languages"
msgstr "Idiomas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:256
#, python-format
msgid "Latest Modification Date:"
msgstr "Última  fecha de modificación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:252
#, python-format
msgid "Latest Modification by:"
msgstr "Última Modificación por:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:225
#, python-format
msgid "Leave Debug Mode"
msgstr "Salir de Modo de Depuración"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:118
#, python-format
msgid "Licenced under the terms of"
msgstr "Licencia bajo los términos de "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:615
#, python-format
msgid "Line Chart"
msgstr "Gráfico de Líneas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:39
#, python-format
msgid "List"
msgstr "Lista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/loading.js:12
#: code:addons/web/static/src/js/widgets/loading.js:46
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/loading.js:44
#, python-format
msgid "Loading (%d)"
msgstr "Cargando (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:13
#: code:addons/web/static/src/xml/base.xml:5
#, python-format
msgid "Loading..."
msgstr "Cargando..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/pyeval.js:997
#, python-format
msgid ""
"Local evaluation failure\n"
"%s\n"
"\n"
"%s"
msgstr ""
"Fallo de la evaluación local\n"
"%s\n"
"\n"
"%s"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Iniciar sesión"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:88
#, python-format
msgid "Log out"
msgstr "Cerrar sesión"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:49
#, python-format
msgid "Low"
msgstr "Baja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:450
#, python-format
msgid "M2O search fields do not currently handle multiple default values"
msgstr "Los campos de búsqueda M2O no soportan multiples valores por defecto"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Gestionar Bases de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:146
#: code:addons/web/static/src/xml/base.xml:216
#, python-format
msgid "Manage Filters"
msgstr "Gestionar filtros"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:19
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""
"Tal vez usted podría considerar volver a cargar la aplicación presionando "
"F5 ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:619
#, python-format
msgid "Measure"
msgstr "Medida"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:651
#, python-format
msgid "Measures"
msgstr "Medidas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:94
#, python-format
msgid "Metadata (%s)"
msgstr "Metadatos (%s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:116
#, python-format
msgid "Method:"
msgstr "Método:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:22
#, python-format
msgid "Missing Record"
msgstr "Registro no encontrado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1517
#, python-format
msgid "Mobile support"
msgstr "Soporte para móviles"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:134
#, python-format
msgid "Model %s fields"
msgstr "Campos del Modelo %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:307
#, python-format
msgid "Modified by :"
msgstr "Modificado por:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:67
#, python-format
msgid "Modifiers:"
msgstr "Modificadores:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:675
#, python-format
msgid "Month"
msgstr "Mes"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:947
#, python-format
msgid "More"
msgstr "Más"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu
msgid "More <b class=\"caret\"/>"
msgstr "Más <b class=\"caret\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1343
#, python-format
msgid "Move Down"
msgstr "Mover hacia abajo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1342
#, python-format
msgid "Move Up"
msgstr "Mover hacia arriba"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:87
#, python-format
msgid "My Odoo.com account"
msgstr "Mi cuenta Odoo.com"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1362
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1422
#, python-format
msgid "Name:"
msgstr "Nombre:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:366
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:48
#, python-format
msgid "New Password:"
msgstr "Nueva contraseña:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1516
#, python-format
msgid "New design"
msgstr "Nuevo diseño"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:330
#, python-format
msgid "No"
msgstr "No"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:240
#, python-format
msgid "No Update:"
msgstr "Sin actualizar:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:95
#, python-format
msgid ""
"No data available for this chart. Try to add some records, or make sure that"
" there is no active filter in the search bar."
msgstr ""
"No existen datos disponibles para este gráfico. Trate de añadir algunos "
"récords, o asegúrese que no existan filtros activos en la barra de búsqueda."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:691
#, python-format
msgid ""
"No data available for this pivot table.  Try to add some records, or make sure\n"
"        that there is at least one measure and no active filter in the search bar."
msgstr ""
"No existen datos disponibles para esta tabla dinámica. Trate de añadir "
"algunos registros, o asegúrese que exista por lo menos una medida y no "
"existan filtros activos en la barra de búsqueda."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:124
#, python-format
msgid "No data provided."
msgstr "No se han facilitado datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:94
#, python-format
msgid "No data to display"
msgstr "Sin datos para mostrar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:690
#, python-format
msgid "No data to display."
msgstr "Sin datos para mostrar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:89
#, python-format
msgid "No metadata available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:245
#, python-format
msgid "No results to show..."
msgstr "Sin resultados que mostrar..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:481
#, python-format
msgid "No selected record"
msgstr "Sin registro seleccionado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:144
#, python-format
msgid "Node [%s] is not a JSONified XML node"
msgstr "El nodo [%s] no es un nodo JSONificado XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:48
#, python-format
msgid "Normal"
msgstr "Normal"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:688
#, python-format
msgid "Not Archived"
msgstr "Sin archivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:238
#, python-format
msgid "Not enough data points"
msgstr "Sin suficientes puntos de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:885
#, python-format
msgid "Not shown in kanban"
msgstr "No se muestra en vista kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:43
#, python-format
msgid "Object:"
msgstr "Objeto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:37
#: model_terms:ir.ui.view,arch_db:web.layout
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:101
#, python-format
msgid "Odoo (Formerly OpenERP)"
msgstr "Odoo (Anteriormente OpenERP)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:189
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Aplicaciones Odoo estarán disponibles próximamente"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1717
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:117
#, python-format
msgid "Odoo S.A."
msgstr "Odoo S.A."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Odoo Web Tests"
msgstr "Test Web para Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:117
#, python-format
msgid "Odoo is a trademark of"
msgstr "Odoo es una marca registrada de "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:119
#, python-format
msgid "Odoo.com"
msgstr "Odoo.com"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:702
#, python-format
msgid "Off"
msgstr "Apagado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:41
#: code:addons/web/static/src/js/framework/dialog.js:159
#: code:addons/web/static/src/js/framework/dialog.js:177
#: code:addons/web/static/src/xml/base.xml:1396
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:43
#, python-format
msgid "Old Password:"
msgstr "Antigua contraseña:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:700
#, python-format
msgid "On"
msgstr "Activo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:75
#, python-format
msgid "On change:"
msgstr "On change:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/progress_bar.js:79
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Solo un valor entero es válido."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:555
#, python-format
msgid "Only you"
msgstr "Sólo usted"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:198
#, python-format
msgid "Open Debug Menu"
msgstr "Abrir el Menú de Desarrollo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:153
#: code:addons/web/static/src/js/views/form_relational_widgets.js:936
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1107
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1341
#, python-format
msgid "Open: "
msgstr "Abrir: "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:616
#, python-format
msgid "Pie Chart"
msgstr "Gráfico de Pastel"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:206
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""
"Los gráficos de pastel no pueden mostrar los números 0. Trate de cambiar sus"
" filtros para mostrar los resultados positivos."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:199
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Los gráficos de pastel no pueden mostrar números positivos y negativos. "
"Trate de cambiar sus filtros para mostrar los resultados positivos."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:24
#, python-format
msgid "Pivot"
msgstr "Tabla dinámica"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:166
#, python-format
msgid "Please enter save field list name"
msgstr "Por favor ingrese el nombre de la lista a guardar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1356
#, python-format
msgid "Please note that only the selected ids will be exported."
msgstr "Por favor, solo los registros seleccionados serán exportados."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1353
#, python-format
msgid ""
"Please pay attention that all records matching your search filter will be "
"exported. Not only the selected ids."
msgstr ""
"Recuerde que todos los registros que coincidan con su filtro de búsqueda "
"serán exportados. No solo los registros seleccionados."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:416
#, python-format
msgid "Please select fields to export..."
msgstr "Por favor seleccione los campos a exportar..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:403
#, python-format
msgid "Please select fields to save export list..."
msgstr "Por favor seleccione los campos de la lista para exportar"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "Powered by"
msgstr "Desarrollado por"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Desarrollado por <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:86
#, python-format
msgid "Preferences"
msgstr "Preferencias"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:23
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:223
#, python-format
msgid "Print Workflow"
msgstr "Imprimir Flujo de Trabajo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:694
#, python-format
msgid "Production Environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:676
#, python-format
msgid "Quarter"
msgstr "Trimestre"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:79
#, python-format
msgid "Relation:"
msgstr "Relación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1340
#, python-format
msgid "Remove"
msgstr "Eliminar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1341
#, python-format
msgid "Remove All"
msgstr "Borrar todo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:328
#, python-format
msgid "Render"
msgstr "Render"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:389
#, python-format
msgid "Resource Error"
msgstr "Error de recurso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:866
#: code:addons/web/static/src/xml/base.xml:422
#: code:addons/web/static/src/xml/base.xml:506
#: code:addons/web/static/src/xml/base.xml:1249
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:881
#, python-format
msgid "Save & New"
msgstr "Grabar & Nuevo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1010
#: code:addons/web/static/src/xml/base.xml:1012
#, python-format
msgid "Save As"
msgstr "Guardar Como"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1235
#, python-format
msgid "Save As..."
msgstr "Guardar como…"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1394
#, python-format
msgid "Save as:"
msgstr "Guardar Como:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1241
#, python-format
msgid "Save current search"
msgstr "Graba la búsqueda actual"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1195
#, python-format
msgid "Save default"
msgstr "Guardar por defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1327
#, python-format
msgid "Save fields list"
msgstr "Guardar lista de campos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1400
#, python-format
msgid "Saved exports:"
msgstr "Exportaciones guardadas:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:359
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr "Buscar %(field)s en: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:191
#: code:addons/web/static/src/js/views/search_inputs.js:210
#: code:addons/web/static/src/js/views/search_inputs.js:401
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr "Buscar %(field)s para: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1117
#, python-format
msgid "Search Again"
msgstr "Volver a buscar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:211
#, python-format
msgid "Search More..."
msgstr "Buscar más..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:281
#, python-format
msgid "Search: "
msgstr "Buscar: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:469
#, python-format
msgid "See selection "
msgstr "Ver selección"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1039
#: code:addons/web/static/src/xml/base.xml:1004
#, python-format
msgid "Select"
msgstr "Seleccionar"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Select <i class=\"fa fa-database\"/>"
msgstr "Seleccionar <i class=\"fa fa-database\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:493
#, python-format
msgid "Select records..."
msgstr "Seleccionar Registro..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1074
#, python-format
msgid "Selected domain"
msgstr "Seleccionar dominio"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:493
#, python-format
msgid "Selected records"
msgstr "Registros seleccionados"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:83
#, python-format
msgid "Selection:"
msgstr "Selección:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1192
#, python-format
msgid "Set Default"
msgstr "Fijar Predeterminado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:211
#, python-format
msgid "Set Defaults"
msgstr "Establecer por Defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1017
#, python-format
msgid "Setting 'id' attribute on existing record %s"
msgstr "Configuración de 'id' en registro actual %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1247
#, python-format
msgid "Share with all users"
msgstr "Compartir con todos los usuarios"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:189
#, python-format
msgid "Showing locally available modules"
msgstr "Mostrar los módulos locales disponibles"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:55
#, python-format
msgid "Size:"
msgstr "Tamaño:"

#. module: web
#: code:addons/web/controllers/main.py:1062
#, python-format
msgid "Something horrible happened"
msgstr "Algo terrible sucedió"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:107
#, python-format
msgid "Special:"
msgstr "Especial:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:14
#, python-format
msgid "Still loading..."
msgstr "Cargando aún..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:15
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Cargando aún...<br /> Por favor espere"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:83
#, python-format
msgid "Support"
msgstr "Soporte"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:701
#, python-format
msgid "Switch Off"
msgstr "Apagar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:703
#, python-format
msgid "Switch On"
msgstr "Encender"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:697
#, python-format
msgid "Switch to production environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:695
#, python-format
msgid "Switch to test environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:18
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Tome un minuto para ir por un café,<br />porque está cargando ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:218
#, python-format
msgid "Technical Translation"
msgstr "Traducción técnica"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:696
#, python-format
msgid "Test Environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:462
#, python-format
msgid "The domain is wrong."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1235
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr "Este campo esta vacio, !no hay nada que guardar!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:954
#, python-format
msgid "The following fields are invalid:"
msgstr "Los siguientes campos son inválidos:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view_editable.js:124
#, python-format
msgid ""
"The line has been modified, your changes will be discarded. Are you sure you "
"want to discard the changes ?"
msgstr ""
"Las líneas han sido modificadas, sus cambios serán descartados. ¿Se "
"encuentra seguro que desea descartar los cambios?"

#. module: web
#: code:addons/web/controllers/main.py:750
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "La nueva contraseña y su confirmación deben ser idénticas."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1126
#, python-format
msgid "The o2m record must be saved before an action can be used"
msgstr ""
"El registro O2M debe guardarse antes que una acción pueda ser utilizada"

#. module: web
#: code:addons/web/controllers/main.py:756
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"El password anterior ingresado es incorrecto, su password no fue cambiado."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:361
#, python-format
msgid "The record could not be found in the database."
msgstr "El registro no fue encontrado en la base de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:825
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Are you sure "
"you want to leave this page ?"
msgstr ""
"El registro ha sido modificado, sus cambios serán descartados. ¿Se encuentra "
"seguro que desea salir de la página?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1199
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "El archivo seleccionado supera el tamaño máximo de %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1550
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""
"El tipo de campo '%s' debe ser un campo many2many con una relación a "
"'ir.attachment' model."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1220
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Hubo un problema mientras se cargaba su archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:242
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr "El filtro es global y será removido para todos si usted continúa."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:389
#, python-format
msgid "This resource is empty"
msgstr "El recurso esta vacío"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1304
#, python-format
msgid ""
"This wizard will export all data that matches the current search criteria to a CSV file.\n"
"        You can export all data or only the fields that can be reimported after modification."
msgstr ""
"Este asistente exportará todos los datos que coincidan con el criterio de "
"búsqueda actual a un archivo CSV.\n"
"            Tu puedes exportar todos los datos o solo los campos que serán "
"reimportados después de la modificación."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:971
#, python-format
msgid ""
"Timezone Mismatch : The timezone of your browser doesn't match the selected "
"one. The time in Odoo is displayed according to your field timezone."
msgstr ""
"Error en Zona Horaria: La zona horaria de su navegador no coincide con la "
"seleccionada. La fecha en Odoo será mostrada de acuerdo a la zona horaria "
"configurada en su registro."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:210
#, python-format
msgid "Toggle Form Layout Outline"
msgstr "Cambiar esquema del formulario de diseño"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:516
#: code:addons/web/static/src/js/views/pivot_view.js:545
#, python-format
msgid "Total"
msgstr "Total"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/web_client.js:134
#, python-format
msgid "Traceback:"
msgstr "Rastrear:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/tree_view.js:18
#, python-format
msgid "Tree"
msgstr "Árbol"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:48
#, python-format
msgid "Trying to reconnect... "
msgstr "Tratando de reconectar..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:47
#, python-format
msgid "Type:"
msgstr "Tipo:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:691
#: code:addons/web/static/src/js/views/list_view.js:308
#, python-format
msgid "Unarchive"
msgstr "Desarchivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:79
#: code:addons/web/static/src/js/views/list_view.js:1471
#: code:addons/web/static/src/js/views/list_view.js:1477
#: code:addons/web/static/src/js/views/list_view.js:1489
#: code:addons/web/static/src/js/views/pivot_view.js:498
#, python-format
msgid "Undefined"
msgstr "Indefinido"

#. module: web
#: code:addons/web/controllers/main.py:877
#, python-format
msgid "Underscore prefixed methods cannot be remotely called"
msgstr "Métodos privados no pueden ser llamados remotamente"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:699
#, python-format
msgid "Unhandled widget"
msgstr "Wdiget no controlado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:911
#, python-format
msgid "Unknown"
msgstr "Desconocido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/data.js:1082
#, python-format
msgid "Unknown field %s in domain %s"
msgstr "Campo desconocido %s en el dominio %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1171
#, python-format
msgid "Unknown m2m command %s"
msgstr "Comando m2m desconocido %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/pyeval.js:961
#, python-format
msgid "Unknown nonliteral type "
msgstr "Tipo no literal desconocido "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/data.js:1074
#, python-format
msgid "Unknown operator %s in domain %s"
msgstr "Operador desconocido %s en el dominio %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:367
#, python-format
msgid "Unlimited"
msgstr "Ilimitado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/data.js:1120
#, python-format
msgid "Unsupported operator %s in domain %s"
msgstr "Operador no soportado %s en el dominio %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1505
#, python-format
msgid "Update translations"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1700
#, python-format
msgid "Upgrade now"
msgstr "Actualizar ahora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1518
#, python-format
msgid "Upgrade to future versions"
msgstr "Actualización a versiones futuras"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:966
#: code:addons/web/static/src/xml/base.xml:1027
#, python-format
msgid "Uploading ..."
msgstr "Subiendo..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1650
#: code:addons/web/static/src/js/widgets/sidebar.js:168
#, python-format
msgid "Uploading Error"
msgstr "Error de Carga"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:196
#, python-format
msgid "Uploading..."
msgstr "Subiendo..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1244
#, python-format
msgid "Use by default"
msgstr "Usar por defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:23
#, python-format
msgid "Validation Error"
msgstr "Error de Validación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:105
#, python-format
msgid "Version"
msgstr "Versión"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:51
#, python-format
msgid "Very High"
msgstr "Muy alto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:2033
#: code:addons/web/static/src/xml/base.xml:219
#, python-format
msgid "View"
msgstr "Vista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:214
#, python-format
msgid "View Fields"
msgstr "Ver Campos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:208
#, python-format
msgid "View Metadata"
msgstr "Ver Metadatos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:757
#, python-format
msgid "View type '%s' is not supported in X2Many."
msgstr "Tipo vista '%s' no es soportado en X2Many."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:19
#: code:addons/web/static/src/js/framework/crash_manager.js:20
#: code:addons/web/static/src/js/framework/crash_manager.js:119
#: code:addons/web/static/src/js/views/form_view.js:830
#: code:addons/web/static/src/js/views/list_view.js:760
#: code:addons/web/static/src/js/widgets/sidebar.js:126
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:674
#, python-format
msgid "Week"
msgstr "Semana"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1362
#, python-format
msgid "Widget type '%s' is not implemented"
msgstr "El tipo de widget '%s' no está implementado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:51
#, python-format
msgid "Widget:"
msgstr "Widget:"

#. module: web
#: code:addons/web/controllers/main.py:491
#, python-format
msgid "Wrong login/password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/progress_bar.js:79
#, python-format
msgid "Wrong value entered!"
msgstr "¡Valor entero no válido!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:236
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:677
#, python-format
msgid "Year"
msgstr "Año"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:329
#: code:addons/web/static/src/xml/base_common.xml:71
#, python-format
msgid "Yes"
msgstr "Si"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:53
#, python-format
msgid "You are back online"
msgstr "Usted está en línea nuevamente"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:49
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr "Usted ha creado un nuevo %s, ¿estás seguro que no existe?"

#. module: web
#: code:addons/web/controllers/main.py:748
#, python-format
msgid "You cannot leave any password empty."
msgstr "No puede dejar vacía ninguna contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1503
#, python-format
msgid "You have updated"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:17
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr ""
"Usted no lo creerá,<br />pero la aplicación actualmente está cargando ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:126
#, python-format
msgid "You must choose at least one record."
msgstr "Debe seleccionar al menos un registro."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:760
#, python-format
msgid "You must select at least one record."
msgstr "Debe seleccionar al menos un registro."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:69
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr "Su sesión Odoo ha expirado. Por favor, actualice su página web."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:133
#, python-format
msgid "a day ago"
msgstr "un día atrás"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:129
#, python-format
msgid "about a minute ago"
msgstr "hace un minuto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:135
#, python-format
msgid "about a month ago"
msgstr "hace un mes"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:137
#, python-format
msgid "about a year ago"
msgstr "hace un año"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:131
#, python-format
msgid "about an hour ago"
msgstr "Hace una hora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:160
#, python-format
msgid "contains"
msgstr "Contiene"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:161
#, python-format
msgid "doesn't contain"
msgstr "No contiene"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:177
#: code:addons/web/static/src/js/views/search_filters.js:211
#: code:addons/web/static/src/js/views/search_filters.js:240
#, python-format
msgid "greater than"
msgstr "Mayor que"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:179
#: code:addons/web/static/src/js/views/search_filters.js:213
#: code:addons/web/static/src/js/views/search_filters.js:242
#, python-format
msgid "greater than or equal to"
msgstr "Mayor o igual a"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:232
#: code:addons/web/static/src/js/views/search_filters.js:267
#, python-format
msgid "is"
msgstr "es"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:162
#: code:addons/web/static/src/js/views/search_filters.js:175
#: code:addons/web/static/src/js/views/search_filters.js:209
#: code:addons/web/static/src/js/views/search_filters.js:238
#, python-format
msgid "is equal to"
msgstr "Es igual a"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:286
#, python-format
msgid "is false"
msgstr "es falso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:268
#, python-format
msgid "is not"
msgstr "no es"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:163
#: code:addons/web/static/src/js/views/search_filters.js:176
#: code:addons/web/static/src/js/views/search_filters.js:210
#: code:addons/web/static/src/js/views/search_filters.js:239
#, python-format
msgid "is not equal to"
msgstr "No es igual a"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:165
#: code:addons/web/static/src/js/views/search_filters.js:182
#: code:addons/web/static/src/js/views/search_filters.js:216
#: code:addons/web/static/src/js/views/search_filters.js:245
#: code:addons/web/static/src/js/views/search_filters.js:270
#, python-format
msgid "is not set"
msgstr "no está configurado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:164
#: code:addons/web/static/src/js/views/search_filters.js:181
#: code:addons/web/static/src/js/views/search_filters.js:215
#: code:addons/web/static/src/js/views/search_filters.js:244
#: code:addons/web/static/src/js/views/search_filters.js:269
#, python-format
msgid "is set"
msgstr "está configurado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:285
#, python-format
msgid "is true"
msgstr "es verdadero"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:303
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:178
#: code:addons/web/static/src/js/views/search_filters.js:212
#: code:addons/web/static/src/js/views/search_filters.js:241
#, python-format
msgid "less than"
msgstr "menor que"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:128
#, python-format
msgid "less than a minute ago"
msgstr "hace menos de un minuto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:180
#: code:addons/web/static/src/js/views/search_filters.js:214
#: code:addons/web/static/src/js/views/search_filters.js:243
#, python-format
msgid "less than or equal to"
msgstr "Menor o igual a "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:229
#, python-format
msgid "not a valid integer"
msgstr "No es un entero válido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:244
#, python-format
msgid "not a valid number"
msgstr "No es un número válido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1254
#: code:addons/web/static/src/xml/website.tour.xml:14
#, python-format
msgid "or"
msgstr "o"

#~ msgid "&lt;!DOCTYPE html&gt;"
#~ msgstr "&lt;!DOCTYPE html&gt;"

#~ msgid ",k,M"
#~ msgstr ",k,M"

#~ msgid "Move your mouse"
#~ msgstr "Mueva su ratón"

#~ msgid "Move your mouse here to open the insert block"
#~ msgstr "Mueva su ratón aquí para abrir el bloque insertado"

#~ msgid "Quantity"
#~ msgstr "Cantidad"

#~ msgid " or "
#~ msgstr "o"

#~ msgid "%d requests (%d ms) %d queries (%d ms)"
#~ msgstr "%d peticiones (%d ms) %d consultas (%d ms)"

#~ msgid "(Enterprise Edition)"
#~ msgstr "(Edición Enterprise)"

#~ msgid "1 month"
#~ msgstr "1 mes"

#~ msgid "Add more users"
#~ msgstr "Añadir más usuarios"

#~ msgid "Clear Events"
#~ msgstr "Limpiar eventos"

#~ msgid "Confirm New Password"
#~ msgstr "Confirmar nueva contraseña"

#~ msgid "Connection lost"
#~ msgstr "Conexión pérdida"

#~ msgid "Connection restored"
#~ msgstr "Conexión restaurada"

#~ msgid "Disable Technical Features"
#~ msgstr "Inactiva características técnicas"

#~ msgid "Enable Technical Features"
#~ msgstr "Activa características técnicas"

#~ msgid "Export Formats :"
#~ msgstr "Formatos de exportación:"

#~ msgid "Export Type :"
#~ msgstr "Tipo de exportación:"

#~ msgid "Fields of %s"
#~ msgstr "Campos de %s"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Leave the Developer Tools"
#~ msgstr "Salir de Herramientas de desarrollo"

#~ msgid "Log in as an administrator to correct the issue."
#~ msgstr "Ingrese como administrador para corregir el problema."

#~ msgid "New Password"
#~ msgstr "Nueva Contraseña"

#~ msgid ""
#~ "No data available for this pivot table.  Try to add some records, or make "
#~ "sure\n"
#~ "        that there is at least one measure and no active filter in the "
#~ "search bar."
#~ msgstr ""
#~ "No existen datos disponibles para esta tabla dinámica. Trate de añadir "
#~ "algunos registros, o asegúrese que exista por lo menos una medida y no "
#~ "existan filtros activos en la barra de búsqueda."

#~ msgid "Old Password"
#~ msgstr "Antigua contraseña"

#~ msgid "Open View"
#~ msgstr "Vista abierta"

#~ msgid "Paste code here"
#~ msgstr "Pegue el código aquí"

#~ msgid "Path"
#~ msgstr "Ruta"

#~ msgid "Register"
#~ msgstr "Registro"

#~ msgid "Register your subscription"
#~ msgstr "Registro su suscripción"

#~ msgid "Renew your subscription"
#~ msgstr "Renueve su suscripción"

#~ msgid "Search..."
#~ msgstr "Búsqueda..."

#~ msgid "Select a view"
#~ msgstr "Selecciona una vista"

#~ msgid "Select records "
#~ msgstr "Seleccionar registros"

#~ msgid ""
#~ "Something went wrong while registering your database. You can try again "
#~ "or contact our support at"
#~ msgstr ""
#~ "Algo salió mal, mientras se procesaba su registro en la base de datos. "
#~ "Usted puede intentar de nuevo o póngase en contacto con nuestro personal "
#~ "en"

#~ msgid "Start tour"
#~ msgstr "Iniciar tutorial"

#~ msgid "Subscription Code:"
#~ msgstr "Código de Suscripción:"

#~ msgid "This database has expired."
#~ msgstr "La base de datos ha expirado."

#~ msgid "This database will expire in"
#~ msgstr "La base de datos expirará en"

#~ msgid "This demo database will expire in"
#~ msgstr "La base de datos de demostración expirará en"

#~ msgid ""
#~ "This wizard will export all data that matches the current search criteria "
#~ "to a CSV file.\n"
#~ "        You can export all data or only the fields that can be reimported "
#~ "after modification."
#~ msgstr ""
#~ "Este asistente exportará todos los datos que coincidan con la actual "
#~ "búsqueda a un archivo CSV -valores separados por coma-. Usted puede "
#~ "exportar todos los datos o solo los campos que serán nuevamente "
#~ "importados luego de alguna modificación."

#~ msgid "Toggle Timelines"
#~ msgstr "Cambia líneas de tiempo"

#~ msgid "Trying to reconnect..."
#~ msgstr "Intentando reconectar..."

#~ msgid "Upload your file"
#~ msgstr "Cargar su archivo"

#~ msgid "You have more users than your subscription allows."
#~ msgstr "Tiene más usuarios que los que su suscripción permite."

#~ msgid ""
#~ "You will be able to register your database once you have installed your "
#~ "first app."
#~ msgstr ""
#~ "Podrá registrar su base de datos una vez que haya instalado su primera "
#~ "aplicación."

#~ msgid "Your subscription code"
#~ msgstr "Su código de suscripción"

#~ msgid "buy a subscription"
#~ msgstr "comprar una suscripción"

#~ msgid "days"
#~ msgstr "días"
