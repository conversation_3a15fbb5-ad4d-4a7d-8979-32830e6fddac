# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 10:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid " [Me]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid " and "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#, python-format
msgid " or "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "&times;"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "(change)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-at\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Fiscal number\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-globe\" role=\"img\" aria-label=\"Website\" title=\"Website\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "<span>Copyright &amp;copy;</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "A name for your favorite is required."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"A popup window with your report was blocked. You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Denied"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#, python-format
msgid "Action"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add a condition"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt and Sign"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Advanced Search..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "All users"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Alt"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin. (Opening your browser "
"console might give you a hint on the error.)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "And more"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Any"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Apply"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attachment :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bar Chart"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Based On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Become Superuser"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "CLEAR"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Cancel"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Cannot render chart with mode : "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/change_password.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Checkbox"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Choose"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Clear Events"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/dialog.xml:0
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Compare To"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Confirm New Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection lost"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection restored"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Control"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Control panel toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Copied !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Copy Text"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Count"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Create"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create a %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Create a new record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and edit"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Create: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Created by :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation Date:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation User:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark purple"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Day"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Delete"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete this attachment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Description"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Discard a record modification"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Documentation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download xls"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit Action"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit ControlPanelView"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit View:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Edit a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Error"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Expand all"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "External link"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "FILTER"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/control_panel_model.js:0
#, python-format
msgid "Failed to evaluate search criterions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Fields View Get"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File Upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Filter on: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/filter_menu.js:0
#, python-format
msgid "Filters"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Flip axis"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fushia"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Global Business Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Got it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Green"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/groupby_menu.js:0
#, python-format
msgid "Group By"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Group by: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Hue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "ID:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid data"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid mode for chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/user_menu.js:0
#, python-format
msgid "Keyboard Shortcuts"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 30 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 365 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 5 Years"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 7 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Month"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Week"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Year"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Leave the Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Light blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Lightness %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Line Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading (%d)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Log out"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Mac"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Main actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Attachments"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Filters"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Measures"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Medium blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Metadata (%s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Missing Record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modified by :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Month"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "More"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "My Odoo.com account"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Name:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New design"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New template"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "No"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "No Update:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No attachment available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "No color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "No data to display"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No metadata available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "No records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "No results to show..."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Node [%s] is not a JSONified XML node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "None"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Ok"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Only Integer or Float Value should be valid."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Opacity %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools#{widget.debug_mode_help}"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the next record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the previous record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to kanban view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to list view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Orange"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Phone"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Pick a color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pie Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pivot settings"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Preferences"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Period"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Year"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#: code:addons/web/static/src/xml/report.xml:0
#, python-format
msgid "Print"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Purple"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q1"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q2"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q3"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q4"
msgstr ""

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Range"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Red"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid "Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Request timeout"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "SEE RESULT"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Salmon pink"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Saturation %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Save As..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save Current Search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Save a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Save default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Select a view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Set Default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Set Defaults"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Share with all users"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Shortcuts"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Something went wrong !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Stacked"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Summary"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Summary:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Switch to this company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Technical Translation"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "The domain you entered is not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or"
" followed a non-relational field?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "The following field is invalid:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "The following fields are invalid:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to"
" proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Month"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Week"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Year"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/date_picker.js:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Time Ranges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Toggle Timelines"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Traceback:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_renderer.js:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is at least one measure and"
" no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Trying to reconnect..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_renderer_mobile.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Undefined"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Unknown CORS error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:0
#, python-format
msgid "Unnamed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upload your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Use by default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "User"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "User Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Users"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Validation Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Metadata"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "Warning : You have to save first before attaching a file."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Week"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:0
#, python-format
msgid "Well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Windows/Linux"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Wrong value entered!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "XML ID:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Year"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Yellow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Yes"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Yesterday"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "You are back online"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "all records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "contains"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "doesn't contain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "is"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is between"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is false"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is not equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is true"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid number"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "or"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion
msgid "portal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr ""
