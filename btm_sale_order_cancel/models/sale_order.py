# -*- coding: utf-8 -*-
# Part of nurosolution Pvt Ltd.
from odoo.exceptions import UserError

from odoo import models, fields, api, _


class SaleOrder(models.Model):
    _inherit = "sale.order"

    cancel_reason = fields.Text(string='Cancel Reason', store=True, readonly=True)

    def action_sale_picking_cancel(self):
        """
        This method called to cancelled done sales orders
        :return:
        """
        self.check_order_back_date()
        picking_env = self.env['stock.picking']
        return_wiz = self.env['stock.return.picking']
        # stock_valuation_layer = self.env['stock.valuation.layer']
        previous_picking_ids = self.picking_ids.filtered(lambda x: x.state != 'cancel' and not x.sale_cancelled)
        for stock in previous_picking_ids:
            if stock.state != 'done':
                stock.action_cancel()
                stock.sale_cancelled = True
            else:
                move_ids = stock.move_lines.ids
                return_wiz_id = return_wiz.create({'picking_id': stock.id})
                return_wiz_id._onchange_picking_id()
                new_picking_id, pick_type_id = return_wiz_id._create_returns()
                return_picking = picking_env.browse(new_picking_id)
                for mv_line in return_picking.move_lines:
                    mv_line.update({'date_expected':self.date_order, 'date':self.date_order})
                return_picking.action_confirm()
                for line_move in return_picking.move_lines:
                    for pic in previous_picking_ids:
                        stock_move_line = pic.move_line_ids_without_package.filtered(lambda x: x.product_id.id == line_move.product_id.id)
                        if len(stock_move_line) > 1 and sum(stock_move_line.mapped('qty_done')) == line_move.product_uom_qty:
                            stock_move_line = pic.move_line_ids_without_package.filtered(
                                lambda x: x.product_id.id == line_move.product_id.id)
                        if len(stock_move_line) > 1 and sum(stock_move_line.mapped('qty_done')) != line_move.product_uom_qty:
                            stock_move_line = pic.move_line_ids_without_package.filtered(
                                lambda x: x.product_id.id == line_move.product_id.id and x.qty_done == line_move.product_uom_qty)
                        for move_line in stock_move_line:
                            self.env['stock.move.line'].create({
                                'product_id': move_line.product_id.id,
                                'location_id': move_line.location_dest_id.id,
                                'location_dest_id': move_line.location_id.id,
                                'lot_id': move_line.lot_id.id or False,
                                'lot_name': move_line.lot_id.name or False,
                                'qty_done': move_line.qty_done,
                                'product_uom_id': move_line.product_uom_id.id,
                                'picking_id': return_picking.id,
                                'move_id': line_move.id,
                                'origin': self.name,
                            })
                return_picking.button_validate()
                return_picking.sale_cancelled = True
                move_ids.extend(return_picking.move_lines.ids)
            stock.write({'is_locked': True, 'sale_cancelled': True})
        for rec in self.invoice_ids.filtered(lambda x: x.state != 'cancel'):
            if self.payment_by == 'cash':
                pay_term_line_ids = rec.sudo().line_ids.filtered(
                    lambda line: line.account_id.user_type_id.type in ('receivable', 'payable'))
                partials = pay_term_line_ids.sudo().mapped('matched_debit_ids') + pay_term_line_ids.sudo().mapped(
                    'matched_credit_ids')
                for partial in partials:
                    counterpart_lines = partial.debit_move_id + partial.credit_move_id
                    counterpart_line = counterpart_lines.filtered(lambda line: line not in rec.line_ids)
                    if counterpart_line.payment_id:
                        payment_id = counterpart_line.payment_id
                        payment_id.action_draft()
                        payment_id.cancel()
            else:
                if rec.state == 'paid':
                    raise UserError(_('Please Unreconcile the Paid Invoices to cancel the Order'))
            rec.button_draft()
            rec.button_cancel()
        ref = 'cancelled'
        if self.client_order_ref:
            ref = self.client_order_ref + ' ' + 'cancelled'
        self.write({'state': 'cancel', 'client_order_ref': ref})
        return True

    def action_draft(self):

        if any(picking.state == 'done' for picking in self.picking_ids):
            raise UserError(_('You can not reset this cancelled order, please create new order'))
        return super(SaleOrder, self).action_draft()

    def fix_cancel_order(self):
        stock_valuation_layer = self.env['stock.valuation.layer']
        for order in self.search([('state', '=', 'cancel'), ('date_order', '>', '2020-06-30')]):
            for picking in order.picking_ids:
                move_ids = []
                for mv_line in picking.move_lines:
                    mv_line.write({'date_expected':order.date_order, 'date': order.date_order})
                move_ids.extend(picking.move_lines.ids)
                svl_ids = stock_valuation_layer.search([('stock_move_id', 'in', move_ids)])
                for svl_id in svl_ids:
                    svl_id.account_move_id.button_draft()
                    svl_id.account_move_id.button_cancel()


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    sale_cancelled = fields.Boolean('Sale Cancelled')

# comment because its show issue and all_uom_ids field is not available
# class StockMove(models.Model):
#     _inherit = 'stock.move'
#
#     @api.onchange('product_id')
#     def onchange_product_id(self):
#         """
#         override for show all product uom
#         :return:
#         """
#         product = self.product_id.with_context(lang=self.partner_id.lang or self.env.user.lang)
#         self.name = product.partner_ref
#         self.product_uom = product.uom_id.id
#         if product.all_uom_ids:
#             return {'domain': {'product_uom': [('id', 'in', product.all_uom_ids.ids)]}}

