# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
from odoo import api, fields, models


class NuroWardOption(models.Model):
    _inherit = 'nuro.ward.option'

    # def write(self, vals):
    #     self.clear_caches()
    #     return super(NuroWardOption, self).write(vals)

    branch_id = fields.Many2one('res.branch', string='Branch')


class NuroInpatientWards(models.Model):
    _inherit = "nuro.inpatient.ward"

    @api.onchange('user_id')
    def _get_default_branch(self):
        branch_lst = []
        if self.env.user.branch_ids:
            if len(self.env.user.branch_ids) == 1:
                self.branch_id = self.env.user.branch_ids[0]
            for branch in self.env.user.branch_ids:
                branch_lst.append(branch.id)
        else:
            for b in self.env['res.branch'].search([]):
                branch_lst.append(b.id)
        return {'domain': {'branch_id': [('id', 'in', branch_lst)]}}

    # def write(self, vals):
    #     self.clear_caches()
    #     return super(NuroInpatientWards, self).write(vals)

    branch_id = fields.Many2one('res.branch', string='Branch')
