<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="res_partner_corporates_view_referral_bt_inherit" model="ir.ui.view">
            <field name="name">Referral</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="hms_referral.res_partner_referral_view"/>
            <field name="arch" type="xml">

                <xpath expr="//page[@name='panel_information_referral']" position="inside">
                    <separator string="Lab"/>
                    <field name="lab_referral_configuration" context="{'default_panel': 'lab'}"
                           domain="[('panel', '=', 'lab')]">
                        <tree editable="bottom">
                            <field name="panel" readonly="1" invisible="1" force_save="1"/>
                            <field name="discount"/>
                            <field name="master_ids" options="{'no_create': True, 'no_open': True}"
                                   widget="many2many_tags" domain="[('panel', '=', 'lab')]"/>
                        </tree>
                    </field>
                </xpath>

            </field>
        </record>

    </data>
</odoo>