# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo.exceptions import UserError

from odoo import api, fields, models, _


class InpatientDischarge(models.TransientModel):
    _inherit = 'inpatient.discharge'

    def create_discount_entry_line(self, amount):
        """Discount Entry Line"""
        entry_line = []
        debit_line = {
            "name": "IPD Discount Entry",
            "account_id": self.env.company.ipd_discount_account_id.id,
            'partner_id': self.inpatient_id.patient_id.partner_id.id,
            "debit": amount,
            "credit": 0.0,
        }
        entry_line.append((0,0, debit_line))
        credit_line = {
            "name": "IPD Discount Entry",
            "account_id": self.inpatient_id.patient_id.partner_id.property_account_receivable_id.id,
            'partner_id': self.inpatient_id.patient_id.partner_id.id,
            "debit": 0.0,
            "credit": amount,
        }
        entry_line.append((0, 0, credit_line))
        return entry_line

    def create_discount_entry_vals(self, line_ids):
        """Create Discount Entry Vals"""
        vals = {
            'date': fields.Date.context_today(self),
            'ref': self.inpatient_id.name,
            'type': 'entry',
            'line_ids': line_ids
        }
        return vals

    def create_inpatient_credit_discount_entry(self, amount):
        """Create Inpatient Credit Discount Entry"""
        if self.credit_discharge and self.discount_amount > 0.0:
            if self.total_amount < self.discount_amount:
                raise UserError(_("Discount Amount can not be more than total amount.!!!"))
            lines = self.create_discount_entry_line(amount=amount)
            entry_vals = self.create_discount_entry_vals(line_ids=lines)
            move_id = self.env['account.move'].create(entry_vals)
            move_id.action_post()
            return move_id

    def credit_discharge_patient(self):
        """
        Credit Discharge for the Patient
        :return:
        """
        if self.bill_to_type not in ('employee', 'company', 'regular', 'hospital', 'hospital_employee'):
            if self.credit_discharge and self.discount_amount > 0.0:
                move_id = self.create_inpatient_credit_discount_entry(amount=self.discount_amount)
                credit_ids = move_id.line_ids.filtered(lambda x: x.account_internal_type == 'receivable')
                debit_ids = self.inpatient_id.inpatient_invoice_line.filtered(lambda x: x.amount_residual_signed > 0.0).mapped('line_ids').filtered(lambda x: x.account_internal_type == 'receivable')
                moves = credit_ids + debit_ids
                moves.reconcile()
        return super(InpatientDischarge, self).credit_discharge_patient()

