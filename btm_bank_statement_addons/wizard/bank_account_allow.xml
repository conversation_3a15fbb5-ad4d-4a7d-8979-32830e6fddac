<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_users_form_allowed_account" model="ir.ui.view">
        <field name="name">res.users.form.allowed.account</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group/field[@name='action_id']" position="after">
                <field name="allowed_bank_account_ids" widget="many2many_tags"
                       options="{'no_create': True, 'no_open': True}"/>
            </xpath>
        </field>
    </record>
    <record model="ir.ui.view" id="bank_select_wiz_inherit">
        <field name="name">bank.select.wiz.inherit</field>
        <field name="model">bank.select.wiz</field>
        <field name="inherit_id" ref="btm_bank_statement_report.bank_select_wiz"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='account_id']" position="after">
                <field name="account_ids" readonly="1" invisible="1"/>
            </xpath>
        </field>
    </record>
</odoo>