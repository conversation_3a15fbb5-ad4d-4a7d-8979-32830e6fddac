# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
from odoo.exceptions import UserError

from odoo import models, _


class LoanPaymentWizard(models.TransientModel):
    _inherit = 'loan.payment.wizard'

    def create_payment(self):
        """
        Create Loan Payment
        :return:
        """
        if self.paid_amount > 0.0:
            partner_id = self.loan_id.employee_id.address_home_id or self.loan_id.employee_id.user_id.partner_id
            # payment_account = False
            # payment_method_id = self.payment_method_id.inbound_payment_method_ids.id or \
            #                     self.payment_method_id.outbound_payment_method_ids.id
            # search_journal = self.env['account.journal'].search(
            #     [('type', '=', 'cash'), ('cashier_journal', '=', True)], limit=1)
            # cashier_acc = self.env.user.has_group(
            #     'nuro_cashier_closing.group_cashier_manager') and not self.env.user.has_group(
            #     'account.group_account_manager')
            # cashier_acc1 = self.env.user.has_group(
            #     'nuro_cashier_closing.group_cashier_manager') and self.env.user.has_group(
            #     'account.group_account_manager') and self.payment_method_id.id == search_journal.id
            # if cashier_acc or cashier_acc1:
            #     payment_account = self.payment_method_id.default_credit_account_id.id
            #     self.payment_method_id.sudo().write({'default_credit_account_id': self.env.user.account_id.id})
            # else:
            payment_method_id = self.payment_method_id.inbound_payment_method_ids.id or \
                                self.payment_method_id.outbound_payment_method_ids.id
            payments = self.loan_id.create_payment(
                partner_id=partner_id, amount=self.paid_amount, journal_id=self.payment_method_id,
                payment_method_id=payment_method_id, payment_type='inbound'
            )
            partner_account = partner_id.property_account_receivable_id.id
            partner_id.property_account_receivable_id = self.loan_id.emp_account_id.id
            payments.post()
            partner_id.property_account_receivable_id = partner_account
            # if cashier_acc or cashier_acc1:
            #     self.payment_method_id.sudo().write({'default_credit_account_id': payment_account})
        else:
            raise UserError(_('Please Enter the Paid Amount.!!!'))
