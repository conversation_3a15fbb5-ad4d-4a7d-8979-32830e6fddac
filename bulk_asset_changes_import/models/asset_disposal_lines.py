# -*- coding: utf-8 -*-

# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import api, fields, models


class AssetDisposeLines(models.Model):
    _name = "asset.dispose.lines"

    dispose_asset_id = fields.Many2one("asset.changes.update", string="Disposed Asset")
    asset_id = fields.Many2one("account.asset.asset", string="Asset ID")
    asset_name = fields.Char(string="Asset Name")
    asset_cat = fields.Many2one("account.asset.category", string="Category")
    asset_no = fields.Char(string="Asset Number")
    asset_model = fields.Many2one("asset.model", string="Asset Model")
    asset_sr_no = fields.Char(string="Serial Number")
    asset_tag_no = fields.Char(string="Tag Number")
    asset_partner_id = fields.Many2one("hr.employee", string="Assigned To")
    asset_loc = fields.Many2one("asset.location", string="Location")
    asset_status = fields.Char(string="Asset Status")
    depr_type_change = fields.Char(string="Change Type")
    asset_new_life = fields.Integer(string="Changed Life")
    asset_retro_date = fields.Date(string="Retrospective Date")
    state = fields.Selection([('draft', 'Not imported'),
                              ('disposed', 'Disposed')], default='draft',)
    depr_start_date = fields.Date("Accounting Date")
    total_posted_life = fields.Integer("Total Posted Life")
    total_life = fields.Integer("Total Life")
    new_total_life = fields.Integer("New Total Life")
    month_in_period = fields.Integer("Month In Period")
    asset_amount = fields.Float("Asset Value")