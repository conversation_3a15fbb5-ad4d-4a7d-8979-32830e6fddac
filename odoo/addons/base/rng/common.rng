<?xml version="1.0" encoding="UTF-8"?>
<rng:grammar xmlns:rng="http://relaxng.org/ns/structure/1.0"
             xmlns:a="http://relaxng.org/ns/annotation/1.0"
             datatypeLibrary="http://www.w3.org/2001/XMLSchema-datatypes">
    <!-- Handling of element overloading when inheriting from a base
         template
    -->

    <rng:define name="overload">
        <rng:optional>
            <!--
                Alter matched element with content
            -->
            <rng:choice>
                <rng:attribute name="position">
                    <rng:choice>
                        <!-- Insert content before first child -->
                        <rng:value>before</rng:value>
                        <!-- Insert content after last child -->
                        <rng:value>after</rng:value>
                        <!-- Replace all children with content -->
                        <rng:value>inside</rng:value>
                        <!-- Replace matched element itself with content -->
                        <rng:value>replace</rng:value>
                    </rng:choice>
                </rng:attribute>
                <rng:group>
                    <rng:attribute name="position">
                        <!-- Edit element attributes -->
                        <rng:value>attributes</rng:value>
                    </rng:attribute>
                    <rng:oneOrMore>
                        <rng:element name="attribute">
                            <rng:attribute name="name"><rng:text/></rng:attribute>
                            <rng:text />
                        </rng:element>
                    </rng:oneOrMore>
                </rng:group>
            </rng:choice>
        </rng:optional>
    </rng:define>

    <rng:define name="modifiable">
        <rng:optional>
            <!-- @modifiers contains a JSON map unifying the various
                 modifier attributes: @readonly, @required, @invisible.
                 Each attribute is a key, mapped to a JSON list representing
                 a condition expressed as an OpenERP `domain` filter
                 Only some of the modifier keys make sense on some
                 elements, for example <filter> and <group> only support
                 `invisible`. -->
            <rng:attribute name="modifiers"/>
        </rng:optional>
    </rng:define>

    <rng:define name="access_rights">
        <rng:optional>
            <rng:attribute name="groups"/>
        </rng:optional>
    </rng:define>

    <rng:define name="container">
        <rng:optional><rng:attribute name="col"/></rng:optional>
        <rng:zeroOrMore>
            <rng:choice>
                <rng:ref name="field"/>
                <rng:ref name="group"/>
                <rng:ref name="button"/>
                <rng:ref name="label" />
                <rng:ref name="separator"/>
                <rng:ref name="image"/>
                <rng:ref name="filter"/>
                <rng:ref name="html"/>
                <rng:element name="newline"><rng:empty/></rng:element>
            </rng:choice>
        </rng:zeroOrMore>
    </rng:define>

    <rng:define name="image">
        <rng:element name="image">
            <rng:attribute name="name"/>
        </rng:element>
    </rng:define>

    <rng:define name="html">
        <rng:element name="html">
            <rng:zeroOrMore>
                    <rng:text/>
                    <rng:ref name="any"/>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="label">
        <rng:element name="label">
            <rng:ref name="overload"/>
            <rng:ref name="access_rights"/>
            <rng:ref name="modifiable"/>
            <rng:optional><rng:attribute name="invisible"/></rng:optional>
            <rng:optional><rng:attribute name="align"/></rng:optional>
            <rng:optional><rng:attribute name="nolabel"/></rng:optional>
            <rng:optional><rng:attribute name="colspan"/></rng:optional>
            <rng:optional><rng:attribute name="string"/></rng:optional>
            <rng:optional><rng:attribute name="angle"/></rng:optional>
            <rng:optional><rng:attribute name="fill"/></rng:optional>
            <rng:optional><rng:attribute name="help"/></rng:optional>
            <rng:optional><rng:attribute name="width"/></rng:optional>
            <rng:optional><rng:attribute name="wrap"/></rng:optional>
            <rng:optional><rng:attribute name="name"/></rng:optional>
            <rng:optional>
                <!-- @for: allows to explicitely link a label to a field -->
                <rng:attribute name="for"/>
            </rng:optional>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:text/>
                    <rng:ref name="field"/>
                    <rng:ref name="group"/>
                    <rng:ref name="button"/>
                    <rng:ref name="label" />
                    <rng:ref name="separator"/>
                    <rng:ref name="image"/>
                    <rng:ref name="filter"/>
                    <rng:ref name="html"/>
                    <rng:element name="newline"><rng:empty/></rng:element>
                </rng:choice>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="any">
        <rng:element>
            <rng:anyName/>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:attribute>
                        <rng:anyName/>
                    </rng:attribute>
                    <rng:text/>
                    <rng:ref name="any"/>
                </rng:choice>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="separator">
        <rng:element name="separator">
            <rng:ref name="overload"/>
            <rng:ref name="access_rights"/>
            <rng:ref name="modifiable"/>
            <rng:optional><rng:attribute name="invisible"/></rng:optional>
            <rng:optional><rng:attribute name="name"/></rng:optional>
            <rng:optional><rng:attribute name="colspan"/></rng:optional>
            <rng:optional><rng:attribute name="rowspan"/></rng:optional>
            <rng:optional><rng:attribute name="string"/></rng:optional>
            <rng:optional><rng:attribute name="col"/></rng:optional>
            <rng:optional><rng:attribute name="select"/></rng:optional>
            <rng:optional><rng:attribute name="orientation"/></rng:optional>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:ref name="separator"/>
                    <rng:ref name="button"/>
                    <rng:ref name="field"/>
                    <rng:ref name="label" />
                    <rng:ref name="group" />
                    <rng:ref name="filter"/>
                    <rng:ref name="html"/>
                    <rng:element name="newline"><rng:empty/></rng:element>
                </rng:choice>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="xpath">
        <rng:element name="xpath">
            <rng:optional><rng:attribute name="expr"/></rng:optional>
            <rng:ref name="overload"/>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:ref name="any"/>
                    <rng:ref name="button"/>
                    <rng:ref name="html"/>
                </rng:choice>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="data">
        <rng:element name="data">
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:ref name="field"/>
                    <rng:ref name="label"/>
                    <rng:ref name="separator"/>
                    <rng:ref name="xpath"/>
                    <rng:ref name="button"/>
                    <rng:ref name="group"/>
                    <rng:ref name="filter"/>
                    <rng:ref name="html"/>
                    <rng:element name="newline"><rng:empty/></rng:element>
                </rng:choice>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="field">
        <rng:element name="field">
            <rng:attribute name="name" />
            <rng:ref name="overload"/>
            <rng:ref name="access_rights"/>
            <rng:ref name="modifiable"/>
            <rng:optional><rng:attribute name="domain_filter"/></rng:optional>
            <rng:optional><rng:attribute name="attrs"/></rng:optional>
            <rng:optional><rng:attribute name="class"/></rng:optional>
            <rng:optional><rng:attribute name="string"/></rng:optional>
            <rng:optional><rng:attribute name="completion"/></rng:optional>
            <rng:optional><rng:attribute name="width"/></rng:optional>
            <rng:optional><rng:attribute name="type"/></rng:optional>
            <rng:optional><rng:attribute name="ref"/></rng:optional>
            <rng:optional><rng:attribute name="eval"/></rng:optional>
            <rng:optional><rng:attribute name="search"/></rng:optional>
            <rng:optional><rng:attribute name="model"/></rng:optional>
            <rng:optional><rng:attribute name="use"/></rng:optional>
            <rng:optional><rng:attribute name="on_change"/></rng:optional>
            <rng:optional><rng:attribute name="domain"/></rng:optional>
            <rng:optional><rng:attribute name="filter_domain"/></rng:optional>
            <rng:optional><rng:attribute name="invisible"/></rng:optional>
            <rng:optional><rng:attribute name="password"/></rng:optional>
            <rng:optional><rng:attribute name="comparator"/></rng:optional>
            <rng:optional><rng:attribute name="sum"/></rng:optional>
            <rng:optional><rng:attribute name="bold"/></rng:optional>
            <rng:optional><rng:attribute name="avg"/></rng:optional>
            <rng:optional><rng:attribute name="select"/></rng:optional>
            <rng:optional><rng:attribute name="group"/></rng:optional>
            <rng:optional><rng:attribute name="color"/></rng:optional>
            <rng:optional><rng:attribute name="groupby"/></rng:optional>
            <rng:optional><rng:attribute name="disable_counters"/></rng:optional>
            <rng:optional><rng:attribute name="operator"/></rng:optional>
            <rng:optional><rng:attribute name="colspan"/></rng:optional>
            <rng:optional><rng:attribute name="nolabel"/></rng:optional>
            <rng:optional><rng:attribute name="required"/></rng:optional>
            <rng:optional><rng:attribute name="readonly"/></rng:optional>
            <rng:optional><rng:attribute name="view_mode"/></rng:optional>
            <rng:optional><rng:attribute name="widget"/></rng:optional>
            <rng:optional><rng:attribute name="context"/></rng:optional>
            <rng:optional><rng:attribute name="states"/></rng:optional>
            <rng:optional><rng:attribute name="digits"/></rng:optional>
            <rng:optional><rng:attribute name="icon"/></rng:optional>
            <rng:optional><rng:attribute name="mode"/></rng:optional>
            <rng:optional><rng:attribute name="size"/></rng:optional>
            <rng:optional><rng:attribute name="filename"/></rng:optional>
            <rng:optional><rng:attribute name="height"/></rng:optional>
            <rng:optional><rng:attribute name="rowspan"/></rng:optional>
            <rng:optional><rng:attribute name="align"/></rng:optional>
            <rng:optional><rng:attribute name="selection"/></rng:optional>
            <rng:optional><rng:attribute name="default_focus"/></rng:optional>
            <rng:optional><rng:attribute name="filters"/></rng:optional>
            <rng:optional><rng:attribute name="statusbar_visible"/></rng:optional>
            <rng:optional><rng:attribute name="can_create" /></rng:optional>
            <rng:optional><rng:attribute name="can_write" /></rng:optional>
            <rng:optional><rng:attribute name="interval" /></rng:optional>
            <rng:optional><rng:attribute name="avatar_field" /></rng:optional>
            <rng:optional><rng:attribute name="write_model" /></rng:optional>
            <rng:optional><rng:attribute name="write_field" /></rng:optional>
            <rng:optional><rng:attribute name="text" /></rng:optional>
            <rng:optional><rng:attribute name="optional" /></rng:optional>
            <rng:optional><rng:attribute name="kanban_view_ref" /></rng:optional>
            <rng:optional>
                <rng:attribute name="force_save">
                    <rng:choice>
                      <rng:value>1</rng:value>
                      <rng:value>0</rng:value>
                    </rng:choice>
                </rng:attribute>
            </rng:optional>
            <!-- Widget *static* options defined as an arbitrary JSON dict, with
                 widget-dependent parameters. To be ignored if widget/client does
                 not support them. -->
            <rng:optional><rng:attribute name="options"/></rng:optional>
            <rng:optional><rng:attribute name="placeholder"/></rng:optional>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:ref name="data"/>
                    <rng:ref name="field"/>
                    <rng:ref name="label"/>
                    <rng:ref name="separator"/>
                    <rng:ref name="xpath"/>
                    <rng:ref name="button"/>
                    <rng:ref name="group"/>
                    <rng:ref name="filter"/>
                    <rng:ref name="html"/>
                    <rng:element name="newline"><rng:empty/></rng:element>
                </rng:choice>
            </rng:zeroOrMore>
        </rng:element>
    </rng:define>

    <rng:define name="group">
        <rng:element name="group">
            <rng:ref name="overload"/>
            <rng:ref name="access_rights"/>
            <rng:ref name="modifiable"/>
            <rng:optional><rng:attribute name="attrs"/></rng:optional>
            <rng:optional><rng:attribute name="colspan"/></rng:optional>
            <rng:optional><rng:attribute name="rowspan"/></rng:optional>
            <rng:optional><rng:attribute name="expand"/></rng:optional>
            <rng:optional><rng:attribute name="states"/></rng:optional>
            <rng:optional><rng:attribute name="string"/></rng:optional>
            <rng:optional><rng:attribute name="fill"/></rng:optional>
            <rng:optional><rng:attribute name="height"/></rng:optional>
            <rng:optional><rng:attribute name="width"/></rng:optional>
            <rng:optional><rng:attribute name="name"/></rng:optional>
            <rng:optional><rng:attribute name="color" /></rng:optional>
            <rng:optional><rng:attribute name="invisible"/></rng:optional>
            <rng:zeroOrMore>
                <rng:ref name="field"/>
            </rng:zeroOrMore>
            <rng:ref name="container"/>
        </rng:element>
    </rng:define>

    <rng:define name="button">
        <rng:element name="button">
            <rng:ref name="overload"/>
            <rng:ref name="access_rights"/>
            <rng:ref name="modifiable"/>
            <rng:optional><rng:attribute name="attrs"/></rng:optional>
            <rng:optional><rng:attribute name="invisible"/></rng:optional>
            <rng:optional><rng:attribute name="disabled"/></rng:optional>
            <rng:optional><rng:attribute name="name" /></rng:optional>
            <rng:optional><rng:attribute name="icon" /></rng:optional>
            <rng:optional><rng:attribute name="string" /></rng:optional>
            <rng:optional><rng:attribute name="states" /></rng:optional>
            <rng:optional><rng:attribute name="type" /></rng:optional>
            <rng:optional><rng:attribute name="special" /></rng:optional>
            <rng:optional><rng:attribute name="align" /></rng:optional>
            <rng:optional><rng:attribute name="colspan"/></rng:optional>
            <rng:optional><rng:attribute name="target"/></rng:optional>
            <rng:optional><rng:attribute name="readonly"/></rng:optional>
            <rng:optional><rng:attribute name="context"/></rng:optional>
            <rng:optional><rng:attribute name="confirm"/></rng:optional>
            <rng:optional><rng:attribute name="help"/></rng:optional>
            <rng:optional><rng:attribute name="class"/></rng:optional>
            <rng:optional><rng:attribute name="default_focus"/></rng:optional>
            <rng:optional><rng:attribute name="tabindex"/></rng:optional>
            <rng:optional><rng:attribute name="title"/></rng:optional>
            <rng:optional><rng:attribute name="aria-label"/></rng:optional>
            <rng:optional><rng:attribute name="aria-pressed"/></rng:optional>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:ref name="field" />
                    <rng:ref name="xpath" />
                    <rng:ref name="separator"/>
                    <rng:ref name="button"/>
                    <rng:ref name="group"/>
                    <rng:ref name="filter"/>
                    <rng:ref name="html"/>
                    <rng:element name="newline"><rng:empty/></rng:element>
                </rng:choice>
            </rng:zeroOrMore>

        </rng:element>
    </rng:define>

    <rng:define name="filter">
        <rng:element name="filter">
            <rng:ref name="overload"/>
            <rng:ref name="access_rights"/>
            <rng:ref name="modifiable"/>
            <rng:attribute name="name"/>
            <rng:optional><rng:attribute name="attrs"/></rng:optional>
            <rng:optional><rng:attribute name="icon"/></rng:optional>
            <rng:optional><rng:attribute name="invisible"/></rng:optional>
            <rng:optional><rng:attribute name="separator" /></rng:optional>
            <rng:optional><rng:attribute name="string" /></rng:optional>
            <rng:optional><rng:attribute name="type" /></rng:optional>
            <rng:optional><rng:attribute name="align" /></rng:optional>
            <rng:optional><rng:attribute name="colspan"/></rng:optional>
            <rng:optional><rng:attribute name="readonly"/></rng:optional>
            <rng:optional><rng:attribute name="context"/></rng:optional>
            <rng:optional><rng:attribute name="help"/></rng:optional>
            <rng:optional><rng:attribute name="domain"/></rng:optional>
            <rng:optional><rng:attribute name="date"/></rng:optional>
            <rng:optional><rng:attribute name="default_period"/></rng:optional>
            <rng:zeroOrMore>
                <rng:choice>
                    <rng:ref name="field" />
                    <rng:ref name="xpath" />
                    <rng:ref name="separator"/>
                    <rng:ref name="button"/>
                    <rng:ref name="filter"/>
                    <rng:ref name="html"/>
                    <rng:element name="newline"><rng:empty/></rng:element>
                </rng:choice>
            </rng:zeroOrMore>

        </rng:element>
    </rng:define>

    <rng:define name="create">
        <rng:element name="create">
            <rng:ref name="overload"/>
            <rng:attribute name="string"/>
            <rng:attribute name="context"/>
        </rng:element>
    </rng:define>

    <rng:define name="control">
        <rng:element name="control">
            <rng:ref name="overload"/>
            <rng:oneOrMore>
                <rng:choice>
                    <rng:ref name="create"/>
                </rng:choice>
            </rng:oneOrMore>
        </rng:element>
    </rng:define>

</rng:grammar>
