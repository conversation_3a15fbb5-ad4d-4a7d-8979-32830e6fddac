<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="action_product_sale" model="ir.actions.act_window">
        <field name="name">Action Product Sale</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.sale.wiz</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
    <record id="sale_predict_qty_action" model="ir.actions.server">
        <field name="name">Sales Prediction</field>
        <field name="type">ir.actions.server</field>
        <field name="model_id" ref="product_sale_prediction.model_predict_sale_qty"/>
        <field name="state">code</field>
        <field name="code">action = model.predict()</field>
    </record>

</odoo>