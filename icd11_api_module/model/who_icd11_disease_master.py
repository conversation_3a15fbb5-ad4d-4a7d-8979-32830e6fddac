from odoo import fields, models, api


class WHOICD11DiseaseMaster(models.Model):
    _name = "who.icd11.disease.master"
    _description = "Who ICD11 Disease Master"

    code = fields.Char("Codes")
    name = fields.Char("ICD Disease")

    def name_get(self):
        """Name Get Method for ICD11 Disease Master"""
        result = []
        for record in self:
            name_disease = str(record.name)
            if record.code:
                name_disease = '['+record.code + ']' + name_disease
            result.append((record.id, name_disease))
        return result


class IcdExtensionCode(models.Model):
    _name = "icd.extension.code"
    _description = "ICD Extension Code"

    chapter_code = fields.Char("Chapter Code")
    chapter_name = fields.Char("Chapter Name")
    block_name = fields.Char("Block Name")
    sub_block_name = fields.Char("Sub Block Name")
    category_id = fields.Many2one("nuro.disease.category", string="Category Name")
    name = fields.Char("Extension Title")
    extension_code = fields.Char("Extension Code")
    sub_block_id = fields.Many2one('sub.block.master', string="Sub Block ID")



class NuroDiseaseMaster(models.Model):
    _inherit = "nuro.disease.master"

    alternat_name = fields.Char('Alternate Name', index=True)
    extension_needed = fields.Boolean('Extension Code Required', index=True)
    icd_extension_ids = fields.Many2many("icd.extension.code", string="Extension Code", index=True)
    sub_block_ids = fields.Many2many('sub.block.master', string="Sub Block Name", index=True)

    @api.onchange('sub_block_ids')
    def _onchange_sub_block_ids(self):
        related_extension_codes = self.env['icd.extension.code'].search([
            ('sub_block_id', 'in', self.sub_block_ids.ids)
        ])
        self.icd_extension_ids = related_extension_codes


    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        if not args:
            args = []
        domain = []
        if name:
            domain = ['|', '|', ('name', operator, name), ('alternat_name', operator, name), ('code', operator, name)]
        records = self.search(domain + args, limit=limit)
        return records.name_get()

    def name_get(self):
        """Name Get Method for ICD11 Disease Master"""
        result = []
        for record in self:
            name_disease = str(record.name)
            if record.code:
                name_disease = '[' + record.code + ']' + name_disease
            result.append((record.id, name_disease))
        return result
