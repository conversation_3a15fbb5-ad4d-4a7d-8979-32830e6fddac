# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * database_cleanup
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-03-03 10:08+0000\n"
"PO-Revision-Date: 2019-08-30 14:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/oca/"
"teams/23907/pt_BR/)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 3.8\n"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__purge_line_ids
msgid "Columns to purge"
msgstr "Colunas para excluir"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.cleanup_create_indexes_line_action
#: model_terms:ir.ui.view,arch_db:database_cleanup.cleanup_create_indexes_wizard_view_form
msgid "Create"
msgstr "Criar"

#. module: database_cleanup
#: model_terms:ir.ui.view,arch_db:database_cleanup.cleanup_create_indexes_wizard_view_form
msgid "Create all"
msgstr "Criar todos"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_create_indexes_wizard
msgid "Create indexes"
msgstr "Criar índices"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.cleanup_create_indexes_wizard_action
#: model:ir.ui.menu,name:database_cleanup.menu_create_indexes
msgid "Create missing indexes"
msgstr "Criar índices faltantes"

#. module: database_cleanup
#: model_terms:ir.ui.view,arch_db:database_cleanup.cleanup_create_indexes_line_view_tree
msgid "Create this index"
msgstr "Criar este índice"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__purged
msgid "Created"
msgstr "Criado"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__create_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__create_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__create_date
msgid "Created on"
msgstr "Criado em"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__data_id
msgid "Data entry"
msgstr "Entrada de dados"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__purge_line_ids
msgid "Data to purge"
msgstr "Dados para excluir"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_database_cleanup
msgid "Database cleanup"
msgstr "Limpeza do Banco de Dados"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__display_name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__display_name
msgid "Display Name"
msgstr "Nome para Mostrar"

#. module: database_cleanup
#: selection:cleanup.purge.line.property,reason:0
msgid "Duplicated property"
msgstr "Propriedade duplicada"

#. module: database_cleanup
#: selection:cleanup.purge.line.property,reason:0
msgid "Empty default property"
msgstr "Propriedade padrão vazia"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__field_id
msgid "Field"
msgstr "Campo"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__id
msgid "ID"
msgstr "Identificação"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property____last_update
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table____last_update
msgid "Last Modified on"
msgstr "Última atualização em"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__write_uid
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_column__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_data__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__write_date
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__menu_id
msgid "Menu entry"
msgstr "Entrada do Menu"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_menu__purge_line_ids
msgid "Menus to purge"
msgstr "Menus para excluir"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__model_id
msgid "Model"
msgstr "Modelo"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_ir_model_data
msgid "Model Data"
msgstr "Modelo de data"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_model__purge_line_ids
msgid "Models to purge"
msgstr "Modelos para excluir"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_module__purge_line_ids
msgid "Modules to purge"
msgstr "Módulos para excluir"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__name
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__name
msgid "Name"
msgstr "Nome"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_menus.py:54
#, python-format
msgid "No dangling menu entries found"
msgstr "Nenhuma entrada de menu pendente encontrada"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_modules.py:95
#, python-format
msgid "No modules found to purge"
msgstr "Nenhum módulo encontrado para excluir"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_columns.py:128
#, python-format
msgid "No orphaned columns found"
msgstr "Nenhuma coluna orfã encontrada"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_data.py:68
#, python-format
msgid "No orphaned data entries found"
msgstr "Nenhuma entrada de dados orfã encontrada"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_models.py:120
#, python-format
msgid "No orphaned models found"
msgstr "Nenhum modelo orfão encontrado"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_tables.py:107
#, python-format
msgid "No orphaned tables found"
msgstr "Nenhuma tabela orfã encontrada"

#. module: database_cleanup
#: model_terms:ir.ui.view,arch_db:database_cleanup.form_purge_wizard
msgid "Nothing found to clean up."
msgstr "Nada foi encontrado para limpar."

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_property__purge_line_ids
msgid "Properties to purge"
msgstr "Propriedades para excluir"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__property_id
msgid "Property"
msgstr "Propriedade"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_column_line
#: model:ir.actions.server,name:database_cleanup.action_purge_data_line
#: model:ir.actions.server,name:database_cleanup.action_purge_menu_line
#: model:ir.actions.server,name:database_cleanup.action_purge_model_line
#: model:ir.actions.server,name:database_cleanup.action_purge_module_line
#: model:ir.actions.server,name:database_cleanup.action_purge_property_line
#: model:ir.actions.server,name:database_cleanup.action_purge_table_line
#: model_terms:ir.ui.view,arch_db:database_cleanup.form_purge_wizard
msgid "Purge"
msgstr "Excluir"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line
msgid "Purge Column Abstract Wizard"
msgstr "Assistente de Resumo da Coluna de Limpeza"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_column
msgid "Purge Column Wizard Lines"
msgstr "Linhas do Assistente de Coluna de Limpeza"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_wizard__purge_line_ids
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard__purge_line_ids
msgid "Purge Line"
msgstr "Linha de exclusão"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__wizard_id
msgid "Purge Wizard"
msgstr "Assistente de exclusão"

#. module: database_cleanup
#: model_terms:ir.ui.view,arch_db:database_cleanup.form_purge_wizard
msgid "Purge all"
msgstr "Excluir todos"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_columns
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_column
msgid "Purge columns"
msgstr "Excluir colunas"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_data
msgid "Purge data"
msgstr "Excluir dados"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_data
msgid "Purge data entries that refer to missing resources"
msgstr "Excluir entrada de dados que se referem a recursos faltantes"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_menus
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_menu
msgid "Purge menus"
msgstr "Excluir menus"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_models
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_model
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_model
#: model_terms:ir.ui.view,arch_db:database_cleanup.tree_purge_line
msgid "Purge models"
msgstr "Excluir modelos"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_modules
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_module
msgid "Purge modules"
msgstr "Excluir módulos"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_columns
msgid "Purge obsolete columns"
msgstr "Excluir colunas obsoletas"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_data
msgid "Purge obsolete data entries"
msgstr "Excluir entrada de dados obsoletas"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_menus
msgid "Purge obsolete menu entries"
msgstr "Excluir entradas de menu obsoletas"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_models
msgid "Purge obsolete models"
msgstr "Excluir modelos obsoletos"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_modules
msgid "Purge obsolete modules"
msgstr "Excluir módulos obsoletos"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_property
msgid "Purge obsolete properties"
msgstr "Eliminar propriedades obsoletas"

#. module: database_cleanup
#: model:ir.ui.menu,name:database_cleanup.menu_purge_tables
msgid "Purge obsolete tables"
msgstr "Excluir tabelas obsoletas"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_property
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_property
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_property
msgid "Purge properties"
msgstr "Excluir Propriedades"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard
msgid "Purge stuff"
msgstr "Limpar coisas"

#. module: database_cleanup
#: model:ir.actions.server,name:database_cleanup.action_purge_tables
#: model:ir.model,name:database_cleanup.model_cleanup_purge_wizard_table
msgid "Purge tables"
msgstr "Excluir tabelas"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_table
msgid "Purge tables wizard lines"
msgstr "Assistente para excluir linhas de tabelas"

#. module: database_cleanup
#: model_terms:ir.ui.view,arch_db:database_cleanup.tree_purge_line
msgid "Purge this model"
msgstr "Excluir este modelo"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_column__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_data__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_menu__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_model__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_module__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__purged
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_table__purged
msgid "Purged"
msgstr "Excluído"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line_property__reason
msgid "Reason"
msgstr "Razão"

#. module: database_cleanup
#: selection:cleanup.purge.line.property,reason:0
msgid "Same value as default"
msgstr "Mesmo valor como padrão"

#. module: database_cleanup
#: model_terms:ir.ui.view,arch_db:database_cleanup.form_purge_wizard
msgid "Select lines"
msgstr "Selecionar linhas"

#. module: database_cleanup
#: code:addons/database_cleanup/models/purge_wizard.py:75
#, python-format
msgid "Select lines to purge"
msgstr "Selecionar linhas para excluir"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_wizard_table__purge_line_ids
msgid "Tables to purge"
msgstr "Tabelas para excluir"

#. module: database_cleanup
#: selection:cleanup.purge.line.property,reason:0
msgid "Unknown model"
msgstr "Modelo desconhecido"

#. module: database_cleanup
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_create_indexes_line__wizard_id
#: model:ir.model.fields,field_description:database_cleanup.field_cleanup_purge_line__wizard_id
msgid "Wizard"
msgstr "Assitente"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_create_indexes_line
msgid "cleanup.create_indexes.line"
msgstr "cleanup.create_indexes.line"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_data
msgid "cleanup.purge.line.data"
msgstr "cleanup.purge.line.data"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_menu
msgid "cleanup.purge.line.menu"
msgstr "cleanup.purge.line.menu"

#. module: database_cleanup
#: model:ir.model,name:database_cleanup.model_cleanup_purge_line_module
msgid "cleanup.purge.line.module"
msgstr "cleanup.purge.line.module"
