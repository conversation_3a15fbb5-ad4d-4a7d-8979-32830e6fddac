<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="inherit_op_master_config_for_branch_rate" model="ir.ui.view">
        <field name="name">op.master</field>
        <field name="model">op.master</field>
        <field name="inherit_id" ref="nuro_op.nuro_op_master_form_view"/>
        <field name="arch" type="xml">
            <field name="op_charges" position="after">
                <field name="branch_wise_rate" groups="nuro_cashier_closing.group_cashier_user"/>
            </field>
            <xpath expr="//sheet" position="inside">
                <group string="Branch Rate" name="rate_branch" attrs="{'invisible': [('branch_wise_rate', '=', False)]}">
                    <field name="op_branch_rate_line" nolabel="1"
                    attrs="{'invisible': [('branch_wise_rate', '=', False)]}">
                        <tree editable="bottom">
                            <field name="branch_id" required="1"
                                   options="{'no_create': True}"/>
                            <field name="charge" required="1"/>
                        </tree>
                    </field>
                </group>
            </xpath>
        </field>
    </record>
</odoo>